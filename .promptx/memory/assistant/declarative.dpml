<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753443400613_2j2gpf57x" time="2025/07/25 19:36">
    <content>
      山西电信出账稽核AI系统项目完整归档整理：1)创建752行技术规格文档TECHNICAL_SPECIFICATIONS.md和完整文档体系；2)建立生产环境配置化部署方案，包括Docker容器化、配置管理系统、一键部署脚本；3)重新组织docs目录为core/technical/guides/reports/archive五个分类；4)合并test_output到outputs目录并按models/data/reports/visualizations/temp分类；5)将requirements.txt归档到deployment目录；6)更新所有配置文件路径，实现清晰的文档结构、专业的部署体系、规范的输出管理和便于维护的架构
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753443408693_8uxp3631y" time="2025/07/25 19:36">
    <content>
      用户偏好和工作模式：注重项目文件的规范化组织和专业化管理，偏好维护完整目录结构，避免在项目开发过程中在根目录随意生成文件，要求清晰的文档分类、专业的部署体系、规范的输出管理和便于维护的架构
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753443415969_ajvbjm9ev" time="2025/07/25 19:36">
    <content>
      项目技术架构和配置：山西电信出账稽核AI系统v2.0.0，支持千万级数据处理，包含完整的Docker容器化部署(Dockerfile+docker-compose.yml)、配置管理系统(production_config.json支持环境变量替换)、一键部署脚本(deploy_production.sh)、配置化训练脚本，实现环境变量支持、配置验证、多环境管理和完全配置化的生产部署能力
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753443649925_mmhh4fckv" time="2025/07/25 19:40">
    <content>
      项目代码架构说明：src/目录包含核心代码库(面向对象设计、模块化架构、可导入的Python包)，scripts/production/目录包含生产执行脚本(可直接执行、命令行参数支持、生产环境优化)。两者是互补关系：src/提供核心功能模块，scripts/production/调用src/模块实现生产部署。开发阶段使用src/模块，生产部署使用scripts/脚本，Docker容器中执行production脚本。
    </content>
    <tags>#工具使用</tags>
  </item>
</memory>