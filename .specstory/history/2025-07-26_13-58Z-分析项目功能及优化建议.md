<!-- Generated by SpecStory -->

# 分析项目功能及优化建议 (2025-07-26 13:58Z)

_**User**_

分析下当前项目实现的所有功能，是否有bug或待优化点，注意不要改代码，只需要展示内容

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我分析当前项目实现的所有功能，找出是否有bug或待优化点，但不要改代码，只需要展示内容。

根据项目结构，这是一个出账稽核AI项目，看起来是用于电信公司的账单稽核。我需要：

1. 首先了解项目的整体架构和功能
2. 查看主要的代码文件，了解实现的功能
3. 分析可能存在的bug或待优化点
4. 只展示分析结果，不修改代码

让我从以下几个方面入手：
1. 查看README和文档，了解项目整体功能
2. 查看主要的代码文件，了解具体实现
3. 查看测试文件，了解测试覆盖情况
4. 查看配置文件，了解系统配置
5. 分析可能的问题和优化点

我需要并行执行多个工具调用来获取这些信息。</details></think>

---

我来帮您分析当前项目的功能实现情况，查找可能的bug和待优化点。让我先了解项目的整体结构和功能。

---

Read file: README.md

---

Read file: docs/文档中心.md

---

Read file: docs/core/技术规格文档.md

---

<details>
                <summary>Searched codebase "主要功能模块有哪些？核心业务逻辑是什么？" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `src/billing_audit/large_scale/__init__.py` | L1-32 |
| `src/billing_audit/training/enhanced_model_trainer.py` | L252-304 |
| `src/billing_audit/training/enhanced_model_trainer.py` | L1-35 |
| `src/billing_audit/training/train_large_scale_model.py` | L171-222 |
| `src/billing_audit/training/enhanced_model_trainer.py` | L304-334 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L1-29 |
| `src/billing_audit/training/enhanced_model_trainer.py` | L90-143 |
| `src/billing_audit/training/enhanced_model_trainer.py` | L143-176 |
| `src/billing_audit/models/large_scale_model_evaluation.py` | L1-28 |
| `src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L1-59 |
| `src/billing_audit/inference/__init__.py` | L1-11 |
| `src/billing_audit/inference/predict_large_scale.py` | L308-346 |
| `src/billing_audit/large_scale/feature_engineer.py` | L1-74 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L395-443 |
| `src/billing_audit/models/large_scale_model_evaluation.py` | L323-356 |
| `src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L81-109 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L29-77 |
| `src/billing_audit/training/train_large_scale_model.py` | L1-37 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L130-176 |
| `legacy_code/src/billing_audit/models/model_evaluator.py` | L277-321 |
| `src/billing_audit/preprocessing/__init__.py` | L1-9 |
| `src/billing_audit/training/__init__.py` | L1-9 |
| `src/billing_audit/preprocessing/data_splitter.py` | L1-63 |
| `legacy_code/src/billing_audit/models/model_evaluator.py` | L120-168 |
| `src/billing_audit/inference/predict_large_scale.py` | L1-30 |

</details>

---

现在让我查看更多核心功能模块和配置文件：

---

Read file: scripts/production/billing_audit_main.py

---

Read file: config/production_config.json

---

<details>
                <summary>Searched codebase "模型预测器的实现逻辑是怎样的？有哪些预测功能？" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L156-199 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L1-53 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L199-247 |
| `src/billing_audit/inference/predict_large_scale.py` | L30-66 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L130-176 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L117-156 |
| `src/billing_audit/inference/predict_large_scale.py` | L308-346 |
| `legacy_code/src/billing_audit/inference/billing_judge.py` | L31-87 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L247-299 |
| `src/billing_audit/inference/predict_large_scale.py` | L269-308 |
| `src/billing_audit/inference/predict_large_scale.py` | L66-131 |
| `src/billing_audit/inference/predict_large_scale.py` | L159-211 |
| `src/billing_audit/inference/predict_large_scale.py` | L131-159 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L329-378 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L299-329 |
| `src/billing_audit/inference/predict_large_scale.py` | L1-30 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L86-117 |
| `src/billing_audit/inference/__init__.py` | L1-11 |
| `src/billing_audit/inference/predict_large_scale.py` | L211-250 |
| `legacy_code/src/billing_audit/inference/billing_judge.py` | L1-31 |
| `legacy_code/src/billing_audit/inference/model_predictor.py` | L53-86 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L29-77 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L77-96 |
| `src/billing_audit/inference/predict_large_scale.py` | L250-269 |
| `src/billing_audit/inference/large_scale_billing_judge.py` | L395-443 |

</details>

---

<details>
            <summary>Grep search for "class BillingJudge" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/billing_judge.py` | L34 | `class BillingJudge:` |

</details>

---

让我继续查看其他重要模块和测试文件：

---

Read file: legacy_code/src/billing_audit/inference/billing_judge.py

---

<details>
            <summary>Grep search for "TODO|FIXME|BUG|XXX|HACK|WARNING" • **24** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L18 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L19 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L285 | `self.logger.warning(f"特征创建部分失败: {e}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L321 | `self.logger.debug(f"开始转换数据块，输入形状: {chunk.shape}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L325 | `self.logger.debug(f"特征创建完成，输出形状: {df_features.shape}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L329 | `self.logger.debug(f"类别特征编码完成")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py` | L333 | `self.logger.debug(f"数值特征标准化完成，最终形状: {df_scaled.shape}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L13 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L38 | `'warnings': [],` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L62 | `result['warnings'].append(f"发现 {duplicate_count} 行重复数据")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L75 | `result['warnings'].append(f"列 '{col}' 缺失率过高: {missing_rate:.2%}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L87 | `'warnings': [],` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L116 | `result['warnings'].append(f"列 '{col_name}' 发现 {len(outliers)} 个异常值 ({outlier_rate:.2%})")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/logger.py` | L98 | `def debug(self, message: str, *args, **kwargs):` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/logger.py` | L100 | `self.logger.debug(message, *args, **kwargs)` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/logger.py` | L106 | `def warning(self, message: str, *args, **kwargs):` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/logger.py` | L108 | `self.logger.warning(message, *args, **kwargs)` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/predict_large_scale.py` | L17 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/predict_large_scale.py` | L18 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L17 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L18 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L378 | `self.logger.warning(f"判定错误: {stats['error_count']:,} 条")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L22 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L23 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L281 | `logger.warning(f"没有数据可以合并，训练终止")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/large_scale/feature_engineer.py` | L18 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/large_scale/feature_engineer.py` | L19 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/models/large_scale_model_evaluation.py` | L18 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/models/large_scale_model_evaluation.py` | L19 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L48 | `self.logger.warning("RandomForest 不可用")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L57 | `self.logger.warning("XGBoost 不可用")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L66 | `self.logger.warning("LightGBM 不可用")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L164 | `self.logger.warning(f"跳过不可用的算法: {algorithm}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L173 | `self.logger.warning(f"算法 {algorithm} 失败，尝试下一个算法")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L188 | `self.logger.warning(f"跳过不可用的算法: {algorithm}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L110 | `self.logger.warning(f"环境变量 {var_name} 未找到，保持原值")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L156 | `self.logger.warning(f"算法 {algo} 缺少超参数配置")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L186 | `self.logger.debug(f"创建目录: {dir_path}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L95 | `self.logger.debug(f"创建目录: {name} -> {path}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L123 | `self.logger.debug(f"输出: {result.stdout}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L265 | `self.logger.warning("增强训练器的命令行接口尚未实现，使用大规模训练脚本")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L519 | `self.logger.warning("模型评估失败，但继续执行后续步骤")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L564 | `# TODO: 实际应该是：` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L1267 | `parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/multi_algorithm_training.py` | L20 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/multi_algorithm_training.py` | L21 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/simple_field_test.py` | L56 | `result = {'is_valid': True, 'errors': [], 'warnings': []}` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/simple_field_test.py` | L61 | `result['warnings'].append(f"列 '{col_name}' 有 {len(invalid_years)} 个年份超出合理范围")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/simple_field_test.py` | L66 | `result = {'is_valid': True, 'errors': [], 'warnings': []}` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/simple_field_test.py` | L77 | `result = {'is_valid': True, 'errors': [], 'warnings': []}` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/simple_field_test.py` | L95 | `if result['warnings']:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/simple_field_test.py` | L96 | `print(f"  {field}: {result['warnings'][0]}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/data_preprocessor.py` | L131 | `'warnings': [],` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/data_preprocessor.py` | L146 | `result['warnings'].append(f"列 '{col_name}' 有 {invalid_count} 个无效日期")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/data_preprocessor.py` | L165 | `result = {'is_valid': True, 'errors': [], 'warnings': [], 'stats': {}}` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/data_preprocessor.py` | L173 | `result['warnings'].append(f"列 '{col_name}' 有 {len(invalid_years)} 个年份超出合理范围(2020-2030)")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/data_preprocessor.py` | L188 | `result = {'is_valid': True, 'errors': [], 'warnings': [], 'stats': {}}` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/data_preprocessor.py` | L212 | `result = {'is_valid': True, 'errors': [], 'warnings': [], 'stats': {}}` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/data_preprocessor.py` | L350 | `logger.warning("数据验证未通过，但继续处理")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/models/model_validator.py` | L98 | `logger.warning("sklearn不可用，使用手动交叉验证")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/feature_engineer.py` | L80 | `logger.warning(f"处理日期列 {col} 失败: {e}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/feature_engineer.py` | L98 | `logger.warning(f"处理生效日期组合特征失败: {e}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/feature_engineer.py` | L115 | `logger.warning(f"处理失效日期组合特征失败: {e}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/feature_engineer.py` | L157 | `logger.warning(f"创建组合日期特征失败: {e}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/preprocessing/feature_engineer.py` | L353 | `logger.warning(f"缺少特征列: {missing_cols}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/model_predictor.py` | L60 | `logger.warning(f"模型目录不存在: {models_dir}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/model_predictor.py` | L67 | `logger.warning(f"未找到{self.fee_type}模型")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/model_predictor.py` | L95 | `logger.warning(f"模型文件不存在: {model_file}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/model_predictor.py` | L112 | `logger.warning("特征工程器文件不存在，将使用默认配置")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/model_predictor.py` | L146 | `logger.warning(f"缺少必要字段: {missing_fields}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/model_predictor.py` | L349 | `'warnings': [],` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/model_predictor.py` | L360 | `validation_result['warnings'].append(f"缺少特征: {missing_features}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/billing_judge.py` | L72 | `logger.debug("开始单个收费判定")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/inference/billing_judge.py` | L109 | `logger.debug(f"判定完成: {judgment_result.value}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/hyperparameter_tuner.py` | L140 | `logger.warning("sklearn或xgboost未安装，使用模拟搜索")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/hyperparameter_tuner.py` | L214 | `logger.warning("sklearn或xgboost未安装，使用模拟搜索")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/hyperparameter_tuner.py` | L337 | `logger.warning("未进行超参数调优")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/hyperparameter_tuner.py` | L345 | `logger.warning("未进行超参数调优")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/model_trainer.py` | L133 | `logger.warning(f"XGBoost不可用: {e}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/model_trainer.py` | L155 | `logger.warning(f"LightGBM不可用: {e2}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/model_trainer.py` | L173 | `logger.warning(f"RandomForest不可用: {e3}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/legacy_code/src/billing_audit/training/model_trainer.py` | L406 | `logger.warning("特征重要性未计算")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L110 | `self.logger.warning(f"环境变量 {var_name} 未找到，保持原值")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L156 | `self.logger.warning(f"算法 {algo} 缺少超参数配置")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L186 | `self.logger.debug(f"创建目录: {dir_path}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L22 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L23 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L281 | `logger.warning(f"没有数据可以合并，训练终止")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L48 | `self.logger.warning("RandomForest 不可用")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L57 | `self.logger.warning("XGBoost 不可用")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L66 | `self.logger.warning("LightGBM 不可用")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L164 | `self.logger.warning(f"跳过不可用的算法: {algorithm}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L173 | `self.logger.warning(f"算法 {algorithm} 失败，尝试下一个算法")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L188 | `self.logger.warning(f"跳过不可用的算法: {algorithm}")` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L17 | `import warnings` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L18 | `warnings.filterwarnings('ignore')` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L378 | `self.logger.warning(f"判定错误: {stats['error_count']:,} 条")` |

</details>

---

<details>
                <summary>Searched codebase "测试用例中有哪些测试场景？测试覆盖情况如何？" • **25** results</summary>
            
| File | Lines |
|------|-------|
| `scripts/testing/comprehensive_system_test.py` | L81-122 |
| `scripts/testing/large_scale_end_to_end_test.py` | L418-478 |
| `scripts/testing/core_functionality_test.py` | L339-372 |
| `scripts/testing/comprehensive_module_test.py` | L287-316 |
| `scripts/testing/scripts_validation_test.py` | L116-143 |
| `scripts/testing/config_only_test.py` | L315-348 |
| `scripts/testing/scripts_validation_test.py` | L284-317 |
| `scripts/testing/test_inference_judgment.py` | L256-292 |
| `scripts/testing/test_model_evaluation.py` | L211-245 |
| `scripts/testing/comprehensive_system_test.py` | L275-313 |
| `scripts/testing/end_to_end_test.py` | L1-31 |
| `scripts/testing/comprehensive_module_test.py` | L1-55 |
| `scripts/testing/comprehensive_system_test.py` | L1-32 |
| `scripts/testing/end_to_end_test.py` | L31-83 |
| `scripts/testing/end_to_end_test.py` | L224-243 |
| `scripts/testing/core_functionality_test.py` | L31-67 |
| `scripts/testing/comprehensive_module_test.py` | L125-173 |
| `scripts/testing/core_functionality_test.py` | L372-384 |
| `scripts/testing/comprehensive_system_test.py` | L32-81 |
| `scripts/testing/comprehensive_system_test.py` | L122-161 |
| `scripts/testing/comprehensive_system_test.py` | L161-206 |
| `scripts/testing/comprehensive_module_test.py` | L316-328 |
| `scripts/testing/core_functionality_test.py` | L254-306 |
| `scripts/testing/comprehensive_system_test.py` | L313-325 |
| `scripts/testing/test_model_evaluation.py` | L80-129 |

</details>

---

<details>
            <summary>Grep search for "try.*except.*pass|except.*:$|except Exception" • **36** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L174 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/utils/data_utils.py` | L193 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/preprocessing/data_splitter.py` | L144 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/demo/main_script_demo.py` | L45 | `except subprocess.TimeoutExpired:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/demo/main_script_demo.py` | L47 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/model_performance_comparison.py` | L248 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/analyze_data_samples.py` | L63 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_config.py` | L59 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_preprocessing_logic.py` | L42 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_preprocessing_logic.py` | L69 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_preprocessing_logic.py` | L100 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_preprocessing_logic.py` | L150 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_training_logic.py` | L74 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_training_logic.py` | L191 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_training_logic.py` | L214 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_model_evaluation.py` | L126 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_model_evaluation.py` | L170 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_model_evaluation.py` | L208 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_model_evaluation.py` | L231 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_inference_judgment.py` | L74 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_inference_judgment.py` | L143 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_inference_judgment.py` | L231 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_inference_judgment.py` | L277 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/large_scale_end_to_end_test.py` | L81 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/large_scale_end_to_end_test.py` | L170 | `except subprocess.TimeoutExpired:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/large_scale_end_to_end_test.py` | L173 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/large_scale_end_to_end_test.py` | L306 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/large_scale_end_to_end_test.py` | L366 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/large_scale_end_to_end_test.py` | L474 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_evaluation_logic.py` | L63 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_evaluation_logic.py` | L203 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_evaluation_logic.py` | L251 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/validation/validate_evaluation_logic.py` | L275 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/comprehensive_system_test.py` | L74 | `except subprocess.TimeoutExpired:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/comprehensive_system_test.py` | L83 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/comprehensive_system_test.py` | L160 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/train_large_scale_new.py` | L62 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/config_only_test.py` | L77 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/config_only_test.py` | L131 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/config_only_test.py` | L277 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_preprocessing.py` | L47 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/test_preprocessing.py` | L80 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/end_to_end_test.py` | L221 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/core_functionality_test.py` | L90 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/core_functionality_test.py` | L155 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/core_functionality_test.py` | L256 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L144 | `except subprocess.TimeoutExpired:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L153 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L192 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L571 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L800 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L841 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L881 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L897 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L908 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L961 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L995 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L1001 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/production/billing_audit_main.py` | L1340 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/scripts_validation_test.py` | L65 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/scripts_validation_test.py` | L107 | `except FileNotFoundError:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/scripts_validation_test.py` | L118 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/scripts_validation_test.py` | L215 | `except subprocess.TimeoutExpired:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/scripts_validation_test.py` | L218 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/scripts_validation_test.py` | L225 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/scripts_validation_test.py` | L244 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L78 | `except ImportError as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L145 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L194 | `if 'except' in legacy_content and ('XGBoost' in legacy_content or 'LightGBM' in legacy_content):` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L198 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L235 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L246 | `except:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L249 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L282 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L293 | `if 'try:' in content and 'except' in content:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/algorithm_support_analysis.py` | L308 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/comprehensive_module_test.py` | L78 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/comprehensive_module_test.py` | L119 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/comprehensive_module_test.py` | L203 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_mock_data.py` | L166 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_best_algorithm.py` | L41 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_best_algorithm.py` | L178 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_best_algorithm.py` | L186 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_best_algorithm.py` | L259 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_best_algorithm.py` | L353 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/update_doc_references.py` | L100 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/update_doc_references.py` | L111 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/quick_train_test.py` | L219 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_updated_preprocessing.py` | L19 | `except ImportError as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_updated_preprocessing.py` | L31 | `except Exception as e2:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_updated_preprocessing.py` | L79 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_updated_preprocessing.py` | L129 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_updated_preprocessing.py` | L164 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/remove_emojis.py` | L46 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/rename_docs_to_chinese.py` | L79 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/rename_docs_to_chinese.py` | L141 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/rename_docs_to_chinese.py` | L166 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/simple_data_analysis.py` | L30 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_config_improvements.py` | L34 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/tools/test_config_improvements.py` | L55 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L18 | `class ConfigValidationError(Exception):` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L88 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L197 | `except (KeyError, TypeError):` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L253 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/config/production_config_manager.py` | L327 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L99 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L128 | `except KeyError as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L259 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/train_large_scale_model.py` | L420 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/config_only_test.py` | L77 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/config_only_test.py` | L131 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/scripts/testing/config_only_test.py` | L277 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L46 | `except ImportError:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L55 | `except ImportError:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L64 | `except ImportError:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L134 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/training/enhanced_model_trainer.py` | L331 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L93 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L127 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L173 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L291 | `except Exception as e:` |
| `file:///Users/<USER>/work/01-%E4%BA%A7%E5%93%81/%E4%B9%9D%E6%80%9D%E8%AE%A1%E8%B4%B9%E4%B8%93%E5%AE%B6/%E7%9C%81%E4%BB%BD%E8%AF%95%E7%82%B9/%E5%B1%B1%E8%A5%BF%E7%94%B5%E4%BF%A1/%E5%87%BA%E8%B4%A6%E7%A8%BD%E6%A0%B8AI/src/billing_audit/inference/large_scale_billing_judge.py` | L438 | `except Exception as e:` |

</details>

---

现在我需要查看一些具体的问题示例和错误处理情况：

---

Read file: src/billing_audit/preprocessing/large_scale_feature_engineer.py

---

Read file: scripts/production/billing_audit_main.py

---

Read file: scripts/production/billing_audit_main.py

---

Failed to execute tool interactive_feedback

---

基于我对项目的分析，让我为您展示当前项目实现的所有功能以及存在的bug和待优化点：

## 📊 山西电信出账稽核AI系统功能分析报告

### 一、✅ 已实现的核心功能

#### 1. **数据处理功能**
- ✅ **大规模数据处理**：支持千万级数据的批量处理（50,000行/批）
- ✅ **特征工程**：自动生成27个特征（原始16个+新增11个）
- ✅ **增量统计计算**：使用Welford算法进行内存高效的统计
- ✅ **数据拆分**：支持训练集/测试集拆分

#### 2. **模型训练功能**
- ✅ **多算法支持**：RandomForest、XGBoost、LightGBM
- ✅ **容错机制**：算法不可用时自动降级
- ✅ **超参数配置**：通过配置文件管理超参数
- ✅ **模型保存**：自动保存模型文件和元数据

#### 3. **预测与判定功能**
- ✅ **批量预测**：80,000-120,000样本/秒的处理速度
- ✅ **收费合理性判定**：基于阈值的三级判定（合理/不合理/不确定）
- ✅ **置信度计算**：基于误差的置信度评分
- ✅ **结果输出**：支持CSV、JSON多种格式

#### 4. **系统功能**
- ✅ **生产级主脚本**：支持全流程自动化和模块化执行
- ✅ **配置管理**：环境变量支持、多环境配置
- ✅ **日志系统**：完整的日志记录和轮转机制
- ✅ **性能监控**：内存、CPU、处理速度监控

### 二、🐛 存在的Bug和问题

#### 1. **代码质量问题**
```python
# 问题1：过于宽泛的异常捕获
except Exception as e:  # 发现大量此类代码
    logger.error(f"错误: {e}")
    
# 问题2：忽略警告
warnings.filterwarnings('ignore')  # 多个文件都有

# 问题3：空的异常处理
except:  # billing_audit_main.py 多处
    pass
```

#### 2. **功能实现不完整**
- ❌ **增强训练器CLI接口缺失**：`billing_audit_main.py:264`
  ```python
  self.logger.warning("增强训练器的命令行接口尚未实现，使用大规模训练脚本")
  ```

- ❌ **特征工程保存逻辑缺失**：`billing_audit_main.py:563`
  ```python
  # TODO: 实际应该是：
  # 1. 加载特征工程器
  # 2. 处理原始数据
  # 3. 保存为CSV格式
  ```

- ❌ **REST API未实现**：技术文档中标注"待实现"
- ❌ **用户行为分析模块未实现**：README中标注"待实现"

#### 3. **错误处理不当**
```python
# LargeScaleBillingJudge.judge_chunk() 捕获所有异常但处理简单
except Exception as e:
    self.logger.error(f"数据块判定失败: {e}")
    raise  # 直接抛出，缺少恢复机制
```

#### 4. **配置管理问题**
- 硬编码路径：多处使用硬编码的相对路径
- 环境变量未找到时只是警告，可能导致运行时错误
- 配置文件中的数据库密码是明文：`"DB_PASSWORD": "your_db_password_here"`

### 三、🔧 待优化点

#### 1. **性能优化**
- **内存管理**：虽然支持批量处理，但某些操作仍可能导致内存峰值
  ```python
  # train_large_scale_model.py 合并所有数据
  X_all.extend(X_processed)  # 可能导致内存问题
  ```

- **并行处理**：虽然设置了`n_jobs=-1`，但某些操作仍是串行的
- **I/O优化**：大文件读写可以使用更高效的方式（如内存映射）

#### 2. **代码结构优化**
- **重复代码**：legacy_code和src目录下有大量重复功能
- **模块耦合**：主脚本过长（1349行），功能耦合严重
- **命名不一致**：如`large_scale`和`enhanced`两套命名体系

#### 3. **测试覆盖不足**
- 缺少单元测试（tests/unit目录基本为空）
- 集成测试主要是端到端测试，缺少中间层测试
- 没有性能基准测试

#### 4. **文档与代码不同步**
- 技术规格文档版本是v1.0.0，但系统已经是v2.1.0
- 某些功能文档中有描述但代码中未实现

#### 5. **安全性问题**
- 配置文件中包含敏感信息（如密码）
- 没有输入验证和清洗机制
- SQL注入风险（如果启用数据库功能）

### 四、🚀 优化建议

#### 1. **紧急修复**
1. 完善异常处理机制，避免使用宽泛的Exception
2. 实现缺失的TODO功能
3. 修复硬编码路径问题
4. 加强输入验证

#### 2. **短期优化**（1-2周）
1. 重构主脚本，拆分功能模块
2. 统一命名规范
3. 补充单元测试
4. 更新文档与代码同步

#### 3. **长期优化**（1-2月）
1. 实现REST API接口
2. 优化内存管理策略
3. 添加分布式处理支持
4. 实现用户行为分析模块
5. 加强安全性措施

### 五、📈 项目评估

**整体评分：7.5/10**

**优点**：
- ✅ 功能完整，支持端到端流程
- ✅ 性能优秀，支持千万级数据
- ✅ 有完善的配置管理系统
- ✅ 日志记录详细

**不足**：
- ❌ 代码质量有待提升
- ❌ 部分功能未实现
- ❌ 测试覆盖不足
- ❌ 安全性考虑不够

**结论**：系统基本满足生产需求，但在代码质量、完整性和安全性方面仍需改进。建议在投入大规模生产使用前，至少完成紧急修复和短期优化项。

---

