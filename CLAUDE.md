# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

山西电信出账稽核AI系统 v2.1.0 - 一个基于机器学习的智能收费合理性判定系统，专为电信运营商设计。系统通过分析用户的定价特征和历史数据，自动判定收费的合理性，支持千万级数据处理和分层建模。

## 核心开发命令

### 环境设置
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r deployment/requirements.txt

# macOS安装OpenMP支持
/opt/homebrew/bin/brew install libomp

# 环境设置脚本
source scripts/production/setup_env.sh
source scripts/production/setup_production_env.sh
```

### 生产级主脚本 (推荐)
```bash
# 完整流程自动化
python scripts/production/billing_audit_main.py full \
    --input data/raw_billing_data.csv \
    --algorithm hierarchical \
    --batch-size 1000

# 模块化执行
python scripts/production/billing_audit_main.py feature-engineering --input data.csv --batch-size 1000
python scripts/production/billing_audit_main.py training --input data.csv --algorithm hierarchical --batch-size 1000
python scripts/production/billing_audit_main.py evaluation --input data.csv --batch-size 1000
python scripts/production/billing_audit_main.py prediction --input data.csv --batch-size 1000
python scripts/production/billing_audit_main.py judgment --input data.csv --abs-threshold 50.0 --rel-threshold 0.1
```

### 大规模数据处理
```bash
# 千万级数据训练
python scripts/production/train_large_scale_model.py \
    --input /path/to/10million_data.csv \
    --output ./models/production \
    --batch-size 50000

# 大规模预测
python scripts/production/predict_large_scale.py \
    --input /path/to/predict_data.csv \
    --model ./models/large_scale_model.pkl \
    --feature-engineer ./models/large_scale_feature_engineer.pkl \
    --output ./results/predictions.csv \
    --batch-size 50000

# 大规模收费判定
python scripts/production/large_scale_billing_judge.py \
    --input /path/to/billing_data.csv \
    --model ./models/large_scale_model.pkl \
    --feature-engineer ./models/large_scale_feature_engineer.pkl \
    --output ./results/billing_judgments.csv \
    --abs-threshold 50.0 \
    --rel-threshold 0.1 \
    --batch-size 50000
```

### 模型训练（传统方式，兼容性）
```bash
# 训练固费模型
python scripts/production/train_billing_models.py --fee-type fixed_fee

# 训练所有模型
python scripts/production/train_billing_models.py --fee-type all

# 启用超参数调优
python scripts/production/train_billing_models.py --fee-type fixed_fee --tune-hyperparams
```

### 测试和验证
```bash
# 端到端测试
python scripts/testing/end_to_end_test.py

# 大规模系统测试
python scripts/testing/large_scale_end_to_end_test.py

# 生产系统测试
python tests/test_production_end_to_end.py

# 单元测试
python -m pytest tests/

# 带覆盖率测试
python -m pytest tests/ --cov=src --cov-report=html

# 性能基准测试
python tests/test_performance_benchmark.py
```

### 数据验证
```bash
# 验证配置文件
python scripts/validation/validate_config.py

# 验证预处理逻辑
python scripts/validation/validate_preprocessing_logic.py

# 验证训练逻辑
python scripts/validation/validate_training_logic.py

# 验证评估逻辑
python scripts/validation/validate_evaluation_logic.py
```

### 数据分析工具
```bash
# 分析数据样例
python scripts/tools/analyze_data_samples.py

# 简化数据分析
python scripts/tools/simple_data_analysis.py

# 生成模拟数据
python scripts/tools/generate_mock_data.py

# 快速训练测试
python scripts/tools/quick_train_test.py

# 模型性能对比
python scripts/tools/model_performance_comparison.py

# 初始化项目结构
python scripts/tools/init_project_structure.py
```

### 代码质量检查
```bash
# 代码格式化
black src/ scripts/ tests/

# 代码风格检查
flake8 src/ scripts/ tests/

# 导入排序
isort src/ scripts/ tests/
```

## 项目架构

### 核心模块结构

1. **`src/billing_audit/`** - 计费稽核核心模块
   - `preprocessing/` - 数据预处理和特征工程
   - `training/` - 模型训练
   - `models/` - 模型评估和验证
   - `inference/` - 模型预测和收费判定
   - `large_scale/` - 大规模数据处理 ⭐ v2.1.0新增
   - `reporting/` - 报告生成 ⭐ v2.1.0新增

2. **`src/config/`** - 配置管理模块 ⭐ v2.1.0增强
   - `production_config_manager.py` - 生产级配置管理器
   - `dynamic_schema_manager.py` - 动态模式管理器

3. **`src/utils/`** - 通用工具模块
   - `config_manager.py` - 统一配置管理
   - `data_utils.py` - 数据处理工具
   - `logger.py` - 日志管理

### 关键类和功能

#### ModelPredictor (`src/billing_audit/inference/model_predictor.py`)
- 模型预测器，负责加载模型和进行预测
- 支持批量预测和详细预测结果
- 自动处理特征预处理和数据验证

#### BillingJudge (`src/billing_audit/inference/billing_judge.py`)
- 收费判定器，基于模型预测进行收费合理性判定
- 支持单个和批量判定
- 提供详细的判定报告和统计信息

#### HierarchicalBillingModel (`src/billing_audit/models/hierarchical_billing_model.py`) ⭐ v2.1.0核心
- 分层计费模型，专门解决零值数据问题
- 零值分类器：96.77%零值识别准确率
- 非零值回归器：精确预测非零值金额
- 两阶段预测策略

#### LargeScaleFeatureEngineer (`src/billing_audit/preprocessing/large_scale_feature_engineer.py`)
- 大规模特征工程器
- 自动创建27个特征（原16个+新增11个）
- 支持增量统计计算（Welford算法）
- 内存高效的数据处理

#### BillingModelTrainer (`src/billing_audit/training/model_trainer.py`)
- 模型训练器，支持XGBoost、LightGBM、RandomForest等算法
- 具备容错机制，在主要库不可用时会使用备选算法
- 自动保存模型、特征重要性和训练历史

#### ConfigManager (`src/utils/config_manager.py`)
- 统一配置管理器，支持JSON和YAML格式
- 提供类型化的配置访问接口
- 支持配置验证和动态更新

### 数据流架构

1. **数据输入** - CSV文件（支持千万级数据）
2. **数据预处理** - 特征工程（16→27特征）、数据清洗、格式转换
3. **模型训练** - 多算法支持（分层建模/RF/XGBoost/LightGBM）、超参数调优
4. **模型评估** - 分层评估、性能指标计算
5. **模型推理** - 预测、判定、批量处理（154,286条/秒）
6. **结果输出** - CSV报告、JSON统计、Markdown执行报告 ⭐ v2.1.0新增

## 配置文件

### 主配置文件
- **开发环境配置** (`config/billing_audit_config.json`)
- **生产环境配置** (`config/production_config.json`) ⭐ v2.1.0新增

### 重要配置项
- `billing_audit.fixed_fee.feature_columns` - 固费特征列
- `billing_audit.judgment_thresholds` - 判定阈值配置
- `large_scale_processing.batch_size` - 批处理大小（默认250）
- `model_training.algorithms` - 支持的算法列表
- `feature_engineering.enable_auto_features` - 自动特征工程
- 环境变量支持（`${VAR_NAME}`语法）

## 模型和数据

### 模型存储
- 训练好的模型保存在 `models/` 目录
- 大规模模型文件：`large_scale_model.pkl`
- 特征工程器：`large_scale_feature_engineer.pkl`
- 模型信息包含训练历史、评估结果、特征重要性

### 数据格式
- 输入：CSV格式的计费数据（支持千万级）
- 输出：CSV格式的预测结果和判定报告
- 必需字段参考配置文件中的 `feature_columns`
- 预测结果：27列标准格式（14原始+11特征+1预测+1判定）

## 开发注意事项

### 分层建模系统 ⭐ v2.1.0核心特性
- **零值分类器**：96.77%零值识别准确率
- **非零值回归器**：精确预测非零值金额
- **两阶段预测**：先分类后回归的策略
- **状态管理**：完整的模型状态备份和恢复机制

### 特征工程
- 自动创建27个特征（原16个+新增11个）
- 业务逻辑特征、日期特征、组合特征、交互特征
- 分类特征one-hot编码，数值特征标准化
- 增量统计计算（Welford算法）

### 模型容错
- 系统优先使用XGBoost，依次降级到LightGBM、RandomForest
- 如果所有ML库都不可用，会使用MockXGBModel进行模拟
- 预测失败时会返回详细的错误信息

### 判定逻辑
- 支持绝对阈值和相对阈值的混合判定
- 判定结果：reasonable（合理）、unreasonable（不合理）、uncertain（不确定）
- 提供置信度评分和详细的误差分析

### 日志和输出
- 所有操作都有详细的日志记录
- 输出文件自动加时间戳避免覆盖
- 支持批量处理的进度跟踪

### 性能指标
- **处理速度**：154,286样本/秒（预测），1,800+样本/秒（判定）
- **模型性能**：R² 0.91+，MAE 7.23元
- **业务准确率**：96%（±50元内）
- **零值识别准确率**：96.77%（分层建模）
- **内存效率**：比传统方法节省80%

## 故障排除

### 常见问题
1. **XGBoost/LightGBM导入失败** - 确保安装了OpenMP：`/opt/homebrew/bin/brew install libomp`
2. **模型加载失败** - 检查模型文件路径和权限
3. **数据格式错误** - 参考配置文件中的特征列定义
4. **内存不足** - 减少批处理大小或使用数据采样
5. **生产环境配置** - 确保设置了正确的环境变量

### 环境要求
- Python 3.9+
- macOS (Apple Silicon) 或 Linux  
- 小规模8GB+ 内存，大规模16GB+（推荐32GB）
- CPU：8核+（推荐16核）
- 依赖包在 `deployment/requirements.txt` 中定义

### 依赖包说明
- **机器学习**: XGBoost, LightGBM, scikit-learn
- **数据处理**: pandas, numpy, openpyxl
- **可视化**: matplotlib, seaborn, plotly
- **配置管理**: PyYAML, python-dotenv
- **Web API**: FastAPI, uvicorn, pydantic
- **开发工具**: black, flake8, isort, pytest

### 生产部署（推荐）
```bash
# 精简版部署包部署（一键部署）
tar -xzf billing_audit_production_v2.1.0_*.tar.gz
cd billing_audit_production_v2.1.0_*/
bash deploy.sh

# 运行测试
bash run_with_mount.sh full your_data.csv hierarchical 1000
```

### Docker部署
```bash
# 构建Docker镜像
docker build -t billing-audit-ai .

# 生产部署
./deployment/scripts/deploy_production.sh

# x86_64跨架构构建
./deployment/scripts/build_x86_64_complete.sh
```

## 完整工作流程

### 1. 开发环境准备
```bash
# 环境设置
source scripts/production/setup_env.sh
source scripts/production/setup_production_env.sh

# 验证配置
python scripts/validation/validate_config.py
```

### 2. 快速开始（推荐使用生产级主脚本）
```bash
# 完整流程一键执行
python scripts/production/billing_audit_main.py full \
    --input data/sample_data.csv \
    --algorithm hierarchical \
    --batch-size 1000
```

### 3. 数据分析（可选）
```bash
# 快速数据分析
python scripts/tools/simple_data_analysis.py

# 算法对比分析
python scripts/tools/algorithm_comparison_analysis.py
```

### 4. 生产部署
```bash
# 使用精简版部署包（推荐）
tar -xzf billing_audit_production_v2.1.0_*.tar.gz
cd billing_audit_production_v2.1.0_*/
bash deploy.sh
```

## 文档导航

- **快速开始指南**：`docs/quick_start.md`
- **生产环境主脚本使用指南**：`docs/production_main_script_guide.md`
- **生产部署文档**：`docs/production_deployment.md`
- **技术规格文档**：`docs/technical/` 目录
- **操作手册**：`docs/operations_manual.md`