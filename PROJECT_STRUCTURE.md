# 山西电信出账稽核AI系统 - 项目结构说明

## 📁 项目目录结构

```
出账稽核AI/
├── README.md                           # 项目主说明文档
├── PROJECT_STRUCTURE.md               # 项目结构说明 (本文件)
├── TECHNICAL_SPECIFICATIONS.md        # 技术规格文档
├── DEPLOYMENT_INFO.md                 # 部署信息文档
├── production_config.json             # 生产环境配置文件
├── requirements.txt                   # Python依赖包列表
│
├── src/                               # 核心源代码目录
│   ├── billing_audit/                # 主要业务逻辑
│   │   ├── __init__.py
│   │   ├── core/                     # 核心功能模块
│   │   ├── models/                   # 机器学习模型
│   │   ├── utils/                    # 工具函数
│   │   └── config/                   # 配置管理
│   └── tests/                        # 单元测试
│
├── scripts/                          # 脚本目录
│   ├── production/                   # 生产环境脚本
│   │   ├── billing_audit_main.py     # 主脚本 ⭐ 核心
│   │   ├── keep_alive.py            # 后台常驻脚本
│   │   └── setup_*.sh               # 环境设置脚本
│   └── development/                  # 开发环境脚本
│
├── config/                           # 配置文件目录
│   ├── production_config.json        # 生产配置
│   ├── development_config.json       # 开发配置
│   └── model_config.json            # 模型配置
│
├── deployment/                       # 部署相关文件
│   ├── docker/                      # Docker配置
│   │   ├── Dockerfile               # 标准版Dockerfile
│   │   ├── Dockerfile.slim          # 精简版Dockerfile ⭐ 推荐
│   │   ├── requirements.slim.txt    # 精简版依赖
│   │   └── docker-compose.yml       # Docker Compose配置
│   └── scripts/                     # 部署脚本
│
├── docs/                            # 文档目录
│   ├── core/                        # 核心文档
│   ├── technical/                   # 技术文档
│   ├── guides/                      # 使用指南
│   └── reports/                     # 报告文档
│
├── data/                            # 数据目录
│   ├── raw/                         # 原始数据
│   ├── processed/                   # 处理后数据
│   ├── input/                       # 输入数据 (生产环境挂载)
│   └── output/                      # 输出数据 (生产环境挂载)
│
├── outputs/                         # 输出结果目录
│   ├── models/                      # 训练好的模型文件
│   ├── data/                        # 预测结果数据
│   ├── reports/                     # 评估报告
│   ├── visualizations/              # 可视化结果
│   └── temp/                        # 临时文件
│
├── logs/                            # 日志文件目录
│   ├── billing_audit.log           # 主日志文件
│   ├── training.log                 # 训练日志
│   ├── prediction.log               # 预测日志
│   └── error.log                    # 错误日志
│
├── tests/                           # 测试目录
│   ├── unit/                        # 单元测试
│   ├── integration/                 # 集成测试
│   └── performance/                 # 性能测试
│
├── production/                      # 生产部署包目录 ⭐ 重要
│   ├── billing_audit_production_v2.1.0_20250730_083326/  # 部署包目录
│   └── billing_audit_production_v2.1.0_20250802_final_fixed.tar.gz  # 最新部署包
│
└── archive/                         # 归档目录
    ├── 代码/                        # 历史代码归档
    ├── 数据/                        # 历史数据归档
    ├── 文档/                        # 历史文档归档
    ├── scripts/                     # 历史脚本归档
    │   ├── deployment/              # 部署相关脚本
    │   ├── fix/                     # 修复脚本
    │   └── setup/                   # 环境设置脚本
    ├── docker_exports/              # Docker导出文件归档
    ├── temp_files/                  # 临时文件归档
    └── test_outputs/                # 测试输出归档
```

## 🎯 核心文件说明

### 生产环境核心文件
- **`production/billing_audit_production_v2.1.0_20250802_final_fixed.tar.gz`**: 最新生产部署包 ⭐
- **`scripts/production/billing_audit_main.py`**: 主脚本，支持全流程和单流程操作
- **`scripts/production/keep_alive.py`**: 后台常驻脚本，保持容器运行
- **`deployment/docker/Dockerfile.slim`**: 精简版Docker镜像构建文件
- **`production_config.json`**: 生产环境配置文件

### 开发环境核心文件
- **`src/billing_audit/`**: 核心业务逻辑代码
- **`requirements.txt`**: Python依赖包列表
- **`config/`**: 各环境配置文件
- **`tests/`**: 完整的测试套件

### 文档文件
- **`README.md`**: 项目主说明文档
- **`TECHNICAL_SPECIFICATIONS.md`**: 详细技术规格
- **`docs/`**: 完整的文档体系

## 🚀 快速使用指南

### 生产环境部署
```bash
# 解压最新部署包
cd production/
tar -xzf billing_audit_production_v2.1.0_20250802_final_fixed.tar.gz
cd billing_audit_production_v2.1.0_20250730_083326/

# 一键部署
bash deploy.sh

# 智能挂载运行 (推荐)
bash run_with_mount.sh full your_data.csv hierarchical 1000
```

### 开发环境设置
```bash
# 安装依赖
pip install -r requirements.txt

# 运行主脚本
python scripts/production/billing_audit_main.py --help

# 运行测试
python -m pytest tests/
```

## 📊 目录大小统计

- **总项目大小**: 约2.5GB (清理后)
- **生产部署包**: 529MB
- **核心源代码**: 约50MB
- **文档资料**: 约20MB
- **测试数据**: 约100MB

## 🔧 维护说明

### 定期清理
- **临时文件**: `outputs/temp/`, `logs/` (定期清理)
- **测试输出**: `outputs/` (测试后清理)
- **Python缓存**: `__pycache__/`, `*.pyc` (自动清理)

### 版本管理
- **生产部署包**: 保留最新版本，归档旧版本
- **源代码**: 使用Git版本控制
- **配置文件**: 区分环境，独立管理

### 备份策略
- **核心代码**: Git仓库备份
- **生产配置**: 定期备份到安全位置
- **重要数据**: 归档到`archive/`目录

## 📝 更新日志

- **2025-08-02**: 项目结构大清理，创建标准化目录结构
- **2025-08-02**: 完成精简版部署包v2.1.0-final-fixed
- **2025-07-30**: 创建生产级部署包v2.1.0
- **2025-07-28**: 完成分层建模系统开发

## 🎯 下一步计划

1. **持续优化**: 模型性能优化和算法改进
2. **功能扩展**: 新增更多业务场景支持
3. **运维完善**: 监控告警和自动化运维
4. **文档完善**: 用户手册和API文档
