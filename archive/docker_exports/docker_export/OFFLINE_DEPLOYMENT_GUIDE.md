# 🚀 山西电信出账稽核AI系统 v2.1.0 - 离线部署指南

## 📦 部署包信息

**版本**: v2.1.0 (流程修正版)  
**构建时间**: 2025-07-27  
**总大小**: 约535MB (压缩后)  
**包含内容**: 完整的Docker镜像 + 配置文件 + 部署脚本 + 文档

## 📋 部署包内容

```
docker_export/
├── images/                                    # Docker镜像文件 (540MB)
│   ├── billing-audit-ai-v2.1.0-*.tar        # 主应用镜像 (342MB)
│   ├── python-3.9-slim.tar                  # Python基础镜像 (44MB)
│   ├── postgres-13.tar                      # PostgreSQL镜像 (142MB)
│   └── redis-6-alpine.tar                   # Redis镜像 (12MB)
├── configs/                                   # 配置文件
│   ├── docker/                               # Docker配置
│   │   ├── Dockerfile                        # 镜像构建文件
│   │   └── docker-compose.yml               # 容器编排配置
│   ├── config/                               # 应用配置
│   │   └── production_config.json           # 生产环境配置
│   └── requirements.txt                      # Python依赖列表
├── scripts/                                  # 部署脚本
│   ├── deploy_linux_production.sh           # Linux生产环境部署脚本
│   └── deploy_production.sh                 # 基础部署脚本
├── docs/                                     # 文档
│   ├── README.md                             # 项目说明
│   ├── guides/                               # 使用指南
│   └── technical/                            # 技术文档
├── import_images.sh                          # 镜像导入脚本
└── README.md                                 # 部署包说明
```

## 🔧 目标服务器要求

### **硬件要求**
- **CPU**: 4核心+ (推荐8核心)
- **内存**: 8GB+ (推荐16GB+)
- **磁盘**: 20GB+ 可用空间 (推荐50GB+)

### **软件要求**
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+)
- **Docker**: 20.10+ (如未安装，部署脚本会自动安装)
- **Docker Compose**: 2.0+ (如未安装，部署脚本会自动安装)

## 🚀 部署步骤

### **第一步: 传输部署包到目标服务器**

#### **方法1: 使用SCP传输**
```bash
# 在开发环境执行
scp billing-audit-ai-v2.1.0-20250727_014539.tar.gz user@target-server:/opt/

# 在目标服务器执行
cd /opt
tar -xzf billing-audit-ai-v2.1.0-20250727_014539.tar.gz
cd docker_export
```

#### **方法2: 使用U盘或其他存储介质**
```bash
# 复制压缩包到U盘
cp billing-audit-ai-v2.1.0-20250727_014539.tar.gz /media/usb/

# 在目标服务器插入U盘并解压
cd /opt
cp /media/usb/billing-audit-ai-v2.1.0-20250727_014539.tar.gz .
tar -xzf billing-audit-ai-v2.1.0-20250727_014539.tar.gz
cd docker_export
```

#### **方法3: 使用内网文件服务器**
```bash
# 上传到内网文件服务器
# 在目标服务器下载并解压
wget http://internal-server/billing-audit-ai-v2.1.0-20250727_014539.tar.gz
tar -xzf billing-audit-ai-v2.1.0-20250727_014539.tar.gz
cd docker_export
```

### **第二步: 导入Docker镜像**

```bash
# 确保在docker_export目录下
cd docker_export

# 设置执行权限
chmod +x import_images.sh

# 导入所有镜像
./import_images.sh
```

**预期输出**:
```
🚀 开始导入Docker镜像...
📦 导入主镜像...
导入: images/billing-audit-ai-v2.1.0-20250727_014539.tar
导入: images/python-3.9-slim.tar
导入: images/postgres-13.tar
导入: images/redis-6-alpine.tar
✅ 镜像导入完成

📋 已导入的镜像:
billing-audit-ai    v2.1.0-20250727_014539    xxx    xxx ago    342MB
billing-audit-ai    latest                    xxx    xxx ago    342MB
python              3.9-slim                  xxx    xxx ago    44MB
postgres            13                        xxx    xxx ago    142MB
redis               6-alpine                  xxx    xxx ago    12MB
```

### **第三步: 执行部署脚本**

#### **推荐: 使用Linux生产环境部署脚本**
```bash
# 设置执行权限
chmod +x scripts/deploy_linux_production.sh

# 执行部署 (默认安装到 /opt/billing-audit-ai)
./scripts/deploy_linux_production.sh

# 自定义安装目录
./scripts/deploy_linux_production.sh --project-dir /home/<USER>/billing-ai
```

#### **或者: 使用基础部署脚本**
```bash
# 设置执行权限
chmod +x scripts/deploy_production.sh

# 执行基础部署
./scripts/deploy_production.sh
```

### **第四步: 验证部署**

```bash
# 检查容器状态
docker ps | grep billing-audit

# 检查服务状态
sudo systemctl status billing-audit-ai

# 进入容器测试
docker exec -it billing-audit-ai python --version

# 测试主脚本
docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help
```

## 🔧 部署后管理

### **服务管理**
```bash
# 启动服务
sudo systemctl start billing-audit-ai

# 停止服务
sudo systemctl stop billing-audit-ai

# 重启服务
sudo systemctl restart billing-audit-ai

# 查看状态
sudo systemctl status billing-audit-ai
```

### **快捷脚本** (安装在 /opt/billing-audit-ai/)
```bash
cd /opt/billing-audit-ai

# 启动系统
./start.sh

# 停止系统
./stop.sh

# 重启系统
./restart.sh

# 查看日志
./logs.sh

# 进入容器
./shell.sh

# 运行主脚本
./run_main.sh full --input /data/input/data.csv
```

## 🚀 使用方法

### **⭐ 推荐: 生产级主脚本** (v2.1.0)

```bash
# 进入项目目录
cd /opt/billing-audit-ai

# 将数据文件放入输入目录
cp your_data.csv data/input/

# 运行完整AI流程
./run_main.sh full --input /data/input/your_data.csv --batch-size 1000

# 查看结果
ls -la data/output/reports/markdown/    # Markdown执行报告
ls -la data/output/models/              # 训练好的模型
ls -la data/output/                     # 预测和判定结果
```

### **模块化执行**
```bash
# 只执行特征工程
./run_main.sh feature-engineering --input /data/input/raw_data.csv

# 只执行模型训练
./run_main.sh training --input /data/input/processed_data.csv

# 只执行预测
./run_main.sh prediction --input /data/input/test_data.csv

# 只执行收费判定
./run_main.sh judgment --input /data/input/prediction_results.csv
```

## 🔍 监控和维护

### **系统监控**
```bash
# 查看容器资源使用
docker stats billing-audit-ai

# 查看磁盘使用
df -h /opt/billing-audit-ai

# 查看日志
tail -f /opt/billing-audit-ai/logs/*.log
```

### **备份重要数据**
```bash
# 备份输出结果
tar -czf backup_output_$(date +%Y%m%d).tar.gz /opt/billing-audit-ai/data/output

# 备份模型文件
tar -czf backup_models_$(date +%Y%m%d).tar.gz /opt/billing-audit-ai/models

# 备份配置文件
cp -r /opt/billing-audit-ai/config /backup/config_$(date +%Y%m%d)
```

## 🆘 故障排除

### **常见问题**

#### **1. Docker镜像导入失败**
```bash
# 检查磁盘空间
df -h

# 检查Docker服务
sudo systemctl status docker

# 手动导入单个镜像
docker load -i images/billing-audit-ai-v2.1.0-20250727_014539.tar
```

#### **2. 容器启动失败**
```bash
# 查看详细错误
docker logs billing-audit-ai

# 检查端口占用
netstat -tlnp | grep :8000

# 重新启动容器
docker restart billing-audit-ai
```

#### **3. 权限问题**
```bash
# 修复目录权限
sudo chown -R $USER:$USER /opt/billing-audit-ai
chmod -R 755 /opt/billing-audit-ai

# 修复脚本权限
chmod +x /opt/billing-audit-ai/*.sh
```

#### **4. 内存不足**
```bash
# 查看内存使用
free -h

# 调整批处理大小
./run_main.sh full --input /data/input/data.csv --batch-size 500

# 清理Docker缓存
docker system prune
```

## 📞 技术支持

### **获取帮助**
```bash
# 查看主脚本帮助
./run_main.sh --help

# 查看部署脚本帮助
./scripts/deploy_linux_production.sh --help

# 查看系统状态
docker ps -a
sudo systemctl status billing-audit-ai
```

### **重要文件位置**
- **项目目录**: `/opt/billing-audit-ai/`
- **输入数据**: `/opt/billing-audit-ai/data/input/`
- **输出结果**: `/opt/billing-audit-ai/data/output/`
- **执行报告**: `/opt/billing-audit-ai/data/output/reports/markdown/`
- **模型文件**: `/opt/billing-audit-ai/models/`
- **日志文件**: `/opt/billing-audit-ai/logs/`
- **配置文件**: `/opt/billing-audit-ai/config/`

## ✅ 部署检查清单

- [ ] 目标服务器满足硬件要求
- [ ] 部署包已成功传输到目标服务器
- [ ] Docker镜像导入成功
- [ ] 部署脚本执行成功
- [ ] 容器启动正常
- [ ] 系统服务注册成功
- [ ] 主脚本测试通过
- [ ] 数据目录权限正确
- [ ] 日志记录正常

---

**部署包版本**: v2.1.0 (流程修正版)  
**构建时间**: 2025-07-27  
**部署状态**: ✅ 离线部署就绪  
**技术支持**: 详见docs/目录下的完整文档
