# 山西电信出账稽核AI系统 v2.1.0 - Docker部署包

## 📦 包内容

```
docker_export/
├── images/                          # Docker镜像文件
│   ├── billing-audit-ai-v2.1.0-20250727_014539.tar  # 主镜像
│   ├── python-3.9-slim.tar         # Python基础镜像
│   ├── postgres-13.tar             # PostgreSQL镜像
│   └── redis-6-alpine.tar          # Redis镜像
├── configs/                         # 配置文件
│   ├── docker/                      # Docker配置
│   ├── config/                      # 应用配置
│   └── requirements.txt             # Python依赖
├── scripts/                         # 部署脚本
│   ├── deploy_linux_production.sh  # Linux生产环境部署
│   └── deploy_production.sh        # 通用部署脚本
├── docs/                           # 文档
│   ├── README.md                   # 项目说明
│   ├── guides/                     # 使用指南
│   └── technical/                  # 技术文档
├── import_images.sh                # 镜像导入脚本
└── README.md                       # 本文件
```

## 🚀 快速部署

### 1. 导入镜像
```bash
chmod +x import_images.sh
./import_images.sh
```

### 2. 运行部署脚本
```bash
chmod +x scripts/deploy_linux_production.sh
./scripts/deploy_linux_production.sh
```

### 3. 验证部署
```bash
docker ps | grep billing-audit
```

## 📋 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **内存**: 8GB+ (推荐16GB+)
- **磁盘**: 20GB+ 可用空间
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

## 🔧 使用方法

### 生产级主脚本 (推荐)
```bash
# 完整流程
docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \
  --input /data/input/data.csv \
  --batch-size 1000

# 模块化执行
docker exec billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \
  --input /data/input/raw_data.csv
```

### 传统方式
```bash
# 模型训练
docker exec billing-audit-ai python src/billing_audit/training/train_large_scale_model.py \
  --input /data/input/training_data.csv \
  --output /models/ \
  --batch-size 50000

# 批量预测
docker exec billing-audit-ai python src/billing_audit/inference/predict_large_scale.py \
  --input /data/input/predict_data.csv \
  --model /models/large_scale_model_latest.pkl \
  --feature-engineer /models/large_scale_feature_engineer_latest.pkl \
  --output /data/output/predictions.csv
```

## 📁 重要目录

- **输入数据**: `/opt/billing-audit-ai/data/input/`
- **输出结果**: `/opt/billing-audit-ai/data/output/`
- **执行报告**: `/opt/billing-audit-ai/data/output/reports/markdown/`
- **模型文件**: `/opt/billing-audit-ai/models/`
- **日志文件**: `/opt/billing-audit-ai/logs/`

## 🆘 故障排除

### 查看日志
```bash
docker logs billing-audit-ai
```

### 进入容器
```bash
docker exec -it billing-audit-ai bash
```

### 重启服务
```bash
docker restart billing-audit-ai
```

## 📞 技术支持

如有问题，请查看 `docs/` 目录下的详细文档。

---

**版本**: v2.1.0  
**构建时间**: 20250727_014539  
**镜像大小**: 
