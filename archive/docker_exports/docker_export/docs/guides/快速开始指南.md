# 快速使用指南

## 🚀 5分钟快速上手

本指南将帮助您在5分钟内快速上手山西电信出账稽核AI系统。

## 📋 前置条件

- Python 3.9+
- macOS (Apple Silicon) 或 Linux
- 8GB+ 内存

## ⚡ 快速安装

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd 出账稽核AI

# 创建虚拟环境
python -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 安装OpenMP (macOS用户)

```bash
# 安装OpenMP支持XGBoost/LightGBM
/opt/homebrew/bin/brew install libomp

# 设置环境
chmod +x scripts/setup_env.sh
./scripts/setup_env.sh
```

### 3. 验证安装

```bash
# 运行环境验证
./scripts/setup_env.sh

# 应该看到以下输出：
# ✅ XGBoost 可用
# ✅ LightGBM 可用
# ✅ RandomForest 可用
```

## 🎯 核心功能演示

### 1. 模型训练 (2分钟)

```bash
# 训练固费模型
source scripts/setup_env.sh
python scripts/train_billing_models.py --fee-type fixed_fee

# 预期输出：
# ✅ 模型训练完成
# - MAE: ~28.49
# - R²: ~0.9778
# - 训练时间: ~0.24秒
```

### 2. 模型评估 (1分钟)

```bash
# 评估模型性能
python scripts/test_model_evaluation.py

# 预期输出：
# ✅ 所有测试通过！
# - 回归指标计算成功
# - 业务指标计算成功
# - 模型验证成功
```

### 3. 端到端测试 (2分钟)

```bash
# 运行完整工作流程
python scripts/end_to_end_test.py

# 预期输出：
# 🎉 端到端测试总结
# ✅ 所有步骤成功完成!
# 📊 系统性能:
#   - 模型准确性 (R²): 0.9778
#   - 业务准确率: 53.85%
#   - 批量判定合理率: 60.0%
```

## 💡 基础使用示例

### 示例1: 单条收费预测

```python
from src.billing_audit.inference import ModelPredictor

# 初始化预测器
predictor = ModelPredictor('fixed_fee', 'models/billing_audit/fixed_fee_model_latest')

# 准备数据
billing_data = {
    'user_id': '12345',
    'final_eff_date': '2024-01-01',
    'final_exp_date': '2024-01-31',
    'should_fee': 100.0,
    'busi_flag': 1,
    'unit_type': 2,
    'charge_day_count': 31
}

# 预测
result = predictor.predict_with_details(billing_data)
print(f"预测金额: {result['predictions'][0]:.2f}")
print(f"置信度: {result['confidence_scores'][0]:.3f}")
```

### 示例2: 收费合理性判定

```python
from src.billing_audit.inference import BillingJudge

# 初始化判定器
judge = BillingJudge('fixed_fee')

# 判定收费合理性
actual_amount = 95.0
judgment = judge.judge_single(billing_data, actual_amount)

print(f"判定结果: {judgment['judgment']}")
print(f"置信度: {judgment['confidence_score']:.3f}")
print(f"相对误差: {judgment['relative_error']:.4f}")

# 可能的输出：
# 判定结果: reasonable
# 置信度: 0.850
# 相对误差: 0.0526
```

### 示例3: 批量处理

```python
# 准备批量数据
batch_records = [
    {'billing_data': billing_data1, 'actual_amount': 95.0},
    {'billing_data': billing_data2, 'actual_amount': 105.0},
    {'billing_data': billing_data3, 'actual_amount': 110.0}
]

# 批量判定
batch_results = judge.judge_batch(batch_records)

# 统计结果
reasonable_count = len([r for r in batch_results if r['judgment'] == 'reasonable'])
print(f"合理收费数量: {reasonable_count}/{len(batch_results)}")
```

### 示例3: 千万级数据收费合理性判定 ⭐ 新增

```bash
# 千万级数据收费合理性判定
python scripts/production/large_scale_billing_judge.py \
    --input /path/to/large_billing_data.csv \
    --model ./models/large_scale_model.pkl \
    --feature-engineer ./models/large_scale_feature_engineer.pkl \
    --output ./results/billing_judgments.csv \
    --abs-threshold 50.0 \
    --rel-threshold 0.1 \
    --batch-size 50000

# 查看判定结果
head -5 ./results/billing_judgments.csv

# 判定统计
python -c "
import pandas as pd
df = pd.read_csv('./results/billing_judgments.csv')
judgments = df['judgment'].value_counts()
total = len(df)
print(f'总判定数: {total:,}')
print(f'合理收费: {judgments.get(\"reasonable\", 0):,} ({judgments.get(\"reasonable\", 0)/total*100:.1f}%)')
print(f'不合理收费: {judgments.get(\"unreasonable\", 0):,} ({judgments.get(\"unreasonable\", 0)/total*100:.1f}%)')
print(f'不确定收费: {judgments.get(\"uncertain\", 0):,} ({judgments.get(\"uncertain\", 0)/total*100:.1f}%)')
print(f'平均置信度: {df[\"confidence_score\"].mean():.3f}')
"
```

## 📊 数据格式说明

### 输入数据格式

您的Excel文件应包含以下必需字段：

```python
required_fields = {
    'user_id': 'string',           # 用户ID
    'final_eff_date': 'datetime',  # 生效日期
    'final_exp_date': 'datetime',  # 失效日期
    'should_fee': 'float',         # 应收费用
    'actual_fee': 'float',         # 实际费用
    'busi_flag': 'int',           # 业务标识
    'unit_type': 'int',           # 单位类型
    'charge_day_count': 'int'     # 计费天数
}
```

### 输出结果格式

```python
# 预测结果
prediction_result = {
    'predictions': [106.17],
    'confidence_scores': [0.95],
    'processing_time_ms': 45,
    'feature_count': 37
}

# 判定结果
judgment_result = {
    'judgment': 'reasonable',      # reasonable/unreasonable/uncertain
    'confidence_score': 0.85,
    'predicted_amount': 106.17,
    'relative_error': 0.0617,
    'absolute_error': 6.17
}
```

## 🛠️ 常用配置

### 修改判定阈值

编辑 `config/config.json`:

```json
{
  "judgment_thresholds": {
    "fixed_fee": {
      "absolute_threshold": 50.0,    # 绝对误差阈值(元)
      "relative_threshold": 0.1,     # 相对误差阈值(10%)
      "method": "hybrid"             # 判定方法: absolute/relative/hybrid
    }
  }
}
```

### 修改模型参数

```json
{
  "model_config": {
    "test_size": 0.2,              # 测试集比例
    "random_state": 42,            # 随机种子
    "algorithms": [                # 算法优先级
      "xgboost",
      "lightgbm", 
      "random_forest"
    ]
  }
}
```

## 🔧 故障排除

### 问题1: XGBoost导入失败

```bash
# 错误信息
ImportError: XGBoost Library (libxgboost.dylib) could not be loaded

# 解决方案
/opt/homebrew/bin/brew install libomp
source scripts/setup_env.sh
```

### 问题2: 数据加载失败

```bash
# 错误信息
FileNotFoundError: 数据文件不存在

# 解决方案
# 1. 检查文件路径
ls 数据/固费预测样例@20250707.xlsx

# 2. 检查配置文件
cat config/config.json | grep data_sources
```

### 问题3: 预测精度低

```python
# 检查数据质量
from src.billing_audit.preprocessing import BillingDataPreprocessor

preprocessor = BillingDataPreprocessor('fixed_fee')
X, y, _ = preprocessor.process()

print(f"数据形状: {X.shape}")
print(f"目标变量统计: {y.describe()}")
print(f"缺失值: {X.isnull().sum().sum()}")
```

## 📈 性能优化建议

### 1. 数据优化

```python
# 使用数据采样进行快速测试
sample_size = 1000
X_sample = X.sample(n=sample_size, random_state=42)
y_sample = y.loc[X_sample.index]
```

### 2. 批量处理优化

```python
# 调整批量大小
batch_size = 100  # 根据内存情况调整
for i in range(0, len(data), batch_size):
    batch = data[i:i+batch_size]
    results = judge.judge_batch(batch)
```

### 3. 模型缓存

```python
# 避免重复加载模型
predictor = ModelPredictor('fixed_fee', model_path)  # 只初始化一次
for data in batch_data:
    result = predictor.predict(data)  # 重复使用
```

## 📚 进阶使用

### 1. 自定义特征工程

```python
from src.billing_audit.preprocessing import FeatureEngineer

# 继承并扩展特征工程器
class CustomFeatureEngineer(FeatureEngineer):
    def create_custom_features(self, X):
        # 添加自定义特征
        X['fee_per_day'] = X['should_fee'] / X['charge_day_count']
        return X
```

### 2. 模型集成

```python
# 训练多个模型并集成
models = []
for algorithm in ['xgboost', 'lightgbm', 'random_forest']:
    trainer = BillingModelTrainer('fixed_fee')
    model = trainer.train_model(X_train, y_train, algorithm=algorithm)
    models.append(model)

# 集成预测
ensemble_pred = np.mean([model.predict(X_test) for model in models], axis=0)
```

### 3. 实时监控

```python
import time
from collections import deque

class PerformanceMonitor:
    def __init__(self, window_size=100):
        self.predictions = deque(maxlen=window_size)
        self.response_times = deque(maxlen=window_size)
    
    def log_prediction(self, prediction, response_time):
        self.predictions.append(prediction)
        self.response_times.append(response_time)
    
    def get_stats(self):
        return {
            'avg_response_time': np.mean(self.response_times),
            'prediction_std': np.std(self.predictions)
        }
```

## 🎯 下一步

1. **探索高级功能**: 查看 `docs/代码文档.md`
2. **API集成**: 参考API接口文档
3. **生产部署**: 查看部署指南
4. **性能调优**: 根据实际数据调整参数

## 📞 获取帮助

- **快速开始**: 本文档
- **详细文档**: `docs/代码文档.md`
- **API使用**: `docs/API使用指南.md`
- **示例代码**: `scripts/` 目录
- **问题反馈**: 提交Issue
- **技术支持**: 联系开发团队

## 📚 相关文档

1. **[README.md](../README.md)** - 项目总览和安装指南
2. **[代码文档.md](代码文档.md)** - 详细代码功能说明
3. **[API使用指南.md](API使用指南.md)** - API接口使用指南

---

**快速指南版本**: v1.0.0
**适用系统版本**: v1.0.0
**最后更新**: 2025-07-16
