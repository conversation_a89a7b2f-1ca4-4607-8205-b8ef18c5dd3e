#!/bin/bash
# Docker镜像导入脚本

set -e

echo "🚀 开始导入Docker镜像..."

# 导入主镜像
echo "📦 导入主镜像..."
for image_file in images/*.tar; do
    if [ -f "$image_file" ]; then
        echo "导入: $image_file"
        docker load -i "$image_file"
    fi
done

echo "✅ 镜像导入完成"
echo ""
echo "📋 已导入的镜像:"
docker images | grep -E "(billing-audit-ai|python|postgres|redis)"
echo ""
echo "🚀 下一步:"
echo "1. 运行部署脚本: ./scripts/deploy_linux_production.sh"
echo "2. 或者使用docker-compose: docker-compose -f configs/docker/docker-compose.yml up -d"
