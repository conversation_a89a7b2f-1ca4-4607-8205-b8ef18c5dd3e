#!/bin/bash
# 山西电信出账稽核AI系统 - 生产环境部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 检查配置文件
check_config() {
    if [ ! -f "deployment/config/production_config.json" ]; then
        log_error "生产配置文件不存在: deployment/config/production_config.json"
        exit 1
    fi

    log_info "配置文件检查通过"
}

# 创建必要的目录
create_directories() {
    log_step "创建必要的目录..."
    
    directories=(
        "data/input"
        "data/output" 
        "models"
        "logs"
        "data/backup"
        "monitoring"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
}

# 设置环境变量
setup_environment() {
    log_step "设置环境变量..."
    
    # 创建.env文件
    cat > .env << EOF
# 山西电信出账稽核AI系统环境变量
BILLING_AUDIT_ENV=production
DATA_INPUT_DIR=/data/input
DATA_OUTPUT_DIR=/data/output
MODEL_DIR=/models
LOGS_DIR=/logs
DB_PASSWORD=billing_password_$(date +%s)
PYTHONPATH=/app
OMP_NUM_THREADS=4
EOF
    
    log_info "环境变量文件已创建: .env"
}

# 构建Docker镜像
build_image() {
    log_step "构建Docker镜像..."

    # 从deployment/docker目录构建，但使用项目根目录作为上下文
    docker build -f deployment/docker/Dockerfile -t billing-audit-ai:latest .

    if [ $? -eq 0 ]; then
        log_info "Docker镜像构建成功"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_step "启动服务..."

    # 基础服务
    docker-compose -f deployment/docker/docker-compose.yml up -d billing-audit-ai
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "billing-audit-ai.*Up"; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        docker-compose logs billing-audit-ai
        exit 1
    fi
}

# 运行健康检查
health_check() {
    log_step "运行健康检查..."
    
    # 检查容器状态
    container_status=$(docker inspect --format='{{.State.Health.Status}}' billing-audit-ai 2>/dev/null || echo "no-health-check")
    
    if [ "$container_status" = "healthy" ] || [ "$container_status" = "no-health-check" ]; then
        log_info "容器健康检查通过"
    else
        log_warn "容器健康检查状态: $container_status"
    fi
    
    # 测试配置加载
    docker exec billing-audit-ai python -c "
from src.config.production_config_manager import get_config_manager
config = get_config_manager()
print('✅ 配置加载成功')
print(f'项目: {config.get(\"project.name\")}')
print(f'版本: {config.get(\"project.version\")}')
print(f'批处理大小: {config.get_batch_size()}')
"
    
    if [ $? -eq 0 ]; then
        log_info "配置测试通过"
    else
        log_error "配置测试失败"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息"
    
    echo "=================================="
    echo "🎉 山西电信出账稽核AI系统 v2.1.0 部署完成"
    echo "=================================="
    echo ""
    echo "📊 服务状态:"
    docker-compose ps
    echo ""
    echo "🔧 常用命令:"
    echo "  查看日志: docker-compose logs -f billing-audit-ai"
    echo "  进入容器: docker exec -it billing-audit-ai bash"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart billing-audit-ai"
    echo ""
    echo "📁 数据目录:"
    echo "  输入数据: ./data/input/"
    echo "  输出结果: ./data/output/"
    echo "  模型文件: ./models/"
    echo "  日志文件: ./logs/"
    echo ""
    echo "🚀 使用示例 (v2.1.0):"
    echo ""
    echo "  ⭐ 推荐: 生产级主脚本 (完整流程)"
    echo "  docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \\"
    echo "    --input /data/input/data.csv \\"
    echo "    --batch-size 1000"
    echo "  # 输出: Markdown报告 + 模型 + 预测 + 判定结果"
    echo ""
    echo "  🔧 模块化执行:"
    echo "  docker exec billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \\"
    echo "    --input /data/input/raw_data.csv"
    echo "  docker exec billing-audit-ai python scripts/production/billing_audit_main.py training \\"
    echo "    --input /data/input/processed_data.csv"
    echo ""
    echo "  📊 传统方式 (单独脚本):"
    echo "  # 模型训练"
    echo "  docker exec billing-audit-ai python src/billing_audit/training/train_large_scale_model.py \\"
    echo "    --input /data/input/training_data.csv \\"
    echo "    --output /models/ \\"
    echo "    --batch-size 50000"
    echo ""
    echo "  # 批量预测"
    echo "  docker exec billing-audit-ai python src/billing_audit/inference/predict_large_scale.py \\"
    echo "    --input /data/input/predict_data.csv \\"
    echo "    --model /models/large_scale_model_latest.pkl \\"
    echo "    --feature-engineer /models/large_scale_feature_engineer_latest.pkl \\"
    echo "    --output /data/output/predictions.csv"
    echo ""
}

# 清理函数
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "部署过程中发生错误，正在清理..."
        docker-compose down 2>/dev/null || true
    fi
}

# 主函数
main() {
    log_info "开始部署山西电信出账稽核AI系统..."
    
    # 设置错误处理
    trap cleanup EXIT
    
    # 执行部署步骤
    check_docker
    check_config
    create_directories
    setup_environment
    build_image
    start_services
    health_check
    show_deployment_info
    
    log_info "部署完成！"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --with-database)
            ENABLE_DATABASE=true
            shift
            ;;
        --with-cache)
            ENABLE_CACHE=true
            shift
            ;;
        --with-monitoring)
            ENABLE_MONITORING=true
            shift
            ;;
        --help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --with-database    启用PostgreSQL数据库"
            echo "  --with-cache       启用Redis缓存"
            echo "  --with-monitoring  启用Prometheus监控"
            echo "  --help            显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            exit 1
            ;;
    esac
done

# 启用可选服务
if [ "$ENABLE_DATABASE" = true ]; then
    export COMPOSE_PROFILES="${COMPOSE_PROFILES},database"
    log_info "启用数据库服务"
fi

if [ "$ENABLE_CACHE" = true ]; then
    export COMPOSE_PROFILES="${COMPOSE_PROFILES},cache"
    log_info "启用缓存服务"
fi

if [ "$ENABLE_MONITORING" = true ]; then
    export COMPOSE_PROFILES="${COMPOSE_PROFILES},monitoring"
    log_info "启用监控服务"
fi

# 运行主函数
main
