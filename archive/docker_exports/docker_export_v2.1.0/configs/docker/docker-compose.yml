version: '3.8'

services:
  billing-audit-ai:
    build: .
    container_name: billing-audit-ai
    restart: unless-stopped
    
    # 环境变量
    environment:
      - BILLING_AUDIT_ENV=production
      - BILLING_AUDIT_VERSION=v2.1.0
      - DATA_INPUT_DIR=/data/input
      - DATA_OUTPUT_DIR=/data/output
      - MODEL_DIR=/models
      - LOGS_DIR=/logs
      - PYTHONPATH=/app
      - OMP_NUM_THREADS=4
    
    # 数据卷挂载
    volumes:
      # 数据目录
      - ./data/input:/data/input:ro          # 输入数据（只读）
      - ./data/output:/data/output           # 输出结果
      - ./data/output/reports:/data/output/reports  # 执行报告 (v2.1.0)
      - ./data/output/models:/data/output/models    # 模型输出 (v2.1.0)
      - ./models:/models                     # 模型文件
      - ./logs:/logs                         # 日志文件
      - ./data/backup:/data/backup           # 备份文件

      # 配置文件
      - ./deployment/config/production_config.json:/app/config/production_config.json:ro

      # 临时目录
      - billing-audit-tmp:/tmp/billing_audit
    
    # 端口映射
    ports:
      - "8000:8000"
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 10G
          cpus: '4.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "from src.config.production_config_manager import get_config_manager; get_config_manager()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 网络
    networks:
      - billing-audit-network

  # 可选：PostgreSQL数据库
  postgres:
    image: postgres:13
    container_name: billing-audit-db
    restart: unless-stopped
    
    environment:
      - POSTGRES_DB=billing_audit
      - POSTGRES_USER=billing_user
      - POSTGRES_PASSWORD=${DB_PASSWORD:-billing_password}
    
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    
    ports:
      - "5432:5432"
    
    networks:
      - billing-audit-network
    
    # 仅在需要数据库时启用
    profiles:
      - database

  # 可选：Redis缓存
  redis:
    image: redis:6-alpine
    container_name: billing-audit-cache
    restart: unless-stopped
    
    command: redis-server --appendonly yes
    
    volumes:
      - redis-data:/data
    
    ports:
      - "6379:6379"
    
    networks:
      - billing-audit-network
    
    # 仅在需要缓存时启用
    profiles:
      - cache

  # 可选：监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: billing-audit-monitoring
    restart: unless-stopped
    
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    ports:
      - "9090:9090"
    
    networks:
      - billing-audit-network
    
    profiles:
      - monitoring

volumes:
  billing-audit-tmp:
  postgres-data:
  redis-data:
  prometheus-data:

networks:
  billing-audit-network:
    driver: bridge
