#!/bin/bash
# 山西电信出账稽核AI系统 - 精简生产部署包制作脚本
# 只包含生产必需文件，支持任意目录部署

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置变量
VERSION="v2.1.0"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="billing_audit_production_${VERSION}_${TIMESTAMP}"
PACKAGE_DIR="$PACKAGE_NAME"

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${PURPLE}[SUCCESS]${NC} $1"; }

# 检查环境
check_environment() {
    log_step "检查制作环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查必需文件
    local required_files=(
        "deployment/config/production_config.json"
        "deployment/docker/Dockerfile"
        "billing_audit_offline_v2.1.0_20250730_003703/deploy.sh"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "必需文件不存在: $file"
            exit 1
        fi
    done
    
    log_info "环境检查通过"
}

# 创建包目录结构
create_package_structure() {
    log_step "创建精简部署包结构..."
    
    # 创建主目录
    mkdir -p "$PACKAGE_DIR"
    
    # 创建数据目录结构
    mkdir -p "$PACKAGE_DIR/data/input"
    mkdir -p "$PACKAGE_DIR/data/output"
    mkdir -p "$PACKAGE_DIR/logs"
    mkdir -p "$PACKAGE_DIR/images"
    
    log_info "目录结构创建完成"
}

# 构建并导出Docker镜像
build_and_export_images() {
    log_step "构建并导出Docker镜像..."
    
    # 构建主应用镜像
    log_info "构建主应用镜像..."
    docker build -f deployment/docker/Dockerfile -t billing-audit-ai:${VERSION} .
    
    # 导出主应用镜像
    log_info "导出主应用镜像..."
    docker save billing-audit-ai:${VERSION} | gzip > "${PACKAGE_DIR}/images/billing-audit-ai-${VERSION}.tar.gz"
    
    # 导出基础镜像（可选）
    log_info "导出基础镜像..."
    
    # Python基础镜像
    if docker images | grep -q "python.*3.9-slim"; then
        docker save python:3.9-slim | gzip > "${PACKAGE_DIR}/images/python-3.9-slim.tar.gz"
    fi
    
    # PostgreSQL镜像（如果存在）
    if docker images | grep -q "postgres.*13"; then
        docker save postgres:13 | gzip > "${PACKAGE_DIR}/images/postgres-13.tar.gz"
    fi
    
    log_success "镜像导出完成"
}

# 复制必需文件
copy_essential_files() {
    log_step "复制生产必需文件..."
    
    # 复制一键部署脚本
    cp billing_audit_offline_v2.1.0_20250730_003703/deploy.sh "$PACKAGE_DIR/"
    chmod +x "$PACKAGE_DIR/deploy.sh"
    
    # 复制生产配置
    cp deployment/config/production_config.json "$PACKAGE_DIR/"
    
    # 创建精简README
    cat > "$PACKAGE_DIR/README.md" << 'EOF'
# 山西电信出账稽核AI系统 v2.1.0 - 生产部署包

## 🚀 一键部署使用

### 快速开始
```bash
# 1. 解压部署包
tar -xzf billing_audit_production_v2.1.0_*.tar.gz

# 2. 进入目录
cd billing_audit_production_v2.1.0_*

# 3. 准备数据文件
cp /path/to/your/data.txt data/input/ofrm_result.txt

# 4. 一键部署运行
bash deploy.sh
```

### 特性说明
- ✅ **零配置部署**: 一个命令完成所有操作
- ✅ **路径无关**: 支持任意目录部署
- ✅ **权限自适应**: 自动处理权限问题
- ✅ **智能检测**: 自动检测环境和依赖
- ✅ **完整验证**: 自动验证执行结果

### 系统要求
- Docker 20.10+
- 内存: 4GB+
- 磁盘: 10GB+
- 操作系统: Linux

### 输出文件
执行完成后，结果文件位于：
- `data/output/models/` - 训练的模型文件
- `data/output/data/` - 预测结果文件
- `data/output/reports/` - 执行报告
- `logs/` - 系统日志

### 故障排除
如遇问题，请检查：
1. Docker服务是否运行
2. 数据文件是否存在且格式正确
3. 磁盘空间是否充足
4. 用户权限是否足够

### 技术支持
如需技术支持，请提供：
- 完整的错误日志
- 系统环境信息
- Docker版本信息
EOF
    
    log_info "必需文件复制完成"
}

# 创建部署包
create_package() {
    log_step "创建部署包..."
    
    # 计算大小
    local package_size=$(du -sh "$PACKAGE_DIR" | cut -f1)
    
    # 创建压缩包
    tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_DIR"
    
    # 计算压缩包大小
    local compressed_size=$(du -sh "${PACKAGE_NAME}.tar.gz" | cut -f1)
    
    log_success "部署包创建完成"
    log_info "原始大小: $package_size"
    log_info "压缩大小: $compressed_size"
    log_info "包文件: ${PACKAGE_NAME}.tar.gz"
}

# 清理临时文件
cleanup() {
    log_step "清理临时文件..."
    rm -rf "$PACKAGE_DIR"
    log_info "清理完成"
}

# 验证部署包
verify_package() {
    log_step "验证部署包..."
    
    # 解压验证
    local test_dir="test_${PACKAGE_NAME}"
    mkdir -p "$test_dir"
    tar -xzf "${PACKAGE_NAME}.tar.gz" -C "$test_dir"
    
    # 检查关键文件
    local test_package_dir="$test_dir/$PACKAGE_NAME"
    local missing_files=()
    
    [ ! -f "$test_package_dir/deploy.sh" ] && missing_files+=("deploy.sh")
    [ ! -f "$test_package_dir/production_config.json" ] && missing_files+=("production_config.json")
    [ ! -f "$test_package_dir/README.md" ] && missing_files+=("README.md")
    [ ! -d "$test_package_dir/images" ] && missing_files+=("images/")
    [ ! -d "$test_package_dir/data" ] && missing_files+=("data/")
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "部署包验证失败，缺少文件："
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        rm -rf "$test_dir"
        exit 1
    fi
    
    # 检查脚本权限
    if [ ! -x "$test_package_dir/deploy.sh" ]; then
        log_error "deploy.sh 没有执行权限"
        rm -rf "$test_dir"
        exit 1
    fi
    
    rm -rf "$test_dir"
    log_success "部署包验证通过"
}

# 显示完成信息
show_completion_info() {
    echo ""
    log_success "🎉 精简生产部署包制作完成！"
    echo ""
    echo "📦 部署包信息:"
    echo "  文件名: ${PACKAGE_NAME}.tar.gz"
    echo "  大小: $(du -sh "${PACKAGE_NAME}.tar.gz" | cut -f1)"
    echo ""
    echo "📋 包含内容:"
    echo "  ✅ deploy.sh - 一键部署运行脚本"
    echo "  ✅ production_config.json - 生产配置文件"
    echo "  ✅ images/ - Docker镜像文件"
    echo "  ✅ data/ - 数据目录结构"
    echo "  ✅ README.md - 使用说明"
    echo ""
    echo "🚀 使用方式:"
    echo "  1. 传输到目标服务器: scp ${PACKAGE_NAME}.tar.gz user@server:/path/"
    echo "  2. 解压: tar -xzf ${PACKAGE_NAME}.tar.gz"
    echo "  3. 进入目录: cd ${PACKAGE_NAME}"
    echo "  4. 准备数据: cp /path/to/data.txt data/input/ofrm_result.txt"
    echo "  5. 一键部署: bash deploy.sh"
    echo ""
    echo "💡 优势特性:"
    echo "  ✅ 支持任意目录部署"
    echo "  ✅ 自动处理权限问题"
    echo "  ✅ 智能环境检测"
    echo "  ✅ 完整错误处理"
    echo "  ✅ 详细执行反馈"
    echo ""
}

# 主函数
main() {
    log_info "开始制作山西电信出账稽核AI系统精简生产部署包..."
    
    check_environment
    create_package_structure
    build_and_export_images
    copy_essential_files
    create_package
    cleanup
    verify_package
    show_completion_info
    
    log_success "制作完成！"
}

# 运行主函数
main "$@"
