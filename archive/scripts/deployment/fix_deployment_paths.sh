#!/bin/bash
# 山西电信出账稽核AI系统 - 部署路径修复脚本
# 修复公司主机部署中的路径问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查当前目录
check_current_directory() {
    log_step "检查当前目录..."
    
    if [ ! -f "scripts/deploy.sh" ] || [ ! -f "config/docker-compose.yml" ]; then
        log_error "请在部署包根目录下执行此脚本！"
        log_info "正确的执行方式："
        log_info "  cd /bas/work/lvpd/jihe/billing_audit_offline_v2.1.0_20250730_003703"
        log_info "  bash fix_deployment_paths.sh"
        exit 1
    fi
    
    log_info "当前目录正确: $(pwd)"
}

# 修复docker-compose.yml配置文件路径
fix_docker_compose_config() {
    log_step "修复docker-compose.yml配置文件路径..."
    
    # 备份原文件
    cp config/docker-compose.yml config/docker-compose.yml.backup
    
    # 修复配置文件路径错误
    sed -i 's|./production_config.json:/ap./production_config.json:ro|./production_config.json:/app/config/production_config.json:ro|g' config/docker-compose.yml
    
    # 验证修复结果
    if grep -q "/app/config/production_config.json" config/docker-compose.yml; then
        log_info "✅ docker-compose.yml配置文件路径修复成功"
    else
        log_error "❌ docker-compose.yml配置文件路径修复失败"
        exit 1
    fi
}

# 创建必要的目录结构
create_directories() {
    log_step "创建必要的目录结构..."
    
    # 创建数据目录
    mkdir -p data/input
    mkdir -p data/output
    mkdir -p data/output/models
    mkdir -p data/output/data
    mkdir -p data/output/reports
    mkdir -p data/output/reports/markdown
    mkdir -p data/backup
    mkdir -p models
    mkdir -p logs
    
    log_info "✅ 目录结构创建完成"
}

# 检查文件权限
check_permissions() {
    log_step "检查文件权限..."
    
    # 设置脚本执行权限
    chmod +x scripts/*.sh
    
    # 检查数据目录权限
    if [ ! -w data/ ]; then
        log_warn "数据目录权限不足，尝试修复..."
        chmod -R 755 data/
    fi
    
    log_info "✅ 文件权限检查完成"
}

# 验证部署包完整性
verify_package_integrity() {
    log_step "验证部署包完整性..."

    # 检查必需文件
    local missing_files=()
    local optional_files=()

    # 必需文件检查
    [ ! -f "scripts/deploy.sh" ] && missing_files+=("scripts/deploy.sh")
    [ ! -f "scripts/load_images.sh" ] && missing_files+=("scripts/load_images.sh")
    [ ! -f "scripts/run_full_pipeline.sh" ] && missing_files+=("scripts/run_full_pipeline.sh")
    [ ! -f "config/docker-compose.yml" ] && missing_files+=("config/docker-compose.yml")
    [ ! -f "production_config.json" ] && missing_files+=("production_config.json")
    [ ! -d "images" ] && missing_files+=("images/")
    [ ! -d "scripts" ] && missing_files+=("scripts/")

    # 可选文件检查（仅提示，不影响部署）
    [ ! -d "src" ] && optional_files+=("src/")
    [ ! -f "README.md" ] && optional_files+=("README.md")

    # 检查必需文件
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "❌ 部署包不完整，缺少以下必需文件："
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi

    # 提示可选文件状态
    if [ ${#optional_files[@]} -gt 0 ]; then
        log_warn "⚠️ 以下文件缺失（不影响部署）："
        for file in "${optional_files[@]}"; do
            echo "  - $file"
        done
        echo ""
        log_info "📝 说明："
        echo "  - src/: 源代码目录（已打包在Docker镜像中，生产环境不需要）"
        echo "  - README.md: 项目说明文档（可选）"
    fi

    log_info "✅ 部署包完整性验证通过"
}

# 创建公司主机专用部署脚本
create_company_deploy_script() {
    log_step "创建公司主机专用部署脚本..."
    
    cat > scripts/deploy_company.sh << 'EOF'
#!/bin/bash
# 山西电信出账稽核AI系统 - 公司主机专用部署脚本
# 适配公司主机环境的部署脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_step "山西电信出账稽核AI系统 v2.1.0 公司主机部署"
log_info "部署路径: $(pwd)"

# 检查当前目录
if [ ! -f "scripts/deploy_company.sh" ]; then
    echo "❌ 请在部署包根目录下执行此脚本！"
    echo "正确执行方式: cd /bas/work/lvpd/jihe/billing_audit_offline_v2.1.0_20250730_003703 && bash scripts/deploy_company.sh"
    exit 1
fi

# 1. 加载镜像
log_step "1. 加载Docker镜像..."
if ! bash scripts/load_images.sh; then
    echo "❌ 镜像加载失败，请检查Docker环境"
    exit 1
fi

# 2. 创建环境变量文件
log_step "2. 创建环境变量..."
cat > .env << 'ENVEOF'
BILLING_AUDIT_ENV=production
BILLING_AUDIT_VERSION=v2.1.0
DATA_INPUT_DIR=/data/input
DATA_OUTPUT_DIR=/data/output
MODEL_DIR=/models
LOGS_DIR=/logs
PYTHONPATH=/app
OMP_NUM_THREADS=4
DB_PASSWORD=billing_password_$(date +%s)
COMPOSE_PROJECT_NAME=billing_audit
ENVEOF

# 3. 启动服务
log_step "3. 启动服务..."
# 使用预构建的镜像，不重新构建
if [ -f "config/docker-compose.yml.bak" ]; then
    cp config/docker-compose.yml.bak config/docker-compose.yml
fi
sed -i.bak 's/build: \./image: billing-audit-ai:v2.1.0/' config/docker-compose.yml

# 启动容器
docker-compose -f config/docker-compose.yml up -d billing-audit-ai

# 4. 等待服务启动
log_step "4. 等待服务启动..."
sleep 10

# 5. 健康检查
log_step "5. 运行健康检查..."
if docker exec billing-audit-ai python -c "
from src.config.production_config_manager import get_config_manager
config = get_config_manager()
print('✅ 配置加载成功')
print(f'项目: {config.get(\"project.name\")}')
print(f'版本: {config.get(\"project.version\")}')
" 2>/dev/null; then
    log_info "✅ 健康检查通过"
else
    log_warn "⚠️ 健康检查失败，但容器可能正常运行"
fi

log_info "🎉 部署完成！"
echo ""
echo "📊 服务状态:"
docker-compose -f config/docker-compose.yml ps
echo ""
echo "🚀 使用示例:"
echo "  完整流程: bash scripts/run_full_pipeline.sh"
echo "  查看日志: docker-compose -f config/docker-compose.yml logs -f"
echo "  进入容器: docker exec -it billing-audit-ai bash"
echo "  停止服务: bash scripts/stop.sh"
echo ""
echo "📁 重要路径:"
echo "  当前目录: $(pwd)"
echo "  输入数据: $(pwd)/data/input/"
echo "  输出结果: $(pwd)/data/output/"
echo "  日志文件: $(pwd)/logs/"
EOF

    chmod +x scripts/deploy_company.sh
    log_info "✅ 公司主机专用部署脚本创建完成"
}

# 创建源代码提取脚本（可选）
create_source_extraction_script() {
    log_step "创建源代码提取脚本（可选）..."

    cat > scripts/extract_source_code.sh << 'EOF'
#!/bin/bash
# 从Docker镜像中提取源代码（可选功能）

set -e

echo "🔍 从Docker镜像中提取源代码..."

# 检查镜像是否存在
if ! docker images | grep -q "billing-audit-ai.*v2.1.0"; then
    echo "❌ Docker镜像不存在，请先加载镜像"
    echo "执行: bash scripts/load_images.sh"
    exit 1
fi

# 创建临时容器并复制源代码
echo "📦 创建临时容器..."
CONTAINER_ID=$(docker create billing-audit-ai:v2.1.0)

echo "📂 提取源代码目录..."
docker cp $CONTAINER_ID:/app/src ./src
docker cp $CONTAINER_ID:/app/scripts ./scripts_from_image
docker cp $CONTAINER_ID:/app/config ./config_from_image

echo "🗑️ 清理临时容器..."
docker rm $CONTAINER_ID

echo "✅ 源代码提取完成！"
echo ""
echo "📁 提取的内容："
echo "  - src/: 完整源代码"
echo "  - scripts_from_image/: 镜像中的脚本"
echo "  - config_from_image/: 镜像中的配置"
echo ""
echo "⚠️ 注意：这些文件仅用于查看和调试，部署时使用的仍是镜像中的代码"
EOF

    chmod +x scripts/extract_source_code.sh
    log_info "✅ 源代码提取脚本创建完成"
}

# 创建使用说明
create_usage_guide() {
    log_step "创建使用说明..."
    
    cat > COMPANY_DEPLOYMENT_GUIDE.md << EOF
# 山西电信出账稽核AI系统 - 公司主机部署指南

## 📍 当前部署路径
\`\`\`
/bas/work/lvpd/jihe/billing_audit_offline_v2.1.0_20250730_003703
\`\`\`

## 🚀 快速部署

### 1. 进入部署目录
\`\`\`bash
cd /bas/work/lvpd/jihe/billing_audit_offline_v2.1.0_20250730_003703
\`\`\`

### 2. 执行部署（推荐使用公司主机专用脚本）
\`\`\`bash
# 使用修复后的公司主机专用部署脚本
bash scripts/deploy_company.sh
\`\`\`

### 3. 准备测试数据
\`\`\`bash
# 将您的数据文件复制到输入目录
cp /path/to/your/data.txt data/input/ofrm_result.txt
\`\`\`

### 4. 运行完整流程
\`\`\`bash
bash scripts/run_full_pipeline.sh
\`\`\`

## 🔧 管理命令

### 查看服务状态
\`\`\`bash
docker-compose -f config/docker-compose.yml ps
\`\`\`

### 查看日志
\`\`\`bash
docker-compose -f config/docker-compose.yml logs -f billing-audit-ai
\`\`\`

### 进入容器
\`\`\`bash
docker exec -it billing-audit-ai bash
\`\`\`

### 停止服务
\`\`\`bash
bash scripts/stop.sh
\`\`\`

## 📁 目录结构

\`\`\`
/bas/work/lvpd/jihe/billing_audit_offline_v2.1.0_20250730_003703/
├── data/
│   ├── input/          # 输入数据目录
│   ├── output/         # 输出结果目录
│   └── backup/         # 备份目录
├── logs/               # 日志目录
├── scripts/            # 脚本目录
├── config/             # 配置目录
└── images/             # Docker镜像目录
\`\`\`

## 📂 关于源代码目录

**src目录不是必需的**，原因：
- Docker镜像中已包含完整源代码
- 容器运行时使用镜像内的代码，不依赖宿主机文件
- 生产环境不暴露源代码更安全

如需查看源代码（调试用途），可执行：
\`\`\`bash
bash scripts/extract_source_code.sh
\`\`\`

## ⚠️ 重要注意事项

1. **必须在部署包根目录下执行所有脚本**
2. **确保Docker服务正在运行**
3. **确保有足够的磁盘空间（建议20GB+）**
4. **输入数据文件必须命名为 ofrm_result.txt**
5. **src目录缺失不影响正常部署和运行**

## 🆘 故障排除

### 问题1: 路径错误
\`\`\`bash
# 确保在正确目录下
pwd
# 应该显示: /bas/work/lvpd/jihe/billing_audit_offline_v2.1.0_20250730_003703
\`\`\`

### 问题2: 权限不足
\`\`\`bash
# 修复权限
chmod +x scripts/*.sh
chmod -R 755 data/
\`\`\`

### 问题3: Docker镜像加载失败
\`\`\`bash
# 手动加载镜像
docker load < images/billing-audit-ai-v2.1.0.tar.gz
\`\`\`

## 📞 技术支持

如遇问题，请提供：
- 错误日志
- 当前目录路径
- Docker版本信息
- 系统环境信息
EOF

    log_info "✅ 使用说明创建完成: COMPANY_DEPLOYMENT_GUIDE.md"
}

# 主函数
main() {
    log_info "开始修复山西电信出账稽核AI系统部署路径问题..."
    
    check_current_directory
    fix_docker_compose_config
    create_directories
    check_permissions
    verify_package_integrity
    create_company_deploy_script
    create_source_extraction_script
    create_usage_guide
    
    echo ""
    log_info "🎉 路径问题修复完成！"
    echo ""
    echo "📋 修复内容:"
    echo "  ✅ 修复了docker-compose.yml配置文件路径错误"
    echo "  ✅ 创建了必要的目录结构"
    echo "  ✅ 设置了正确的文件权限"
    echo "  ✅ 验证了部署包完整性（src目录为可选）"
    echo "  ✅ 创建了公司主机专用部署脚本"
    echo "  ✅ 创建了源代码提取脚本（可选）"
    echo "  ✅ 创建了详细的使用说明"
    echo ""
    echo "🚀 下一步操作:"
    echo "  1. 使用公司主机专用部署脚本: bash scripts/deploy_company.sh"
    echo "  2. 查看详细说明: cat COMPANY_DEPLOYMENT_GUIDE.md"
    echo ""
    echo "⚠️ 重要提醒:"
    echo "  - 必须在当前目录下执行所有脚本"
    echo "  - 当前目录: $(pwd)"
}

# 运行主函数
main "$@"
