#!/bin/bash
# 修复输出文件挂载问题的脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step "修复山西电信出账稽核AI系统输出文件挂载问题"

# 检查当前目录
if [ ! -f "config/docker-compose.yml" ]; then
    log_error "请在部署包根目录下执行此脚本！"
    exit 1
fi

# 1. 创建正确的运行脚本
log_step "1. 创建修复后的运行脚本..."

cat > scripts/run_full_pipeline_fixed.sh << 'EOF'
#!/bin/bash
# 修复后的完整流程运行脚本

set -e

echo "🚀 运行山西电信出账稽核AI系统完整流程..."

# 检查输入数据
if [ ! -f "data/input/ofrm_result.txt" ]; then
    echo "❌ 输入数据文件不存在: data/input/ofrm_result.txt"
    echo "请将数据文件放置到 data/input/ 目录下"
    exit 1
fi

# 创建输出目录
mkdir -p data/output/models
mkdir -p data/output/data
mkdir -p data/output/reports
mkdir -p data/output/reports/markdown
mkdir -p logs

echo "📁 当前目录: $(pwd)"
echo "📂 输入文件: $(ls -la data/input/)"

# 使用正确的挂载路径运行
echo "🔄 启动完整流程..."
docker run --rm \
  -v $(pwd)/data/input:/data/input:ro \
  -v $(pwd)/data/output:/app/outputs \
  -v $(pwd)/logs:/logs \
  -v $(pwd)/production_config.json:/app/config/production_config.json:ro \
  billing-audit-ai:v2.1.0 \
  python scripts/production/billing_audit_main.py full \
  --input /data/input/ofrm_result.txt \
  --algorithm hierarchical \
  --batch-size 1000

echo "✅ 完整流程执行完成！"
echo ""
echo "📁 输出文件位置:"
echo "  模型文件: $(pwd)/data/output/models/"
echo "  预测结果: $(pwd)/data/output/data/"
echo "  执行报告: $(pwd)/data/output/reports/"
echo "  Markdown报告: $(pwd)/data/output/reports/markdown/"
echo "  日志文件: $(pwd)/logs/"
echo ""
echo "📊 检查输出文件:"
if [ -d "data/output/models" ] && [ "$(ls -A data/output/models 2>/dev/null)" ]; then
    echo "  ✅ 模型文件: $(ls data/output/models/)"
else
    echo "  ❌ 模型文件: 未找到"
fi

if [ -d "data/output/data" ] && [ "$(ls -A data/output/data 2>/dev/null)" ]; then
    echo "  ✅ 预测结果: $(ls data/output/data/)"
else
    echo "  ❌ 预测结果: 未找到"
fi

if [ -d "data/output/reports" ] && [ "$(ls -A data/output/reports 2>/dev/null)" ]; then
    echo "  ✅ 执行报告: $(ls data/output/reports/)"
else
    echo "  ❌ 执行报告: 未找到"
fi

if [ -d "logs" ] && [ "$(ls -A logs 2>/dev/null)" ]; then
    echo "  ✅ 日志文件: $(ls logs/)"
else
    echo "  ❌ 日志文件: 未找到"
fi
EOF

chmod +x scripts/run_full_pipeline_fixed.sh
log_info "✅ 修复后的运行脚本创建完成"

# 2. 创建单次运行脚本（推荐）
log_step "2. 创建单次运行脚本（推荐使用）..."

cat > scripts/run_once.sh << 'EOF'
#!/bin/bash
# 单次运行脚本（不依赖docker-compose）

set -e

echo "🚀 山西电信出账稽核AI系统 - 单次运行模式"
echo "📍 当前目录: $(pwd)"

# 检查Docker镜像
if ! docker images | grep -q "billing-audit-ai.*v2.1.0"; then
    echo "❌ Docker镜像不存在，请先加载镜像"
    echo "执行: bash scripts/load_images.sh"
    exit 1
fi

# 检查输入数据
if [ ! -f "data/input/ofrm_result.txt" ]; then
    echo "❌ 输入数据文件不存在: data/input/ofrm_result.txt"
    echo "请将数据文件放置到 data/input/ 目录下"
    echo "示例: cp /path/to/your/data.txt data/input/ofrm_result.txt"
    exit 1
fi

# 创建输出目录
echo "📁 创建输出目录..."
mkdir -p data/output/models
mkdir -p data/output/data  
mkdir -p data/output/reports
mkdir -p data/output/reports/markdown
mkdir -p logs

# 显示输入文件信息
echo "📂 输入文件信息:"
echo "  文件: data/input/ofrm_result.txt"
echo "  大小: $(du -h data/input/ofrm_result.txt | cut -f1)"
echo "  行数: $(wc -l < data/input/ofrm_result.txt)"

# 运行完整流程
echo ""
echo "🔄 开始执行完整流程..."
echo "⏱️  预计耗时: 10-30秒（取决于数据量）"
echo ""

docker run --rm \
  -v $(pwd)/data/input:/data/input:ro \
  -v $(pwd)/data/output:/app/outputs \
  -v $(pwd)/logs:/logs \
  -v $(pwd)/production_config.json:/app/config/production_config.json:ro \
  billing-audit-ai:v2.1.0 \
  python scripts/production/billing_audit_main.py full \
  --input /data/input/ofrm_result.txt \
  --algorithm hierarchical \
  --batch-size 1000

echo ""
echo "🎉 完整流程执行完成！"
echo ""

# 详细检查输出文件
echo "📊 输出文件检查:"
echo "----------------------------------------"

# 检查模型文件
if [ -d "data/output/models" ] && [ "$(ls -A data/output/models 2>/dev/null)" ]; then
    echo "✅ 模型文件目录: data/output/models/"
    ls -la data/output/models/ | head -5
    echo "  总计: $(ls data/output/models/ | wc -l) 个文件"
else
    echo "❌ 模型文件目录: 空或不存在"
fi

echo ""

# 检查预测结果
if [ -d "data/output/data" ] && [ "$(ls -A data/output/data 2>/dev/null)" ]; then
    echo "✅ 预测结果目录: data/output/data/"
    ls -la data/output/data/ | head -5
    echo "  总计: $(ls data/output/data/ | wc -l) 个文件"
else
    echo "❌ 预测结果目录: 空或不存在"
fi

echo ""

# 检查执行报告
if [ -d "data/output/reports" ] && [ "$(ls -A data/output/reports 2>/dev/null)" ]; then
    echo "✅ 执行报告目录: data/output/reports/"
    ls -la data/output/reports/ | head -5
    if [ -d "data/output/reports/markdown" ] && [ "$(ls -A data/output/reports/markdown 2>/dev/null)" ]; then
        echo "✅ Markdown报告: data/output/reports/markdown/"
        ls -la data/output/reports/markdown/
    fi
    echo "  总计: $(find data/output/reports -type f | wc -l) 个文件"
else
    echo "❌ 执行报告目录: 空或不存在"
fi

echo ""

# 检查日志文件
if [ -d "logs" ] && [ "$(ls -A logs 2>/dev/null)" ]; then
    echo "✅ 日志文件目录: logs/"
    ls -la logs/ | head -5
    echo "  总计: $(ls logs/ | wc -l) 个文件"
else
    echo "❌ 日志文件目录: 空或不存在"
fi

echo ""
echo "📁 完整输出路径:"
echo "  $(pwd)/data/output/"
echo "  $(pwd)/logs/"
echo ""
echo "🔍 查看具体文件内容:"
echo "  ls -la $(pwd)/data/output/models/"
echo "  ls -la $(pwd)/data/output/data/"
echo "  ls -la $(pwd)/data/output/reports/"
EOF

chmod +x scripts/run_once.sh
log_info "✅ 单次运行脚本创建完成"

# 3. 创建输出文件检查脚本
log_step "3. 创建输出文件检查脚本..."

cat > scripts/check_outputs.sh << 'EOF'
#!/bin/bash
# 检查输出文件脚本

echo "🔍 检查山西电信出账稽核AI系统输出文件"
echo "📍 当前目录: $(pwd)"
echo ""

# 检查各个输出目录
directories=("data/output/models" "data/output/data" "data/output/reports" "data/output/reports/markdown" "logs")

for dir in "${directories[@]}"; do
    echo "📂 检查目录: $dir"
    if [ -d "$dir" ]; then
        if [ "$(ls -A $dir 2>/dev/null)" ]; then
            echo "  ✅ 存在，包含 $(ls $dir | wc -l) 个文件"
            echo "  📄 文件列表:"
            ls -la "$dir" | head -10
        else
            echo "  ⚠️ 目录存在但为空"
        fi
    else
        echo "  ❌ 目录不存在"
    fi
    echo ""
done

# 显示总体统计
echo "📊 总体统计:"
echo "  模型文件: $(find data/output/models -type f 2>/dev/null | wc -l) 个"
echo "  预测结果: $(find data/output/data -type f 2>/dev/null | wc -l) 个"  
echo "  执行报告: $(find data/output/reports -type f 2>/dev/null | wc -l) 个"
echo "  日志文件: $(find logs -type f 2>/dev/null | wc -l) 个"
EOF

chmod +x scripts/check_outputs.sh
log_info "✅ 输出文件检查脚本创建完成"

echo ""
log_info "🎉 修复完成！"
echo ""
echo "📋 创建的脚本:"
echo "  ✅ scripts/run_full_pipeline_fixed.sh - 修复后的完整流程脚本"
echo "  ✅ scripts/run_once.sh - 单次运行脚本（推荐）"
echo "  ✅ scripts/check_outputs.sh - 输出文件检查脚本"
echo ""
echo "🚀 推荐使用方式:"
echo "  1. 准备数据: cp /path/to/your/data.txt data/input/ofrm_result.txt"
echo "  2. 运行系统: bash scripts/run_once.sh"
echo "  3. 检查输出: bash scripts/check_outputs.sh"
echo ""
echo "💡 关键修复点:"
echo "  - 修复了容器内外路径映射问题"
echo "  - 使用 /app/outputs 作为容器内输出路径"
echo "  - 添加了详细的输出文件检查"
echo "  - 提供了不依赖docker-compose的运行方式"
