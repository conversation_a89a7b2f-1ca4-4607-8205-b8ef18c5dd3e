#!/bin/bash
# 修复山西电信出账稽核AI系统权限问题

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step "修复山西电信出账稽核AI系统权限问题"

# 检查当前目录
if [ ! -f "production_config.json" ]; then
    log_error "请在部署包根目录下执行此脚本！"
    exit 1
fi

log_info "当前目录: $(pwd)"
log_info "当前用户: $(whoami)"
log_info "用户ID: $(id)"

# 1. 创建完整的目录结构
log_step "1. 创建完整的目录结构..."

directories=(
    "data/input"
    "data/output"
    "data/output/temp"
    "data/output/models"
    "data/output/data"
    "data/output/reports"
    "data/output/reports/markdown"
    "data/backup"
    "logs"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    else
        log_info "目录已存在: $dir"
    fi
done

# 2. 设置目录权限
log_step "2. 设置目录权限..."

# 设置宽松权限，确保容器用户可以写入
chmod -R 755 data/ 2>/dev/null || true
chmod -R 777 data/output/ 2>/dev/null || true
chmod -R 777 logs/ 2>/dev/null || true

# 特别处理temp目录
if [ -d "data/output/temp" ]; then
    chmod 777 data/output/temp
    log_info "设置temp目录权限: 777"
fi

log_info "权限设置完成"

# 3. 验证权限设置
log_step "3. 验证权限设置..."

echo "📁 目录权限检查:"
ls -la data/
echo ""
ls -la data/output/
echo ""

# 4. 创建权限测试脚本
log_step "4. 创建权限测试脚本..."

cat > scripts/test_permissions.sh << 'EOF'
#!/bin/bash
# 权限测试脚本

echo "🧪 测试容器权限..."

# 测试创建文件权限
docker run --rm \
  -v $(pwd)/data/output:/app/outputs \
  billing-audit-ai:v2.1.0 \
  bash -c "
    echo '测试权限...'
    echo '当前用户: $(whoami)'
    echo '用户ID: $(id)'
    echo '挂载目录权限:'
    ls -la /app/outputs/
    echo '尝试创建测试目录...'
    mkdir -p /app/outputs/temp/test_$(date +%s)
    echo '✅ 权限测试通过'
    ls -la /app/outputs/
  "

if [ $? -eq 0 ]; then
    echo "✅ 权限测试成功"
else
    echo "❌ 权限测试失败"
fi
EOF

chmod +x scripts/test_permissions.sh
log_info "权限测试脚本创建完成"

# 5. 创建修复后的运行脚本
log_step "5. 创建修复后的运行脚本..."

cat > scripts/run_with_fixed_permissions.sh << 'EOF'
#!/bin/bash
# 权限修复后的运行脚本

set -e

echo "🚀 山西电信出账稽核AI系统 - 权限修复版"
echo "📍 当前目录: $(pwd)"

# 检查输入数据
if [ ! -f "data/input/ofrm_result.txt" ]; then
    echo "❌ 输入数据文件不存在: data/input/ofrm_result.txt"
    echo "请将数据文件放置到 data/input/ 目录下"
    exit 1
fi

# 确保目录存在且权限正确
echo "📁 确保目录权限..."
mkdir -p data/output/temp
mkdir -p data/output/models
mkdir -p data/output/data
mkdir -p data/output/reports/markdown
mkdir -p logs

chmod -R 777 data/output/ 2>/dev/null || true
chmod -R 777 logs/ 2>/dev/null || true

echo "📂 输入文件信息:"
echo "  文件: data/input/ofrm_result.txt"
echo "  大小: $(du -h data/input/ofrm_result.txt | cut -f1)"
echo "  行数: $(wc -l < data/input/ofrm_result.txt)"

echo ""
echo "🔄 开始执行完整流程..."

# 方法1: 使用root用户运行（推荐）
echo "使用root用户运行容器..."
docker run --rm \
  --user root \
  -v $(pwd)/data/input:/data/input:ro \
  -v $(pwd)/data/output:/app/outputs \
  -v $(pwd)/logs:/logs \
  -v $(pwd)/production_config.json:/app/config/production_config.json:ro \
  billing-audit-ai:v2.1.0 \
  python scripts/production/billing_audit_main.py full \
  --input /data/input/ofrm_result.txt \
  --algorithm hierarchical \
  --batch-size 1000

echo ""
echo "🎉 完整流程执行完成！"

# 修复输出文件权限，确保宿主机用户可以访问
echo "🔧 修复输出文件权限..."
sudo chown -R $(whoami):$(whoami) data/output/ 2>/dev/null || chown -R $(whoami):$(whoami) data/output/ 2>/dev/null || true
sudo chown -R $(whoami):$(whoami) logs/ 2>/dev/null || chown -R $(whoami):$(whoami) logs/ 2>/dev/null || true

echo ""
echo "📊 检查输出文件:"
if [ -d "data/output/models" ] && [ "$(ls -A data/output/models 2>/dev/null)" ]; then
    echo "  ✅ 模型文件: $(ls data/output/models/ | wc -l) 个"
    ls data/output/models/ | head -3
else
    echo "  ❌ 模型文件: 未找到"
fi

if [ -d "data/output/data" ] && [ "$(ls -A data/output/data 2>/dev/null)" ]; then
    echo "  ✅ 预测结果: $(ls data/output/data/ | wc -l) 个"
    ls data/output/data/ | head -3
else
    echo "  ❌ 预测结果: 未找到"
fi

if [ -d "data/output/reports" ] && [ "$(ls -A data/output/reports 2>/dev/null)" ]; then
    echo "  ✅ 执行报告: $(ls data/output/reports/ | wc -l) 个"
    ls data/output/reports/ | head -3
else
    echo "  ❌ 执行报告: 未找到"
fi

echo ""
echo "📁 完整输出路径: $(pwd)/data/output/"
EOF

chmod +x scripts/run_with_fixed_permissions.sh
log_info "修复后的运行脚本创建完成"

# 6. 运行权限测试
log_step "6. 运行权限测试..."
if command -v docker &> /dev/null; then
    bash scripts/test_permissions.sh
else
    log_warn "Docker未找到，跳过权限测试"
fi

echo ""
log_info "🎉 权限修复完成！"
echo ""
echo "📋 修复内容:"
echo "  ✅ 创建了完整的目录结构"
echo "  ✅ 设置了正确的目录权限 (777)"
echo "  ✅ 创建了权限测试脚本"
echo "  ✅ 创建了权限修复版运行脚本"
echo ""
echo "🚀 推荐使用方式:"
echo "  1. 测试权限: bash scripts/test_permissions.sh"
echo "  2. 运行系统: bash scripts/run_with_fixed_permissions.sh"
echo ""
echo "💡 如果仍有权限问题，可以尝试:"
echo "  - 使用sudo运行脚本"
echo "  - 检查SELinux设置"
echo "  - 联系系统管理员调整目录权限"
