#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复特征工程问题的脚本
解决"Found array with 0 feature(s)"的错误
"""

import os
import sys
import shutil
from pathlib import Path

def fix_large_scale_feature_engineer():
    """修复大规模特征工程器"""
    
    # 查找特征工程器文件
    possible_paths = [
        "src/billing_audit/preprocessing/large_scale_feature_engineer.py",
        "billing_audit_offline_v2.1.0_20250730_003703/billing_audit/preprocessing/large_scale_feature_engineer.py"
    ]
    
    target_file = None
    for path in possible_paths:
        if os.path.exists(path):
            target_file = path
            break
    
    if not target_file:
        print("❌ 未找到特征工程器文件")
        return False
    
    print(f"📝 修复文件: {target_file}")
    
    # 备份原文件
    backup_file = target_file + ".backup"
    shutil.copy2(target_file, backup_file)
    print(f"📋 备份文件: {backup_file}")
    
    # 读取原文件
    with open(target_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复transform_chunk方法
    old_transform_chunk = '''    def transform_chunk(self, chunk):
        """转换单个数据块"""
        self.logger.debug(f"开始转换数据块，输入形状: {chunk.shape}")

        # 1. 创建特征
        df_features = self.create_efficient_features(chunk)
        self.logger.debug(f"特征创建完成，输出形状: {df_features.shape}")

        # 2. 编码类别特征
        df_encoded = self.encode_categorical_chunk(df_features)
        self.logger.debug(f"类别特征编码完成")

        # 3. 标准化数值特征
        df_scaled = self.scale_numerical_chunk(df_encoded)
        self.logger.debug(f"数值特征标准化完成，最终形状: {df_scaled.shape}")

        return df_scaled'''
    
    new_transform_chunk = '''    def transform_chunk(self, chunk):
        """转换单个数据块"""
        self.logger.debug(f"开始转换数据块，输入形状: {chunk.shape}")

        # 1. 创建特征
        df_features = self.create_efficient_features(chunk)
        self.logger.debug(f"特征创建完成，输出形状: {df_features.shape}")

        # 2. 编码类别特征
        df_encoded = self.encode_categorical_chunk(df_features)
        self.logger.debug(f"类别特征编码完成")

        # 3. 标准化数值特征
        df_scaled = self.scale_numerical_chunk(df_encoded)
        self.logger.debug(f"数值特征标准化完成，最终形状: {df_scaled.shape}")

        # 4. 选择最终特征（确保有特征输出）
        final_features = self._select_final_features(df_scaled)
        self.logger.debug(f"最终特征选择完成，输出形状: {final_features.shape}")

        return final_features
    
    def _select_final_features(self, df):
        """选择最终特征"""
        # 确保至少有一些基础特征
        available_features = []
        
        # 1. 优先选择数值特征
        for col in self.numerical_columns:
            if col in df.columns:
                available_features.append(col)
        
        # 2. 添加类别特征
        for col in self.categorical_columns:
            if col in df.columns:
                available_features.append(col)
        
        # 3. 添加创建的特征
        created_features = ['daily_should_fee', 'billing_efficiency', 'eff_is_weekend', 
                          'eff_quarter', 'subscription_years', 'cal_type_day_interaction']
        for col in created_features:
            if col in df.columns:
                available_features.append(col)
        
        # 4. 如果没有特征，使用所有可用列（除了目标列）
        if not available_features:
            target_col = self.config.get('target_column', 'amount')
            available_features = [col for col in df.columns if col != target_col]
        
        # 5. 确保至少有一个特征
        if not available_features:
            # 创建一个常数特征作为fallback
            df['constant_feature'] = 1.0
            available_features = ['constant_feature']
            self.logger.warning("没有找到有效特征，创建常数特征")
        
        # 去重并保持顺序
        available_features = list(dict.fromkeys(available_features))
        
        self.logger.info(f"选择的特征: {available_features[:10]}{'...' if len(available_features) > 10 else ''}")
        
        return df[available_features]'''
    
    # 替换内容
    if old_transform_chunk in content:
        content = content.replace(old_transform_chunk, new_transform_chunk)
        print("✅ 修复了transform_chunk方法")
    else:
        print("⚠️ 未找到需要修复的transform_chunk方法")
    
    # 写入修复后的文件
    with open(target_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 文件修复完成: {target_file}")
    return True

def fix_docker_image():
    """修复Docker镜像中的特征工程器"""
    print("🐳 检查是否需要重新构建Docker镜像...")
    
    # 检查是否有Docker镜像
    import subprocess
    try:
        result = subprocess.run(['docker', 'images', 'billing-audit-ai:v2.1.0'], 
                              capture_output=True, text=True)
        if 'billing-audit-ai' in result.stdout:
            print("📦 发现现有Docker镜像，建议重新构建以包含修复")
            print("💡 重新构建命令: docker build -f deployment/docker/Dockerfile -t billing-audit-ai:v2.1.0 .")
            return True
    except FileNotFoundError:
        print("⚠️ Docker未安装或不可用")
    
    return False

def update_deploy_script():
    """更新一键部署脚本，支持任意文件名"""
    
    deploy_script_paths = [
        "billing_audit_production_v2.1.0_20250730_081941/deploy.sh",
        "deploy.sh"
    ]
    
    target_script = None
    for path in deploy_script_paths:
        if os.path.exists(path):
            target_script = path
            break
    
    if not target_script:
        print("❌ 未找到部署脚本")
        return False
    
    print(f"📝 更新部署脚本: {target_script}")
    
    # 备份原脚本
    backup_script = target_script + ".backup"
    shutil.copy2(target_script, backup_script)
    
    # 读取原脚本
    with open(target_script, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复硬编码的文件名
    old_check = '''    if [ ! -f "data/input/ofrm_result.txt" ]; then
        log_warn "输入数据文件不存在: data/input/ofrm_result.txt"'''
    
    new_check = '''    # 检查输入数据文件（支持任意文件名）
    local input_files=(data/input/*.txt data/input/*.csv)
    local input_file=""
    
    # 查找第一个有效的数据文件
    for file in "${input_files[@]}"; do
        if [ -f "$file" ]; then
            input_file="$file"
            break
        fi
    done
    
    if [ -z "$input_file" ]; then
        log_warn "输入数据目录中没有找到数据文件"'''
    
    if old_check in content:
        content = content.replace(old_check, new_check)
        print("✅ 修复了输入文件检查逻辑")
    
    # 修复Docker运行命令中的硬编码文件名
    old_docker_cmd = '''        --input /data/input/ofrm_result.txt \\'''
    new_docker_cmd = '''        --input "$input_file" \\'''
    
    if old_docker_cmd in content:
        content = content.replace(old_docker_cmd, new_docker_cmd)
        print("✅ 修复了Docker运行命令")
    
    # 写入修复后的脚本
    with open(target_script, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 确保脚本有执行权限
    os.chmod(target_script, 0o755)
    
    print(f"✅ 部署脚本更新完成: {target_script}")
    return True

def main():
    """主函数"""
    print("🔧 开始修复山西电信出账稽核AI系统...")
    print()
    
    success_count = 0
    total_tasks = 3
    
    # 1. 修复特征工程器
    print("1️⃣ 修复特征工程器...")
    if fix_large_scale_feature_engineer():
        success_count += 1
    print()
    
    # 2. 检查Docker镜像
    print("2️⃣ 检查Docker镜像...")
    if fix_docker_image():
        success_count += 1
    print()
    
    # 3. 更新部署脚本
    print("3️⃣ 更新部署脚本...")
    if update_deploy_script():
        success_count += 1
    print()
    
    # 总结
    print("📊 修复总结:")
    print(f"  ✅ 成功: {success_count}/{total_tasks}")
    
    if success_count == total_tasks:
        print("🎉 所有修复完成！")
        print()
        print("📋 下一步操作:")
        print("  1. 重新构建Docker镜像（如果需要）")
        print("  2. 重新制作部署包")
        print("  3. 测试修复后的系统")
    else:
        print("⚠️ 部分修复失败，请检查错误信息")
    
    return success_count == total_tasks

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
