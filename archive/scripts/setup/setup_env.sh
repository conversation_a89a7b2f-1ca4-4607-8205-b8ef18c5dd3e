#!/bin/bash

# 山西电信出账稽核AI项目环境设置脚本

echo "🚀 开始设置山西电信出账稽核AI项目环境..."

# 检查Python版本
python_version=$(python3 --version 2>&1)
echo "当前Python版本: $python_version"

# 创建虚拟环境
echo "📦 创建虚拟环境..."
python3 -m venv venv

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "⬆️ 升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📚 安装项目依赖..."
pip install -r requirements.txt

echo "✅ 环境设置完成！"
echo ""
echo "使用方法："
echo "1. 激活环境: source venv/bin/activate"
echo "2. 运行项目: python src/main.py"
echo "3. 退出环境: deactivate"
