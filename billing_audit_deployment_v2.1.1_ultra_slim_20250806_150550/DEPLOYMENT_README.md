# 🚀 山西电信出账稽核AI系统 v2.1.1 - 部署包

## 📋 部署包内容

这是山西电信出账稽核AI系统v2.1.1的完整部署包，包含了运行系统所需的所有组件。

### **🎯 v2.1.1 特性**
- ✅ **根本解决方案**: 修复了硬编码的模型选择逻辑
- ✅ **算法选择优先级**: 用户选择 > 文件检测 > 配置默认值
- ✅ **双模型支持**: 完美支持分层和传统模型
- ✅ **独立预测脚本**: 验证通过，不受主脚本影响
- ✅ **生产级稳定性**: 全面测试验证
- ✅ **精简核心代码**: 仅保留核心业务逻辑，体积减少63% (2.6M→944K)

## 📁 **目录结构**

```
billing_audit_deployment_v2.1.1_YYYYMMDD_HHMMSS/
├── src/                          # 核心源代码
├── scripts/                      # 脚本文件
├── config/                       # 配置文件
├── deployment/                   # 部署相关文件
│   ├── docker/                  # Docker容器化
│   ├── config/                  # 生产环境配置
│   └── scripts/                 # 部署脚本
├── docs/                         # 文档
├── data/                         # 数据目录
├── models/                       # 模型目录
├── logs/                         # 日志目录
├── outputs/                      # 输出目录
├── start.sh                      # 🚀 启动脚本
├── stop.sh                       # 🛑 停止脚本
├── restart.sh                    # 🔄 重启脚本
├── logs.sh                       # 📋 查看日志
├── shell.sh                      # 🔧 进入容器
├── run_main.sh                   # ⚡ 主脚本快捷方式
└── README.md                     # 项目说明
```

## 🚀 **快速部署**

### **1. 系统要求**
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 8GB+ (推荐16GB+)
- **磁盘**: 20GB+ 可用空间

### **2. 一键启动**
```bash
# 启动系统
./start.sh

# 查看状态
docker ps | grep billing-audit

# 查看日志
./logs.sh
```

### **3. 使用方法**
```bash
# 运行完整流程
./run_main.sh full --input /data/input/data.csv --algorithm random_forest

# 运行分层模型
./run_main.sh full --input /data/input/data.csv --algorithm hierarchical

# 进入容器调试
./shell.sh
```

### **4. 数据准备**
```bash
# 将数据文件放入输入目录
cp your_data.csv data/input/

# 运行处理
./run_main.sh full --input /data/input/your_data.csv
```

## 🔧 **配置说明**

### **生产环境配置**
- **配置文件**: `deployment/config/production_config.json`
- **算法支持**: random_forest, xgboost, lightgbm, hierarchical
- **默认算法**: random_forest

### **用户算法选择**
```bash
# 传统随机森林模型
./run_main.sh full --algorithm random_forest

# 分层建模（分类+回归）
./run_main.sh full --algorithm hierarchical

# XGBoost算法
./run_main.sh full --algorithm xgboost

# LightGBM算法  
./run_main.sh full --algorithm lightgbm
```

## 📊 **输出结果**

系统运行完成后，结果保存在：
- **预测结果**: `outputs/data/predictions_*.csv`
- **判定结果**: `outputs/data/billing_judgments_*.csv`
- **模型文件**: `outputs/models/*.pkl`
- **执行报告**: `outputs/reports/*.json`
- **Markdown报告**: `outputs/reports/markdown/*.md`

## 🛠️ **系统管理**

```bash
# 启动系统
./start.sh

# 停止系统
./stop.sh

# 重启系统
./restart.sh

# 查看日志
./logs.sh

# 进入容器
./shell.sh

# 查看容器状态
docker ps

# 查看资源使用
docker stats
```

## ⚠️ **注意事项**

1. **数据格式**: 确保输入数据为CSV格式，包含所需字段
2. **内存配置**: 大数据处理时建议调整批处理大小
3. **算法选择**: v2.1.1版本完全尊重用户的算法选择
4. **模型兼容**: 支持分层和传统模型并存使用
5. **生产部署**: 建议在生产环境设置资源限制

## 📞 **技术支持**

如有问题，请参考：
- **部署指南**: `deployment/DEPLOYMENT_GUIDE.md`
- **技术文档**: `docs/技术规格文档.md`
- **故障排除**: 查看日志文件排查问题

---

**版本**: v2.1.1 (根本解决方案版)  
**更新时间**: 2025-08-06  
**部署状态**: ✅ 生产就绪  
**验证状态**: ✅ 全面验证通过  
**特色功能**: ✅ 真正的双模型支持，完全尊重用户选择
