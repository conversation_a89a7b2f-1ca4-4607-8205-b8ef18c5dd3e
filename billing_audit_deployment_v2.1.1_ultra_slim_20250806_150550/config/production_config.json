{"project": {"name": "山西电信出账稽核AI系统", "version": "2.0.0", "description": "千万级数据处理收费稽核AI系统", "environment": "production"}, "data_paths": {"input_data_dir": "./data/input", "output_data_dir": "./outputs/data", "output_reports_dir": "./outputs/reports", "output_models_dir": "./outputs/models", "output_temp_dir": "./outputs/temp", "output_visualizations_dir": "./outputs/visualizations", "model_dir": "./models", "logs_dir": "./logs", "temp_dir": "./tmp/billing_audit", "backup_dir": "./data/backup"}, "data_sources": {"training_data": "${DATA_INPUT_DIR}/training_data.csv", "test_data": "${DATA_INPUT_DIR}/test_data.csv", "prediction_data": "${DATA_INPUT_DIR}/prediction_data.csv", "billing_data": "${DATA_INPUT_DIR}/billing_data.csv"}, "model_paths": {"large_scale_model": "${MODEL_DIR}/large_scale_model_latest.pkl", "large_scale_feature_engineer": "${MODEL_DIR}/large_scale_feature_engineer_latest.pkl", "hierarchical_model": "${MODEL_DIR}/hierarchical_model_latest.pkl", "model_backup_pattern": "${MODEL_DIR}/backup/model_backup_{timestamp}.pkl", "feature_engineer_backup_pattern": "${MODEL_DIR}/backup/feature_engineer_backup_{timestamp}.pkl", "hierarchical_model_backup_pattern": "${MODEL_DIR}/backup/hierarchical_model_backup_{timestamp}.pkl"}, "large_scale_processing": {"batch_size": 250, "max_memory_gb": 8, "n_jobs": -1, "chunk_size": 10000, "save_intermediate_results": true, "intermediate_save_frequency": 20, "enable_progress_monitoring": true, "enable_memory_monitoring": true}, "model_training": {"algorithms": ["random_forest", "xgboost", "lightgbm", "hierarchical"], "default_algorithm": "random_forest", "hyperparameters": {"random_forest": {"n_estimators": 100, "max_depth": 10, "min_samples_split": 5, "min_samples_leaf": 2, "random_state": 42, "n_jobs": -1}, "xgboost": {"n_estimators": 100, "max_depth": 6, "learning_rate": 0.1, "subsample": 0.8, "colsample_bytree": 0.8, "random_state": 42, "n_jobs": -1}, "lightgbm": {"n_estimators": 100, "max_depth": 6, "learning_rate": 0.1, "subsample": 0.8, "colsample_bytree": 0.8, "random_state": 42, "n_jobs": -1}, "hierarchical": {"use_lightgbm": true, "zero_threshold": 1e-06, "classifier_params": {"n_estimators": 100, "max_depth": 10, "learning_rate": 0.1, "random_state": 42, "n_jobs": -1, "verbose": -1}, "regressor_params": {"n_estimators": 100, "max_depth": 10, "learning_rate": 0.1, "random_state": 42, "n_jobs": -1, "verbose": -1}}, "lightgbm_classifier": {"n_estimators": 100, "max_depth": 10, "learning_rate": 0.1, "random_state": 42, "n_jobs": -1, "verbose": -1}, "lightgbm_regressor": {"n_estimators": 100, "max_depth": 10, "learning_rate": 0.1, "random_state": 42, "n_jobs": -1, "verbose": -1}}, "train_test_split": {"test_size": 0.2, "random_state": 42, "shuffle": true}, "cross_validation": {"cv_folds": 5, "scoring": "neg_mean_absolute_error", "enable_cv": false}, "early_stopping": {"enable": true, "patience": 10, "min_delta": 0.001}}, "feature_engineering": {"enable_auto_features": true, "feature_types": {"business_logic_features": true, "date_features": true, "combination_features": true, "interaction_features": true, "statistical_features": true}, "categorical_encoding": {"method": "onehot", "handle_unknown": "ignore", "drop_first": false}, "numerical_scaling": {"method": "standard", "with_mean": true, "with_std": true}, "missing_value_handling": {"numerical_strategy": "median", "categorical_strategy": "mode", "drop_threshold": 0.5}}, "billing_judgment": {"thresholds": {"absolute_threshold": 10.0, "relative_threshold": 0.1, "use_mixed_threshold": true, "uncertainty_factor": 2.0}, "confidence_calculation": {"method": "error_based", "min_confidence": 0.0, "max_confidence": 1.0}, "output_format": {"include_original_data": true, "include_predictions": true, "include_errors": true, "include_confidence": true, "include_timestamp": true}}, "data_schema": {"fixed_fee": {"training_features": ["cal_type", "unit_type", "rate_unit", "final_eff_year", "final_eff_mon", "final_eff_day", "final_exp_year", "final_exp_mon", "final_exp_day", "cur_year_month", "charge_day_count", "month_day_count", "should_fee", "busi_flag"], "target_column": "amount", "passthrough_columns": ["offer_inst_id", "prod_inst_id", "prod_id", "offer_id", "sub_prod_id", "event_pricing_strategy_id", "event_type_id", "calc_priority", "pricing_section_id", "calc_method_id", "role_id"], "required_columns": ["amount", "should_fee", "busi_flag", "cal_type", "unit_type", "offer_inst_id", "prod_inst_id", "prod_id", "offer_id", "sub_prod_id", "event_pricing_strategy_id", "event_type_id", "calc_priority", "pricing_section_id", "calc_method_id", "role_id", "rate_unit", "final_eff_year", "final_eff_mon", "final_eff_day", "final_exp_year", "final_exp_mon", "final_exp_day", "cur_year_month", "charge_day_count", "month_day_count"], "categorical_columns": ["cal_type", "unit_type", "rate_unit", "busi_flag"], "numerical_columns": ["final_eff_year", "final_eff_mon", "final_eff_day", "final_exp_year", "final_exp_mon", "final_exp_day", "charge_day_count", "month_day_count", "should_fee"], "date_columns": ["cur_year_month"]}}, "performance_monitoring": {"enable_monitoring": true, "metrics": {"processing_speed": true, "memory_usage": true, "cpu_usage": true, "disk_io": true, "model_performance": true, "judgment_accuracy": true}, "thresholds": {"max_memory_usage_gb": 8, "max_processing_time_hours": 4, "min_judgment_accuracy": 0.9, "max_error_rate": 0.05}, "alerts": {"enable_alerts": true, "alert_channels": ["log", "email"], "email_recipients": ["<EMAIL>"]}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "handlers": {"file": {"enabled": true, "path": "${LOGS_DIR}/billing_audit.log", "max_size": "100MB", "backup_count": 10, "rotation": "daily"}, "console": {"enabled": true, "level": "INFO"}, "syslog": {"enabled": false, "host": "localhost", "port": 514}}}, "security": {"data_encryption": {"enable": false, "algorithm": "AES-256", "key_file": "/etc/billing_audit/encryption.key"}, "access_control": {"enable": false, "allowed_ips": ["127.0.0.1", "10.0.0.0/8"], "api_key_required": false}}, "backup": {"enable_auto_backup": true, "backup_frequency": "daily", "backup_retention_days": 30, "backup_types": {"models": true, "data": false, "logs": true, "config": true}, "backup_compression": true}, "api_server": {"host": "0.0.0.0", "port": 8000, "workers": 4, "timeout": 300, "max_request_size": "100MB", "enable_cors": true, "cors_origins": ["*"]}, "database": {"enable": false, "type": "postgresql", "host": "localhost", "port": 5432, "database": "billing_audit", "username": "billing_user", "password": "${DB_PASSWORD}", "connection_pool": {"min_connections": 5, "max_connections": 20}}, "hierarchical_model_evaluation": {"zero_threshold": 1e-06, "business_accuracy_thresholds": [1, 5, 10, 20, 50, 100], "performance_grades": {"A+": 90, "A": 80, "B+": 70, "B": 60, "C": 50}, "evaluation_weights": {"classification_score": 0.3, "regression_score": 0.4, "business_score": 0.3}, "report_format": {"include_classification_metrics": true, "include_regression_metrics": true, "include_business_metrics": true, "include_distribution_analysis": true, "save_detailed_results": true}}, "environment_variables": {"DATA_INPUT_DIR": "/data/input", "DATA_OUTPUT_DIR": "/data/output", "MODEL_DIR": "/models", "LOGS_DIR": "/logs", "DB_PASSWORD": "your_db_password_here", "PYTHONPATH": "/app", "OMP_NUM_THREADS": "4"}}