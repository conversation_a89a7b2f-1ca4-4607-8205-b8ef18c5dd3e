# 🚀 山西电信出账稽核AI系统 v2.1.0 - 生产环境部署

本目录包含山西电信出账稽核AI系统v2.1.0的生产环境部署相关文件，支持Docker容器化部署和Linux生产环境一键部署。

### **🎯 v2.1.0 新特性**
- ✅ **生产级主脚本**: 完整的端到端自动化流程
- ✅ **Markdown执行报告**: 详细的业务分析和智能评级
- ✅ **流程修正**: 正确的数据流向
- ✅ **日志标准化**: 175个问题点修复，100%生产标准
- ✅ **Linux一键部署**: 全自动化的Linux生产环境部署

## 📁 **目录结构**

```
deployment/
├── README.md                          # 本文件
├── docker/                            # Docker容器化部署
│   ├── Dockerfile                    # Docker镜像构建文件 (v2.1.0 增强)
│   └── docker-compose.yml            # Docker Compose编排配置 (v2.1.0 增强)
├── config/                            # 生产环境配置
│   └── production_config.json        # 生产环境配置文件
├── scripts/                           # 部署脚本
│   ├── deploy_production.sh          # 通用一键部署脚本
│   ├── deploy_linux_production.sh    # Linux生产环境部署脚本 ⭐ 新增
│   └── export_docker_image.sh        # Docker镜像导出脚本 ⭐ 新增
└── requirements.txt                   # Python依赖文件
```

## 🚀 **快速部署**

### **⭐ 推荐: Linux生产环境一键部署** (v2.1.0)
```bash
# 下载部署脚本
chmod +x deployment/scripts/deploy_linux_production.sh

# 一键部署到Linux生产环境
./deployment/scripts/deploy_linux_production.sh

# 自定义部署目录
./deployment/scripts/deploy_linux_production.sh --project-dir /home/<USER>/billing-ai
```

### **1. Docker镜像导出和传输**
```bash
# 导出Docker镜像包
chmod +x deployment/scripts/export_docker_image.sh
./deployment/scripts/export_docker_image.sh

# 传输到目标服务器后导入
./docker_export/import_images.sh
```

### **2. 基础Docker部署**
```bash
# 进入部署目录
cd deployment

# 执行一键部署
./scripts/deploy_production.sh
```

### **3. 完整部署（包含数据库和监控）**
```bash
./scripts/deploy_production.sh --with-database --with-monitoring
```

### **3. 手动Docker部署**
```bash
# 进入Docker目录
cd deployment/docker

# 构建镜像
docker build -t billing-audit-ai:latest .

# 启动服务
docker-compose up -d
```

## ⚙️ **配置说明**

### **生产配置文件**
- **位置**: `deployment/config/production_config.json`
- **用途**: 生产环境的完整配置参数
- **特性**: 支持环境变量替换、配置验证

### **主要配置项**
- **数据路径**: 输入、输出、模型、日志目录配置
- **处理参数**: 批处理大小、内存限制、并行度
- **模型配置**: 算法选择、超参数设置
- **判定阈值**: 收费合理性判定的阈值配置

## 🐳 **Docker配置**

### **Dockerfile特性**
- 基于Python 3.9-slim镜像
- 自动安装系统和Python依赖
- 创建非root用户运行
- 包含健康检查机制

### **Docker Compose特性**
- 主应用服务 + 可选数据库/缓存/监控
- 完整的数据卷挂载
- 资源限制和健康检查
- 支持多环境配置

## 📊 **使用示例**

### **配置化模型训练**
```bash
docker exec billing-audit-ai python scripts/production/train_with_config.py \
    --config /app/deployment/config/production_config.json
```

### **大规模收费判定**
```bash
docker exec billing-audit-ai python scripts/production/large_scale_billing_judge.py \
    --input /data/input/billing_data.csv \
    --output /data/output/billing_judgments.csv
```

### **批量预测**
```bash
docker exec billing-audit-ai python scripts/production/predict_large_scale.py \
    --input /data/input/prediction_data.csv \
    --output /data/output/predictions.csv
```

## 🔧 **维护操作**

### **查看服务状态**
```bash
cd deployment/docker
docker-compose ps
```

### **查看日志**
```bash
docker-compose logs -f billing-audit-ai
```

### **重启服务**
```bash
docker-compose restart billing-audit-ai
```

### **停止服务**
```bash
docker-compose down
```

### **更新部署**
```bash
# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

## 📋 **部署检查清单**

### **部署前检查**
- [ ] Docker和Docker Compose已安装
- [ ] 配置文件已正确设置
- [ ] 数据目录已准备
- [ ] 网络端口已开放

### **部署后验证**
- [ ] 容器正常启动
- [ ] 健康检查通过
- [ ] 配置加载成功
- [ ] 日志输出正常
- [ ] 功能测试通过

## ⚠️ **注意事项**

1. **数据目录**: 确保数据目录有足够的磁盘空间
2. **内存配置**: 根据实际硬件调整内存限制
3. **网络安全**: 生产环境请配置防火墙规则
4. **备份策略**: 定期备份模型和重要数据
5. **监控告警**: 建议配置系统监控和告警

## 📞 **技术支持**

如有部署问题，请参考：
- [生产环境部署指南](../docs/生产环境部署指南.md)
- [技术规格文档](../docs/技术规格文档.md)
- [故障排除指南](../README.md#故障排除)

---

**维护团队**: 技术团队  
**最后更新**: 2025-07-25  
**版本**: v2.0.0
