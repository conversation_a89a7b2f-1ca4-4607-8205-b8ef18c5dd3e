#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - x86_64架构完整构建脚本
# 一键完成镜像构建和部署包制作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
VERSION="v2.1.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

log_header() {
    echo -e "${CYAN}[HEADER]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║          山西电信出账稽核AI系统 v2.1.0                        ║${NC}"
    echo -e "${CYAN}║              x86_64架构完整构建脚本                          ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  功能: ARM64 → x86_64 跨架构构建 + 离线部署包制作             ║${NC}"
    echo -e "${CYAN}║  目标: Linux x86_64主机无外网环境部署                        ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --build-only      仅构建x86_64镜像，不制作部署包"
    echo "  --package-only    仅制作部署包（需要已存在x86_64镜像）"
    echo "  --clean          清理所有x86_64相关镜像和文件"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "默认行为: 完整构建（镜像构建 + 部署包制作）"
    echo ""
    echo "示例:"
    echo "  $0                # 完整构建"
    echo "  $0 --build-only   # 仅构建镜像"
    echo "  $0 --package-only # 仅制作部署包"
    echo "  $0 --clean        # 清理环境"
}

# 检查系统环境
check_system() {
    log_header "检查系统环境"
    
    # 检查当前架构
    CURRENT_ARCH=$(uname -m)
    log_info "当前系统架构: $CURRENT_ARCH"
    
    if [ "$CURRENT_ARCH" != "arm64" ] && [ "$CURRENT_ARCH" != "aarch64" ]; then
        log_warn "当前系统不是ARM64架构，但仍可进行x86_64构建"
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    # 检查buildx
    if ! docker buildx version &> /dev/null; then
        log_error "Docker buildx不可用，请更新Docker到最新版本"
        exit 1
    fi
    
    # 显示Docker信息
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    log_info "Docker版本: $DOCKER_VERSION"
    
    # 检查磁盘空间
    AVAILABLE_SPACE=$(df -h . | awk 'NR==2 {print $4}')
    log_info "可用磁盘空间: $AVAILABLE_SPACE"
    
    log_success "系统环境检查通过"
}

# 构建x86_64镜像
build_x86_64_image() {
    log_header "构建x86_64架构镜像"
    
    cd "$PROJECT_ROOT"
    
    log_info "执行x86_64镜像构建脚本..."
    bash deployment/scripts/build_x86_64_image.sh
    
    if [ $? -eq 0 ]; then
        log_success "x86_64镜像构建完成"
    else
        log_error "x86_64镜像构建失败"
        exit 1
    fi
}

# 制作部署包
create_deployment_package() {
    log_header "制作x86_64部署包"
    
    cd "$PROJECT_ROOT"
    
    log_info "执行x86_64部署包制作脚本..."
    bash deployment/scripts/create_x86_64_deployment.sh
    
    if [ $? -eq 0 ]; then
        log_success "x86_64部署包制作完成"
    else
        log_error "x86_64部署包制作失败"
        exit 1
    fi
}

# 清理环境
clean_environment() {
    log_header "清理x86_64环境"
    
    log_info "清理x86_64相关镜像..."
    
    # 删除x86_64镜像
    docker images | grep "billing-audit-ai.*x86_64" | awk '{print $1":"$2}' | xargs -r docker rmi -f
    
    # 删除构建器
    docker buildx ls | grep "x86_64_builder" && docker buildx rm x86_64_builder || true
    
    # 删除导出的镜像文件
    find . -name "*x86_64*.tar.gz" -type f -exec rm -f {} \;
    find . -name "docker_images_x86_64_*" -type d -exec rm -rf {} \;
    find . -name "billing_audit_x86_64_*" -type d -exec rm -rf {} \;
    
    log_success "环境清理完成"
}

# 显示构建结果
show_build_results() {
    log_header "构建结果总结"
    
    echo ""
    echo "📦 x86_64镜像信息:"
    docker images | grep "billing-audit-ai.*x86_64" || echo "  未找到x86_64镜像"
    
    echo ""
    echo "📁 部署包文件:"
    find . -name "billing_audit_x86_64_*.tar.gz" -type f -exec ls -lh {} \; || echo "  未找到部署包文件"
    
    echo ""
    echo "🎯 下一步操作:"
    echo "  1. 将部署包传输到x86_64 Linux主机"
    echo "  2. 在目标主机上解压并运行 bash deploy.sh"
    echo "  3. 使用 bash run_with_mount.sh full 运行完整流程"
    
    echo ""
    echo "💡 技术特性:"
    echo "  ✅ 跨架构构建 (ARM64 → x86_64)"
    echo "  ✅ 无外网环境部署支持"
    echo "  ✅ 完整的v2.1.0功能"
    echo "  ✅ 分层建模算法"
    echo "  ✅ 智能挂载运行"
}

# 主函数
main() {
    show_banner
    
    # 解析命令行参数
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        --build-only)
            log_info "模式: 仅构建x86_64镜像"
            check_system
            build_x86_64_image
            show_build_results
            ;;
        --package-only)
            log_info "模式: 仅制作部署包"
            check_system
            create_deployment_package
            show_build_results
            ;;
        --clean)
            log_info "模式: 清理环境"
            clean_environment
            ;;
        "")
            log_info "模式: 完整构建（镜像 + 部署包）"
            check_system
            build_x86_64_image
            create_deployment_package
            show_build_results
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
    
    log_success "🎉 x86_64架构构建任务完成！"
}

# 运行主函数
main "$@"
