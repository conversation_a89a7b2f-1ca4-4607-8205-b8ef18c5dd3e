#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - 部署包验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 版本信息
VERSION="v2.1.0"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🔍 山西电信出账稽核AI系统 ${VERSION}"
    echo "   部署包验证工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "📅 验证时间: $(date)"
    echo ""
}

# 验证项目结构
validate_project_structure() {
    log_step "验证项目结构..."
    
    # 获取项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    echo "项目根目录: $PROJECT_ROOT"
    
    # 必需的目录
    required_dirs=(
        "src"
        "src/billing_audit"
        "src/billing_audit/training"
        "src/billing_audit/inference"
        "src/billing_audit/preprocessing"
        "scripts"
        "scripts/production"
        "deployment"
        "deployment/docker"
        "deployment/config"
        "deployment/scripts"
        "config"
        "docs"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$PROJECT_ROOT/$dir" ]; then
            log_info "✅ 目录存在: $dir"
        else
            log_error "❌ 目录缺失: $dir"
            return 1
        fi
    done
    
    log_info "项目结构验证通过"
}

# 验证核心文件
validate_core_files() {
    log_step "验证核心文件..."
    
    # 必需的文件
    required_files=(
        "README.md"
        "config/production_config.json"
        "deployment/docker/Dockerfile"
        "deployment/docker/docker-compose.yml"
        "deployment/config/production_config.json"
        "deployment/scripts/deploy_production.sh"
        "deployment/scripts/deploy_linux_production.sh"
        "deployment/scripts/export_docker_image.sh"
        "scripts/production/billing_audit_main.py"
        "src/billing_audit/training/train_large_scale_model.py"
        "src/billing_audit/inference/predict_large_scale.py"
        "src/billing_audit/inference/large_scale_billing_judge.py"
        "src/billing_audit/preprocessing/large_scale_feature_engineer.py"
        "src/billing_audit/preprocessing/data_splitter.py"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            log_info "✅ 文件存在: $file"
        else
            log_error "❌ 文件缺失: $file"
            return 1
        fi
    done
    
    log_info "核心文件验证通过"
}

# 验证脚本权限
validate_script_permissions() {
    log_step "验证脚本权限..."
    
    # 需要执行权限的脚本
    executable_scripts=(
        "deployment/scripts/deploy_production.sh"
        "deployment/scripts/deploy_linux_production.sh"
        "deployment/scripts/export_docker_image.sh"
        "deployment/scripts/validate_deployment.sh"
        "scripts/production/billing_audit_main.py"
    )
    
    for script in "${executable_scripts[@]}"; do
        if [ -x "$PROJECT_ROOT/$script" ]; then
            log_info "✅ 脚本可执行: $script"
        else
            log_warn "⚠️  脚本不可执行: $script (正在修复...)"
            chmod +x "$PROJECT_ROOT/$script"
            if [ -x "$PROJECT_ROOT/$script" ]; then
                log_info "✅ 权限修复成功: $script"
            else
                log_error "❌ 权限修复失败: $script"
                return 1
            fi
        fi
    done
    
    log_info "脚本权限验证通过"
}

# 验证Python语法
validate_python_syntax() {
    log_step "验证Python语法..."
    
    # 主要Python文件
    python_files=(
        "scripts/production/billing_audit_main.py"
        "src/billing_audit/training/train_large_scale_model.py"
        "src/billing_audit/inference/predict_large_scale.py"
        "src/billing_audit/inference/large_scale_billing_judge.py"
        "src/billing_audit/preprocessing/large_scale_feature_engineer.py"
        "src/billing_audit/preprocessing/data_splitter.py"
    )
    
    for file in "${python_files[@]}"; do
        if python3 -m py_compile "$PROJECT_ROOT/$file" 2>/dev/null; then
            log_info "✅ Python语法正确: $file"
        else
            log_error "❌ Python语法错误: $file"
            return 1
        fi
    done
    
    log_info "Python语法验证通过"
}

# 验证配置文件
validate_config_files() {
    log_step "验证配置文件..."
    
    # 验证JSON配置文件
    config_files=(
        "config/production_config.json"
        "deployment/config/production_config.json"
    )
    
    for file in "${config_files[@]}"; do
        if python3 -c "import json; json.load(open('$PROJECT_ROOT/$file'))" 2>/dev/null; then
            log_info "✅ JSON格式正确: $file"
        else
            log_error "❌ JSON格式错误: $file"
            return 1
        fi
    done
    
    log_info "配置文件验证通过"
}

# 验证Docker文件
validate_docker_files() {
    log_step "验证Docker文件..."
    
    # 验证Dockerfile语法
    if [ -f "$PROJECT_ROOT/deployment/docker/Dockerfile" ]; then
        # 基本语法检查
        if grep -q "FROM python:3.9-slim" "$PROJECT_ROOT/deployment/docker/Dockerfile"; then
            log_info "✅ Dockerfile基础镜像正确"
        else
            log_error "❌ Dockerfile基础镜像错误"
            return 1
        fi
        
        if grep -q "WORKDIR /app" "$PROJECT_ROOT/deployment/docker/Dockerfile"; then
            log_info "✅ Dockerfile工作目录正确"
        else
            log_error "❌ Dockerfile工作目录错误"
            return 1
        fi
    fi
    
    # 验证docker-compose.yml
    if [ -f "$PROJECT_ROOT/deployment/docker/docker-compose.yml" ]; then
        if grep -q "version:" "$PROJECT_ROOT/deployment/docker/docker-compose.yml"; then
            log_info "✅ docker-compose.yml格式正确"
        else
            log_error "❌ docker-compose.yml格式错误"
            return 1
        fi
    fi
    
    log_info "Docker文件验证通过"
}

# 验证文档完整性
validate_documentation() {
    log_step "验证文档完整性..."
    
    # 重要文档文件
    doc_files=(
        "README.md"
        "docs/core/README.md"
        "docs/core/文档索引.md"
        "docs/guides/生产环境主脚本使用指南.md"
        "docs/technical/主脚本流程修正说明.md"
        "docs/technical/生产脚本日志修复报告.md"
        "deployment/README.md"
    )
    
    for file in "${doc_files[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            # 检查文件是否为空
            if [ -s "$PROJECT_ROOT/$file" ]; then
                log_info "✅ 文档存在且非空: $file"
            else
                log_warn "⚠️  文档为空: $file"
            fi
        else
            log_error "❌ 文档缺失: $file"
            return 1
        fi
    done
    
    log_info "文档完整性验证通过"
}

# 验证版本一致性
validate_version_consistency() {
    log_step "验证版本一致性..."
    
    # 检查主要文件中的版本信息
    version_files=(
        "README.md"
        "deployment/README.md"
        "deployment/docker/Dockerfile"
        "deployment/scripts/deploy_linux_production.sh"
    )
    
    for file in "${version_files[@]}"; do
        if grep -q "v2.1.0\|2.1.0" "$PROJECT_ROOT/$file"; then
            log_info "✅ 版本信息正确: $file"
        else
            log_warn "⚠️  版本信息可能过时: $file"
        fi
    done
    
    log_info "版本一致性验证通过"
}

# 生成验证报告
generate_validation_report() {
    log_step "生成验证报告..."
    
    REPORT_FILE="$PROJECT_ROOT/deployment_validation_report.txt"
    
    cat > "$REPORT_FILE" << EOF
# 山西电信出账稽核AI系统 v2.1.0 - 部署包验证报告

## 验证信息
- 验证时间: $(date)
- 验证版本: ${VERSION}
- 项目根目录: ${PROJECT_ROOT}

## 验证结果
✅ 项目结构验证通过
✅ 核心文件验证通过
✅ 脚本权限验证通过
✅ Python语法验证通过
✅ 配置文件验证通过
✅ Docker文件验证通过
✅ 文档完整性验证通过
✅ 版本一致性验证通过

## 部署就绪状态
🎉 系统已准备好进行生产环境部署

## 推荐部署方式
1. Linux生产环境一键部署:
   ./deployment/scripts/deploy_linux_production.sh

2. Docker镜像导出和传输:
   ./deployment/scripts/export_docker_image.sh

3. 基础Docker部署:
   ./deployment/scripts/deploy_production.sh

## 重要提醒
- 确保目标服务器满足系统要求 (8GB+ 内存, 20GB+ 磁盘)
- 确保Docker和Docker Compose已安装
- 确保有足够的权限执行部署脚本

---
验证完成时间: $(date)
EOF
    
    log_info "验证报告已生成: $REPORT_FILE"
}

# 显示验证结果
show_validation_results() {
    log_step "验证结果"
    
    echo ""
    echo -e "${GREEN}=================================================="
    echo "🎉 山西电信出账稽核AI系统 ${VERSION} 验证完成"
    echo "==================================================${NC}"
    echo ""
    echo -e "${BLUE}✅ 验证项目:${NC}"
    echo "  ✅ 项目结构"
    echo "  ✅ 核心文件"
    echo "  ✅ 脚本权限"
    echo "  ✅ Python语法"
    echo "  ✅ 配置文件"
    echo "  ✅ Docker文件"
    echo "  ✅ 文档完整性"
    echo "  ✅ 版本一致性"
    echo ""
    echo -e "${BLUE}🚀 部署就绪状态:${NC}"
    echo "  🎉 系统已准备好进行生产环境部署"
    echo ""
    echo -e "${BLUE}📋 推荐部署方式:${NC}"
    echo "  1. Linux生产环境一键部署:"
    echo "     ./deployment/scripts/deploy_linux_production.sh"
    echo ""
    echo "  2. Docker镜像导出和传输:"
    echo "     ./deployment/scripts/export_docker_image.sh"
    echo ""
    echo "  3. 基础Docker部署:"
    echo "     ./deployment/scripts/deploy_production.sh"
    echo ""
    echo -e "${GREEN}✅ 验证完成！系统已就绪，可以开始部署。${NC}"
    echo ""
}

# 主函数
main() {
    show_banner
    
    # 执行所有验证
    validate_project_structure
    validate_core_files
    validate_script_permissions
    validate_python_syntax
    validate_config_files
    validate_docker_files
    validate_documentation
    validate_version_consistency
    
    # 生成报告和显示结果
    generate_validation_report
    show_validation_results
    
    log_info "🎉 验证完成！"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --help)
            echo "部署包验证工具"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --help    显示此帮助信息"
            echo ""
            echo "功能:"
            echo "  - 验证项目结构完整性"
            echo "  - 验证核心文件存在性"
            echo "  - 验证脚本执行权限"
            echo "  - 验证Python语法正确性"
            echo "  - 验证配置文件格式"
            echo "  - 验证Docker文件"
            echo "  - 验证文档完整性"
            echo "  - 验证版本一致性"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            exit 1
            ;;
    esac
done

# 运行主函数
main
