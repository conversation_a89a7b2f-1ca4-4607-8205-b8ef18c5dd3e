2025-08-06 15:09:01,260 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器
2025-08-06 15:09:01,260 - large_scale_feature_engineer - INFO - 特征列数: 14
2025-08-06 15:09:01,260 - large_scale_feature_engineer - INFO - 类别列数: 4
2025-08-06 15:09:01,260 - large_scale_feature_engineer - INFO - 数值列数: 9
2025-08-06 15:09:01,260 - large_scale_feature_engineer - INFO - 开始拟合统计量
2025-08-06 15:09:01,260 - large_scale_feature_engineer - INFO - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv
2025-08-06 15:09:01,260 - large_scale_feature_engineer - INFO - 批次大小: 1,000
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=8, 总行数=7,379
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 统计摘要生成完成
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=2021.66, 标准差=2.88
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.29, 标准差=3.39
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=8.65, 标准差=9.93
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=2039.83, 标准差=9.83
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=7.46, 标准差=3.83
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=23.40, 标准差=11.00
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=23.46, 标准差=12.55
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=30.00, 标准差=0.00
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=6361.62, 标准差=13478.20
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 3 个唯一值
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值
2025-08-06 15:09:01,333 - large_scale_feature_engineer - INFO - 保存预处理器到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150901.pkl
2025-08-06 15:09:01,334 - large_scale_feature_engineer - INFO - 预处理器保存成功: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150901.pkl
2025-08-06 15:09:03,743 - large_scale_model_trainer - INFO - 开始大规模数据模型训练: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/train_data_20250806_150902.csv
2025-08-06 15:09:03,743 - large_scale_model_trainer - INFO - 开始拟合大规模特征工程器: 文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/train_data_20250806_150902.csv, 批次大小=1,000
2025-08-06 15:09:03,743 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器
2025-08-06 15:09:03,743 - large_scale_feature_engineer - INFO - 特征列数: 14
2025-08-06 15:09:03,743 - large_scale_feature_engineer - INFO - 类别列数: 4
2025-08-06 15:09:03,743 - large_scale_feature_engineer - INFO - 数值列数: 9
2025-08-06 15:09:03,743 - large_scale_model_trainer - INFO - 特征工程器初始化完成
2025-08-06 15:09:03,743 - large_scale_model_trainer - INFO - 特征配置加载完成: 特征列数=14, 目标列=amount
2025-08-06 15:09:03,743 - large_scale_feature_engineer - INFO - 开始拟合统计量
2025-08-06 15:09:03,743 - large_scale_feature_engineer - INFO - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/train_data_20250806_150902.csv
2025-08-06 15:09:03,743 - large_scale_feature_engineer - INFO - 批次大小: 1,000
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=6, 总行数=5,903
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 统计摘要生成完成
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=2021.67, 标准差=2.89
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.29, 标准差=3.40
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=8.71, 标准差=9.96
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=2039.88, 标准差=9.81
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=7.45, 标准差=3.85
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=23.28, 标准差=11.11
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=23.66, 标准差=12.43
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=30.00, 标准差=0.00
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=6339.86, 标准差=13565.48
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 3 个唯一值
2025-08-06 15:09:03,805 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值
2025-08-06 15:09:03,805 - large_scale_model_trainer - INFO - 特征工程器统计量拟合完成
2025-08-06 15:09:03,805 - large_scale_model_trainer - INFO - 开始分批处理和训练: 批次大小=1,000
2025-08-06 15:09:03,805 - large_scale_model_trainer - INFO - 模型初始化完成: RandomForestRegressor, 参数={'n_estimators': 100, 'max_depth': 10, 'min_samples_split': 5, 'min_samples_leaf': 2, 'random_state': 42, 'n_jobs': -1}
2025-08-06 15:09:03,805 - large_scale_model_trainer - INFO - 开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/train_data_20250806_150902.csv, 批次大小: 1,000
2025-08-06 15:09:03,805 - large_scale_model_trainer - INFO - 检测到分隔符: ','
2025-08-06 15:09:03,806 - large_scale_model_trainer - INFO - 开始分批处理数据，使用分隔符: ','
2025-08-06 15:09:03,812 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:03,818 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:03,823 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:03,829 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:03,835 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:03,841 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:03,841 - large_scale_model_trainer - INFO - 合并剩余的 6 批数据
2025-08-06 15:09:03,842 - large_scale_model_trainer - INFO - 剩余数据合并完成，数据形状: (5903, 19)
2025-08-06 15:09:03,842 - large_scale_model_trainer - INFO - 开始最终数据合并，待合并批次数: 1
2025-08-06 15:09:03,842 - large_scale_model_trainer - INFO - 最终数据合并完成: X(5903, 19), y(5903,)
2025-08-06 15:09:03,842 - large_scale_model_trainer - INFO - 开始分割训练测试集，测试集比例: 0.2
2025-08-06 15:09:03,843 - large_scale_model_trainer - INFO - 数据集分割完成: 训练集=4,722样本, 测试集=1,181样本
2025-08-06 15:09:03,843 - large_scale_model_trainer - INFO - 开始模型训练: 算法=RandomForestRegressor, 特征数=19
2025-08-06 15:09:03,843 - large_scale_model_trainer - INFO - 模型参数: n_estimators=100, max_depth=10
2025-08-06 15:09:03,924 - large_scale_model_trainer - INFO - 模型训练完成，耗时: 0.08秒
2025-08-06 15:09:03,924 - large_scale_model_trainer - INFO - 开始模型性能评估，测试样本数: 1,181
2025-08-06 15:09:03,939 - large_scale_model_trainer - INFO - 模型性能评估: MAE=328.28, RMSE=860.86, R²=0.9355
2025-08-06 15:09:03,939 - large_scale_model_trainer - INFO - 性能等级评估: 优秀 (R²=0.9355)
2025-08-06 15:09:03,939 - large_scale_model_trainer - INFO - 开始保存模型和特征工程器: 时间戳=20250806_150903
2025-08-06 15:09:03,957 - large_scale_feature_engineer - INFO - 保存预处理器到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150903.pkl
2025-08-06 15:09:03,958 - large_scale_feature_engineer - INFO - 预处理器保存成功: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150903.pkl
2025-08-06 15:09:03,958 - large_scale_model_trainer - INFO - 模型保存完成: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_model_20250806_150903.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150903.pkl
2025-08-06 15:09:04,949 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150901.pkl
2025-08-06 15:09:04,949 - large_scale_model_evaluator - INFO - 开始大规模模型评估: 测试文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/test_data_20250806_150902.csv, 目标列=amount
2025-08-06 15:09:04,956 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:04,983 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:05,957 - large_scale_prediction - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_model_20250806_150903.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150901.pkl
2025-08-06 15:09:05,967 - large_scale_prediction - INFO - 传统模型加载成功: RandomForestRegressor
2025-08-06 15:09:05,968 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150901.pkl
2025-08-06 15:09:05,968 - large_scale_prediction - INFO - 开始大规模数据预测: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/test_data_20250806_150902.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/data/predictions_20250806_150901.csv
2025-08-06 15:09:05,983 - large_scale_prediction - INFO - 开始处理第 1 批数据，样本数: 1000
2025-08-06 15:09:05,989 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:05,989 - large_scale_prediction - INFO - 处理批次 1, 状态检查间隔: 5
2025-08-06 15:09:06,005 - large_scale_prediction - INFO - 第 1 批预测完成: 1,000 条，累计: 1,000 条
2025-08-06 15:09:06,007 - large_scale_prediction - INFO - 开始处理第 2 批数据，样本数: 476
2025-08-06 15:09:06,011 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:06,012 - large_scale_prediction - INFO - 处理批次 2, 状态检查间隔: 5
2025-08-06 15:09:06,027 - large_scale_prediction - INFO - 第 2 批预测完成: 476 条，累计: 1,476 条
2025-08-06 15:09:06,034 - large_scale_prediction - INFO - 最终结果已保存(包含特征): /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/data/predictions_20250806_150901.csv, 记录数: 1476
2025-08-06 15:09:06,034 - large_scale_prediction - INFO - 列顺序: 训练特征(14) + 透传字段(11) + 目标字段(1) + 预测字段(1)
2025-08-06 15:09:06,034 - large_scale_prediction - INFO - 预测完成: 总数=1,476, 耗时=0.07秒, 速度=22394条/秒
2025-08-06 15:09:06,034 - large_scale_prediction - INFO - 短期改进统计: 批次数=2, 状态恢复=0次, 成功率=73.8%
2025-08-06 15:09:07,041 - large_scale_billing_judge - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_model_20250806_150903.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150901.pkl
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 传统模型加载成功: RandomForestRegressor
2025-08-06 15:09:07,051 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/models/large_scale_feature_engineer_20250806_150901.pkl
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 大规模收费合理性判定器初始化完成
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 批次大小: 1,000
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 绝对阈值: ±10.0元
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 相对阈值: ±10.0%
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 开始大规模收费合理性判定: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/test_data_20250806_150902.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/data/billing_judgments_20250806_150901.csv, 目标列=amount
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/temp/run_20250806_150901/test_data_20250806_150902.csv, 批次大小: 1,000
2025-08-06 15:09:07,051 - large_scale_billing_judge - INFO - 检测到分隔符: ','
2025-08-06 15:09:07,053 - large_scale_billing_judge - INFO - 开始判定第 1 批数据，样本数: 1000
2025-08-06 15:09:07,058 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:07,080 - large_scale_billing_judge - INFO - 第 1 批判定完成: 1,000 条，累计: 1,000 条
2025-08-06 15:09:07,080 - large_scale_billing_judge - INFO - 开始判定第 2 批数据，样本数: 476
2025-08-06 15:09:07,085 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 15:09:07,101 - large_scale_billing_judge - INFO - 第 2 批判定完成: 476 条，累计: 1,476 条
2025-08-06 15:09:07,101 - large_scale_billing_judge - INFO - 开始保存最终判定结果到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/data/billing_judgments_20250806_150901.csv
2025-08-06 15:09:07,110 - large_scale_billing_judge - INFO - 最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/data/billing_judgments_20250806_150901.csv, 总记录数: 1476
2025-08-06 15:09:07,110 - large_scale_billing_judge - INFO - 大规模收费合理性判定完成: 总数=1,476, 耗时=0.06秒, 速度=25326条/秒
2025-08-06 15:09:07,110 - large_scale_billing_judge - INFO - 判定统计: 合理=782(53.0%), 不合理=459(31.1%), 不确定=235(15.9%)
2025-08-06 15:09:07,378 - comprehensive_report_generator - INFO - 开始生成综合分层建模报告...
2025-08-06 15:09:07,379 - comprehensive_report_generator - INFO - 综合报告已生成: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/billing_audit_deployment_v2.1.1_ultra_slim_20250806_150550/outputs/reports/markdown/comprehensive_standard_report_20250806_150901.md
