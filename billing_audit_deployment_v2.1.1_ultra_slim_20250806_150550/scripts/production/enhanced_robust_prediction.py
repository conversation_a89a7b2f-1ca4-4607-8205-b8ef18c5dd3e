#!/usr/bin/env python3
"""
增强型健壮预测解决方案 - 短期改进方案
专门针对非零值回归器状态异常问题的快速修复
"""
import pandas as pd
import numpy as np
import joblib
import gc
import time
import logging
from pathlib import Path
from typing import Optional, Union, List, Dict, Any

class EnhancedRobustPredictor:
    """增强型健壮预测器 - 短期改进方案"""
    
    def __init__(self, model_path: str, feature_engineer_path: str):
        self.model_path = Path(model_path)
        self.feature_engineer_path = Path(feature_engineer_path)
        self.logger = self._setup_logger()
        
        # 加载模型组件
        self.model_dict = joblib.load(model_path)
        self.feature_engineer_dict = joblib.load(feature_engineer_path)
        
        # 提取组件
        self.zero_classifier = self.model_dict['zero_classifier']
        self.nonzero_regressor = self.model_dict['nonzero_regressor']
        self.feature_engineer = self.feature_engineer_dict['feature_engineer']
        
        # 状态管理
        self.regressor_state_backup = {}
        self.prediction_count = 0
        self.regressor_failures = 0
        self.classifier_failures = 0
        
        # 备份初始状态
        self._backup_regressor_state()
        
        self.logger.info("增强型健壮预测器初始化完成")
    
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger("EnhancedRobustPredictor")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def _backup_regressor_state(self):
        """备份非零值回归器状态"""
        critical_attrs = ['_n_classes', '_objective', '_n_features', 'n_features_in_']
        
        for attr in critical_attrs:
            if hasattr(self.nonzero_regressor, attr):
                value = getattr(self.nonzero_regressor, attr)
                self.regressor_state_backup[attr] = value
        
        # 特殊处理：确保_n_classes为正确值
        if '_n_classes' not in self.regressor_state_backup or self.regressor_state_backup['_n_classes'] is None:
            self.regressor_state_backup['_n_classes'] = 1  # 回归任务默认为1
        
        self.logger.info(f"回归器状态备份完成: {self.regressor_state_backup}")
    
    def _restore_regressor_state(self):
        """恢复非零值回归器状态"""
        restored_count = 0
        
        for attr, value in self.regressor_state_backup.items():
            if not hasattr(self.nonzero_regressor, attr) or getattr(self.nonzero_regressor, attr) is None:
                setattr(self.nonzero_regressor, attr, value)
                restored_count += 1
        
        if restored_count > 0:
            self.logger.info(f"恢复回归器状态: {restored_count} 个属性")
        
        return restored_count > 0
    
    def _aggressive_state_reset(self):
        """激进的状态重置机制"""
        try:
            # 强制设置关键状态
            self.nonzero_regressor._n_classes = 1
            if hasattr(self.nonzero_regressor, '_objective'):
                self.nonzero_regressor._objective = 'regression'
            
            # 内存清理
            gc.collect()
            
            self.logger.info("执行激进状态重置")
            return True
            
        except Exception as e:
            self.logger.error(f"激进状态重置失败: {e}")
            return False
    
    def _safe_classifier_predict(self, X: np.ndarray) -> np.ndarray:
        """安全的分类器预测"""
        try:
            return self.zero_classifier.predict(X)
        except Exception as e:
            self.classifier_failures += 1
            self.logger.error(f"零值分类器预测失败: {e}")
            # 保守策略：全部预测为零值
            return np.zeros(len(X), dtype=int)
    
    def _safe_regressor_predict(self, X: np.ndarray) -> np.ndarray:
        """安全的回归器预测"""
        try:
            # 预测前状态检查和恢复
            self._restore_regressor_state()
            
            return self.nonzero_regressor.predict(X)
            
        except Exception as e:
            self.regressor_failures += 1
            self.logger.error(f"非零值回归器预测失败: {e}")
            
            # 尝试激进状态重置
            if self._aggressive_state_reset():
                try:
                    return self.nonzero_regressor.predict(X)
                except Exception as e2:
                    self.logger.error(f"状态重置后仍然失败: {e2}")
            
            # 最终安全策略：返回零值
            return np.zeros(len(X))
    
    def predict_batch_enhanced(self, X: np.ndarray) -> np.ndarray:
        """增强的批次预测"""
        # 第一阶段：零值分类
        zero_predictions = self._safe_classifier_predict(X)
        
        # 初始化预测结果
        final_predictions = np.zeros(len(X))
        
        # 第二阶段：非零值回归
        nonzero_mask = zero_predictions == 1
        if np.any(nonzero_mask):
            X_nonzero = X[nonzero_mask]
            nonzero_predictions = self._safe_regressor_predict(X_nonzero)
            final_predictions[nonzero_mask] = nonzero_predictions
        
        return final_predictions
    
    def predict_large_file_enhanced(self, input_file: str, output_file: str, 
                                   batch_size: int = 250) -> Dict[str, Any]:
        """增强的大文件预测"""
        self.logger.info(f"开始增强预测: {input_file} -> {output_file}")
        start_time = time.time()
        
        # 读取数据
        df = pd.read_csv(input_file)
        total_samples = len(df)
        
        self.logger.info(f"数据规模: {total_samples:,} 行")
        
        all_predictions = []
        successful_batches = 0
        failed_batches = 0
        
        # 分批处理
        for i in range(0, total_samples, batch_size):
            batch_idx = i // batch_size + 1
            batch_end = min(i + batch_size, total_samples)
            batch_data = df.iloc[i:batch_end]
            
            try:
                # 特征工程
                X_features = self.feature_engineer.transform(batch_data)
                
                # 增强预测
                batch_predictions = self.predict_batch_enhanced(X_features)
                all_predictions.extend(batch_predictions)
                
                successful_batches += 1
                self.prediction_count += len(batch_predictions)
                
                # 每5个批次进行状态检查（增加频率）
                if batch_idx % 5 == 0:
                    self._restore_regressor_state()
                    gc.collect()
                    self.logger.info(f"批次 {batch_idx}: 状态检查完成")
                
                if batch_idx % 20 == 0:
                    success_rate = successful_batches / batch_idx * 100
                    self.logger.info(f"进度: {batch_idx} 批次, 成功率: {success_rate:.1f}%")
                
            except Exception as e:
                failed_batches += 1
                self.logger.error(f"批次 {batch_idx} 完全失败: {e}")
                # 使用零值填充失败的批次
                batch_predictions = np.zeros(len(batch_data))
                all_predictions.extend(batch_predictions)
        
        # 保存结果
        result_df = df.copy()
        result_df['predicted_amount'] = all_predictions
        result_df.to_csv(output_file, index=False)
        
        # 统计结果
        end_time = time.time()
        duration = end_time - start_time
        
        result = {
            'success': True,
            'total_samples': total_samples,
            'predictions_count': len(all_predictions),
            'successful_batches': successful_batches,
            'failed_batches': failed_batches,
            'success_rate': successful_batches / (successful_batches + failed_batches) * 100,
            'data_completeness': len(all_predictions) / total_samples * 100,
            'classifier_failures': self.classifier_failures,
            'regressor_failures': self.regressor_failures,
            'duration_seconds': duration,
            'processing_speed': total_samples / duration,
            'output_file': output_file
        }
        
        self.logger.info(f"增强预测完成: 成功率 {result['success_rate']:.1f}%, 数据完整性 {result['data_completeness']:.1f}%")
        
        return result

def main():
    """主函数 - 演示增强预测"""
    import argparse
    
    parser = argparse.ArgumentParser(description="增强型健壮预测解决方案")
    parser.add_argument("--input", required=True, help="输入文件路径")
    parser.add_argument("--output", required=True, help="输出文件路径")
    parser.add_argument("--model", required=True, help="模型文件路径")
    parser.add_argument("--feature-engineer", required=True, help="特征工程器路径")
    parser.add_argument("--batch-size", type=int, default=250, help="批次大小")
    
    args = parser.parse_args()
    
    # 创建增强预测器
    predictor = EnhancedRobustPredictor(args.model, args.feature_engineer)
    
    # 执行预测
    result = predictor.predict_large_file_enhanced(
        args.input, args.output, args.batch_size
    )
    
    # 输出结果
    if result['success']:
        print(f"✅ 增强预测成功完成")
        print(f"📊 处理样本: {result['total_samples']:,}")
        print(f"📈 成功率: {result['success_rate']:.1f}%")
        print(f"📋 数据完整性: {result['data_completeness']:.1f}%")
        print(f"⏱️  处理时间: {result['duration_seconds']:.2f} 秒")
        print(f"🚀 处理速度: {result['processing_speed']:.0f} 样本/秒")
        print(f"📁 输出文件: {result['output_file']}")
        
        if result['regressor_failures'] > 0:
            print(f"⚠️ 回归器失败: {result['regressor_failures']} 次")
        if result['classifier_failures'] > 0:
            print(f"⚠️ 分类器失败: {result['classifier_failures']} 次")
    else:
        print(f"❌ 增强预测失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
