#!/usr/bin/env python3
"""
文件分割预测策略
解决LightGBM大规模连续批处理状态异常问题
"""
import pandas as pd
import numpy as np
import os
import time
import logging
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Tuple
import shutil

class FileSplitPredictionStrategy:
    """文件分割预测策略"""
    
    def __init__(self, chunk_size: int = 12000):
        self.chunk_size = chunk_size  # 每个分割文件的行数
        self.logger = self._setup_logger()
        self.split_files = []
        self.prediction_results = []
        self.performance_metrics = {
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'total_processing_time': 0,
            'individual_times': [],
            'success_rates': [],
            'total_predictions': 0
        }
        
        self.logger.info(f"文件分割预测策略初始化完成，分割大小: {chunk_size:,} 行")
    
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger("FileSplitPredictionStrategy")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def split_file(self, input_file: str, output_dir: str) -> List[str]:
        """分割文件"""
        input_path = Path(input_file)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"开始分割文件: {input_file}")
        
        # 读取原始文件
        df = pd.read_csv(input_file)
        total_rows = len(df)
        
        self.logger.info(f"原始文件: {total_rows:,} 行")
        
        # 计算分割数量
        num_chunks = (total_rows + self.chunk_size - 1) // self.chunk_size
        self.logger.info(f"将分割为 {num_chunks} 个文件，每个约 {self.chunk_size:,} 行")
        
        split_files = []
        
        for i in range(num_chunks):
            start_idx = i * self.chunk_size
            end_idx = min(start_idx + self.chunk_size, total_rows)
            
            # 创建分割文件
            chunk_df = df.iloc[start_idx:end_idx]
            
            # 生成文件名
            chunk_filename = f"{input_path.stem}_part_{i+1:03d}.csv"
            chunk_filepath = output_path / chunk_filename
            
            # 保存分割文件
            chunk_df.to_csv(chunk_filepath, index=False)
            split_files.append(str(chunk_filepath))
            
            self.logger.info(f"创建分割文件 {i+1}/{num_chunks}: {chunk_filename} ({len(chunk_df):,} 行)")
        
        self.split_files = split_files
        self.performance_metrics['total_files'] = len(split_files)
        
        self.logger.info(f"文件分割完成: {len(split_files)} 个文件")
        return split_files
    
    def predict_single_file(self, input_file: str, model_path: str, 
                           feature_engineer_path: str, output_file: str) -> Dict[str, Any]:
        """对单个文件进行预测"""
        start_time = time.time()
        
        try:
            # 构建预测命令
            cmd = [
                'python', 'src/billing_audit/inference/predict_large_scale.py',
                '--input', input_file,
                '--model', model_path,
                '--feature-engineer', feature_engineer_path,
                '--output', output_file,
                '--batch-size', '1000',
                '--include-features'
            ]
            
            self.logger.info(f"开始预测文件: {Path(input_file).name}")
            
            # 执行预测
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            processing_time = time.time() - start_time
            
            # 检查执行结果
            if result.returncode == 0:
                # 检查输出文件
                if Path(output_file).exists():
                    output_df = pd.read_csv(output_file)
                    input_df = pd.read_csv(input_file)
                    
                    success_rate = len(output_df) / len(input_df) * 100
                    
                    prediction_result = {
                        'input_file': input_file,
                        'output_file': output_file,
                        'success': True,
                        'input_rows': len(input_df),
                        'output_rows': len(output_df),
                        'success_rate': success_rate,
                        'processing_time': processing_time,
                        'error_message': None
                    }
                    
                    self.logger.info(f"预测成功: {Path(input_file).name} - {success_rate:.1f}% ({len(output_df):,}/{len(input_df):,} 行)")
                    
                else:
                    prediction_result = {
                        'input_file': input_file,
                        'output_file': output_file,
                        'success': False,
                        'input_rows': 0,
                        'output_rows': 0,
                        'success_rate': 0,
                        'processing_time': processing_time,
                        'error_message': '输出文件未生成'
                    }
                    
                    self.logger.error(f"预测失败: {Path(input_file).name} - 输出文件未生成")
            else:
                prediction_result = {
                    'input_file': input_file,
                    'output_file': output_file,
                    'success': False,
                    'input_rows': 0,
                    'output_rows': 0,
                    'success_rate': 0,
                    'processing_time': processing_time,
                    'error_message': result.stderr
                }
                
                self.logger.error(f"预测失败: {Path(input_file).name} - {result.stderr}")
            
            return prediction_result
            
        except subprocess.TimeoutExpired:
            prediction_result = {
                'input_file': input_file,
                'output_file': output_file,
                'success': False,
                'input_rows': 0,
                'output_rows': 0,
                'success_rate': 0,
                'processing_time': 300,
                'error_message': '预测超时'
            }
            
            self.logger.error(f"预测超时: {Path(input_file).name}")
            return prediction_result
            
        except Exception as e:
            processing_time = time.time() - start_time
            prediction_result = {
                'input_file': input_file,
                'output_file': output_file,
                'success': False,
                'input_rows': 0,
                'output_rows': 0,
                'success_rate': 0,
                'processing_time': processing_time,
                'error_message': str(e)
            }
            
            self.logger.error(f"预测异常: {Path(input_file).name} - {e}")
            return prediction_result
    
    def predict_all_files(self, model_path: str, feature_engineer_path: str, 
                         output_dir: str) -> List[Dict[str, Any]]:
        """对所有分割文件进行预测"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"开始对 {len(self.split_files)} 个文件进行预测")
        
        prediction_results = []
        
        for i, split_file in enumerate(self.split_files):
            # 生成输出文件名
            input_filename = Path(split_file).stem
            output_filename = f"{input_filename}_predictions.csv"
            output_file = output_path / output_filename
            
            # 执行预测
            result = self.predict_single_file(
                split_file, model_path, feature_engineer_path, str(output_file)
            )
            
            prediction_results.append(result)
            
            # 更新性能指标
            if result['success']:
                self.performance_metrics['successful_files'] += 1
            else:
                self.performance_metrics['failed_files'] += 1
            
            self.performance_metrics['individual_times'].append(result['processing_time'])
            self.performance_metrics['success_rates'].append(result['success_rate'])
            self.performance_metrics['total_predictions'] += result['output_rows']
            
            # 进度报告
            progress = (i + 1) / len(self.split_files) * 100
            self.logger.info(f"预测进度: {i+1}/{len(self.split_files)} ({progress:.1f}%)")
        
        self.prediction_results = prediction_results
        self.performance_metrics['total_processing_time'] = sum(self.performance_metrics['individual_times'])
        
        self.logger.info("所有文件预测完成")
        return prediction_results
    
    def merge_predictions(self, output_file: str) -> Dict[str, Any]:
        """合并预测结果"""
        self.logger.info("开始合并预测结果")
        
        successful_results = [r for r in self.prediction_results if r['success']]
        
        if not successful_results:
            self.logger.error("没有成功的预测结果可以合并")
            return {'success': False, 'error': '没有成功的预测结果'}
        
        merged_dfs = []
        total_merged_rows = 0
        
        for result in successful_results:
            if Path(result['output_file']).exists():
                df = pd.read_csv(result['output_file'])
                merged_dfs.append(df)
                total_merged_rows += len(df)
                self.logger.info(f"合并文件: {Path(result['output_file']).name} ({len(df):,} 行)")
        
        if merged_dfs:
            # 合并所有数据框
            final_df = pd.concat(merged_dfs, ignore_index=True)
            
            # 保存合并结果
            final_df.to_csv(output_file, index=False)
            
            merge_result = {
                'success': True,
                'output_file': output_file,
                'total_rows': len(final_df),
                'merged_files': len(merged_dfs),
                'expected_rows': sum(r['input_rows'] for r in successful_results)
            }
            
            self.logger.info(f"合并完成: {len(final_df):,} 行数据保存到 {output_file}")
            
            return merge_result
        else:
            return {'success': False, 'error': '没有有效的预测文件可以合并'}
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        if not self.prediction_results:
            return {'error': '没有预测结果数据'}
        
        successful_files = self.performance_metrics['successful_files']
        total_files = self.performance_metrics['total_files']
        
        report = {
            'file_level_metrics': {
                'total_files': total_files,
                'successful_files': successful_files,
                'failed_files': self.performance_metrics['failed_files'],
                'file_success_rate': successful_files / total_files * 100 if total_files > 0 else 0
            },
            'data_level_metrics': {
                'total_predictions': self.performance_metrics['total_predictions'],
                'average_file_success_rate': np.mean(self.performance_metrics['success_rates']) if self.performance_metrics['success_rates'] else 0,
                'min_file_success_rate': np.min(self.performance_metrics['success_rates']) if self.performance_metrics['success_rates'] else 0,
                'max_file_success_rate': np.max(self.performance_metrics['success_rates']) if self.performance_metrics['success_rates'] else 0
            },
            'performance_metrics': {
                'total_processing_time': self.performance_metrics['total_processing_time'],
                'average_file_processing_time': np.mean(self.performance_metrics['individual_times']) if self.performance_metrics['individual_times'] else 0,
                'processing_speed': self.performance_metrics['total_predictions'] / self.performance_metrics['total_processing_time'] if self.performance_metrics['total_processing_time'] > 0 else 0
            },
            'detailed_results': self.prediction_results
        }
        
        return report

def main():
    """主函数 - 演示文件分割预测策略"""
    import argparse
    
    parser = argparse.ArgumentParser(description="文件分割预测策略")
    parser.add_argument("--input", required=True, help="输入文件路径")
    parser.add_argument("--model", required=True, help="模型文件路径")
    parser.add_argument("--feature-engineer", required=True, help="特征工程器路径")
    parser.add_argument("--output", required=True, help="最终输出文件路径")
    parser.add_argument("--chunk-size", type=int, default=12000, help="分割文件大小")
    parser.add_argument("--work-dir", default="./file_split_work", help="工作目录")
    
    args = parser.parse_args()
    
    # 创建策略实例
    strategy = FileSplitPredictionStrategy(chunk_size=args.chunk_size)
    
    # 创建工作目录
    work_dir = Path(args.work_dir)
    work_dir.mkdir(parents=True, exist_ok=True)
    
    split_dir = work_dir / "split_files"
    prediction_dir = work_dir / "predictions"
    
    try:
        # 1. 分割文件
        print("🔪 步骤1: 分割文件")
        split_files = strategy.split_file(args.input, str(split_dir))
        print(f"✅ 文件分割完成: {len(split_files)} 个文件")
        
        # 2. 预测所有文件
        print("\n🎯 步骤2: 预测所有文件")
        prediction_results = strategy.predict_all_files(
            args.model, args.feature_engineer, str(prediction_dir)
        )
        
        # 3. 合并结果
        print("\n🔗 步骤3: 合并预测结果")
        merge_result = strategy.merge_predictions(args.output)
        
        if merge_result['success']:
            print(f"✅ 合并完成: {merge_result['total_rows']:,} 行")
        else:
            print(f"❌ 合并失败: {merge_result['error']}")
        
        # 4. 生成报告
        print("\n📊 步骤4: 生成性能报告")
        report = strategy.generate_performance_report()
        
        print(f"📈 性能报告:")
        print(f"  - 文件成功率: {report['file_level_metrics']['file_success_rate']:.1f}%")
        print(f"  - 平均数据成功率: {report['data_level_metrics']['average_file_success_rate']:.1f}%")
        print(f"  - 总处理时间: {report['performance_metrics']['total_processing_time']:.2f} 秒")
        print(f"  - 处理速度: {report['performance_metrics']['processing_speed']:.0f} 样本/秒")
        
        # 保存详细报告
        import json
        report_file = work_dir / "performance_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📋 详细报告已保存: {report_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
