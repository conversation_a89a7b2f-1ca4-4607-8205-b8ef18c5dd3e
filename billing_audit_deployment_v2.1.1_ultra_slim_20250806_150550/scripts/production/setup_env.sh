#!/bin/bash
# 环境设置脚本
# 设置OpenMP库路径以支持XGBoost和LightGBM

# 激活虚拟环境
source venv/bin/activate

# 设置OpenMP库路径
export DYLD_LIBRARY_PATH="/opt/homebrew/opt/libomp/lib:$DYLD_LIBRARY_PATH"

# 设置编译器标志（如果需要重新编译包）
export LDFLAGS="-L/opt/homebrew/opt/libomp/lib"
export CPPFLAGS="-I/opt/homebrew/opt/libomp/include"

echo "✅ 环境设置完成:"
echo "  - 虚拟环境: $(which python)"
echo "  - OpenMP路径: /opt/homebrew/opt/libomp/lib"
echo "  - DYLD_LIBRARY_PATH: $DYLD_LIBRARY_PATH"

# 测试XGBoost和LightGBM
echo ""
echo "🧪 测试机器学习库..."

# 测试XGBoost
if python -c "import xgboost; print('XGBoost version:', xgboost.__version__)" 2>/dev/null; then
    echo "✅ XGBoost 可用"
else
    echo "❌ XGBoost 不可用"
fi

# 测试LightGBM
if python -c "import lightgbm; print('LightGBM version:', lightgbm.__version__)" 2>/dev/null; then
    echo "✅ LightGBM 可用"
else
    echo "❌ LightGBM 不可用"
fi

# 测试RandomForest (sklearn)
if python -c "from sklearn.ensemble import RandomForestRegressor; print('RandomForest 可用')" 2>/dev/null; then
    echo "✅ RandomForest 可用"
else
    echo "❌ RandomForest 不可用"
fi

echo ""
echo "🎉 环境设置完成！现在可以运行收费稽核AI系统了。"
