#!/bin/bash
# 山西电信出账稽核AI系统环境变量设置
# 生成时间: 2025年 7月26日 星期六 08时28分36秒 CST

export PYTHONPATH="/app"
export BILLING_AUDIT_CONFIG="/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json"
export BILLING_AUDIT_ENV="production"
export DATA_INPUT_DIR="/data/input"
export DATA_OUTPUT_DIR="/data/output"
export MODEL_DIR="/models"
export LOGS_DIR="/logs"
export DB_PASSWORD="your_db_password_here"
export OMP_NUM_THREADS="4"

echo "环境变量设置完成"
echo "配置文件: $BILLING_AUDIT_CONFIG"
echo "Python路径: $PYTHONPATH"
