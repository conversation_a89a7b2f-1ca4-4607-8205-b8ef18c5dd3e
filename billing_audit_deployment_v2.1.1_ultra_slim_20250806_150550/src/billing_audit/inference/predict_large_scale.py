#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模数据预测脚本
专门针对千万级数据规模优化的预测脚本
支持CSV/TXT文件，分批处理，内存优化
"""

import sys
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import argparse
from datetime import datetime
import gc
import warnings
warnings.filterwarnings('ignore')

# 进度条
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 导入大规模特征工程器和配置管理器
from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer
from src.config.production_config_manager import get_config_manager
from src.utils.logger import get_logger

# 导入分层模型
from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel


class LargeScalePrediction:
    """大规模数据预测器 - 增强版（短期改进方案）"""

    def __init__(self, model_path, feature_engineer_path, batch_size=None):
        # 使用生产配置管理器
        self.config_manager = get_config_manager()
        # 短期改进：默认批次大小改为250
        self.batch_size = batch_size or 250
        self.model = None
        self.feature_engineer = None

        # 初始化日志器
        self.logger = get_logger('large_scale_prediction')

        # 短期改进：状态管理相关属性
        self.batch_count = 0
        self.state_check_interval = 5  # 每5个批次检查状态
        self.regressor_failures = 0
        self.classifier_failures = 0
        self.state_restorations = 0
        self.regressor_state_backup = {}

        # 加载模型和特征工程器
        self.load_model_and_feature_engineer(model_path, feature_engineer_path)

        # 短期改进：备份初始状态
        self._backup_regressor_state()

    def _backup_regressor_state(self):
        """短期改进：备份非零值回归器状态"""
        if hasattr(self.model, 'nonzero_regressor') and self.model.nonzero_regressor is not None:
            critical_attrs = ['_n_classes', '_objective', '_n_features', 'n_features_in_']

            for attr in critical_attrs:
                if hasattr(self.model.nonzero_regressor, attr):
                    value = getattr(self.model.nonzero_regressor, attr)
                    self.regressor_state_backup[attr] = value

            # 确保_n_classes为正确值
            if '_n_classes' not in self.regressor_state_backup or self.regressor_state_backup['_n_classes'] is None:
                self.regressor_state_backup['_n_classes'] = 1  # 回归任务默认为1

            self.logger.info(f"回归器状态备份完成: {self.regressor_state_backup}")

    def _restore_regressor_state(self):
        """短期改进：恢复非零值回归器状态"""
        if not hasattr(self.model, 'nonzero_regressor') or self.model.nonzero_regressor is None:
            return False

        restored_count = 0
        for attr, value in self.regressor_state_backup.items():
            if not hasattr(self.model.nonzero_regressor, attr) or getattr(self.model.nonzero_regressor, attr) is None:
                setattr(self.model.nonzero_regressor, attr, value)
                restored_count += 1

        if restored_count > 0:
            self.state_restorations += 1
            self.logger.info(f"恢复回归器状态: {restored_count} 个属性")

        return restored_count > 0

    def _aggressive_state_reset(self):
        """短期改进：激进的状态重置机制"""
        if not hasattr(self.model, 'nonzero_regressor') or self.model.nonzero_regressor is None:
            return False

        try:
            # 强制设置关键状态
            self.model.nonzero_regressor._n_classes = 1
            if hasattr(self.model.nonzero_regressor, '_objective'):
                self.model.nonzero_regressor._objective = 'regression'

            # 内存清理
            import gc
            gc.collect()

            self.logger.info("执行激进状态重置")
            return True

        except Exception as e:
            self.logger.error(f"激进状态重置失败: {e}")
            return False

    def load_model_and_feature_engineer(self, model_path, feature_engineer_path):
        """加载模型和特征工程器"""
        self.logger.info(f"开始加载模型和特征工程器: 模型={model_path}, 特征工程器={feature_engineer_path}")
        print(f"加载模型和特征工程器...")

        try:
            # 尝试加载分层模型
            if str(model_path).find('hierarchical') != -1:
                self.model = HierarchicalBillingModel.load(model_path)
                self.logger.info(f"分层模型加载成功: {type(self.model).__name__}")
                print(f"  分层模型加载成功: {type(self.model).__name__}")
            else:
                # 加载传统模型
                self.model = joblib.load(model_path)
                self.logger.info(f"传统模型加载成功: {type(self.model).__name__}")
                print(f"  传统模型加载成功: {type(self.model).__name__}")

                # 显示特征数（仅对传统模型）
                if hasattr(self.model, 'n_features_in_'):
                    print(f"  模型特征数: {self.model.n_features_in_}")

            # 加载特征工程器
            self.feature_engineer = LargeScaleFeatureEngineer.load_preprocessor(feature_engineer_path)
            print(f"  特征工程器加载成功")

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            print(f"模型加载失败: {e}")
            raise
    
    def read_large_csv(self, file_path, chunksize=None):
        """分批读取大型CSV文件"""
        if chunksize is None:
            chunksize = self.batch_size
            
        print(f"开始分批读取数据文件: {file_path}")
        print(f"  - 批次大小: {chunksize:,} 行")
        
        try:
            # 检测文件分隔符
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline()
                if ',' in first_line:
                    sep = ','
                elif '\t' in first_line:
                    sep = '\t'
                else:
                    sep = ','
            
            print(f"  - 检测到分隔符: '{sep}'")
            
            # 分批读取
            chunk_reader = pd.read_csv(
                file_path, 
                sep=sep,
                chunksize=chunksize,
                encoding='utf-8',
                low_memory=False
            )
            
            return chunk_reader, sep
            
        except Exception as e:
            print(f"文件读取失败: {e}")
            raise
    
    def preprocess_chunk(self, chunk):
        """预处理数据块"""
        try:
            # 提取特征
            feature_columns = self.feature_engineer.feature_columns
            X = chunk[feature_columns].copy()

            # 处理缺失值
            X = X.fillna(0)

            # 应用特征工程
            X_processed = self.feature_engineer.transform_chunk(X)

            return X_processed

        except Exception as e:
            print(f"数据预处理失败: {e}")
            raise
    
    def predict_chunk(self, X_chunk):
        """预测数据块 - 增强版（短期改进方案）"""
        self.batch_count += 1

        # 调试信息：记录每个批次
        self.logger.info(f"处理批次 {self.batch_count}, 状态检查间隔: {self.state_check_interval}")

        try:
            # 短期改进：每5个批次进行状态检查
            if self.batch_count % self.state_check_interval == 0:
                self.logger.info(f"批次 {self.batch_count}: 开始状态检查")
                self._restore_regressor_state()
                import gc
                gc.collect()
                self.logger.info(f"批次 {self.batch_count}: 状态检查完成")

            # 尝试预测
            predictions = self.model.predict(X_chunk)
            return predictions

        except Exception as e:
            self.logger.error(f"批次 {self.batch_count} 预测失败: {e}")

            # 短期改进：尝试状态恢复
            if self._restore_regressor_state():
                try:
                    predictions = self.model.predict(X_chunk)
                    self.logger.info(f"批次 {self.batch_count}: 状态恢复后预测成功")
                    return predictions
                except Exception as e2:
                    self.logger.error(f"批次 {self.batch_count}: 状态恢复后仍然失败: {e2}")

            # 短期改进：尝试激进状态重置
            if self._aggressive_state_reset():
                try:
                    predictions = self.model.predict(X_chunk)
                    self.logger.info(f"批次 {self.batch_count}: 激进重置后预测成功")
                    return predictions
                except Exception as e3:
                    self.logger.error(f"批次 {self.batch_count}: 激进重置后仍然失败: {e3}")

            # 短期改进：零值安全预测策略
            self.logger.warning(f"批次 {self.batch_count}: 使用零值安全预测")
            return np.zeros(len(X_chunk))  # 返回零值作为安全预测
    
    def predict_large_file(self, input_file, output_file, include_features=False):
        """预测大型文件"""
        self.logger.info(f"开始大规模数据预测: 输入={input_file}, 输出={output_file}")
        print(f"🔮 开始大规模数据预测")
        print(f"  输入文件: {input_file}")
        print(f"  输出文件: {output_file}")
        print("=" * 60)
        
        # 读取数据
        chunk_reader, _ = self.read_large_csv(input_file, self.batch_size)
        
        # 准备输出文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        chunk_count = 0
        total_predictions = 0
        all_predictions = []
        
        # 如果需要包含特征，准备存储原始数据
        if include_features:
            all_original_data = []
        
        start_time = datetime.now()

        # 创建进度条（无法预知总批次数，使用无限进度条）
        pbar = tqdm(desc="🔮 批量预测", unit="批次")

        for chunk in chunk_reader:
            chunk_count += 1
            self.logger.info(f"开始处理第 {chunk_count} 批数据，样本数: {len(chunk)}")

            try:
                # 预处理
                X_chunk = self.preprocess_chunk(chunk)
            except Exception as e:
                self.logger.error(f"第 {chunk_count} 批预处理失败: {e}")
                print(f"预处理失败: {e}")

                # 对于预处理失败的批次，创建零值预测作为占位符
                failed_predictions = np.zeros(len(chunk))
                all_predictions.extend(failed_predictions)

                if include_features:
                    all_original_data.append(chunk)

                total_predictions += len(failed_predictions)

                pbar.set_postfix({
                    '批次': chunk_count,
                    '当前': f"{len(failed_predictions):,}(预处理失败)",
                    '累计': f"{total_predictions:,}",
                    '预测范围': "0.0-0.0(占位符)"
                })
                pbar.update(1)
                continue

            # 预测（不使用try-catch，让predict_chunk内部的错误处理逻辑正常工作）
            predictions = self.predict_chunk(X_chunk)

            # 存储结果
            all_predictions.extend(predictions)

            if include_features:
                all_original_data.append(chunk)

            total_predictions += len(predictions)

            self.logger.info(f"第 {chunk_count} 批预测完成: {len(predictions):,} 条，累计: {total_predictions:,} 条")

            # 更新进度条
            pbar.set_postfix({
                '批次': chunk_count,
                '当前': f"{len(predictions):,}",
                '累计': f"{total_predictions:,}",
                '预测范围': f"{predictions.min():.1f}-{predictions.max():.1f}"
            })
            pbar.update(1)

            # 内存管理：每处理一定数量的批次就保存一次
            if chunk_count % 20 == 0:
                self.logger.info(f"执行中间保存，已处理 {chunk_count} 批次")
                print(f"  中间保存...")
                self._save_intermediate_results(
                    all_predictions, all_original_data if include_features else None,
                    output_file, chunk_count, include_features
                )

                # 清理内存
                if include_features:
                    all_original_data = []
                gc.collect()
                continue

        # 关闭进度条
        pbar.close()

        # 保存最终结果
        print(f"\n保存最终预测结果...")
        self._save_final_results(
            all_predictions, all_original_data if include_features else None,
            output_file, include_features
        )
        
        total_time = (datetime.now() - start_time).total_seconds()

        # 短期改进：详细性能统计
        success_rate = (total_predictions / (chunk_count * self.batch_size)) * 100 if chunk_count > 0 else 0

        self.logger.info(f"预测完成: 总数={total_predictions:,}, 耗时={total_time:.2f}秒, 速度={total_predictions/total_time:.0f}条/秒")
        self.logger.info(f"短期改进统计: 批次数={chunk_count}, 状态恢复={self.state_restorations}次, 成功率={success_rate:.1f}%")

        print(f"\n🎉 预测完成！")
        print(f"  总预测数: {total_predictions:,} 条")
        print(f"  总批次数: {chunk_count} 批次")
        print(f"  批次大小: {self.batch_size} 行/批次")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  预测速度: {total_predictions/total_time:.0f} 条/秒")
        print(f"  数据成功率: {success_rate:.1f}%")
        print(f"  状态恢复次数: {self.state_restorations} 次")
        print(f"  结果文件: {output_file}")

        # 返回性能统计
        return {
            'total_predictions': total_predictions,
            'total_batches': chunk_count,
            'batch_size': self.batch_size,
            'total_time': total_time,
            'processing_speed': total_predictions/total_time if total_time > 0 else 0,
            'success_rate': success_rate,
            'state_restorations': self.state_restorations,
            'output_file': output_file
        }
    
    def _save_intermediate_results(self, predictions, original_data, output_file, chunk_count, include_features):
        """保存中间结果"""
        intermediate_file = str(output_file).replace('.csv', f'_intermediate_{chunk_count}.csv')

        if include_features and original_data:
            # 合并原始数据，按照指定顺序
            combined_data = pd.concat(original_data, ignore_index=True)

            # 获取字段配置
            from src.config.production_config_manager import get_config_manager
            config = get_config_manager()
            training_features = config.get_training_features()
            target_column = config.get_target_column()
            passthrough_columns = config.get_passthrough_columns()

            # 按照指定顺序重新排列列
            ordered_columns = training_features + passthrough_columns + [target_column]
            available_columns = [col for col in ordered_columns if col in combined_data.columns]
            result_data = combined_data[available_columns].copy()

            # 添加预测结果
            result_data['prediction'] = predictions[:len(result_data)]
            result_data.to_csv(intermediate_file, index=False)
        else:
            # 只保存预测结果
            pd.DataFrame({'prediction': predictions}).to_csv(intermediate_file, index=False)

        self.logger.info(f"中间结果已保存: {intermediate_file}")
        print(f"    中间结果已保存: {intermediate_file}")

    def _save_final_results(self, predictions, original_data, output_file, include_features):
        """保存最终结果"""
        if include_features and original_data:
            # 合并原始数据和预测结果，按照指定顺序：训练特征+透传字段+目标字段+预测字段
            combined_data = pd.concat(original_data, ignore_index=True)

            # 获取字段配置
            from src.config.production_config_manager import get_config_manager
            config = get_config_manager()
            training_features = config.get_training_features()
            target_column = config.get_target_column()
            passthrough_columns = config.get_passthrough_columns()

            # 按照指定顺序重新排列列
            ordered_columns = training_features + passthrough_columns + [target_column]

            # 确保所有列都存在
            available_columns = [col for col in ordered_columns if col in combined_data.columns]
            result_data = combined_data[available_columns].copy()

            # 添加预测结果
            result_data['prediction'] = predictions[:len(result_data)]

            # 保存结果
            result_data.to_csv(output_file, index=False)
            self.logger.info(f"最终结果已保存(包含特征): {output_file}, 记录数: {len(result_data)}")
            self.logger.info(f"列顺序: 训练特征({len(training_features)}) + 透传字段({len(passthrough_columns)}) + 目标字段(1) + 预测字段(1)")
        else:
            # 只保存预测结果
            result_df = pd.DataFrame({
                'row_id': range(len(predictions)),
                'prediction': predictions
            })
            result_df.to_csv(output_file, index=False)
            self.logger.info(f"最终结果已保存(仅预测): {output_file}, 记录数: {len(result_df)}")

        print(f"  最终结果已保存: {output_file}")


def predict_with_hierarchical_model(input_file, model_path, feature_engineer_path, output_file,
                                   batch_size=250, include_features=False):
    """使用分层模型进行预测"""
    logger = get_logger('hierarchical_prediction')

    logger.info("=" * 60)
    logger.info("开始分层模型预测")
    logger.info("=" * 60)

    start_time = datetime.now()

    # 初始化预测器
    logger.info(f"创建预测器，批次大小参数: {batch_size}")
    predictor = LargeScalePrediction(model_path, feature_engineer_path, batch_size)
    logger.info(f"预测器实际批次大小: {predictor.batch_size}")

    # 检查是否为分层模型
    if not isinstance(predictor.model, HierarchicalBillingModel):
        logger.warning("模型不是分层模型，将使用传统预测方式")
        return predict_with_statistics(input_file, model_path, feature_engineer_path, output_file, batch_size, include_features)

    logger.info(f"输入文件: {input_file}")
    logger.info(f"分层模型: {model_path}")
    logger.info(f"输出文件: {output_file}")

    # 执行预测
    prediction_result = predictor.predict_large_file(input_file, output_file, include_features)

    # 从输出文件读取预测结果进行统计分析
    predictions_df = pd.read_csv(output_file)

    # 计算统计信息
    total_samples = len(predictions_df)
    # 检查列名：可能是 'prediction' 或 'predicted_amount'
    pred_column = 'predicted_amount' if 'predicted_amount' in predictions_df.columns else 'prediction'
    zero_predictions = (predictions_df[pred_column] == 0).sum()
    nonzero_predictions = total_samples - zero_predictions
    zero_ratio = zero_predictions / total_samples * 100

    # 非零值统计
    nonzero_values = predictions_df[predictions_df[pred_column] > 0][pred_column]
    if len(nonzero_values) > 0:
        nonzero_stats = {
            'count': len(nonzero_values),
            'mean': float(nonzero_values.mean()),
            'median': float(nonzero_values.median()),
            'std': float(nonzero_values.std()),
            'min': float(nonzero_values.min()),
            'max': float(nonzero_values.max())
        }
    else:
        nonzero_stats = {
            'count': 0,
            'mean': 0.0,
            'median': 0.0,
            'std': 0.0,
            'min': 0.0,
            'max': 0.0
        }

    # 处理时间
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    processing_speed = total_samples / processing_time if processing_time > 0 else 0

    # 统计结果
    stats = {
        'total_samples': total_samples,
        'zero_predictions': int(zero_predictions),
        'nonzero_predictions': int(nonzero_predictions),
        'zero_ratio': float(zero_ratio),
        'nonzero_stats': nonzero_stats,
        'processing_time': processing_time,
        'processing_speed': processing_speed,
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat()
    }

    logger.info(f"分层预测完成:")
    logger.info(f"  总样本数: {total_samples:,}")
    logger.info(f"  零值预测: {zero_predictions:,} ({zero_ratio:.1f}%)")
    logger.info(f"  非零值预测: {nonzero_predictions:,}")
    logger.info(f"  非零值均值: {nonzero_stats['mean']:.2f}元")
    logger.info(f"  处理时间: {processing_time:.2f}秒")
    logger.info(f"  处理速度: {processing_speed:,.0f}条/秒")

    print(f"\n🎉 分层模型预测完成！")
    print(f"总样本数: {total_samples:,}")
    print(f"零值预测: {zero_predictions:,} ({zero_ratio:.1f}%)")
    print(f"非零值预测: {nonzero_predictions:,}")
    print(f"非零值统计: 均值={nonzero_stats['mean']:.2f}元, 中位数={nonzero_stats['median']:.2f}元")
    print(f"处理时间: {processing_time:.2f}秒")
    print(f"处理速度: {processing_speed:,.0f}条/秒")

    return stats


def predict_with_statistics(input_file, model_path, feature_engineer_path, output_file,
                          batch_size=250, include_features=False):
    """带统计信息的预测（传统模型）"""
    
    # 初始化预测器
    predictor = LargeScalePrediction(model_path, feature_engineer_path, batch_size)
    
    # 执行预测
    result = predictor.predict_large_file(input_file, output_file, include_features)
    
    # 生成统计报告
    print(f"\n预测统计报告:")
    print(f"  - 输入文件: {input_file}")
    print(f"  - 输出文件: {output_file}")
    print(f"  - 预测总数: {result['total_predictions']:,} 条")
    print(f"  - 处理时间: {result['total_time']:.2f} 秒")
    print(f"  - 处理速度: {result['processing_speed']:.0f} 条/秒")
    print(f"  - 批次大小: {batch_size:,} 条")
    
    # 读取并分析预测结果
    try:
        predictions_df = pd.read_csv(output_file)
        if 'prediction' in predictions_df.columns:
            predictions = predictions_df['prediction']
            
            print(f"\n预测结果分析:")
            print(f"  - 最小值: {predictions.min():.2f}")
            print(f"  - 最大值: {predictions.max():.2f}")
            print(f"  - 平均值: {predictions.mean():.2f}")
            print(f"  - 中位数: {predictions.median():.2f}")
            print(f"  - 标准差: {predictions.std():.2f}")
            print(f"  - 零值数量: {(predictions == 0).sum():,} ({(predictions == 0).mean()*100:.1f}%)")
    
    except Exception as e:
        print(f"无法分析预测结果: {e}")
    
    return result


def main():
    """主函数"""
    import logging
    logger = logging.getLogger(__name__)

    parser = argparse.ArgumentParser(description='大规模数据预测')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径 (CSV/TXT)')
    parser.add_argument('--model', '-m', required=True, help='模型文件路径 (.pkl)')
    parser.add_argument('--feature-engineer', '-f', required=True, help='特征工程器文件路径 (.pkl)')
    parser.add_argument('--output', '-o', required=True, help='输出预测结果文件路径 (.csv)')
    parser.add_argument('--batch-size', '-b', type=int, default=250, help='批处理大小（短期改进：默认250行）')
    parser.add_argument('--include-features', action='store_true', help='在输出中包含原始特征')

    args = parser.parse_args()

    try:
        logger.info(f"开始大规模预测任务: 输入={args.input}, 输出={args.output}, 批次大小={args.batch_size}")

        # 智能选择预测函数：检测是否为分层模型
        if str(args.model).find('hierarchical') != -1:
            logger.info("检测到分层模型，使用分层预测函数")
            result = predict_with_hierarchical_model(
                input_file=args.input,
                model_path=args.model,
                feature_engineer_path=args.feature_engineer,
                output_file=args.output,
                batch_size=args.batch_size,
                include_features=args.include_features
            )
        else:
            logger.info("检测到传统模型，使用传统预测函数")
            result = predict_with_statistics(
                input_file=args.input,
                model_path=args.model,
                feature_engineer_path=args.feature_engineer,
                output_file=args.output,
                batch_size=args.batch_size,
                include_features=args.include_features
            )

        logger.info(f"大规模预测任务完成")
        print(f"\n大规模预测完成！")
        return True

    except Exception as e:
        logger.error(f"预测失败: {e}")
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
