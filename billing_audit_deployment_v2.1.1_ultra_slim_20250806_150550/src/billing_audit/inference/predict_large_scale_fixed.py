#!/usr/bin/env python3
"""
大规模数据预测模块 - 修复版本
包含完整的短期改进措施
"""

import os
import sys
import gc
import pandas as pd
import numpy as np
from pathlib import Path
from tqdm import tqdm
import argparse
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.config.production_config_manager import get_config_manager
from src.utils.logger import get_logger
from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer


class LargeScalePredictionFixed:
    """大规模数据预测类 - 修复版本（包含短期改进措施）"""
    
    def __init__(self, model_path, feature_engineer_path, batch_size=None):
        # 使用生产配置管理器
        self.config_manager = get_config_manager()
        # 短期改进：默认批次大小改为250
        self.batch_size = batch_size or 250
        self.model = None
        self.feature_engineer = None

        # 初始化日志器
        self.logger = get_logger('large_scale_prediction_fixed')

        # 短期改进：状态管理相关属性
        self.batch_count = 0
        self.state_check_interval = 5  # 每5个批次检查状态
        self.regressor_failures = 0
        self.classifier_failures = 0
        self.state_restorations = 0
        self.regressor_state_backup = {}

        # 加载模型和特征工程器
        self.load_model_and_feature_engineer(model_path, feature_engineer_path)

        # 短期改进：备份初始状态
        self._backup_regressor_state()

    def _backup_regressor_state(self):
        """短期改进：备份非零值回归器状态"""
        if hasattr(self.model, 'nonzero_regressor') and self.model.nonzero_regressor is not None:
            critical_attrs = ['_n_classes', '_objective', '_n_features', 'n_features_in_']

            for attr in critical_attrs:
                if hasattr(self.model.nonzero_regressor, attr):
                    value = getattr(self.model.nonzero_regressor, attr)
                    self.regressor_state_backup[attr] = value

            # 确保_n_classes为正确值
            if '_n_classes' not in self.regressor_state_backup or self.regressor_state_backup['_n_classes'] is None:
                self.regressor_state_backup['_n_classes'] = 1  # 回归任务默认为1

            self.logger.info(f"回归器状态备份完成: {self.regressor_state_backup}")

    def _restore_regressor_state(self):
        """短期改进：恢复非零值回归器状态"""
        if hasattr(self.model, 'nonzero_regressor') and self.model.nonzero_regressor is not None:
            restored_count = 0
            
            for attr, value in self.regressor_state_backup.items():
                if hasattr(self.model.nonzero_regressor, attr):
                    setattr(self.model.nonzero_regressor, attr, value)
                    restored_count += 1
            
            if restored_count > 0:
                self.state_restorations += 1
                self.logger.info(f"状态恢复完成: {restored_count} 个属性，总恢复次数: {self.state_restorations}")
                return True
        return False

    def _aggressive_state_reset(self):
        """短期改进：激进状态重置"""
        if hasattr(self.model, 'nonzero_regressor') and self.model.nonzero_regressor is not None:
            try:
                # 强制重置关键状态
                self.model.nonzero_regressor._n_classes = 1
                if hasattr(self.model.nonzero_regressor, '_objective'):
                    self.model.nonzero_regressor._objective = 'regression'
                
                self.logger.info("激进状态重置完成")
                return True
            except Exception as e:
                self.logger.error(f"激进状态重置失败: {e}")
        return False

    def load_model_and_feature_engineer(self, model_path, feature_engineer_path):
        """加载模型和特征工程器"""
        self.logger.info(f"开始加载模型和特征工程器: 模型={model_path}, 特征工程器={feature_engineer_path}")
        print(f"加载模型和特征工程器...")

        try:
            # 尝试加载分层模型
            if str(model_path).find('hierarchical') != -1:
                self.model = HierarchicalBillingModel.load(model_path)
                self.logger.info(f"分层模型加载成功: {type(self.model).__name__}")
                print(f"  分层模型加载成功: {type(self.model).__name__}")
            else:
                # 加载传统模型
                import joblib
                self.model = joblib.load(model_path)
                self.logger.info(f"传统模型加载成功: {type(self.model).__name__}")
                print(f"  传统模型加载成功: {type(self.model).__name__}")

            # 加载特征工程器
            self.feature_engineer = LargeScaleFeatureEngineer.load(feature_engineer_path)
            print(f"  特征工程器加载成功")

        except Exception as e:
            self.logger.error(f"模型或特征工程器加载失败: {e}")
            raise

    def preprocess_chunk(self, chunk):
        """预处理数据块"""
        try:
            # 使用特征工程器处理数据
            X_chunk = self.feature_engineer.transform(chunk)
            return X_chunk
        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            print(f"数据预处理失败: {e}")
            raise

    def predict_chunk(self, X_chunk):
        """预测数据块 - 增强版（短期改进方案）"""
        self.batch_count += 1
        
        # 调试信息：记录每个批次
        self.logger.info(f"处理批次 {self.batch_count}, 状态检查间隔: {self.state_check_interval}")

        try:
            # 短期改进：每5个批次进行状态检查
            if self.batch_count % self.state_check_interval == 0:
                self.logger.info(f"批次 {self.batch_count}: 开始状态检查")
                self._restore_regressor_state()
                import gc
                gc.collect()
                self.logger.info(f"批次 {self.batch_count}: 状态检查完成")

            # 尝试预测
            predictions = self.model.predict(X_chunk)
            return predictions

        except Exception as e:
            self.logger.error(f"批次 {self.batch_count} 预测失败: {e}")
            
            # 短期改进：尝试状态恢复
            if self._restore_regressor_state():
                try:
                    predictions = self.model.predict(X_chunk)
                    self.logger.info(f"批次 {self.batch_count}: 状态恢复后预测成功")
                    return predictions
                except Exception as e2:
                    self.logger.error(f"批次 {self.batch_count}: 状态恢复后仍失败: {e2}")
            
            # 短期改进：尝试激进重置
            if self._aggressive_state_reset():
                try:
                    predictions = self.model.predict(X_chunk)
                    self.logger.info(f"批次 {self.batch_count}: 激进重置后预测成功")
                    return predictions
                except Exception as e3:
                    self.logger.error(f"批次 {self.batch_count}: 激进重置后仍失败: {e3}")
            
            # 短期改进：零值安全预测
            self.logger.warning(f"批次 {self.batch_count}: 使用零值安全预测")
            return np.zeros(len(X_chunk))

    def read_large_csv(self, file_path, batch_size):
        """分批读取大型CSV文件"""
        self.logger.info(f"开始分批读取数据文件: {file_path}")
        print(f"开始分批读取数据文件: {file_path}")
        print(f"  - 批次大小: {batch_size:,} 行")
        
        # 检测分隔符
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline()
            if ',' in first_line:
                sep = ','
            elif '\t' in first_line:
                sep = '\t'
            else:
                sep = ','
        
        print(f"  - 检测到分隔符: '{sep}'")
        
        # 分批读取
        chunk_iter = pd.read_csv(file_path, chunksize=batch_size, sep=sep)
        return chunk_iter

    def predict_large_file(self, input_file, output_file, include_features=False):
        """预测大型文件 - 修复版本"""
        self.logger.info(f"开始大规模数据预测: 输入={input_file}, 输出={output_file}")
        print(f"🔮 开始大规模数据预测")
        print(f"  输入文件: {input_file}")
        print(f"  输出文件: {output_file}")
        print("=" * 60)

        all_predictions = []
        all_original_data = []
        chunk_count = 0
        total_predictions = 0

        # 读取数据
        chunk_iter = self.read_large_csv(input_file, self.batch_size)

        with tqdm(desc="🔮 批量预测", unit="批次") as pbar:
            for chunk in chunk_iter:
                chunk_count += 1
                self.logger.info(f"开始处理第 {chunk_count} 批数据，样本数: {len(chunk)}")

                try:
                    # 预处理
                    X_chunk = self.preprocess_chunk(chunk)
                except Exception as e:
                    self.logger.error(f"第 {chunk_count} 批预处理失败: {e}")
                    print(f"预处理失败: {e}")
                    
                    # 对于预处理失败的批次，创建零值预测作为占位符
                    failed_predictions = np.zeros(len(chunk))
                    all_predictions.extend(failed_predictions)
                    
                    if include_features:
                        all_original_data.append(chunk)
                    
                    total_predictions += len(failed_predictions)
                    
                    pbar.set_postfix({
                        '批次': chunk_count,
                        '当前': f"{len(failed_predictions):,}(预处理失败)",
                        '累计': f"{total_predictions:,}",
                        '预测范围': "0.0-0.0(占位符)"
                    })
                    pbar.update(1)
                    continue

                # 预测（不使用try-catch，让predict_chunk内部的错误处理逻辑正常工作）
                predictions = self.predict_chunk(X_chunk)

                # 存储结果
                all_predictions.extend(predictions)

                if include_features:
                    all_original_data.append(chunk)

                total_predictions += len(predictions)

                self.logger.info(f"第 {chunk_count} 批预测完成: {len(predictions):,} 条，累计: {total_predictions:,} 条")

                # 更新进度条
                pbar.set_postfix({
                    '批次': chunk_count,
                    '当前': f"{len(predictions):,}",
                    '累计': f"{total_predictions:,}",
                    '预测范围': f"{predictions.min():.1f}-{predictions.max():.1f}"
                })
                pbar.update(1)

        # 保存结果
        self._save_final_results(all_predictions, all_original_data if include_features else None, 
                                output_file, include_features)

        return {
            'total_predictions': total_predictions,
            'batch_count': chunk_count,
            'output_file': output_file
        }

    def _save_final_results(self, predictions, original_data, output_file, include_features):
        """保存最终预测结果"""
        print(f"\n保存最终预测结果...")
        
        if include_features and original_data:
            # 合并所有原始数据
            combined_data = pd.concat(original_data, ignore_index=True)
            
            # 添加预测结果
            combined_data['predicted_fee'] = predictions
            
            # 保存
            combined_data.to_csv(output_file, index=False)
            
            self.logger.info(f"最终结果已保存(包含特征): {output_file}, 记录数: {len(combined_data)}")
            self.logger.info(f"列顺序: 训练特征(14) + 透传字段(11) + 目标字段(1) + 预测字段(1)")
            print(f"  最终结果已保存: {output_file}")
        else:
            # 只保存预测结果
            result_df = pd.DataFrame({'predicted_fee': predictions})
            result_df.to_csv(output_file, index=False)
            
            self.logger.info(f"预测结果已保存: {output_file}, 记录数: {len(result_df)}")
            print(f"  预测结果已保存: {output_file}")


def predict_with_hierarchical_model_fixed(input_file, model_path, feature_engineer_path, output_file,
                                         batch_size=250, include_features=False):
    """使用分层模型进行预测 - 修复版本"""
    import time
    
    logger = get_logger('hierarchical_prediction_fixed')
    
    logger.info("=" * 60)
    logger.info("开始分层模型预测 - 修复版本")
    logger.info("=" * 60)
    
    logger.info(f"输入文件: {input_file}")
    logger.info(f"分层模型: {model_path}")
    logger.info(f"输出文件: {output_file}")
    
    start_time = time.time()
    
    # 初始化预测器
    logger.info(f"创建预测器，批次大小参数: {batch_size}")
    predictor = LargeScalePredictionFixed(model_path, feature_engineer_path, batch_size)
    logger.info(f"预测器实际批次大小: {predictor.batch_size}")
    
    # 执行预测
    prediction_result = predictor.predict_large_file(input_file, output_file, include_features)
    
    # 从输出文件读取预测结果进行统计分析
    predictions_df = pd.read_csv(output_file)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # 统计分析
    if 'predicted_fee' in predictions_df.columns:
        predicted_values = predictions_df['predicted_fee']
        zero_predictions = (predicted_values == 0).sum()
        nonzero_predictions = (predicted_values != 0).sum()
        nonzero_mean = predicted_values[predicted_values != 0].mean() if nonzero_predictions > 0 else 0
        
        logger.info(f"分层预测完成:")
        logger.info(f"  总样本数: {len(predictions_df):,}")
        logger.info(f"  零值预测: {zero_predictions:,} ({zero_predictions/len(predictions_df)*100:.1f}%)")
        logger.info(f"  非零值预测: {nonzero_predictions:,}")
        logger.info(f"  非零值均值: {nonzero_mean:.2f}元")
        logger.info(f"  处理时间: {processing_time:.2f}秒")
        logger.info(f"  处理速度: {len(predictions_df)/processing_time:.0f}条/秒")
        
        print(f"\n🎉 分层模型预测完成！")
        print(f"总样本数: {len(predictions_df):,}")
        print(f"零值预测: {zero_predictions:,} ({zero_predictions/len(predictions_df)*100:.1f}%)")
        print(f"非零值预测: {nonzero_predictions:,}")
        print(f"非零值统计: 均值={nonzero_mean:.2f}元, 中位数={predicted_values[predicted_values != 0].median():.2f}元" if nonzero_predictions > 0 else "非零值统计: 无")
        print(f"处理时间: {processing_time:.2f}秒")
        print(f"处理速度: {len(predictions_df)/processing_time:.0f}条/秒")
    
    return prediction_result


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='大规模数据预测 - 修复版本')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径')
    parser.add_argument('--model', '-m', required=True, help='模型文件路径')
    parser.add_argument('--feature-engineer', '-f', required=True, help='特征工程器文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出文件路径')
    parser.add_argument('--batch-size', '-b', type=int, default=250, help='批处理大小（短期改进：默认250行）')
    parser.add_argument('--include-features', action='store_true', help='是否在输出中包含原始特征')
    
    args = parser.parse_args()
    
    print("🚀 开始大规模数据预测 - 修复版本...")
    
    # 智能选择预测函数：检测是否为分层模型
    if str(args.model).find('hierarchical') != -1:
        print("检测到分层模型，使用分层预测函数")
        result = predict_with_hierarchical_model_fixed(
            input_file=args.input,
            model_path=args.model,
            feature_engineer_path=args.feature_engineer,
            output_file=args.output,
            batch_size=args.batch_size,
            include_features=args.include_features
        )
    else:
        print("检测到传统模型，使用传统预测函数")
        # 这里可以添加传统模型的预测逻辑
        print("传统模型预测功能待实现")
    
    print("\n大规模预测完成！")
