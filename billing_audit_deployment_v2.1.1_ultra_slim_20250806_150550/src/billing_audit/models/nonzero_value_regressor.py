#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
非零值回归器优化组件
专门用于优化非零值金额预测的回归器，包括特征工程、参数调优和性能优化
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils.logger import get_logger


class NonzeroValueRegressorOptimizer:
    """
    非零值回归器优化器
    
    专门用于优化非零值金额预测性能，包括：
    1. 非零值特征工程
    2. 回归器超参数调优
    3. 异常值处理
    4. 性能评估和分析
    """
    
    def __init__(self, 
                 use_lightgbm: bool = True,
                 zero_threshold: float = 1e-6,
                 cv_folds: int = 5,
                 random_state: int = 42):
        """
        初始化优化器
        
        Args:
            use_lightgbm: 是否使用LightGBM
            zero_threshold: 零值判定阈值
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.logger = get_logger('nonzero_value_regressor_optimizer')
        self.use_lightgbm = use_lightgbm and LIGHTGBM_AVAILABLE
        self.zero_threshold = zero_threshold
        self.cv_folds = cv_folds
        self.random_state = random_state
        
        # 优化结果
        self.best_features = None
        self.best_params = None
        self.scaler = None
        self.optimization_results = {}
        
        self.logger.info(f"非零值回归器优化器初始化，使用算法: {'LightGBM' if self.use_lightgbm else 'RandomForest'}")
    
    def analyze_nonzero_data(self, X: pd.DataFrame, y: np.ndarray) -> Dict[str, Any]:
        """
        分析非零值数据特征
        
        Args:
            X: 特征数据
            y: 目标变量（原始值）
            
        Returns:
            非零值数据分析结果
        """
        self.logger.info("开始非零值数据分析...")
        
        # 提取非零值数据
        nonzero_mask = np.abs(y) > self.zero_threshold
        X_nonzero = X[nonzero_mask]
        y_nonzero = y[nonzero_mask]
        
        if len(y_nonzero) == 0:
            self.logger.warning("没有非零值数据")
            return {'error': 'No nonzero data found'}
        
        # 基本统计
        stats = {
            'total_samples': len(y),
            'nonzero_count': len(y_nonzero),
            'nonzero_ratio': len(y_nonzero) / len(y) * 100,
            'min_value': y_nonzero.min(),
            'max_value': y_nonzero.max(),
            'mean_value': y_nonzero.mean(),
            'median_value': np.median(y_nonzero),
            'std_value': y_nonzero.std(),
            'cv': y_nonzero.std() / y_nonzero.mean() if y_nonzero.mean() > 0 else 0
        }
        
        self.logger.info(f"非零值数据统计: {len(y_nonzero)}样本 ({stats['nonzero_ratio']:.1f}%)")
        self.logger.info(f"金额范围: {stats['min_value']:.2f} - {stats['max_value']:.2f}元")
        self.logger.info(f"平均值: {stats['mean_value']:.2f}元, 变异系数: {stats['cv']:.3f}")
        
        # 分位数分析
        percentiles = [25, 50, 75, 90, 95, 99]
        percentile_values = {}
        for p in percentiles:
            percentile_values[f'p{p}'] = np.percentile(y_nonzero, p)
        
        stats['percentiles'] = percentile_values
        
        # 异常值检测
        q1 = np.percentile(y_nonzero, 25)
        q3 = np.percentile(y_nonzero, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = (y_nonzero < lower_bound) | (y_nonzero > upper_bound)
        outlier_ratio = outliers.sum() / len(y_nonzero) * 100
        
        stats['outliers'] = {
            'count': outliers.sum(),
            'ratio': outlier_ratio,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound
        }
        
        self.logger.info(f"异常值: {outliers.sum()}个 ({outlier_ratio:.1f}%)")
        
        return {
            'stats': stats,
            'X_nonzero': X_nonzero,
            'y_nonzero': y_nonzero,
            'nonzero_mask': nonzero_mask
        }
    
    def engineer_nonzero_features(self, X: pd.DataFrame, y: np.ndarray) -> pd.DataFrame:
        """
        针对非零值数据进行特征工程
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            工程化后的特征数据
        """
        self.logger.info("开始非零值特征工程...")
        
        # 提取非零值数据
        nonzero_mask = np.abs(y) > self.zero_threshold
        X_nonzero = X[nonzero_mask].copy()
        y_nonzero = y[nonzero_mask]
        
        # 1. 基础特征
        X_engineered = X_nonzero.copy()
        
        # 2. 比率特征（针对非零值更有意义）
        if 'should_fee' in X_engineered.columns and 'charge_day_count' in X_engineered.columns:
            # 日均应收费用
            X_engineered['daily_should_fee'] = X_engineered['should_fee'] / np.maximum(X_engineered['charge_day_count'], 1)
        
        if 'charge_day_count' in X_engineered.columns and 'month_day_count' in X_engineered.columns:
            # 计费效率
            X_engineered['billing_efficiency'] = X_engineered['charge_day_count'] / np.maximum(X_engineered['month_day_count'], 1)
        
        # 3. 交互特征
        if 'should_fee' in X_engineered.columns and 'cal_type' in X_engineered.columns:
            # 计费类型与应收费用的交互
            X_engineered['should_fee_cal_type'] = X_engineered['should_fee'] * X_engineered['cal_type']
        
        # 4. 对数变换（对于金额数据通常有效）
        if 'should_fee' in X_engineered.columns:
            X_engineered['log_should_fee'] = np.log1p(X_engineered['should_fee'])
        
        # 5. 分箱特征
        if 'should_fee' in X_engineered.columns:
            # 将should_fee分为不同档次
            should_fee_bins = pd.qcut(X_engineered['should_fee'], q=5, labels=False, duplicates='drop')
            X_engineered['should_fee_bin'] = should_fee_bins
        
        # 6. 统计特征（基于分组）
        if 'cal_type' in X_engineered.columns and 'should_fee' in X_engineered.columns:
            # 按计费类型分组的统计特征
            cal_type_stats = X_engineered.groupby('cal_type')['should_fee'].agg(['mean', 'std']).reset_index()
            cal_type_stats.columns = ['cal_type', 'cal_type_should_fee_mean', 'cal_type_should_fee_std']
            X_engineered = X_engineered.merge(cal_type_stats, on='cal_type', how='left')
            
            # 相对于组均值的偏差
            X_engineered['should_fee_deviation'] = X_engineered['should_fee'] - X_engineered['cal_type_should_fee_mean']
        
        self.logger.info(f"特征工程完成，特征数量: {X_nonzero.shape[1]} -> {X_engineered.shape[1]}")
        
        return X_engineered
    
    def analyze_feature_importance_for_regression(self, X: pd.DataFrame, y: np.ndarray) -> Dict[str, Any]:
        """
        分析回归特征重要性

        Args:
            X: 特征数据（已经是非零值数据或原始数据）
            y: 目标变量

        Returns:
            特征重要性分析结果
        """
        self.logger.info("开始回归特征重要性分析...")

        # 如果X和y长度不匹配，说明X已经是非零值数据
        if len(X) != len(y):
            # X已经是非零值数据
            X_nonzero = X
            y_nonzero = y[np.abs(y) > self.zero_threshold]
        else:
            # 提取非零值数据
            nonzero_mask = np.abs(y) > self.zero_threshold
            X_nonzero = X[nonzero_mask]
            y_nonzero = y[nonzero_mask]
        
        if len(y_nonzero) == 0:
            return {'error': 'No nonzero data found'}
        
        # 1. 模型特征重要性
        if self.use_lightgbm:
            model = lgb.LGBMRegressor(random_state=self.random_state, verbose=-1)
        else:
            model = RandomForestRegressor(random_state=self.random_state, n_jobs=-1)
        
        model.fit(X_nonzero, y_nonzero)
        model_importance = model.feature_importances_
        
        # 2. 统计特征重要性
        f_scores = f_regression(X_nonzero, y_nonzero)[0]
        mi_scores = mutual_info_regression(X_nonzero, y_nonzero, random_state=self.random_state)
        
        # 3. 相关性分析
        correlations = []
        for col in X_nonzero.columns:
            corr = np.corrcoef(X_nonzero[col], y_nonzero)[0, 1]
            correlations.append(abs(corr) if not np.isnan(corr) else 0)
        
        # 整理结果
        feature_importance_df = pd.DataFrame({
            'feature': X_nonzero.columns,
            'model_importance': model_importance,
            'f_score': f_scores,
            'mutual_info': mi_scores,
            'correlation': correlations
        })
        
        # 标准化分数
        for col in ['f_score', 'mutual_info']:
            max_val = feature_importance_df[col].max()
            if max_val > 0:
                feature_importance_df[col] = feature_importance_df[col] / max_val
        
        # 计算综合重要性分数
        feature_importance_df['composite_score'] = (
            feature_importance_df['model_importance'] * 0.4 +
            feature_importance_df['f_score'] * 0.3 +
            feature_importance_df['mutual_info'] * 0.2 +
            feature_importance_df['correlation'] * 0.1
        )
        
        feature_importance_df = feature_importance_df.sort_values('composite_score', ascending=False)
        
        self.logger.info("回归特征重要性分析完成")
        return {'feature_importance': feature_importance_df}
    
    def optimize_regressor_hyperparameters(self, X: pd.DataFrame, y: np.ndarray) -> Dict[str, Any]:
        """
        优化回归器超参数

        Args:
            X: 特征数据（已经是非零值数据或原始数据）
            y: 目标变量

        Returns:
            最佳参数和性能结果
        """
        self.logger.info("开始回归器超参数优化...")

        # 如果X和y长度不匹配，说明X已经是非零值数据
        if len(X) != len(y):
            # X已经是非零值数据
            X_nonzero = X
            y_nonzero = y[np.abs(y) > self.zero_threshold]
        else:
            # 提取非零值数据
            nonzero_mask = np.abs(y) > self.zero_threshold
            X_nonzero = X[nonzero_mask]
            y_nonzero = y[nonzero_mask]
        
        if len(y_nonzero) == 0:
            return {'error': 'No nonzero data found'}
        
        # 定义参数搜索空间
        if self.use_lightgbm:
            param_grid = [
                {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [5, 10, 15],
                    'learning_rate': [0.05, 0.1, 0.15],
                    'num_leaves': [31, 50, 100],
                    'min_child_samples': [20, 30, 50]
                }
            ]
        else:
            param_grid = [
                {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [5, 10, 15, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4],
                    'max_features': ['sqrt', 'log2', None]
                }
            ]
        
        best_score = float('-inf')
        best_params = None
        results = []
        
        # 简化的网格搜索
        for params in param_grid:
            param_combinations = self._generate_param_combinations(params)
            
            for param_combo in param_combinations[:15]:  # 限制搜索数量
                try:
                    # 创建模型
                    if self.use_lightgbm:
                        model = lgb.LGBMRegressor(
                            random_state=self.random_state,
                            verbose=-1,
                            **param_combo
                        )
                    else:
                        model = RandomForestRegressor(
                            random_state=self.random_state,
                            n_jobs=-1,
                            **param_combo
                        )
                    
                    # 交叉验证
                    cv_scores = cross_val_score(
                        model, X_nonzero, y_nonzero,
                        cv=KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state),
                        scoring='r2'
                    )
                    
                    mean_score = cv_scores.mean()
                    std_score = cv_scores.std()
                    
                    results.append({
                        'params': param_combo,
                        'mean_r2': mean_score,
                        'std_r2': std_score,
                        'cv_scores': cv_scores
                    })
                    
                    if mean_score > best_score:
                        best_score = mean_score
                        best_params = param_combo
                    
                    self.logger.debug(f"参数 {param_combo}: CV R² {mean_score:.4f} ± {std_score:.4f}")
                    
                except Exception as e:
                    self.logger.warning(f"参数 {param_combo} 测试失败: {e}")
                    continue
        
        self.best_params = best_params
        optimization_result = {
            'best_params': best_params,
            'best_r2': best_score,
            'all_results': results
        }
        
        self.logger.info(f"回归器超参数优化完成，最佳R²: {best_score:.4f}")
        self.logger.info(f"最佳参数: {best_params}")
        
        return optimization_result
    
    def create_optimized_regressor(self, X: pd.DataFrame, y: np.ndarray) -> Any:
        """
        创建优化后的回归器
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            优化后的回归器
        """
        self.logger.info("创建优化后的非零值回归器...")
        
        # 1. 特征工程
        X_engineered = self.engineer_nonzero_features(X, y)
        
        # 2. 特征选择
        importance_results = self.analyze_feature_importance_for_regression(X_engineered, y)
        if 'error' in importance_results:
            raise ValueError("无法分析特征重要性：没有非零值数据")
        
        feature_importance_df = importance_results['feature_importance']
        # 选择前10个重要特征
        self.best_features = feature_importance_df.head(10)['feature'].tolist()
        
        # 3. 提取非零值数据
        nonzero_mask = np.abs(y) > self.zero_threshold
        # X_engineered已经是非零值数据，直接选择特征
        X_selected = X_engineered[self.best_features]
        y_nonzero = y[nonzero_mask]
        
        # 4. 超参数优化
        if self.best_params is None:
            param_results = self.optimize_regressor_hyperparameters(X_selected, y_nonzero)
            if 'error' in param_results:
                raise ValueError("无法优化超参数：没有非零值数据")
        
        # 5. 创建最终模型
        if self.use_lightgbm:
            regressor = lgb.LGBMRegressor(
                random_state=self.random_state,
                verbose=-1,
                **self.best_params
            )
        else:
            regressor = RandomForestRegressor(
                random_state=self.random_state,
                n_jobs=-1,
                **self.best_params
            )
        
        regressor.fit(X_selected, y_nonzero)
        
        # 6. 评估性能
        y_pred = regressor.predict(X_selected)
        r2 = r2_score(y_nonzero, y_pred)
        mae = mean_absolute_error(y_nonzero, y_pred)
        rmse = np.sqrt(mean_squared_error(y_nonzero, y_pred))
        
        self.logger.info(f"优化后回归器性能: R²={r2:.4f}, MAE={mae:.2f}, RMSE={rmse:.2f}")
        
        return regressor
    
    def _generate_param_combinations(self, param_grid: Dict) -> List[Dict]:
        """生成参数组合"""
        import itertools
        
        keys = param_grid.keys()
        values = param_grid.values()
        combinations = []
        
        for combination in itertools.product(*values):
            combinations.append(dict(zip(keys, combination)))
        
        return combinations
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化结果摘要"""
        return {
            'best_features': self.best_features,
            'best_params': self.best_params,
            'algorithm': 'LightGBM' if self.use_lightgbm else 'RandomForest'
        }
