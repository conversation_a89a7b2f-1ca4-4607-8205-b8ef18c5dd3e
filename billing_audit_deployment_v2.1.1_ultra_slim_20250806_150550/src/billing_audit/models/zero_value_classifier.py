#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零值分类器优化组件
专门用于优化零值识别的分类器，包括特征选择、参数调优和阈值优化
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils.logger import get_logger


class ZeroValueClassifierOptimizer:
    """
    零值分类器优化器
    
    专门用于优化零值识别性能，包括：
    1. 特征选择和工程
    2. 超参数调优
    3. 分类阈值优化
    4. 性能评估和分析
    """
    
    def __init__(self, 
                 use_lightgbm: bool = True,
                 zero_threshold: float = 1e-6,
                 cv_folds: int = 5,
                 random_state: int = 42):
        """
        初始化优化器
        
        Args:
            use_lightgbm: 是否使用LightGBM
            zero_threshold: 零值判定阈值
            cv_folds: 交叉验证折数
            random_state: 随机种子
        """
        self.logger = get_logger('zero_value_classifier_optimizer')
        self.use_lightgbm = use_lightgbm and LIGHTGBM_AVAILABLE
        self.zero_threshold = zero_threshold
        self.cv_folds = cv_folds
        self.random_state = random_state
        
        # 优化结果
        self.best_features = None
        self.best_params = None
        self.best_threshold = 0.5
        self.optimization_results = {}
        
        self.logger.info(f"零值分类器优化器初始化，使用算法: {'LightGBM' if self.use_lightgbm else 'RandomForest'}")
    
    def analyze_feature_importance(self, X: pd.DataFrame, y: np.ndarray) -> Dict[str, Any]:
        """
        分析特征重要性
        
        Args:
            X: 特征数据
            y: 目标变量（原始值）
            
        Returns:
            特征重要性分析结果
        """
        self.logger.info("开始特征重要性分析...")
        
        # 创建二分类标签
        y_binary = (np.abs(y) > self.zero_threshold).astype(int)
        
        # 统计信息
        zero_count = np.sum(y_binary == 0)
        nonzero_count = np.sum(y_binary == 1)
        zero_ratio = zero_count / len(y_binary) * 100
        
        self.logger.info(f"数据分布: 零值={zero_count}({zero_ratio:.1f}%), 非零值={nonzero_count}")
        
        results = {
            'data_stats': {
                'total_samples': len(y_binary),
                'zero_count': zero_count,
                'nonzero_count': nonzero_count,
                'zero_ratio': zero_ratio
            }
        }
        
        # 1. 统计特征重要性（基于模型）
        if self.use_lightgbm:
            model = lgb.LGBMClassifier(random_state=self.random_state, verbose=-1)
        else:
            model = RandomForestClassifier(random_state=self.random_state, n_jobs=-1)
        
        model.fit(X, y_binary)
        model_importance = model.feature_importances_
        
        # 2. 统计特征重要性（基于统计）
        f_scores = f_classif(X, y_binary)[0]
        mi_scores = mutual_info_classif(X, y_binary, random_state=self.random_state)
        
        # 3. 特征区分度分析
        feature_discrimination = {}
        for i, col in enumerate(X.columns):
            zero_mean = X[y_binary == 0][col].mean()
            nonzero_mean = X[y_binary == 1][col].mean()
            zero_std = X[y_binary == 0][col].std()
            nonzero_std = X[y_binary == 1][col].std()
            
            # 计算区分度指标
            mean_diff = abs(nonzero_mean - zero_mean)
            pooled_std = np.sqrt((zero_std**2 + nonzero_std**2) / 2)
            discrimination_score = mean_diff / max(pooled_std, 1e-6)
            
            feature_discrimination[col] = {
                'zero_mean': zero_mean,
                'nonzero_mean': nonzero_mean,
                'mean_diff': mean_diff,
                'discrimination_score': discrimination_score
            }
        
        # 整理特征重要性结果
        feature_importance_df = pd.DataFrame({
            'feature': X.columns,
            'model_importance': model_importance,
            'f_score': f_scores,
            'mutual_info': mi_scores,
            'discrimination_score': [feature_discrimination[col]['discrimination_score'] for col in X.columns]
        })
        
        # 计算综合重要性分数
        feature_importance_df['composite_score'] = (
            feature_importance_df['model_importance'] * 0.4 +
            feature_importance_df['f_score'] / feature_importance_df['f_score'].max() * 0.3 +
            feature_importance_df['mutual_info'] * 0.3
        )
        
        feature_importance_df = feature_importance_df.sort_values('composite_score', ascending=False)
        
        results['feature_importance'] = feature_importance_df
        results['feature_discrimination'] = feature_discrimination
        
        self.logger.info("特征重要性分析完成")
        return results
    
    def select_best_features(self, X: pd.DataFrame, y: np.ndarray, 
                           max_features: int = 10) -> List[str]:
        """
        选择最佳特征
        
        Args:
            X: 特征数据
            y: 目标变量
            max_features: 最大特征数量
            
        Returns:
            选择的特征列表
        """
        self.logger.info(f"开始特征选择，最大特征数: {max_features}")
        
        # 分析特征重要性
        importance_results = self.analyze_feature_importance(X, y)
        feature_importance_df = importance_results['feature_importance']
        
        # 选择前N个重要特征
        selected_features = feature_importance_df.head(max_features)['feature'].tolist()
        
        self.logger.info(f"选择的特征: {selected_features}")
        self.best_features = selected_features
        
        return selected_features
    
    def optimize_hyperparameters(self, X: pd.DataFrame, y: np.ndarray) -> Dict[str, Any]:
        """
        优化超参数
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            最佳参数和性能结果
        """
        self.logger.info("开始超参数优化...")
        
        # 创建二分类标签
        y_binary = (np.abs(y) > self.zero_threshold).astype(int)
        
        # 定义参数搜索空间
        if self.use_lightgbm:
            param_grid = [
                {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [5, 10, 15],
                    'learning_rate': [0.05, 0.1, 0.2],
                    'num_leaves': [31, 50, 100]
                }
            ]
        else:
            param_grid = [
                {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [5, 10, 15, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                }
            ]
        
        best_score = 0
        best_params = None
        results = []
        
        # 简化的网格搜索（避免过度复杂）
        for params in param_grid:
            # 生成参数组合
            param_combinations = self._generate_param_combinations(params)
            
            for param_combo in param_combinations[:10]:  # 限制搜索数量
                try:
                    # 创建模型
                    if self.use_lightgbm:
                        model = lgb.LGBMClassifier(
                            random_state=self.random_state,
                            verbose=-1,
                            **param_combo
                        )
                    else:
                        model = RandomForestClassifier(
                            random_state=self.random_state,
                            n_jobs=-1,
                            **param_combo
                        )
                    
                    # 交叉验证
                    cv_scores = cross_val_score(
                        model, X, y_binary, 
                        cv=StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state),
                        scoring='accuracy'
                    )
                    
                    mean_score = cv_scores.mean()
                    std_score = cv_scores.std()
                    
                    results.append({
                        'params': param_combo,
                        'mean_score': mean_score,
                        'std_score': std_score,
                        'cv_scores': cv_scores
                    })
                    
                    if mean_score > best_score:
                        best_score = mean_score
                        best_params = param_combo
                    
                    self.logger.debug(f"参数 {param_combo}: CV准确率 {mean_score:.4f} ± {std_score:.4f}")
                    
                except Exception as e:
                    self.logger.warning(f"参数 {param_combo} 测试失败: {e}")
                    continue
        
        self.best_params = best_params
        optimization_result = {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': results
        }
        
        self.logger.info(f"超参数优化完成，最佳准确率: {best_score:.4f}")
        self.logger.info(f"最佳参数: {best_params}")
        
        return optimization_result
    
    def optimize_threshold(self, X: pd.DataFrame, y: np.ndarray, 
                          model: Any = None) -> Dict[str, Any]:
        """
        优化分类阈值
        
        Args:
            X: 特征数据
            y: 目标变量
            model: 训练好的模型（可选）
            
        Returns:
            最佳阈值和性能结果
        """
        self.logger.info("开始分类阈值优化...")
        
        # 创建二分类标签
        y_binary = (np.abs(y) > self.zero_threshold).astype(int)
        
        # 如果没有提供模型，使用最佳参数创建模型
        if model is None:
            if self.best_params is None:
                self.optimize_hyperparameters(X, y)
            
            if self.use_lightgbm:
                model = lgb.LGBMClassifier(
                    random_state=self.random_state,
                    verbose=-1,
                    **self.best_params
                )
            else:
                model = RandomForestClassifier(
                    random_state=self.random_state,
                    n_jobs=-1,
                    **self.best_params
                )
            
            model.fit(X, y_binary)
        
        # 获取预测概率
        y_proba = model.predict_proba(X)[:, 1]
        
        # 测试不同阈值
        thresholds = np.arange(0.1, 0.9, 0.05)
        threshold_results = []
        
        for threshold in thresholds:
            y_pred = (y_proba >= threshold).astype(int)
            
            accuracy = accuracy_score(y_binary, y_pred)
            precision = precision_score(y_binary, y_pred, zero_division=0)
            recall = recall_score(y_binary, y_pred, zero_division=0)
            f1 = f1_score(y_binary, y_pred, zero_division=0)
            
            threshold_results.append({
                'threshold': threshold,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1
            })
        
        # 选择最佳阈值（基于F1分数）
        best_result = max(threshold_results, key=lambda x: x['f1'])
        self.best_threshold = best_result['threshold']
        
        self.logger.info(f"阈值优化完成，最佳阈值: {self.best_threshold:.3f}")
        self.logger.info(f"最佳性能: F1={best_result['f1']:.4f}, 准确率={best_result['accuracy']:.4f}")
        
        return {
            'best_threshold': self.best_threshold,
            'best_performance': best_result,
            'all_results': threshold_results
        }
    
    def _generate_param_combinations(self, param_grid: Dict) -> List[Dict]:
        """生成参数组合"""
        import itertools
        
        keys = param_grid.keys()
        values = param_grid.values()
        combinations = []
        
        for combination in itertools.product(*values):
            combinations.append(dict(zip(keys, combination)))
        
        return combinations
    
    def create_optimized_classifier(self, X: pd.DataFrame, y: np.ndarray) -> Any:
        """
        创建优化后的分类器
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            优化后的分类器
        """
        self.logger.info("创建优化后的零值分类器...")
        
        # 1. 特征选择
        if self.best_features is None:
            self.select_best_features(X, y)
        
        X_selected = X[self.best_features]
        
        # 2. 超参数优化
        if self.best_params is None:
            self.optimize_hyperparameters(X_selected, y)
        
        # 3. 创建最终模型
        y_binary = (np.abs(y) > self.zero_threshold).astype(int)
        
        if self.use_lightgbm:
            classifier = lgb.LGBMClassifier(
                random_state=self.random_state,
                verbose=-1,
                **self.best_params
            )
        else:
            classifier = RandomForestClassifier(
                random_state=self.random_state,
                n_jobs=-1,
                **self.best_params
            )
        
        classifier.fit(X_selected, y_binary)
        
        # 4. 阈值优化
        self.optimize_threshold(X_selected, y, classifier)
        
        self.logger.info("优化后的零值分类器创建完成")
        return classifier
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化结果摘要"""
        return {
            'best_features': self.best_features,
            'best_params': self.best_params,
            'best_threshold': self.best_threshold,
            'algorithm': 'LightGBM' if self.use_lightgbm else 'RandomForest'
        }
