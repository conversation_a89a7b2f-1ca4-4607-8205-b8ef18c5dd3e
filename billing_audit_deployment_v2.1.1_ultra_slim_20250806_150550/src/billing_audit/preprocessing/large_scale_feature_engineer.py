#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千万级数据特征工程器
专门针对大规模数据优化的特征工程系统
支持增量统计、分批处理、内存优化
"""

import sys
import pandas as pd
import numpy as np
import json
import pickle
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import gc
import warnings
warnings.filterwarnings('ignore')

# 进度条
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 导入配置管理器和日志工具
from src.config.production_config_manager import get_config_manager
from src.utils.logger import get_logger


class IncrementalStatistics:
    """增量统计计算器"""
    
    def __init__(self):
        self.count = 0
        self.mean = 0.0
        self.m2 = 0.0  # 用于计算方差
        self.min_val = float('inf')
        self.max_val = float('-inf')
    
    def update(self, values):
        """更新统计量"""
        values = np.array(values)
        values = values[~np.isnan(values)]  # 移除NaN
        
        if len(values) == 0:
            return
        
        for value in values:
            self.count += 1
            delta = value - self.mean
            self.mean += delta / self.count
            delta2 = value - self.mean
            self.m2 += delta * delta2
            
            self.min_val = min(self.min_val, value)
            self.max_val = max(self.max_val, value)
    
    def get_stats(self):
        """获取统计量"""
        if self.count < 2:
            return {
                'count': self.count,
                'mean': self.mean,
                'std': 0.0,
                'min': self.min_val if self.count > 0 else 0,
                'max': self.max_val if self.count > 0 else 0
            }
        
        variance = self.m2 / (self.count - 1)
        std = np.sqrt(variance)
        
        return {
            'count': self.count,
            'mean': self.mean,
            'std': std,
            'min': self.min_val,
            'max': self.max_val
        }


class LargeScaleFeatureEngineer:
    """大规模特征工程器"""
    
    def __init__(self, config_path=None):
        # 初始化日志器
        self.logger = get_logger('large_scale_feature_engineer')

        # 使用生产配置管理器
        self.config_manager = get_config_manager()
        self.config = self.load_config(config_path)
        self.numerical_stats = {}
        self.categorical_mappings = {}
        self.feature_columns = self.config['feature_columns']
        self.categorical_columns = self.config['categorical_columns']
        self.numerical_columns = self.config['numerical_columns']

        self.logger.info(f"初始化大规模特征工程器")
        self.logger.info(f"特征列数: {len(self.feature_columns)}")
        self.logger.info(f"类别列数: {len(self.categorical_columns)}")
        self.logger.info(f"数值列数: {len(self.numerical_columns)}")

        # 保留控制台输出用于用户交互
        print(f"初始化大规模特征工程器")
        print(f"  - 特征列数: {len(self.feature_columns)}")
        print(f"  - 类别列数: {len(self.categorical_columns)}")
        print(f"  - 数值列数: {len(self.numerical_columns)}")
    
    def load_config(self, config_path=None):
        """加载配置文件"""
        if config_path is not None:
            # 如果指定了配置文件路径，使用传统方式加载
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config['billing_audit']['fixed_fee']
        else:
            # 使用生产配置管理器
            feature_columns = self.config_manager.get_feature_columns('fixed_fee')
            categorical_columns = self.config_manager.get('data_schema.fixed_fee.categorical_columns', [])
            numerical_columns = self.config_manager.get('data_schema.fixed_fee.numerical_columns', [])

            return {
                'feature_columns': feature_columns,
                'categorical_columns': categorical_columns,
                'numerical_columns': numerical_columns,
                'target_column': self.config_manager.get('data_schema.fixed_fee.target_column', 'amount')
            }
    
    def fit_statistics(self, file_path, batch_size=None):
        """拟合统计量（增量计算）"""
        if batch_size is None:
            batch_size = self.config_manager.get_batch_size()

        self.logger.info(f"开始拟合统计量")
        self.logger.info(f"数据文件: {file_path}")
        self.logger.info(f"批次大小: {batch_size:,}")

        # 保留控制台输出用于用户交互
        print(f"开始拟合统计量...")
        print(f"  - 数据文件: {file_path}")
        print(f"  - 批次大小: {batch_size:,}")
        
        # 初始化统计器
        for col in self.numerical_columns:
            self.numerical_stats[col] = IncrementalStatistics()
        
        for col in self.categorical_columns:
            self.categorical_mappings[col] = {}
        
        # 分批读取数据
        chunk_count = 0
        total_rows = 0
        
        try:
            # 检测分隔符
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline()
                sep = ',' if ',' in first_line else '\t'
            
            # 分批处理
            chunk_reader = pd.read_csv(
                file_path,
                sep=sep,
                chunksize=batch_size,
                encoding='utf-8',
                low_memory=False
            )

            # 创建进度条（无法预知总批次数，使用无限进度条）
            pbar = tqdm(desc="统计拟合", unit="批次")

            for chunk in chunk_reader:
                chunk_count += 1
                total_rows += len(chunk)

                # 更新数值列统计量
                for col in self.numerical_columns:
                    if col in chunk.columns:
                        values = pd.to_numeric(chunk[col], errors='coerce')
                        self.numerical_stats[col].update(values.dropna())

                # 更新类别列映射
                for col in self.categorical_columns:
                    if col in chunk.columns:
                        unique_values = chunk[col].astype(str).unique()
                        for val in unique_values:
                            if val not in self.categorical_mappings[col]:
                                self.categorical_mappings[col][val] = len(self.categorical_mappings[col])

                # 更新进度条
                pbar.set_postfix({
                    '批次': chunk_count,
                    '当前行数': f"{len(chunk):,}",
                    '累计行数': f"{total_rows:,}",
                    '数值列': len(self.numerical_columns),
                    '类别列': len(self.categorical_columns)
                })
                pbar.update(1)

                # 阶段性日志记录（每10个批次记录一次）
                if chunk_count % 10 == 0:
                    self.logger.info(f"处理进度: 已完成{chunk_count}批次, 累计{total_rows:,}行")
                    gc.collect()

            # 关闭进度条
            pbar.close()
            
            self.logger.info(f"统计量拟合完成: 总批次={chunk_count}, 总行数={total_rows:,}")
            print(f"  统计量拟合完成:")
            print(f"    - 总批次: {chunk_count}")
            print(f"    - 总行数: {total_rows:,}")

            # 显示统计信息
            self._print_statistics_summary()

        except Exception as e:
            self.logger.error(f"统计量拟合失败: {e}")
            print(f"统计量拟合失败: {e}")
            raise
    
    def _print_statistics_summary(self):
        """打印统计摘要"""
        # 记录到日志
        self.logger.info("统计摘要生成完成")
        for col, stats in self.numerical_stats.items():
            stat_info = stats.get_stats()
            self.logger.info(f"数值列 {col}: 均值={stat_info['mean']:.2f}, 标准差={stat_info['std']:.2f}")

        for col, mapping in self.categorical_mappings.items():
            self.logger.info(f"类别列 {col}: {len(mapping)} 个唯一值")

        # 保留控制台输出用于用户交互
        print(f"\n数值列统计摘要:")
        for col, stats in self.numerical_stats.items():
            stat_info = stats.get_stats()
            print(f"  {col}: 均值={stat_info['mean']:.2f}, 标准差={stat_info['std']:.2f}, 范围=[{stat_info['min']:.2f}, {stat_info['max']:.2f}]")

        print(f"\n类别列统计摘要:")
        for col, mapping in self.categorical_mappings.items():
            print(f"  {col}: {len(mapping)} 个唯一值")
    
    def create_efficient_features(self, chunk):
        """创建高效特征（避免复杂操作）"""
        df_features = chunk.copy()

        try:
            # 首先处理所有None值，填充为0或合理默认值
            numeric_cols = ['final_eff_year', 'final_eff_mon', 'final_eff_day',
                          'final_exp_year', 'final_exp_mon', 'final_exp_day',
                          'charge_day_count', 'month_day_count', 'should_fee']

            for col in numeric_cols:
                if col in df_features.columns:
                    df_features[col] = df_features[col].fillna(0)

            # 1. 简单的数值特征（避免复杂日期操作）
            if 'should_fee' in df_features.columns and 'charge_day_count' in df_features.columns:
                # 使用numpy操作，更高效，确保没有None值
                charge_days = np.maximum(df_features['charge_day_count'].fillna(1).values, 1)
                df_features['daily_should_fee'] = df_features['should_fee'].fillna(0).values / charge_days

            if 'charge_day_count' in df_features.columns and 'month_day_count' in df_features.columns:
                month_days = np.maximum(df_features['month_day_count'].fillna(1).values, 1)
                df_features['billing_efficiency'] = df_features['charge_day_count'].fillna(0).values / month_days

            # 2. 简化的日期特征（避免pd.to_datetime），安全处理None值
            if all(col in df_features.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']):
                # 先填充None值，再进行数值计算
                eff_day_safe = df_features['final_eff_day'].fillna(0)
                eff_mon_safe = df_features['final_eff_mon'].fillna(1)

                df_features['eff_is_weekend'] = ((eff_day_safe % 7) >= 5).astype(int)
                df_features['eff_quarter'] = ((eff_mon_safe - 1) // 3) + 1
                df_features['eff_month_start'] = (eff_day_safe == 1).astype(int)

            if all(col in df_features.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day']):
                # 先填充None值，再进行数值计算
                exp_day_safe = df_features['final_exp_day'].fillna(0)
                exp_mon_safe = df_features['final_exp_mon'].fillna(1)

                df_features['exp_is_weekend'] = ((exp_day_safe % 7) >= 5).astype(int)
                df_features['exp_quarter'] = ((exp_mon_safe - 1) // 3) + 1
                df_features['exp_month_end'] = (exp_day_safe >= 28).astype(int)
            
            # 3. 简化的组合特征，安全处理None值
            if all(col in df_features.columns for col in ['final_eff_year', 'final_exp_year']):
                eff_year_safe = df_features['final_eff_year'].fillna(0)
                exp_year_safe = df_features['final_exp_year'].fillna(0)
                df_features['subscription_years'] = exp_year_safe - eff_year_safe

            if all(col in df_features.columns for col in ['final_eff_mon', 'final_exp_mon']):
                eff_mon_safe = df_features['final_eff_mon'].fillna(0)
                exp_mon_safe = df_features['final_exp_mon'].fillna(0)
                df_features['subscription_months'] = exp_mon_safe - eff_mon_safe

            # 4. 交互特征，安全处理None值
            if 'cal_type' in df_features.columns and 'charge_day_count' in df_features.columns:
                cal_type_safe = df_features['cal_type'].fillna(0)
                charge_day_safe = df_features['charge_day_count'].fillna(0)
                df_features['cal_type_day_interaction'] = (
                    cal_type_safe.values * charge_day_safe.values
                )
            
        except Exception as e:
            self.logger.warning(f"特征创建部分失败: {e}")
            print(f"特征创建部分失败: {e}")
        
        return df_features
    
    def encode_categorical_chunk(self, chunk):
        """编码类别特征（分批处理）"""
        df_encoded = chunk.copy()
        
        for col in self.categorical_columns:
            if col in df_encoded.columns:
                # 使用预计算的映射进行编码
                mapping = self.categorical_mappings[col]
                
                # 处理未见过的类别
                df_encoded[col] = df_encoded[col].astype(str).map(mapping).fillna(0).astype(int)
        
        return df_encoded
    
    def scale_numerical_chunk(self, chunk):
        """标准化数值特征（分批处理）"""
        df_scaled = chunk.copy()
        
        for col in self.numerical_columns:
            if col in df_scaled.columns:
                stats = self.numerical_stats[col].get_stats()
                if stats['std'] > 0:
                    # 手动标准化，避免sklearn的内存开销
                    df_scaled[col] = (df_scaled[col] - stats['mean']) / stats['std']
                else:
                    df_scaled[col] = 0  # 标准差为0的情况
        
        return df_scaled
    
    def transform_chunk(self, chunk):
        """转换单个数据块"""
        self.logger.debug(f"开始转换数据块，输入形状: {chunk.shape}")

        # 1. 创建特征
        df_features = self.create_efficient_features(chunk)
        self.logger.debug(f"特征创建完成，输出形状: {df_features.shape}")

        # 2. 编码类别特征
        df_encoded = self.encode_categorical_chunk(df_features)
        self.logger.debug(f"类别特征编码完成")

        # 3. 标准化数值特征
        df_scaled = self.scale_numerical_chunk(df_encoded)
        self.logger.debug(f"数值特征标准化完成，最终形状: {df_scaled.shape}")

        # 4. 选择最终特征（确保有特征输出）
        final_features = self._select_final_features(df_scaled)
        self.logger.debug(f"最终特征选择完成，输出形状: {final_features.shape}")

        return final_features
    
    def _select_final_features(self, df):
        """选择最终特征"""
        # 确保至少有一些基础特征
        available_features = []
        
        # 1. 优先选择数值特征
        for col in self.numerical_columns:
            if col in df.columns:
                available_features.append(col)
        
        # 2. 添加类别特征
        for col in self.categorical_columns:
            if col in df.columns:
                available_features.append(col)
        
        # 3. 添加创建的特征
        created_features = ['daily_should_fee', 'billing_efficiency', 'eff_is_weekend', 
                          'eff_quarter', 'subscription_years', 'cal_type_day_interaction']
        for col in created_features:
            if col in df.columns:
                available_features.append(col)
        
        # 4. 如果没有特征，使用所有可用列（除了目标列）
        if not available_features:
            target_col = self.config.get('target_column', 'amount')
            available_features = [col for col in df.columns if col != target_col]
        
        # 5. 确保至少有一个特征
        if not available_features:
            # 创建一个常数特征作为fallback
            df['constant_feature'] = 1.0
            available_features = ['constant_feature']
            self.logger.warning("没有找到有效特征，创建常数特征")
        
        # 去重并保持顺序
        available_features = list(dict.fromkeys(available_features))
        
        self.logger.info(f"选择的特征: {available_features[:10]}{'...' if len(available_features) > 10 else ''}")
        
        return df[available_features]
    
    def save_preprocessor(self, save_path):
        """保存预处理器"""
        self.logger.info(f"保存预处理器到: {save_path}")

        preprocessor_data = {
            'config': self.config,
            'numerical_stats': {col: stats.get_stats() for col, stats in self.numerical_stats.items()},
            'categorical_mappings': self.categorical_mappings,
            'feature_columns': self.feature_columns,
            'categorical_columns': self.categorical_columns,
            'numerical_columns': self.numerical_columns
        }
        
        with open(save_path, 'wb') as f:
            pickle.dump(preprocessor_data, f)

        self.logger.info(f"预处理器保存成功: {save_path}")
        print(f"预处理器已保存: {save_path}")
    
    @classmethod
    def load_preprocessor(cls, load_path):
        """加载预处理器"""
        with open(load_path, 'rb') as f:
            preprocessor_data = pickle.load(f)

        # 重建对象
        engineer = cls.__new__(cls)

        # 初始化必要的属性
        engineer.logger = get_logger('large_scale_feature_engineer')
        engineer.config_manager = get_config_manager()

        # 恢复保存的数据
        engineer.config = preprocessor_data['config']
        engineer.categorical_mappings = preprocessor_data['categorical_mappings']
        engineer.feature_columns = preprocessor_data['feature_columns']
        engineer.categorical_columns = preprocessor_data['categorical_columns']
        engineer.numerical_columns = preprocessor_data['numerical_columns']

        # 重建统计对象
        engineer.numerical_stats = {}
        for col, stats_dict in preprocessor_data['numerical_stats'].items():
            stats_obj = IncrementalStatistics()
            stats_obj.count = stats_dict['count']
            stats_obj.mean = stats_dict['mean']
            stats_obj.m2 = stats_dict['std'] ** 2 * (stats_dict['count'] - 1) if stats_dict['count'] > 1 else 0
            stats_obj.min_val = stats_dict['min']
            stats_obj.max_val = stats_dict['max']
            engineer.numerical_stats[col] = stats_obj

        engineer.logger.info(f"预处理器已加载: {load_path}")
        print(f"预处理器已加载: {load_path}")
        return engineer


def main():
    """测试函数"""
    import argparse
    import logging
    logger = logging.getLogger(__name__)

    parser = argparse.ArgumentParser(description='大规模特征工程器测试')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径')
    parser.add_argument('--output', '-o', help='预处理器保存路径')
    parser.add_argument('--batch-size', '-b', type=int, default=50000, help='批处理大小')

    args = parser.parse_args()

    try:
        logger.info(f"开始大规模特征工程器测试: 输入={args.input}, 批次大小={args.batch_size}")

        # 初始化特征工程器
        engineer = LargeScaleFeatureEngineer()
        logger.info(f"特征工程器初始化完成")

        # 拟合统计量
        engineer.fit_statistics(args.input, args.batch_size)
        logger.info(f"统计量拟合完成")

        # 保存预处理器
        if args.output:
            engineer.save_preprocessor(args.output)
            logger.info(f"预处理器保存完成: {args.output}")

        logger.info(f"大规模特征工程器测试任务完成")
        print(f"\n大规模特征工程器测试完成！")
        return True

    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
