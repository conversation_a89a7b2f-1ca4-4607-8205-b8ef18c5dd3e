#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模数据模型训练脚本
专门针对千万级数据规模优化的训练脚本
支持CSV/TXT文件，分批处理，内存优化
"""

import sys
import pandas as pd
import numpy as np
import json
import joblib
from pathlib import Path
import argparse
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
# from sklearn.preprocessing import LabelEncoder  # 不再需要
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import gc
import warnings
warnings.filterwarnings('ignore')

# 进度条和可视化
from tqdm import tqdm
import matplotlib.pyplot as plt
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 导入大规模特征工程器和配置管理器
from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer
from src.config.production_config_manager import get_config_manager
from src.utils.logger import get_logger

# 导入分层模型
from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel


class LargeScaleDataProcessor:
    """大规模数据处理器"""

    def __init__(self, batch_size=None):
        # 使用生产配置管理器
        self.config_manager = get_config_manager()
        self.batch_size = batch_size or self.config_manager.get_batch_size()
        self.feature_engineer = None
        self.feature_columns = None
        self.target_column = None

        # 初始化日志器
        self.logger = get_logger('large_scale_model_trainer')

    def load_config(self):
        """加载配置文件"""
        # 使用生产配置管理器
        feature_columns = self.config_manager.get_feature_columns('fixed_fee')
        return {
            'feature_columns': feature_columns,
            'categorical_columns': self.config_manager.get('data_schema.fixed_fee.categorical_columns', []),
            'numerical_columns': self.config_manager.get('data_schema.fixed_fee.numerical_columns', []),
            'target_column': self.config_manager.get('data_schema.fixed_fee.target_column', 'amount')
        }
    
    def read_large_csv(self, file_path, chunksize=None):
        """分批读取大型CSV文件"""
        if chunksize is None:
            chunksize = self.batch_size
            
        self.logger.info(f"开始分批读取数据文件: {file_path}, 批次大小: {chunksize:,}")
        print(f"开始分批读取数据文件: {file_path}")
        print(f"  - 批次大小: {chunksize:,} 行")
        
        try:
            # 检测文件分隔符
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline()
                if ',' in first_line:
                    sep = ','
                elif '\t' in first_line:
                    sep = '\t'
                else:
                    sep = ','
            
            self.logger.info(f"检测到分隔符: '{sep}'")
            print(f"  - 检测到分隔符: '{sep}'")
            
            # 分批读取
            chunk_reader = pd.read_csv(
                file_path, 
                sep=sep,
                chunksize=chunksize,
                encoding='utf-8',
                low_memory=False
            )
            
            return chunk_reader, sep
            
        except Exception as e:
            self.logger.error(f"文件读取失败: {e}")
            print(f"文件读取失败: {e}")
            raise
    
    def process_chunk(self, chunk, is_first_chunk=False):
        """处理单个数据块"""
        if is_first_chunk:
            # 第一个块：初始化特征列表
            config = self.load_config()
            self.feature_columns = config['feature_columns']
            self.target_column = config['target_column']
            
            self.logger.info(f"初始化特征配置: 特征列数={len(self.feature_columns)}, 目标列={self.target_column}")
            print(f"  初始化特征配置:")
            print(f"    - 特征列数: {len(self.feature_columns)}")
            print(f"    - 目标列: {self.target_column}")
        
        # 提取特征和目标变量
        try:
            X = chunk[self.feature_columns].copy()
            y = chunk[self.target_column].copy()
            
            # 处理缺失值
            X = X.fillna(0)
            y = y.fillna(0)
            
            return X, y
            
        except KeyError as e:
            self.logger.error(f"列不存在: {e}, 可用列: {list(chunk.columns)}")
            print(f"列不存在: {e}")
            print(f"可用列: {list(chunk.columns)}")
            raise
    
    def fit_feature_engineer(self, file_path, batch_size=50000):
        """拟合特征工程器"""
        self.logger.info(f"开始拟合大规模特征工程器: 文件={file_path}, 批次大小={batch_size:,}")
        print(f"拟合大规模特征工程器...")

        # 初始化特征工程器
        self.feature_engineer = LargeScaleFeatureEngineer()
        self.logger.info(f"特征工程器初始化完成")

        # 初始化特征列配置
        config = self.load_config()
        self.feature_columns = config['feature_columns']
        self.target_column = config['target_column']
        self.logger.info(f"特征配置加载完成: 特征列数={len(self.feature_columns)}, 目标列={self.target_column}")

        # 拟合统计量
        self.feature_engineer.fit_statistics(file_path, batch_size)
        self.logger.info(f"特征工程器统计量拟合完成")

        print(f"  特征工程器拟合完成")
    
    def process_chunk_with_features(self, chunk):
        """使用特征工程器处理数据块"""
        if self.feature_engineer is None:
            raise ValueError("特征工程器未初始化，请先调用fit_feature_engineer")

        # 提取特征和目标变量
        X = chunk[self.feature_columns].copy()
        y = chunk[self.target_column].copy()

        # 处理缺失值
        X = X.fillna(0)
        y = y.fillna(0)

        # 应用特征工程
        X_processed = self.feature_engineer.transform_chunk(X)

        return X_processed, y


def train_large_scale_model(file_path, output_dir=None, batch_size=None):
    """训练大规模数据模型"""
    # 获取logger和配置管理器
    from src.utils.logger import get_logger
    logger = get_logger('large_scale_model_trainer')
    config_manager = get_config_manager()

    # 使用配置管理器的默认值
    if batch_size is None:
        batch_size = config_manager.get_batch_size()

    if output_dir is None:
        output_dir = config_manager.get_data_path('output_data_dir') or (project_root / "outputs" / "models")
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"开始大规模数据模型训练: {file_path}")
    print("大规模数据模型训练")
    print("=" * 60)

    # 初始化处理器
    processor = LargeScaleDataProcessor(batch_size=batch_size)
    
    # 第一步：拟合特征工程器
    processor.fit_feature_engineer(file_path, batch_size)
    
    # 第二步：分批处理数据并训练
    logger.info(f"开始分批处理和训练: 批次大小={batch_size:,}")
    print(f"\n开始分批处理和训练...")

    # 使用配置管理器的模型超参数
    model_params = config_manager.get_model_hyperparameters('random_forest')
    model = RandomForestRegressor(**model_params)
    logger.info(f"模型初始化完成: {type(model).__name__}, 参数={model_params}")

    chunk_reader, sep = processor.read_large_csv(file_path, chunksize=batch_size)

    # 由于无法预知总行数，使用无限进度条
    expected_chunks = None

    X_all = []
    y_all = []
    chunk_count = 0
    total_samples = 0

    # 创建进度条（无法预知总批次数，使用无限进度条）
    logger.info(f"开始分批处理数据，使用分隔符: '{sep}'")
    print(f"  开始分批处理数据...")
    pbar = tqdm(desc="数据处理", unit="批次")

    for chunk in chunk_reader:
        chunk_count += 1

        try:
            # 处理数据块（使用新的特征工程器）
            X_chunk, y_chunk = processor.process_chunk_with_features(chunk)

            # 累积数据（注意内存限制）
            X_all.append(X_chunk)
            y_all.append(y_chunk)

            total_samples += len(X_chunk)

            # 更新进度条
            pbar.set_postfix({
                '当前批次': f"{chunk_count}/{expected_chunks}",
                '样本数': f"{len(X_chunk):,}",
                '累计': f"{total_samples:,}",
                '内存批次': len(X_all)
            })
            pbar.update(1)

            # 内存管理：每处理一定数量的批次就合并一次
            if chunk_count % 10 == 0:
                pbar.set_description("合并数据")
                X_combined = pd.concat(X_all, ignore_index=True)
                y_combined = pd.concat(y_all, ignore_index=True)

                # 清理内存
                X_all = [X_combined]
                y_all = [y_combined]
                gc.collect()

                pbar.set_description("数据处理")

        except Exception as e:
            pbar.set_postfix({'错误': f"批次{chunk_count}失败"})
            continue

    # 关闭进度条
    pbar.close()

    # 处理剩余的数据（如果没有达到10批次的倍数）
    if len(X_all) > 1:
        logger.info(f"合并剩余的 {len(X_all)} 批数据")
        print(f"  合并剩余的 {len(X_all)} 批数据...")
        X_combined = pd.concat(X_all, ignore_index=True)
        y_combined = pd.concat(y_all, ignore_index=True)
        X_all = [X_combined]
        y_all = [y_combined]
        logger.info(f"剩余数据合并完成，数据形状: {X_combined.shape}")
        print(f"    最终合并完成，数据形状: {X_combined.shape}")

    # 最终合并所有数据
    logger.info(f"开始最终数据合并，待合并批次数: {len(X_all)}")
    print(f"\n合并所有数据...")
    if len(X_all) == 0:
        logger.warning(f"没有数据可以合并，训练终止")
        print(f"没有数据可以合并")
        return None

    X_final = pd.concat(X_all, ignore_index=True)
    y_final = pd.concat(y_all, ignore_index=True)

    logger.info(f"最终数据合并完成: X{X_final.shape}, y{y_final.shape}")
    print(f"  最终数据形状: X{X_final.shape}, y{y_final.shape}")

    # 分割训练测试集
    logger.info(f"开始分割训练测试集，测试集比例: 0.2")
    print(f"\n分割训练测试集...")
    X_train, X_test, y_train, y_test = train_test_split(
        X_final, y_final, test_size=0.2, random_state=42
    )
    
    logger.info(f"数据集分割完成: 训练集={X_train.shape[0]:,}样本, 测试集={X_test.shape[0]:,}样本")
    print(f"  训练集: {X_train.shape[0]:,} 样本")
    print(f"  测试集: {X_test.shape[0]:,} 样本")

    # 训练模型
    logger.info(f"开始模型训练: 算法={type(model).__name__}, 特征数={X_train.shape[1]}")
    logger.info(f"模型参数: n_estimators={model.n_estimators}, max_depth={model.max_depth}")
    print(f"\n开始训练模型...")
    print(f"  模型参数: n_estimators={model.n_estimators}, max_depth={model.max_depth}")
    print(f"  训练样本: {X_train.shape[0]:,} 行 × {X_train.shape[1]} 特征")

    start_time = datetime.now()

    # 创建训练进度条（模拟进度，因为RandomForest不支持回调）
    with tqdm(total=100, desc="模型训练", unit="%") as train_pbar:
        # 开始训练
        train_pbar.set_postfix({'状态': '初始化'})
        train_pbar.update(10)

        train_pbar.set_postfix({'状态': '构建决策树'})
        model.fit(X_train, y_train)
        train_pbar.update(90)

        train_pbar.set_postfix({'状态': '完成'})

    training_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"模型训练完成，耗时: {training_time:.2f}秒")
    print(f"  训练完成，耗时: {training_time:.2f}秒")
    
    # 评估模型
    logger.info(f"开始模型性能评估，测试样本数: {len(X_test):,}")
    print(f"\n评估模型性能...")

    # 创建评估进度条
    with tqdm(total=100, desc="模型评估", unit="%") as eval_pbar:
        eval_pbar.set_postfix({'状态': '预测中'})
        y_pred_test = model.predict(X_test)
        eval_pbar.update(50)

        eval_pbar.set_postfix({'状态': '计算指标'})
        mae = mean_absolute_error(y_test, y_pred_test)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
        r2 = r2_score(y_test, y_pred_test)
        eval_pbar.update(50)

        eval_pbar.set_postfix({'状态': '完成'})

    logger.info(f"模型性能评估: MAE={mae:.2f}, RMSE={rmse:.2f}, R²={r2:.4f}")
    print(f"  测试集性能:")
    print(f"    - MAE: {mae:.2f}")
    print(f"    - RMSE: {rmse:.2f}")
    print(f"    - R²: {r2:.4f}")

    # 显示性能等级
    if r2 >= 0.9:
        performance_level = "优秀"
    elif r2 >= 0.8:
        performance_level = "良好"
    elif r2 >= 0.6:
        performance_level = "一般"
    else:
        performance_level = "较差"

    logger.info(f"性能等级评估: {performance_level} (R²={r2:.4f})")
    print(f"    - 性能等级: {performance_level}")

    # 保存模型和特征工程器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = output_dir / f"large_scale_model_{timestamp}.pkl"
    feature_engineer_path = output_dir / f"large_scale_feature_engineer_{timestamp}.pkl"

    logger.info(f"开始保存模型和特征工程器: 时间戳={timestamp}")
    joblib.dump(model, model_path)
    processor.feature_engineer.save_preprocessor(feature_engineer_path)

    logger.info(f"模型保存完成: 模型={model_path}, 特征工程器={feature_engineer_path}")
    print(f"\n模型保存完成:")
    print(f"  模型文件: {model_path}")
    print(f"  特征工程器: {feature_engineer_path}")

    return {
        'model_path': str(model_path),
        'feature_engineer_path': str(feature_engineer_path),
        'performance': {'mae': mae, 'rmse': rmse, 'r2': r2},
        'training_time': training_time,
        'total_samples': total_samples
    }


def main():
    """主函数"""
    import logging
    logger = logging.getLogger(__name__)

    parser = argparse.ArgumentParser(description='大规模数据模型训练')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径 (CSV/TXT)')
    parser.add_argument('--output', '-o', help='输出目录路径')
    parser.add_argument('--batch-size', '-b', type=int, default=50000, help='批处理大小')

    args = parser.parse_args()

    try:
        logger.info(f"开始大规模模型训练任务: 输入={args.input}, 批次大小={args.batch_size}")
        result = train_large_scale_model(
            file_path=args.input,
            output_dir=args.output,
            batch_size=args.batch_size
        )

        if result is None:
            logger.error(f"模型训练失败，返回结果为空")
            print(f"模型训练失败")
            return False

        logger.info(f"大规模模型训练任务完成: 样本数={result['total_samples']:,}, 训练时间={result['training_time']:.2f}秒, R²={result['performance']['r2']:.4f}")
        print(f"\n大规模模型训练完成！")
        print(f"处理样本: {result['total_samples']:,} 条")
        print(f"训练时间: {result['training_time']:.2f}秒")
        print(f"模型性能: R²={result['performance']['r2']:.4f}")

        return True

    except Exception as e:
        logger.error(f"训练失败: {e}")
        print(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def train_hierarchical_model(file_path, output_dir=None, batch_size=None, use_lightgbm=True):
    """训练分层模型"""
    # 获取logger和配置管理器
    from src.utils.logger import get_logger
    logger = get_logger('hierarchical_model_trainer')
    config_manager = get_config_manager()

    # 使用配置管理器的默认值
    if batch_size is None:
        batch_size = config_manager.get_batch_size()

    if output_dir is None:
        output_dir = config_manager.get_data_path('output_data_dir') or (project_root / "outputs" / "models")
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"开始分层模型训练: {file_path}")
    logger.info(f"使用算法: {'LightGBM' if use_lightgbm else 'RandomForest'}")

    start_time = time.time()

    # 第一步：初始化处理器
    processor = LargeScaleDataProcessor(batch_size=batch_size)

    # 第二步：拟合特征工程器
    logger.info(f"拟合特征工程器...")
    print(f"\n拟合特征工程器...")
    processor.fit_feature_engineer(file_path, batch_size)

    # 第三步：分批处理数据
    logger.info(f"开始分批处理数据: 批次大小={batch_size:,}")
    print(f"\n开始分批处理数据...")

    chunk_reader, sep = processor.read_large_csv(file_path, chunksize=batch_size)

    X_all = []
    y_all = []
    chunk_count = 0
    total_samples = 0

    pbar = tqdm(desc="数据处理", unit="批次")

    for chunk in chunk_reader:
        chunk_count += 1

        try:
            # 处理数据块
            X_chunk, y_chunk = processor.process_chunk_with_features(chunk)

            # 累积数据
            X_all.append(X_chunk)
            y_all.append(y_chunk)

            total_samples += len(X_chunk)

            # 更新进度条
            pbar.set_postfix({
                '当前批次': chunk_count,
                '样本数': f"{len(X_chunk):,}",
                '累计': f"{total_samples:,}"
            })
            pbar.update(1)

            # 内存管理
            if chunk_count % 10 == 0:
                pbar.set_description("合并数据")
                X_combined = pd.concat(X_all, ignore_index=True)
                y_combined = pd.concat(y_all, ignore_index=True)

                X_all = [X_combined]
                y_all = [y_combined]

                gc.collect()
                pbar.set_description("数据处理")

        except Exception as e:
            logger.warning(f"批次{chunk_count}处理失败: {e}")
            continue

    pbar.close()

    # 第三步：合并所有数据
    logger.info("合并所有处理后的数据...")
    print("合并所有处理后的数据...")

    X_final = pd.concat(X_all, ignore_index=True)
    y_final = pd.concat(y_all, ignore_index=True).values

    logger.info(f"数据合并完成: {len(X_final):,} 样本, {X_final.shape[1]} 特征")
    print(f"数据合并完成: {len(X_final):,} 样本, {X_final.shape[1]} 特征")

    # 清理内存
    del X_all, y_all
    gc.collect()

    # 第四步：创建和训练分层模型
    logger.info("创建分层模型...")
    print("创建分层模型...")

    # 获取分层模型参数
    if use_lightgbm:
        classifier_params = {
            'n_estimators': 100,
            'max_depth': 10,
            'learning_rate': 0.1,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        regressor_params = classifier_params.copy()
    else:
        classifier_params = config_manager.get_model_hyperparameters('random_forest')
        regressor_params = classifier_params.copy()

    hierarchical_model = HierarchicalBillingModel(
        classifier_params=classifier_params,
        regressor_params=regressor_params,
        use_lightgbm=use_lightgbm
    )

    logger.info("开始训练分层模型...")
    print("开始训练分层模型...")

    hierarchical_model.fit(X_final, y_final)

    # 第五步：模型评估
    logger.info("评估模型性能...")
    print("评估模型性能...")

    # 预测
    y_pred = hierarchical_model.predict(X_final)

    # 计算性能指标
    r2 = r2_score(y_final, y_pred)
    mae = mean_absolute_error(y_final, y_pred)
    rmse = np.sqrt(mean_squared_error(y_final, y_pred))

    # 零值识别性能
    zero_mask_true = np.abs(y_final) <= 1e-6
    zero_mask_pred = np.abs(y_pred) <= 1e-6
    zero_accuracy = (zero_mask_true == zero_mask_pred).mean()

    logger.info(f"模型性能: R²={r2:.4f}, MAE={mae:.2f}, RMSE={rmse:.2f}")
    logger.info(f"零值识别准确率: {zero_accuracy:.4f}")

    print(f"\n模型性能评估:")
    print(f"  R²: {r2:.4f}")
    print(f"  MAE: {mae:.2f}元")
    print(f"  RMSE: {rmse:.2f}元")
    print(f"  零值识别准确率: {zero_accuracy:.4f}")

    # 第六步：保存模型
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model_filename = f"hierarchical_model_{timestamp}.pkl"
    model_path = output_dir / model_filename

    logger.info(f"保存分层模型到: {model_path}")
    hierarchical_model.save(str(model_path))

    # 保存训练统计
    training_stats = {
        'model_type': 'HierarchicalBillingModel',
        'algorithm': 'LightGBM' if use_lightgbm else 'RandomForest',
        'samples': len(X_final),
        'features': X_final.shape[1],
        'r2': r2,
        'mae': mae,
        'rmse': rmse,
        'zero_accuracy': zero_accuracy,
        'training_time': time.time() - start_time,
        'timestamp': datetime.now().isoformat()
    }

    stats_path = model_path.with_suffix('.json')
    with open(stats_path, 'w', encoding='utf-8') as f:
        json.dump(training_stats, f, indent=2, ensure_ascii=False)

    logger.info(f"训练统计保存到: {stats_path}")

    total_time = time.time() - start_time
    logger.info(f"分层模型训练完成，总耗时: {total_time:.2f}秒")
    print(f"\n🎉 分层模型训练完成！")
    print(f"模型文件: {model_path}")
    print(f"统计文件: {stats_path}")
    print(f"总耗时: {total_time:.2f}秒")

    return hierarchical_model, training_stats


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
