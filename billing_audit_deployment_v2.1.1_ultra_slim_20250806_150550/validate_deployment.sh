#!/bin/bash
# 部署包完整性验证脚本

echo "🔍 验证山西电信出账稽核AI系统 v2.1.1 部署包"
echo "================================================"

# 计数器
checks=0
passed=0

# 检查函数
check_item() {
    checks=$((checks + 1))
    if [ "$1" = "true" ]; then
        echo "✅ $2"
        passed=$((passed + 1))
    else
        echo "❌ $2"
    fi
}

echo ""
echo "📁 检查目录结构..."
check_item "$([ -d "src" ] && echo true || echo false)" "核心源码目录存在"
check_item "$([ -d "scripts" ] && echo true || echo false)" "脚本目录存在"
check_item "$([ -d "config" ] && echo true || echo false)" "配置目录存在"
check_item "$([ -d "deployment" ] && echo true || echo false)" "部署目录存在"
# check_item "$([ -d "docs" ] && echo true || echo false)" "文档目录存在 (已精简移除)"

echo ""
echo "📄 检查核心文件..."
check_item "$([ -f "scripts/production/billing_audit_main.py" ] && echo true || echo false)" "主脚本文件存在"
check_item "$([ -f "config/production_config.json" ] && echo true || echo false)" "生产配置文件存在"
check_item "$([ -f "deployment/config/production_config.json" ] && echo true || echo false)" "部署配置文件存在"
check_item "$([ -f "deployment/docker/Dockerfile" ] && echo true || echo false)" "Docker文件存在"
check_item "$([ -f "deployment/docker/docker-compose.yml" ] && echo true || echo false)" "Docker Compose文件存在"

echo ""
echo "🔧 检查管理脚本..."
check_item "$([ -f "start.sh" ] && [ -x "start.sh" ] && echo true || echo false)" "启动脚本存在且可执行"
check_item "$([ -f "stop.sh" ] && [ -x "stop.sh" ] && echo true || echo false)" "停止脚本存在且可执行"
check_item "$([ -f "restart.sh" ] && [ -x "restart.sh" ] && echo true || echo false)" "重启脚本存在且可执行"
check_item "$([ -f "logs.sh" ] && [ -x "logs.sh" ] && echo true || echo false)" "日志脚本存在且可执行"
check_item "$([ -f "run_main.sh" ] && [ -x "run_main.sh" ] && echo true || echo false)" "主脚本快捷方式存在且可执行"

echo ""
echo "📊 检查核心Python模块 (已精简为核心代码)..."
check_item "$([ -f "src/billing_audit/models/hierarchical_billing_model.py" ] && echo true || echo false)" "分层模型模块存在"
check_item "$([ -f "src/billing_audit/inference/predict_large_scale.py" ] && echo true || echo false)" "预测模块存在"
check_item "$([ -f "src/billing_audit/training/train_large_scale_model.py" ] && echo true || echo false)" "训练模块存在"
check_item "$([ -f "src/config/production_config_manager.py" ] && echo true || echo false)" "配置管理模块存在"
check_item "$([ -f "scripts/production/billing_audit_main.py" ] && echo true || echo false)" "核心生产主脚本存在"

echo ""
echo "📋 检查文档文件..."
check_item "$([ -f "README.md" ] && echo true || echo false)" "项目README存在"
check_item "$([ -f "DEPLOYMENT_README.md" ] && echo true || echo false)" "部署说明存在"
check_item "$([ -f "VERSION.md" ] && echo true || echo false)" "版本信息存在"
check_item "$([ -f "deployment/DEPLOYMENT_GUIDE.md" ] && echo true || echo false)" "部署指南存在"

echo ""
echo "🎯 验证结果总结"
echo "================================================"
echo "总检查项: $checks"
echo "通过项目: $passed"
echo "通过率: $((passed * 100 / checks))%"

if [ $passed -eq $checks ]; then
    echo ""
    echo "🎉 恭喜！部署包完整性验证通过！"
    echo "📦 部署包已就绪，可以进行部署"
else
    echo ""
    echo "⚠️ 警告：部署包可能不完整"
    echo "❌ 失败项目: $((checks - passed))"
fi

echo ""
echo "📁 部署包大小信息："
du -sh . 2>/dev/null || echo "无法计算大小"

echo ""
echo "🚀 下一步操作："
echo "1. 运行 ./start.sh 启动系统"
echo "2. 运行 ./run_main.sh --help 查看使用帮助"
echo "3. 查看 DEPLOYMENT_README.md 了解详细使用方法"
