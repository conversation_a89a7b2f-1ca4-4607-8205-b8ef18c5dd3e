# 山西电信出账稽核AI系统 v2.1.0 - x86_64离线构建包

## 📋 包含内容

### 🐳 基础镜像
- `python-3.9-slim-current.tar.gz` - Python 3.9基础镜像

### 📦 离线依赖包
- `offline_packages/python_wheels/` - Python依赖包 (37个包, ~94MB)
- `offline_packages/install_python_packages.sh` - 离线安装脚本

### 📁 项目源代码
- `src/` - 核心源代码
- `scripts/production/` - 生产脚本
- `config/` - 配置文件
- `deployment/` - 部署相关文件
- `docs/` - 项目文档

### 🔧 构建脚本
- `deployment/scripts/build_x86_64_offline.sh` - 离线构建脚本
- `deployment/scripts/verify_offline_build.sh` - 验证脚本
- `deployment/docker/Dockerfile.x86_64.offline` - 离线构建Dockerfile

## 🚀 使用方法

### 在x86_64 Linux主机上执行：

1. **解压包**
   ```bash
   tar -xzf billing_audit_x86_64_offline_complete_*.tar.gz
   cd billing_audit_x86_64_offline_complete_*
   ```

2. **执行离线构建**
   ```bash
   bash deployment/scripts/build_x86_64_offline.sh
   ```

3. **验证构建结果**
   ```bash
   bash deployment/scripts/verify_offline_build.sh
   ```

4. **制作部署包**
   ```bash
   bash deployment/scripts/create_x86_64_deployment.sh
   ```

## ⚠️ 重要说明

- **目标环境**: x86_64 Linux主机
- **网络要求**: 无需外网连接
- **Docker要求**: Docker 20.10+
- **架构要求**: 必须在x86_64系统上构建

## 📊 包大小信息

- **总大小**: ~289MB
- **基础镜像**: ~175MB
- **Python包**: ~94MB  
- **源代码**: ~20MB

