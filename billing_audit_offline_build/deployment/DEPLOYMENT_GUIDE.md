# 🚀 山西电信出账稽核AI系统 v2.1.0 - 生产环境部署指南

## 📋 部署概述

本指南提供山西电信出账稽核AI系统v2.1.0在Linux生产环境的完整部署方案，包括Docker镜像打包、传输和部署的全流程。

### **🎯 v2.1.0 核心特性**
- ✅ **生产级主脚本**: 完整的端到端自动化流程
- ✅ **Markdown执行报告**: 详细的业务分析和智能评级
- ✅ **流程修正**: 正确的数据流向(原始数据→特征工程→数据拆分→训练→评估→预测→判定)
- ✅ **日志标准化**: 175个问题点修复，100%生产标准
- ✅ **Linux一键部署**: 全自动化的Linux生产环境部署

## 🔧 系统要求

### **硬件要求**
- **CPU**: 4核心+ (推荐8核心)
- **内存**: 8GB+ (推荐16GB+)
- **磁盘**: 20GB+ 可用空间 (推荐50GB+)
- **网络**: 稳定的网络连接

### **软件要求**
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+)
- **Docker**: 20.10+ 
- **Docker Compose**: 2.0+
- **Python**: 3.9+ (容器内自动安装)

## 🚀 部署方案

### **方案一: Linux生产环境一键部署** ⭐ **推荐**

适用于有网络连接的Linux服务器，可以直接下载和构建镜像。

```bash
# 1. 克隆项目到服务器
git clone <repository-url> /opt/billing-audit-ai-source
cd /opt/billing-audit-ai-source

# 2. 运行一键部署脚本
chmod +x deployment/scripts/deploy_linux_production.sh
./deployment/scripts/deploy_linux_production.sh

# 3. 验证部署
docker ps | grep billing-audit
```

**特点**:
- ✅ 全自动安装Docker和依赖
- ✅ 自动创建项目目录结构
- ✅ 自动构建和启动服务
- ✅ 创建系统服务和管理脚本
- ✅ 完整的健康检查

### **方案二: Docker镜像离线部署**

适用于无网络连接或网络受限的生产环境。

#### **步骤1: 在开发环境导出镜像**
```bash
# 在有网络的开发环境执行
chmod +x deployment/scripts/export_docker_image.sh
./deployment/scripts/export_docker_image.sh

# 生成的部署包
ls docker_export/
# ├── images/                    # Docker镜像文件
# ├── configs/                   # 配置文件
# ├── scripts/                   # 部署脚本
# ├── docs/                      # 文档
# └── import_images.sh           # 导入脚本
```

#### **步骤2: 传输到生产服务器**
```bash
# 压缩部署包
tar -czf billing-audit-ai-v2.1.0.tar.gz docker_export/

# 传输到生产服务器 (示例)
scp billing-audit-ai-v2.1.0.tar.gz user@production-server:/opt/
```

#### **步骤3: 在生产服务器部署**
```bash
# 解压部署包
cd /opt
tar -xzf billing-audit-ai-v2.1.0.tar.gz
cd docker_export

# 导入镜像
chmod +x import_images.sh
./import_images.sh

# 运行部署脚本
chmod +x scripts/deploy_linux_production.sh
./scripts/deploy_linux_production.sh
```

### **方案三: 基础Docker部署**

适用于已有Docker环境的快速部署。

```bash
# 1. 进入部署目录
cd deployment

# 2. 运行基础部署
chmod +x scripts/deploy_production.sh
./scripts/deploy_production.sh

# 3. 可选: 启用额外服务
./scripts/deploy_production.sh --with-database --with-monitoring
```

## 📁 部署后目录结构

```
/opt/billing-audit-ai/                    # 主项目目录
├── data/
│   ├── input/                            # 输入数据目录
│   ├── output/                           # 输出结果目录
│   │   ├── reports/markdown/             # Markdown执行报告 (v2.1.0)
│   │   ├── models/                       # 模型输出
│   │   ├── data/                         # 数据输出
│   │   └── visualizations/               # 可视化结果
│   └── backup/                           # 备份目录
├── models/                               # 模型文件目录
├── logs/                                 # 日志文件目录
├── src/                                  # 源代码
├── scripts/                              # 脚本文件
├── deployment/                           # 部署配置
├── start.sh                              # 启动脚本
├── stop.sh                               # 停止脚本
├── restart.sh                            # 重启脚本
├── logs.sh                               # 日志查看脚本
├── shell.sh                              # 进入容器脚本
└── run_main.sh                           # 主脚本快捷方式
```

## 🔧 系统管理

### **服务管理**
```bash
# 启动服务
sudo systemctl start billing-audit-ai

# 停止服务
sudo systemctl stop billing-audit-ai

# 重启服务
sudo systemctl restart billing-audit-ai

# 查看状态
sudo systemctl status billing-audit-ai

# 查看日志
sudo journalctl -u billing-audit-ai -f
```

### **容器管理**
```bash
# 查看容器状态
docker ps | grep billing-audit

# 查看容器日志
docker logs -f billing-audit-ai

# 进入容器
docker exec -it billing-audit-ai bash

# 重启容器
docker restart billing-audit-ai
```

### **快捷脚本**
```bash
cd /opt/billing-audit-ai

# 启动系统
./start.sh

# 停止系统
./stop.sh

# 重启系统
./restart.sh

# 查看日志
./logs.sh

# 进入容器
./shell.sh

# 运行主脚本
./run_main.sh full --input /data/input/data.csv
```

## 🚀 使用方法

### **⭐ 推荐: 生产级主脚本** (v2.1.0)

#### **完整流程执行**
```bash
# 进入项目目录
cd /opt/billing-audit-ai

# 将数据文件放入输入目录
cp your_data.csv data/input/

# 运行完整AI流程
./run_main.sh full --input /data/input/your_data.csv --batch-size 1000

# 输出结果:
# - data/output/reports/markdown/     # 详细的Markdown执行报告
# - data/output/models/               # 训练好的模型
# - data/output/data/                 # 处理后的数据
# - data/output/                      # 预测和判定结果
```

#### **模块化执行**
```bash
# 只执行特征工程
./run_main.sh feature-engineering --input /data/input/raw_data.csv

# 只执行模型训练
./run_main.sh training --input /data/input/processed_data.csv

# 只执行预测
./run_main.sh prediction --input /data/input/test_data.csv

# 只执行收费判定
./run_main.sh judgment --input /data/input/prediction_results.csv
```

### **传统方式: 单独脚本**

#### **模型训练**
```bash
docker exec billing-audit-ai python src/billing_audit/training/train_large_scale_model.py \
  --input /data/input/training_data.csv \
  --output /models/ \
  --batch-size 50000
```

#### **批量预测**
```bash
docker exec billing-audit-ai python src/billing_audit/inference/predict_large_scale.py \
  --input /data/input/predict_data.csv \
  --model /models/large_scale_model_latest.pkl \
  --feature-engineer /models/large_scale_feature_engineer_latest.pkl \
  --output /data/output/predictions.csv
```

#### **收费合理性判定**
```bash
docker exec billing-audit-ai python src/billing_audit/inference/large_scale_billing_judge.py \
  --input /data/input/billing_data.csv \
  --model /models/large_scale_model_latest.pkl \
  --feature-engineer /models/large_scale_feature_engineer_latest.pkl \
  --output /data/output/billing_judgments.csv
```

## 🔍 监控和维护

### **系统监控**
```bash
# 查看系统资源使用
docker stats billing-audit-ai

# 查看磁盘使用
df -h /opt/billing-audit-ai

# 查看内存使用
free -h

# 查看CPU使用
top
```

### **日志监控**
```bash
# 查看应用日志
tail -f /opt/billing-audit-ai/logs/*.log

# 查看容器日志
docker logs -f billing-audit-ai

# 查看系统日志
sudo journalctl -u billing-audit-ai -f
```

### **备份和恢复**
```bash
# 备份重要数据
tar -czf backup_$(date +%Y%m%d).tar.gz \
  /opt/billing-audit-ai/data/output \
  /opt/billing-audit-ai/models \
  /opt/billing-audit-ai/logs

# 备份配置
cp -r /opt/billing-audit-ai/config /backup/config_$(date +%Y%m%d)
```

## 🆘 故障排除

### **常见问题**

#### **1. 容器启动失败**
```bash
# 查看详细错误
docker logs billing-audit-ai

# 检查配置文件
docker exec billing-audit-ai python -c "from src.config.production_config_manager import get_config_manager; get_config_manager()"

# 重新构建镜像
docker build -f deployment/docker/Dockerfile -t billing-audit-ai:latest .
```

#### **2. 内存不足**
```bash
# 查看内存使用
free -h
docker stats

# 调整批处理大小
./run_main.sh full --input /data/input/data.csv --batch-size 500
```

#### **3. 磁盘空间不足**
```bash
# 清理Docker缓存
docker system prune -a

# 清理旧日志
find /opt/billing-audit-ai/logs -name "*.log" -mtime +7 -delete

# 清理临时文件
rm -rf /opt/billing-audit-ai/data/output/temp/*
```

#### **4. 权限问题**
```bash
# 修复目录权限
sudo chown -R $USER:$USER /opt/billing-audit-ai
chmod -R 755 /opt/billing-audit-ai

# 修复脚本权限
chmod +x /opt/billing-audit-ai/*.sh
```

## 📞 技术支持

### **获取帮助**
```bash
# 查看主脚本帮助
./run_main.sh --help

# 查看部署脚本帮助
./deployment/scripts/deploy_linux_production.sh --help

# 运行系统验证
./deployment/scripts/validate_deployment.sh
```

### **联系信息**
- 📧 技术支持: [技术支持邮箱]
- 📋 文档中心: `/opt/billing-audit-ai/docs/`
- 🔗 项目仓库: [项目仓库地址]

---

**版本**: v2.1.0 (流程修正版)  
**更新时间**: 2025-07-27  
**部署状态**: ✅ 生产就绪  
**验证状态**: ✅ 全面验证通过
