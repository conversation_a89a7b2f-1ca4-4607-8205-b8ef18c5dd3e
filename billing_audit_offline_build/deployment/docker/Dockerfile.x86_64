# 山西电信出账稽核AI系统 v2.1.0 - x86_64跨平台优化Docker镜像
# 支持多架构构建，专为x86_64 Linux主机优化

# 平台参数声明
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETOS
ARG TARGETARCH

# 使用平台参数的基础镜像
FROM --platform=$TARGETPLATFORM python:3.9-slim

# 显示构建信息
RUN echo "Building on $BUILDPLATFORM, targeting $TARGETPLATFORM" > /build_info.txt

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV BILLING_AUDIT_ENV=production
ENV DEBIAN_FRONTEND=noninteractive
ENV BILLING_AUDIT_VERSION=v2.1.0-x86_64
ENV BILLING_AUDIT_TARGET_ARCH=$TARGETARCH
ENV BILLING_AUDIT_TARGET_OS=$TARGETOS

# 设置时区为中国时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 根据目标架构安装系统依赖
RUN apt-get update && \
    if [ "$TARGETARCH" = "amd64" ]; then \
        echo "Installing packages for x86_64/amd64 architecture..."; \
        apt-get install -y --no-install-recommends \
            gcc \
            libomp-dev \
            procps \
            iputils-ping \
            telnet \
            net-tools \
            curl \
            wget \
            vim \
            htop \
            tree \
            less; \
    elif [ "$TARGETARCH" = "arm64" ]; then \
        echo "Installing packages for ARM64 architecture..."; \
        apt-get install -y --no-install-recommends \
            gcc \
            libomp-dev \
            procps \
            iputils-ping \
            telnet \
            net-tools \
            curl \
            wget \
            vim \
            htop \
            tree \
            less; \
    else \
        echo "Installing packages for unknown architecture: $TARGETARCH"; \
        apt-get install -y --no-install-recommends \
            gcc \
            libomp-dev \
            procps \
            curl \
            wget; \
    fi && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

# 创建必要的目录
RUN mkdir -p \
    /app/outputs/models \
    /app/outputs/data \
    /app/outputs/reports \
    /app/outputs/temp \
    /app/outputs/visualizations \
    /app/logs \
    /tmp/billing_audit

# 复制requirements文件并安装Python依赖
COPY deployment/docker/requirements.slim.txt ./requirements.txt

# 根据目标架构优化Python包安装
RUN echo "Installing Python packages for $TARGETARCH architecture..." && \
    if [ "$TARGETARCH" = "amd64" ]; then \
        # x86_64架构优化安装
        pip install --no-cache-dir --no-compile -r requirements.txt \
            --extra-index-url https://download.pytorch.org/whl/cpu; \
    else \
        # 其他架构标准安装
        pip install --no-cache-dir --no-compile -r requirements.txt; \
    fi && \
    pip cache purge && \
    find /usr/local/lib/python3.9 -name "*.pyc" -delete && \
    find /usr/local/lib/python3.9 -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 复制核心项目文件
COPY src/ ./src/
COPY scripts/production/ ./scripts/production/
COPY config/ ./config/

# 设置权限
RUN chmod +x scripts/production/*.py

# 创建架构信息文件
RUN echo "$TARGETARCH" > /app/.target_arch && \
    echo "$TARGETOS" > /app/.target_os && \
    echo "Built for $TARGETARCH on $TARGETOS" > /app/.build_info && \
    echo "Build platform: $BUILDPLATFORM" >> /app/.build_info && \
    echo "Target platform: $TARGETPLATFORM" >> /app/.build_info

# 创建非root用户
RUN useradd -m -u 1000 billing_user && \
    chown -R billing_user:billing_user /app /tmp/billing_audit

# 切换到非root用户
USER billing_user

# 架构验证脚本
RUN echo '#!/bin/bash' > /app/verify_arch.sh && \
    echo 'echo "Container Architecture: $(uname -m)"' >> /app/verify_arch.sh && \
    echo 'echo "Target Architecture: $(cat /app/.target_arch)"' >> /app/verify_arch.sh && \
    echo 'echo "Target OS: $(cat /app/.target_os)"' >> /app/verify_arch.sh && \
    echo 'echo "Build Info:"' >> /app/verify_arch.sh && \
    echo 'cat /app/.build_info' >> /app/verify_arch.sh && \
    chmod +x /app/verify_arch.sh

# 健康检查
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=2 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 暴露端口
EXPOSE 8000

# 默认命令
CMD ["python", "scripts/production/keep_alive.py"]

# 构建时标签
LABEL maintainer="九思计费专家团队"
LABEL version="v2.1.0"
LABEL architecture="$TARGETARCH"
LABEL platform="$TARGETPLATFORM"
LABEL description="山西电信出账稽核AI系统 - x86_64跨平台优化版"
