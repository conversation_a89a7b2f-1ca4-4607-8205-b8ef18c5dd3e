# 山西电信出账稽核AI系统 v2.1.0 - x86_64本地构建Docker镜像
# 使用本地基础镜像，避免网络依赖

# 使用本地标签的python:3.9-slim镜像（避免网络拉取）
FROM local-python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV BILLING_AUDIT_ENV=production
ENV DEBIAN_FRONTEND=noninteractive
ENV BILLING_AUDIT_VERSION=v2.1.0-x86_64
ENV BILLING_AUDIT_TARGET_ARCH=amd64
ENV BILLING_AUDIT_TARGET_OS=linux

# 设置时区为中国时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装系统依赖和常用工具
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libomp-dev \
    procps \
    iputils-ping \
    telnet \
    net-tools \
    curl \
    wget \
    vim \
    htop \
    tree \
    less \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建必要的目录
RUN mkdir -p \
    /app/outputs/models \
    /app/outputs/data \
    /app/outputs/reports \
    /app/outputs/temp \
    /app/outputs/visualizations \
    /app/logs \
    /tmp/billing_audit

# 复制requirements文件并安装Python依赖
COPY deployment/docker/requirements.slim.txt ./requirements.txt

# 安装Python包
RUN echo "Installing Python packages for x86_64 architecture..." && \
    pip install --no-cache-dir --no-compile -r requirements.txt && \
    pip cache purge && \
    find /usr/local/lib/python3.9 -name "*.pyc" -delete && \
    find /usr/local/lib/python3.9 -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 复制核心项目文件
COPY src/ ./src/
COPY scripts/production/ ./scripts/production/
COPY config/ ./config/

# 设置权限
RUN chmod +x scripts/production/*.py

# 创建架构信息文件
RUN echo "amd64" > /app/.target_arch && \
    echo "linux" > /app/.target_os && \
    echo "Built for x86_64 (amd64) on linux" > /app/.build_info && \
    echo "Build method: Local Docker build with --platform linux/amd64" >> /app/.build_info && \
    echo "Base image: python:3.9-slim (local)" >> /app/.build_info

# 创建非root用户
RUN useradd -m -u 1000 billing_user && \
    chown -R billing_user:billing_user /app /tmp/billing_audit

# 切换到非root用户
USER billing_user

# 架构验证脚本
RUN echo '#!/bin/bash' > /app/verify_arch.sh && \
    echo 'echo "Container Architecture: $(uname -m)"' >> /app/verify_arch.sh && \
    echo 'echo "Target Architecture: $(cat /app/.target_arch)"' >> /app/verify_arch.sh && \
    echo 'echo "Target OS: $(cat /app/.target_os)"' >> /app/verify_arch.sh && \
    echo 'echo "Build Info:"' >> /app/verify_arch.sh && \
    echo 'cat /app/.build_info' >> /app/verify_arch.sh && \
    chmod +x /app/verify_arch.sh

# 健康检查
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=2 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 暴露端口
EXPOSE 8000

# 创建后台常驻脚本
RUN echo '#!/usr/bin/env python3' > /app/scripts/production/keep_alive.py && \
    echo 'import time' >> /app/scripts/production/keep_alive.py && \
    echo 'import signal' >> /app/scripts/production/keep_alive.py && \
    echo 'import sys' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'def signal_handler(sig, frame):' >> /app/scripts/production/keep_alive.py && \
    echo '    print("\\n收到停止信号，正在优雅退出...")' >> /app/scripts/production/keep_alive.py && \
    echo '    sys.exit(0)' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'signal.signal(signal.SIGINT, signal_handler)' >> /app/scripts/production/keep_alive.py && \
    echo 'signal.signal(signal.SIGTERM, signal_handler)' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'print("🚀 山西电信出账稽核AI系统 v2.1.0 (x86_64) 已启动")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("📊 容器架构:", "$(uname -m)")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("🎯 目标架构: x86_64 (amd64)")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("💡 使用方法: docker exec -it <container> bash")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("⏰ 容器将保持运行状态...")' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'try:' >> /app/scripts/production/keep_alive.py && \
    echo '    while True:' >> /app/scripts/production/keep_alive.py && \
    echo '        time.sleep(60)' >> /app/scripts/production/keep_alive.py && \
    echo 'except KeyboardInterrupt:' >> /app/scripts/production/keep_alive.py && \
    echo '    print("\\n容器正在停止...")' >> /app/scripts/production/keep_alive.py && \
    chmod +x /app/scripts/production/keep_alive.py

# 默认命令
CMD ["python", "scripts/production/keep_alive.py"]

# 构建时标签
LABEL maintainer="九思计费专家团队"
LABEL version="v2.1.0"
LABEL architecture="amd64"
LABEL platform="linux/amd64"
LABEL description="山西电信出账稽核AI系统 - x86_64本地构建版"
LABEL build.method="local-docker-build"
