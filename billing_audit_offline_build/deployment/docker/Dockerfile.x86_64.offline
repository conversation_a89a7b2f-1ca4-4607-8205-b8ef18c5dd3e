# 山西电信出账稽核AI系统 v2.1.0 - x86_64离线构建Docker镜像
# 使用离线包进行构建，适用于无网络环境

FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV BILLING_AUDIT_ENV=production
ENV DEBIAN_FRONTEND=noninteractive
ENV BILLING_AUDIT_VERSION=v2.1.0-x86_64-offline
ENV BILLING_AUDIT_TARGET_ARCH=amd64
ENV BILLING_AUDIT_TARGET_OS=linux

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制离线包
COPY offline_packages/ /offline_packages/

# 安装系统依赖（使用apt-get，因为我们在x86_64主机上构建）
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libc6-dev \
    libomp-dev \
    build-essential \
    pkg-config \
    procps \
    iputils-ping \
    telnet \
    net-tools \
    curl \
    wget \
    vim \
    htop \
    tree \
    less \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建目录
RUN mkdir -p \
    /app/outputs/models \
    /app/outputs/data \
    /app/outputs/reports \
    /app/outputs/temp \
    /app/outputs/visualizations \
    /app/logs \
    /tmp/billing_audit

# 安装Python依赖（离线）
RUN pip install --no-index --find-links /offline_packages/python_wheels \
    pandas numpy scikit-learn xgboost lightgbm joblib \
    tqdm psutil matplotlib seaborn plotly openpyxl xlsxwriter \
    python-dateutil pytz requests urllib3 certifi \
    charset-normalizer idna six setuptools wheel && \
    pip cache purge && \
    find /usr/local/lib/python3.9 -name "*.pyc" -delete && \
    find /usr/local/lib/python3.9 -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 复制项目文件
COPY src/ ./src/
COPY scripts/production/ ./scripts/production/
COPY config/ ./config/

# 设置权限
RUN chmod +x scripts/production/*.py

# 创建架构信息文件
RUN echo "amd64" > /app/.target_arch && \
    echo "linux" > /app/.target_os && \
    echo "Built for x86_64 (amd64) on linux - OFFLINE BUILD" > /app/.build_info && \
    echo "Build method: Offline Docker build on x86_64 host" >> /app/.build_info && \
    echo "Base image: python:3.9-slim (x86_64)" >> /app/.build_info && \
    echo "Build date: $(date)" >> /app/.build_info

# 创建非root用户
RUN useradd -m -u 1000 billing_user && \
    chown -R billing_user:billing_user /app /tmp/billing_audit

# 切换到非root用户
USER billing_user

# 架构验证脚本
RUN echo '#!/bin/bash' > /app/verify_arch.sh && \
    echo 'echo "Container Architecture: $(uname -m)"' >> /app/verify_arch.sh && \
    echo 'echo "Target Architecture: $(cat /app/.target_arch)"' >> /app/verify_arch.sh && \
    echo 'echo "Target OS: $(cat /app/.target_os)"' >> /app/verify_arch.sh && \
    echo 'echo "Build Info:"' >> /app/verify_arch.sh && \
    echo 'cat /app/.build_info' >> /app/verify_arch.sh && \
    chmod +x /app/verify_arch.sh

# 创建后台常驻脚本
RUN echo '#!/usr/bin/env python3' > /app/scripts/production/keep_alive.py && \
    echo 'import time' >> /app/scripts/production/keep_alive.py && \
    echo 'import signal' >> /app/scripts/production/keep_alive.py && \
    echo 'import sys' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'def signal_handler(sig, frame):' >> /app/scripts/production/keep_alive.py && \
    echo '    print("\\n收到停止信号，正在优雅退出...")' >> /app/scripts/production/keep_alive.py && \
    echo '    sys.exit(0)' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'signal.signal(signal.SIGINT, signal_handler)' >> /app/scripts/production/keep_alive.py && \
    echo 'signal.signal(signal.SIGTERM, signal_handler)' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'print("🚀 山西电信出账稽核AI系统 v2.1.0 (x86_64-offline) 已启动")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("📊 容器架构:", "$(uname -m)")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("🎯 目标架构: x86_64 (amd64)")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("🔧 构建方式: 离线构建")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("💡 使用方法: docker exec -it <container> bash")' >> /app/scripts/production/keep_alive.py && \
    echo 'print("⏰ 容器将保持运行状态...")' >> /app/scripts/production/keep_alive.py && \
    echo '' >> /app/scripts/production/keep_alive.py && \
    echo 'try:' >> /app/scripts/production/keep_alive.py && \
    echo '    while True:' >> /app/scripts/production/keep_alive.py && \
    echo '        time.sleep(60)' >> /app/scripts/production/keep_alive.py && \
    echo 'except KeyboardInterrupt:' >> /app/scripts/production/keep_alive.py && \
    echo '    print("\\n容器正在停止...")' >> /app/scripts/production/keep_alive.py && \
    chmod +x /app/scripts/production/keep_alive.py

# 健康检查
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=2 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 暴露端口
EXPOSE 8000

# 默认命令
CMD ["python", "scripts/production/keep_alive.py"]

# 构建时标签
LABEL maintainer="九思计费专家团队"
LABEL version="v2.1.0"
LABEL architecture="amd64"
LABEL platform="linux/amd64"
LABEL description="山西电信出账稽核AI系统 - x86_64离线构建版"
LABEL build.method="offline-docker-build"
LABEL build.target="x86_64-linux-host"
