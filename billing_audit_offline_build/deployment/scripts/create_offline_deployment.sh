#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - 离线部署包制作脚本
# 支持完整功能的离线部署，包含配置、数据、日志挂载

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置变量
VERSION="v2.1.0"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
EXPORT_DIR="billing_audit_production_${VERSION}_${TIMESTAMP}"
IMAGES_DIR="${EXPORT_DIR}/images"
SCRIPTS_DIR="${EXPORT_DIR}/scripts"
CONFIG_DIR="${EXPORT_DIR}/config"
DATA_DIR="${EXPORT_DIR}/data"
DOCS_DIR="${EXPORT_DIR}/docs"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 检查Docker环境
check_docker() {
    log_step "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 创建导出目录结构
create_export_structure() {
    log_step "创建离线部署包目录结构..."
    
    # 创建主目录
    mkdir -p "$EXPORT_DIR"
    mkdir -p "$IMAGES_DIR"
    mkdir -p "$SCRIPTS_DIR"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$DOCS_DIR"
    
    # 创建数据子目录
    mkdir -p "$DATA_DIR/input"
    mkdir -p "$DATA_DIR/output"
    mkdir -p "$DATA_DIR/output/models"
    mkdir -p "$DATA_DIR/output/data"
    mkdir -p "$DATA_DIR/output/reports"
    mkdir -p "$DATA_DIR/output/reports/markdown"
    mkdir -p "$DATA_DIR/models"
    mkdir -p "$DATA_DIR/logs"
    mkdir -p "$DATA_DIR/backup"
    
    log_info "目录结构创建完成"
}

# 构建Docker镜像
build_images() {
    log_step "构建Docker镜像..."
    
    # 构建主应用镜像
    log_info "构建主应用镜像..."
    docker build -f deployment/docker/Dockerfile -t billing-audit-ai:${VERSION} .
    docker tag billing-audit-ai:${VERSION} billing-audit-ai:latest
    
    log_success "Docker镜像构建完成"
}

# 导出Docker镜像
export_images() {
    log_step "导出Docker镜像..."
    
    # 导出主应用镜像
    log_info "导出主应用镜像..."
    docker save billing-audit-ai:${VERSION} | gzip > "${IMAGES_DIR}/billing-audit-ai-${VERSION}.tar.gz"
    
    # 导出基础镜像
    log_info "导出Python基础镜像..."
    docker pull python:3.9-slim
    docker save python:3.9-slim | gzip > "${IMAGES_DIR}/python-3.9-slim.tar.gz"
    
    # 导出可选服务镜像（使用本地镜像）
    log_info "导出PostgreSQL镜像..."
    if docker images | grep -q "postgres.*13"; then
        docker save postgres:13 | gzip > "${IMAGES_DIR}/postgres-13.tar.gz"
    else
        log_warn "PostgreSQL镜像不存在，跳过导出"
    fi

    log_info "导出Redis镜像..."
    if docker images | grep -q "redis.*alpine"; then
        # 使用本地可用的Redis镜像
        REDIS_IMAGE=$(docker images | grep "redis.*alpine" | head -1 | awk '{print $1":"$2}')
        docker save $REDIS_IMAGE | gzip > "${IMAGES_DIR}/redis-alpine.tar.gz"
        log_info "已导出Redis镜像: $REDIS_IMAGE"
    else
        log_warn "Redis镜像不存在，跳过导出"
    fi
    
    log_success "镜像导出完成"
}

# 复制生产必需文件（精简版）
copy_production_files() {
    log_step "复制生产必需文件..."

    # 只复制生产必需的文件
    cp deployment/config/production_config.json "$EXPORT_DIR/"

    # 创建精简的README
    cat > "$EXPORT_DIR/README.md" << 'EOF'
# 山西电信出账稽核AI系统 v2.1.0 - 生产部署包

## 🚀 一键部署

```bash
# 1. 解压部署包
tar -xzf billing_audit_production_v2.1.0_*.tar.gz

# 2. 进入目录
cd billing_audit_production_v2.1.0_*

# 3. 准备数据文件
cp /path/to/your/data.txt data/input/ofrm_result.txt

# 4. 一键部署运行
bash deploy.sh
```

## 📋 部署包内容

- `deploy.sh` - 一键部署运行脚本
- `production_config.json` - 生产配置文件
- `images/` - Docker镜像文件
- `data/` - 数据目录（输入输出）
- `README.md` - 本说明文件

## 💡 特性

- ✅ 支持任意目录部署
- ✅ 自动处理权限问题
- ✅ 智能环境检测
- ✅ 一键完成所有操作
- ✅ 详细的执行反馈

## 📞 技术支持

如遇问题，请提供：
- 错误日志信息
- 系统环境信息
- Docker版本信息
EOF

    log_info "生产必需文件复制完成"
}

# 创建部署脚本
create_deployment_scripts() {
    log_step "创建部署脚本..."
    
    # 创建镜像加载脚本
    cat > "${SCRIPTS_DIR}/load_images.sh" << 'EOF'
#!/bin/bash
# 加载Docker镜像脚本

set -e

echo "🚀 开始加载Docker镜像..."

# 加载镜像
echo "📦 加载主应用镜像..."
docker load < images/billing-audit-ai-v2.1.0.tar.gz

echo "📦 加载Python基础镜像..."
docker load < images/python-3.9-slim.tar.gz

echo "📦 加载PostgreSQL镜像..."
docker load < images/postgres-13.tar.gz

echo "📦 加载Redis镜像..."
if [ -f "images/redis-alpine.tar.gz" ]; then
    docker load < images/redis-alpine.tar.gz
else
    echo "⚠️ Redis镜像文件不存在，跳过加载"
fi

echo "✅ 所有镜像加载完成"
docker images | grep -E "(billing-audit-ai|python|postgres|redis)"
EOF

    # 创建一键部署脚本
    cat > "${SCRIPTS_DIR}/deploy.sh" << 'EOF'
#!/bin/bash
# 一键部署脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_step "山西电信出账稽核AI系统 v2.1.0 一键部署"

# 1. 加载镜像
log_step "1. 加载Docker镜像..."
bash scripts/load_images.sh

# 2. 创建环境变量文件
log_step "2. 创建环境变量..."
cat > .env << 'ENVEOF'
BILLING_AUDIT_ENV=production
BILLING_AUDIT_VERSION=v2.1.0
DATA_INPUT_DIR=/data/input
DATA_OUTPUT_DIR=/data/output
MODEL_DIR=/models
LOGS_DIR=/logs
PYTHONPATH=/app
OMP_NUM_THREADS=4
DB_PASSWORD=billing_password_$(date +%s)
ENVEOF

# 3. 启动服务（使用预构建的镜像，不重新构建）
log_step "3. 启动服务..."
# 修改docker-compose.yml，使用预构建的镜像
sed -i.bak 's/build: \./image: billing-audit-ai:v2.1.0/' config/docker-compose.yml
docker-compose -f config/docker-compose.yml up -d billing-audit-ai

# 4. 等待服务启动
log_step "4. 等待服务启动..."
sleep 15

# 5. 健康检查
log_step "5. 运行健康检查..."
docker exec billing-audit-ai python -c "
from src.config.production_config_manager import get_config_manager
config = get_config_manager()
print('✅ 配置加载成功')
print(f'项目: {config.get(\"project.name\")}')
print(f'版本: {config.get(\"project.version\")}')
"

log_info "🎉 部署完成！"
echo ""
echo "📊 服务状态:"
docker-compose -f config/docker-compose.yml ps
echo ""
echo "🚀 使用示例:"
echo "  完整流程: bash scripts/run_full_pipeline.sh"
echo "  查看日志: docker-compose -f config/docker-compose.yml logs -f"
echo "  进入容器: docker exec -it billing-audit-ai bash"
EOF

    # 创建运行脚本
    cat > "${SCRIPTS_DIR}/run_full_pipeline.sh" << 'EOF'
#!/bin/bash
# 运行完整流程脚本

set -e

echo "🚀 运行山西电信出账稽核AI系统完整流程..."

# 检查输入数据
if [ ! -f "data/input/ofrm_result.txt" ]; then
    echo "❌ 输入数据文件不存在: data/input/ofrm_result.txt"
    echo "请将数据文件放置到 data/input/ 目录下"
    exit 1
fi

# 运行完整流程
docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \
    --input /data/input/ofrm_result.txt \
    --algorithm hierarchical \
    --batch-size 1000

echo "✅ 完整流程执行完成！"
echo ""
echo "📁 输出文件位置:"
echo "  模型文件: data/output/models/"
echo "  预测结果: data/output/data/"
echo "  执行报告: data/output/reports/"
echo "  Markdown报告: data/output/reports/markdown/"
echo "  日志文件: data/logs/"
EOF

    # 创建停止脚本
    cat > "${SCRIPTS_DIR}/stop.sh" << 'EOF'
#!/bin/bash
# 停止服务脚本

echo "🛑 停止山西电信出账稽核AI系统..."
docker-compose -f config/docker-compose.yml down

echo "✅ 服务已停止"
EOF

    # 创建查看日志脚本
    cat > "${SCRIPTS_DIR}/view_logs.sh" << 'EOF'
#!/bin/bash
# 查看日志脚本

echo "📋 查看系统日志..."
docker-compose -f config/docker-compose.yml logs -f billing-audit-ai
EOF

    # 设置执行权限
    chmod +x "${SCRIPTS_DIR}"/*.sh
    
    log_info "部署脚本创建完成"
}

# 创建文档
create_documentation() {
    log_step "创建部署文档..."
    
    cat > "${DOCS_DIR}/README.md" << 'EOF'
# 山西电信出账稽核AI系统 v2.1.0 - 离线部署包

## 📋 部署包内容

```
billing_audit_offline_v2.1.0_YYYYMMDD_HHMMSS/
├── images/                          # Docker镜像文件
│   ├── billing-audit-ai-v2.1.0.tar.gz
│   ├── python-3.9-slim.tar.gz
│   ├── postgres-13.tar.gz
│   └── redis-6-alpine.tar.gz
├── scripts/                         # 部署和运行脚本
│   ├── load_images.sh              # 加载镜像
│   ├── deploy.sh                   # 一键部署
│   ├── run_full_pipeline.sh        # 运行完整流程
│   ├── stop.sh                     # 停止服务
│   └── view_logs.sh                # 查看日志
├── config/                          # 配置文件
│   ├── docker-compose.yml
│   ├── Dockerfile
│   ├── production_config.json
│   └── requirements.txt
├── data/                           # 数据目录（挂载点）
│   ├── input/                      # 输入数据
│   ├── output/                     # 输出结果
│   ├── models/                     # 模型文件
│   ├── logs/                       # 日志文件
│   └── backup/                     # 备份文件
└── docs/                           # 文档
    └── README.md                   # 本文档
```

## 🚀 快速部署

### 1. 系统要求
- Docker 20.10+
- Docker Compose 1.29+
- 内存: 8GB+
- 磁盘: 20GB+

### 2. 一键部署
```bash
# 进入部署包目录
cd billing_audit_offline_v2.1.0_YYYYMMDD_HHMMSS/

# 执行一键部署
bash scripts/deploy.sh
```

### 3. 运行完整流程
```bash
# 将数据文件放置到 data/input/ 目录
cp your_data.txt data/input/ofrm_result.txt

# 运行完整流程
bash scripts/run_full_pipeline.sh
```

## 📊 功能说明

### 支持的运行模式

#### 1. 完整流程（推荐）
```bash
docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \
    --input /data/input/ofrm_result.txt \
    --algorithm hierarchical \
    --batch-size 1000
```

#### 2. 单个环节
```bash
# 特征工程
docker exec billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \
    --input /data/input/data.txt --batch-size 1000

# 模型训练
docker exec billing-audit-ai python scripts/production/billing_audit_main.py training \
    --input /data/input/data.txt --algorithm hierarchical --batch-size 1000

# 模型评估
docker exec billing-audit-ai python scripts/production/billing_audit_main.py evaluation \
    --input /data/input/data.txt --batch-size 1000

# 模型预测
docker exec billing-audit-ai python scripts/production/billing_audit_main.py prediction \
    --input /data/input/data.txt --batch-size 1000

# 收费判定
docker exec billing-audit-ai python scripts/production/billing_audit_main.py judgment \
    --input /data/input/data.txt --batch-size 1000
```

## 🔧 管理命令

```bash
# 查看服务状态
docker-compose -f config/docker-compose.yml ps

# 查看日志
bash scripts/view_logs.sh

# 进入容器
docker exec -it billing-audit-ai bash

# 停止服务
bash scripts/stop.sh

# 重启服务
docker-compose -f config/docker-compose.yml restart billing-audit-ai
```

## 📁 数据目录说明

- **data/input/**: 放置输入数据文件
- **data/output/models/**: 训练生成的模型文件
- **data/output/data/**: 预测结果文件
- **data/output/reports/**: 执行报告（JSON格式）
- **data/output/reports/markdown/**: Markdown格式报告
- **data/logs/**: 系统日志文件
- **data/backup/**: 备份文件

## ⚠️ 注意事项

1. 确保Docker服务正在运行
2. 确保有足够的磁盘空间（建议20GB+）
3. 输入数据文件必须符合系统要求的格式
4. 首次运行可能需要较长时间进行模型训练

## 🆘 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose -f config/docker-compose.yml logs billing-audit-ai
   ```

2. **内存不足**
   ```bash
   # 调整批处理大小
   --batch-size 500
   ```

3. **权限问题**
   ```bash
   # 修复权限
   sudo chown -R $USER:$USER data/
   ```

## 📞 技术支持

如遇问题，请联系技术支持团队，并提供：
- 错误日志
- 系统环境信息
- 数据文件格式
EOF

    log_info "部署文档创建完成"
}

# 创建压缩包
create_package() {
    log_step "创建离线部署包..."
    
    # 计算大小
    PACKAGE_SIZE=$(du -sh "$EXPORT_DIR" | cut -f1)
    
    # 创建压缩包
    tar -czf "${EXPORT_DIR}.tar.gz" "$EXPORT_DIR"
    
    # 计算压缩包大小
    COMPRESSED_SIZE=$(du -sh "${EXPORT_DIR}.tar.gz" | cut -f1)
    
    log_success "离线部署包创建完成"
    log_info "部署包大小: $PACKAGE_SIZE"
    log_info "压缩包大小: $COMPRESSED_SIZE"
    log_info "压缩包位置: ${EXPORT_DIR}.tar.gz"
}

# 清理临时文件
cleanup() {
    log_step "清理临时文件..."
    rm -rf "$EXPORT_DIR"
    log_info "临时文件清理完成"
}

# 显示完成信息
show_completion_info() {
    log_success "🎉 离线部署包制作完成！"
    echo ""
    echo "📦 部署包信息:"
    echo "  文件名: ${EXPORT_DIR}.tar.gz"
    echo "  大小: $(du -sh "${EXPORT_DIR}.tar.gz" | cut -f1)"
    echo ""
    echo "🚀 部署步骤:"
    echo "  1. 将压缩包传输到目标服务器"
    echo "  2. 解压: tar -xzf ${EXPORT_DIR}.tar.gz"
    echo "  3. 进入目录: cd ${EXPORT_DIR}"
    echo "  4. 执行部署: bash scripts/deploy.sh"
    echo "  5. 运行系统: bash scripts/run_full_pipeline.sh"
    echo ""
    echo "📋 包含功能:"
    echo "  ✅ 完整的7步端到端流程"
    echo "  ✅ 单个环节独立运行"
    echo "  ✅ 分层建模算法"
    echo "  ✅ Markdown报告生成"
    echo "  ✅ 配置、数据、日志挂载"
    echo "  ✅ 一键部署和管理脚本"
}

# 主函数
main() {
    log_info "开始制作山西电信出账稽核AI系统离线部署包..."
    
    check_docker
    create_export_structure
    build_images
    export_images
    copy_configs
    create_deployment_scripts
    create_documentation
    create_package
    cleanup
    show_completion_info
    
    log_success "离线部署包制作完成！"
}

# 运行主函数
main "$@"
