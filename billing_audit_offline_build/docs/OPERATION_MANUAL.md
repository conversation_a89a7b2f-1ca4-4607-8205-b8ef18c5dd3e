# 山西电信出账稽核AI系统 - 操作手册

## 📋 快速操作指南

### 🚀 一键部署
```bash
# 1. 解压部署包
tar -xzf billing_audit_production_v2.1.0_20250802_slim.tar.gz
cd billing_audit_production_v2.1.0_20250730_083326

# 2. 准备数据文件
cp /path/to/your/data.csv data/input/

# 3. 一键部署运行
bash deploy.sh
```

### ⚡ 一键运行 - 全流程
```bash
# 方式1: 容器内执行
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py full \
  --input /app/data/input/your_data.csv \
  --algorithm hierarchical \
  --batch-size 1000

# 方式2: 挂载执行
docker run --rm \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/outputs:/app/outputs \
  billing-audit-ai:v2.1.0-slim-fixed \
  python scripts/production/billing_audit_main.py full \
  --input data/input/your_data.csv \
  --algorithm hierarchical \
  --batch-size 1000
```

## 🔧 单流程操作

### 1️⃣ 特征工程
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \
  --input /app/data/input/your_data.csv \
  --batch-size 1000
```

### 2️⃣ 模型训练
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py training \
  --input /app/data/input/your_data.csv \
  --algorithm hierarchical \
  --batch-size 1000
```

### 3️⃣ 模型预测
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py prediction \
  --input /app/data/input/your_data.csv \
  --batch-size 1000
```

### 4️⃣ 模型评估
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py evaluation \
  --input /app/data/input/your_data.csv \
  --batch-size 1000
```

### 5️⃣ 收费判定
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py judgment \
  --input /app/data/input/your_data.csv \
  --batch-size 1000 \
  --abs-threshold 50.0 \
  --rel-threshold 0.1
```

## 🔄 运维操作

### 📊 监控检查
```bash
# 容器状态
docker ps | grep billing-audit
docker stats billing-audit-ai

# 健康检查
docker inspect billing-audit-ai | grep Health -A 10

# 日志查看
docker logs -f billing-audit-ai
docker exec -it billing-audit-ai tail -f /app/logs/billing_audit.log

# 数据检查
ls -la data/input/
ls -la outputs/
head outputs/data/*predictions*.csv
```

### 🔧 运维操作
```bash
# 重启服务
docker restart billing-audit-ai

# 清理操作
rm -rf outputs/*
rm -rf logs/*

# 备份操作
cp -r outputs/models/ backup/models_$(date +%Y%m%d_%H%M%S)/
cp production_config.json backup/config_$(date +%Y%m%d_%H%M%S).json
```

## 🚨 故障排除

### 常见问题解决
```bash
# 1. 容器启动失败
docker images | grep billing-audit-ai
docker load < images/billing-audit-ai-v2.1.0-slim-fixed.tar.gz

# 2. 数据处理失败
head -5 data/input/your_data.csv
docker exec -it billing-audit-ai ls -la /app/data/input/

# 3. 内存不足
# 调整批处理大小: --batch-size 500

# 4. 预测结果异常
ls -la outputs/models/
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py training \
  --input /app/data/input/your_data.csv --algorithm hierarchical
```

## 📈 输出结果说明

### 输出文件位置
- **模型文件**: `outputs/models/`
- **预测结果**: `outputs/data/`
- **评估报告**: `outputs/reports/`
- **日志文件**: `logs/`

### 预测结果格式
- **列数**: 27列 (14训练特征 + 11透传字段 + 1目标字段 + 1预测字段)
- **格式**: CSV文件
- **命名**: `hierarchical_predictions_YYYYMMDD_HHMMSS.csv`

## ⚙️ 参数说明

### 主要参数
- `--input`: 输入数据文件路径
- `--algorithm`: 算法选择 (hierarchical/random_forest/xgboost/lightgbm)
- `--batch-size`: 批处理大小 (默认1000)
- `--abs-threshold`: 绝对误差阈值 (默认50.0)
- `--rel-threshold`: 相对误差阈值 (默认0.1)

### 性能调优
- **小数据集**: `--batch-size 500`
- **大数据集**: `--batch-size 2000`
- **内存不足**: `--batch-size 100`
- **高性能**: `--batch-size 5000`
