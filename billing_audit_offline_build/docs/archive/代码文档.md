# 代码功能说明文档

## 📋 概述

本文档详细说明了山西电信出账稽核AI系统的代码结构、核心功能和API接口。

## 🏗️ 核心模块架构

### 1. 数据预处理模块 (`src/billing_audit/preprocessing/`)

#### 1.1 数据预处理器 (`data_preprocessor.py`)

**主要功能**：
- Excel数据文件加载和验证
- 数据质量检查和清洗
- 异常值检测和处理
- 特征和目标变量准备

**核心类**：
```python
class BillingDataPreprocessor:
    def __init__(self, fee_type: str)
    def process(self) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame]
    def load_data(self) -> pd.DataFrame
    def validate_data(self, data: pd.DataFrame) -> bool
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame
```

**使用示例**：
```python
preprocessor = BillingDataPreprocessor('fixed_fee')
X, y, passthrough = preprocessor.process()
```

#### 1.2 特征工程器 (`feature_engineer.py`)

**主要功能**：
- 日期特征提取（年、月、日、星期等）
- 类别特征编码（One-Hot编码）
- 数值特征标准化
- 特征转换器保存和加载

**核心类**：
```python
class FeatureEngineer:
    def __init__(self, fee_type: str)
    def fit_transform(self, X: pd.DataFrame) -> pd.DataFrame
    def transform(self, X: pd.DataFrame) -> pd.DataFrame
    def save_transformers(self, save_dir: str)
    def load_transformers(self, save_path: str)
```

**特征工程流程**：
1. 日期特征创建 - 从日期字段提取时间特征
2. 类别特征编码 - 使用One-Hot编码处理分类变量
3. 数值特征标准化 - 使用StandardScaler标准化数值特征

### 2. 模型训练模块 (`src/billing_audit/training/`)

#### 2.1 模型训练器 (`model_trainer.py`)

**主要功能**：
- 多算法支持（XGBoost、LightGBM、RandomForest）
- 自动算法降级机制
- 超参数调优
- 模型保存和加载

**核心类**：
```python
class BillingModelTrainer:
    def __init__(self, fee_type: str)
    def split_data(self, X, y) -> Tuple[np.ndarray, ...]
    def train_model(self, X_train, y_train, tune_hyperparams=False)
    def evaluate_model(self, model, X_test, y_test) -> Dict
    def save_model(self) -> str
```

**算法优先级**：
1. XGBoost（首选）
2. LightGBM（备选）
3. RandomForest（保底）

**超参数调优**：
```python
# XGBoost参数空间
param_space = {
    'n_estimators': [100, 200, 300],
    'max_depth': [3, 6, 9],
    'learning_rate': [0.01, 0.1, 0.2],
    'subsample': [0.8, 0.9, 1.0]
}
```

### 3. 模型评估模块 (`src/billing_audit/models/`)

#### 3.1 模型评估器 (`model_evaluator.py`)

**主要功能**：
- 回归指标计算（MAE、RMSE、R²、MAPE）
- 业务指标计算（准确率、误差分布）
- 误差分析和异常检测
- 综合评分计算

**核心类**：
```python
class ModelEvaluator:
    def __init__(self, fee_type: str)
    def comprehensive_evaluation(self, model, X_test, y_test, passthrough_data=None) -> Dict
    def calculate_regression_metrics(self, y_true, y_pred) -> Dict
    def calculate_business_metrics(self, y_true, y_pred, passthrough_data=None) -> Dict
    def analyze_prediction_errors(self, y_true, y_pred) -> Dict
```

**评估指标**：
- **MAE**: 平均绝对误差
- **RMSE**: 均方根误差
- **R²**: 决定系数
- **MAPE**: 平均绝对百分比误差
- **业务准确率**: 在阈值范围内的预测准确率

#### 3.2 模型验证器 (`model_validator.py`)

**主要功能**：
- K折交叉验证
- 模型稳定性测试
- 数据漂移检测
- 验证结果保存

**核心类**：
```python
class ModelValidator:
    def __init__(self, fee_type: str)
    def cross_validate(self, X, y, cv_folds=5) -> Dict
    def stability_test(self, X, y, n_runs=10) -> Dict
    def detect_data_drift(self, X_train, X_test) -> Dict
    def comprehensive_validation(self, X, y) -> Dict
```

### 4. 推理判定模块 (`src/billing_audit/inference/`)

#### 4.1 模型预测器 (`model_predictor.py`)

**主要功能**：
- 训练好的模型加载
- 单条和批量预测
- 预测置信度计算
- 详细预测结果生成

**核心类**：
```python
class ModelPredictor:
    def __init__(self, fee_type: str, model_path: str)
    def predict(self, billing_data: Union[Dict, List[Dict]]) -> np.ndarray
    def predict_with_details(self, billing_data: Union[Dict, List[Dict]]) -> Dict
    def load_model(self, model_path: str)
```

**预测流程**：
1. 输入数据验证
2. 特征工程转换
3. 模型预测
4. 结果后处理

#### 4.2 收费判定器 (`billing_judge.py`)

**主要功能**：
- 收费合理性判定
- 多种判定策略（绝对阈值、相对阈值、混合阈值）
- 置信度计算
- 批量判定处理

**核心类**：
```python
class BillingJudge:
    def __init__(self, fee_type: str)
    def judge_single(self, billing_data: Dict, actual_amount: float) -> Dict
    def judge_batch(self, batch_records: List[Dict]) -> List[Dict]
    def _calculate_confidence(self, predicted_amount: float, actual_amount: float) -> float
```

**判定策略**：
- **reasonable**: 预测误差在可接受范围内
- **unreasonable**: 预测误差超出阈值
- **uncertain**: 预测置信度不足

### 5. 工具模块 (`src/utils/`)

#### 5.1 数据工具 (`data_utils.py`)

**主要功能**：
- Excel文件加载和多sheet处理
- 数据类型转换和验证
- 缺失值和异常值处理
- 数据采样和分割

**核心函数**：
```python
def load_excel_data(file_path: str, sheet_name: str = None) -> pd.DataFrame
def handle_missing_values(data: pd.DataFrame, strategy: str = 'drop') -> pd.DataFrame
def detect_outliers(data: pd.DataFrame, method: str = 'iqr') -> pd.DataFrame
def convert_data_types(data: pd.DataFrame, type_mapping: Dict) -> pd.DataFrame
```

#### 5.2 模型工具 (`model_utils.py`)

**主要功能**：
- 模型保存和加载
- 模型性能比较
- 特征重要性分析
- 模型版本管理

**核心函数**：
```python
def save_model_with_metadata(model, model_info: Dict, save_path: str)
def load_model_with_metadata(model_path: str) -> Tuple[Any, Dict]
def compare_model_performance(models: List, X_test, y_test) -> pd.DataFrame
def analyze_feature_importance(model, feature_names: List[str]) -> pd.DataFrame
```

#### 5.3 日志工具 (`logger.py`)

**主要功能**：
- 统一日志配置
- 多级别日志记录
- 文件和控制台输出
- 日志轮转管理

**使用示例**：
```python
from src.utils import get_logger

logger = get_logger(__name__)
logger.info("模型训练开始")
logger.error("数据加载失败", exc_info=True)
```

### 6. 配置管理模块 (`src/config/`)

#### 6.1 配置管理器 (`config_manager.py`)

**主要功能**：
- 配置文件加载和验证
- 环境变量管理
- 配置热更新
- 默认配置提供

**核心类**：
```python
class ConfigManager:
    def __init__(self, config_path: str = None)
    def get_config(self, key: str, default=None)
    def get_data_source(self, fee_type: str) -> str
    def get_model_config(self) -> Dict
    def get_judgment_thresholds(self, fee_type: str) -> Dict
```

## 🔧 API接口设计

### 1. 预测接口

```python
# POST /api/v1/predict
{
    "fee_type": "fixed_fee",
    "billing_data": {
        "user_id": "12345",
        "should_fee": 100.0,
        "busi_flag": 1,
        # ... 其他字段
    }
}

# 响应
{
    "success": true,
    "data": {
        "predicted_amount": 106.17,
        "confidence_score": 0.95,
        "processing_time_ms": 45
    }
}
```

### 2. 判定接口

```python
# POST /api/v1/judge
{
    "fee_type": "fixed_fee",
    "billing_data": {...},
    "actual_amount": 95.0
}

# 响应
{
    "success": true,
    "data": {
        "judgment": "unreasonable",
        "confidence_score": 0.85,
        "predicted_amount": 106.17,
        "relative_error": 0.1175,
        "reason": "预测金额与实际金额差异较大"
    }
}
```

### 3. 批量处理接口

```python
# POST /api/v1/batch_judge
{
    "fee_type": "fixed_fee",
    "records": [
        {"billing_data": {...}, "actual_amount": 95.0},
        {"billing_data": {...}, "actual_amount": 105.0}
    ]
}

# 响应
{
    "success": true,
    "data": {
        "total_records": 2,
        "results": [...],
        "summary": {
            "reasonable_count": 1,
            "unreasonable_count": 1,
            "uncertain_count": 0
        }
    }
}
```

## 🧪 测试框架

### 单元测试结构

```
tests/
├── unit/
│   ├── test_data_preprocessor.py
│   ├── test_feature_engineer.py
│   ├── test_model_trainer.py
│   ├── test_model_evaluator.py
│   ├── test_model_predictor.py
│   └── test_billing_judge.py
├── integration/
│   ├── test_training_pipeline.py
│   ├── test_inference_pipeline.py
│   └── test_end_to_end.py
└── fixtures/
    ├── sample_data.xlsx
    └── test_config.json
```

### 测试用例示例

```python
import pytest
from src.billing_audit.preprocessing import BillingDataPreprocessor

class TestBillingDataPreprocessor:
    def test_load_data_success(self):
        preprocessor = BillingDataPreprocessor('fixed_fee')
        data = preprocessor.load_data()
        assert not data.empty
        assert 'should_fee' in data.columns
    
    def test_data_validation(self):
        preprocessor = BillingDataPreprocessor('fixed_fee')
        data = preprocessor.load_data()
        is_valid = preprocessor.validate_data(data)
        assert is_valid == True
```

## 📊 性能优化

### 1. 数据处理优化

- **向量化操作**: 使用pandas和numpy的向量化操作
- **内存管理**: 及时释放大型DataFrame
- **数据类型优化**: 使用合适的数据类型减少内存占用

### 2. 模型推理优化

- **模型缓存**: 避免重复加载模型
- **批量预测**: 批量处理提高效率
- **特征缓存**: 缓存特征工程结果

### 3. 并发处理

```python
from concurrent.futures import ThreadPoolExecutor

def batch_predict_parallel(self, batch_data: List[Dict], max_workers: int = 4):
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(self.predict_single, data) for data in batch_data]
        results = [future.result() for future in futures]
    return results
```

## 🔒 安全考虑

### 1. 数据安全

- **敏感数据脱敏**: 用户ID等敏感信息脱敏处理
- **数据加密**: 传输和存储数据加密
- **访问控制**: 基于角色的访问控制

### 2. 模型安全

- **模型版本控制**: 严格的模型版本管理
- **模型验证**: 部署前的模型验证
- **异常监控**: 实时监控模型异常行为

## 📈 监控和日志

### 1. 性能监控

```python
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.3f}秒")
        return result
    return wrapper
```

### 2. 业务监控

- **预测准确率监控**: 实时监控模型预测准确率
- **数据质量监控**: 监控输入数据质量
- **系统健康监控**: 监控系统资源使用情况

---

**文档版本**: v1.0.0  
**最后更新**: 2025-07-16  
**维护者**: 山西电信AI团队
