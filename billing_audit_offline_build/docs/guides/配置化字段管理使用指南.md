# ⚙️ 山西电信出账稽核AI系统 - 配置化字段管理使用指南

## 📋 功能概述

配置化字段管理是山西电信出账稽核AI系统v2.1.0的重要新功能，支持在配置文件中动态增减字段，无需修改任何代码即可完成全流程处理。

### **🎯 核心优势**
- ✅ **无代码修改**: 只需修改配置文件，无需修改任何Python代码
- ✅ **三类字段管理**: 训练特征、透传字段、目标字段分类管理
- ✅ **自动验证**: 配置一致性自动检查和验证
- ✅ **文档生成**: 自动生成字段映射和使用文档
- ✅ **向前兼容**: 保持旧接口兼容性，平滑升级

## 🔧 三类字段说明

### **A. 训练特征字段 (training_features)** - 14个字段
用于AI模型训练和预测的核心特征字段。

```json
"training_features": [
  "cal_type",           // 费用计算类型
  "unit_type",          // 周期类型
  "rate_unit",          // 周期数量
  "final_eff_year",     // 最终生效年
  "final_eff_mon",      // 最终生效月
  "final_eff_day",      // 最终生效日
  "final_exp_year",     // 最终失效年
  "final_exp_mon",      // 最终失效月
  "final_exp_day",      // 最终失效日
  "cur_year_month",     // 当前年月
  "charge_day_count",   // 计费天数
  "month_day_count",    // 当月总天数
  "should_fee",         // 应收费
  "busi_flag"           // 业务标识
]
```

### **B. 目标字段 (target_column)** - 1个字段
AI模型的预测目标，用于监督学习。

```json
"target_column": "amount"  // 账单费用金额
```

### **C. 透传字段 (passthrough_columns)** - 11个字段
不参与AI训练，但在结果文件中保留的业务标识字段。

```json
"passthrough_columns": [
  "offer_inst_id",              // 优惠实例ID
  "prod_inst_id",               // 产品实例ID
  "prod_id",                    // 产品ID
  "offer_id",                   // 优惠ID
  "sub_prod_id",                // 子产品ID
  "event_pricing_strategy_id",  // 事件定价策略ID
  "event_type_id",              // 事件类型ID
  "calc_priority",              // 计算优先级
  "pricing_section_id",         // 定价段ID
  "calc_method_id",             // 计算方法ID
  "role_id"                     // 角色ID
]
```

## 🛠️ 配置管理工具

### **安装和使用**
```bash
# 进入项目目录
cd /path/to/billing-audit-ai

# 查看配置信息
python scripts/tools/schema_manager.py info

# 验证配置正确性
python scripts/tools/schema_manager.py validate

# 列出所有字段
python scripts/tools/schema_manager.py list

# 列出特定类别字段
python scripts/tools/schema_manager.py list --category training_features

# 生成字段文档
python scripts/tools/schema_manager.py doc --output docs/schema_doc.md
```

### **工具功能说明**
| 命令 | 功能 | 用途 |
|------|------|------|
| `info` | 显示配置概览 | 快速了解当前字段配置状态 |
| `validate` | 验证配置一致性 | 检查配置文件是否正确 |
| `list` | 列出字段信息 | 查看字段详情和分类 |
| `doc` | 生成文档 | 自动生成字段映射文档 |

## 📝 字段管理操作

### **添加新字段**

#### **步骤1: 修改配置文件**
编辑 `config/production_config.json`：

```json
{
  "data_schema": {
    "fixed_fee": {
      "training_features": [
        "cal_type",
        "unit_type",
        // ... 现有字段 ...
        "new_feature_field"  // 新增训练特征
      ],
      "passthrough_columns": [
        "offer_inst_id",
        // ... 现有字段 ...
        "new_business_id"    // 新增透传字段
      ],
      "required_columns": [
        // 将新字段添加到必需字段列表
        "new_feature_field",
        "new_business_id"
      ],
      "categorical_columns": [
        // 如果新字段是类别类型，添加到这里
        "new_feature_field"
      ]
    }
  }
}
```

#### **步骤2: 验证配置**
```bash
python scripts/tools/schema_manager.py validate
```

#### **步骤3: 测试运行**
```bash
# 使用包含新字段的数据文件测试
python scripts/production/billing_audit_main.py full --input data/input/new_data.csv
```

### **删除字段**

#### **步骤1: 从配置中移除**
```json
{
  "data_schema": {
    "fixed_fee": {
      "training_features": [
        // 移除不需要的字段
        // "old_field_to_remove"
      ],
      "required_columns": [
        // 同时从必需字段中移除
        // "old_field_to_remove"
      ]
    }
  }
}
```

#### **步骤2: 验证配置**
```bash
python scripts/tools/schema_manager.py validate
```

### **修改字段分类**

#### **从训练特征改为透传字段**
```json
{
  "data_schema": {
    "fixed_fee": {
      "training_features": [
        // 从这里移除字段
        // "field_to_move"
      ],
      "passthrough_columns": [
        // 添加到这里
        "field_to_move"
      ]
    }
  }
}
```

## 🔍 配置验证

### **验证检查项**
- ✅ **字段完整性**: 所有字段都在required_columns中
- ✅ **分类一致性**: 训练特征有对应的数据类型定义
- ✅ **重复检查**: 没有重复的字段定义
- ✅ **目标字段**: 必须有且仅有一个目标字段

### **验证示例**
```bash
$ python scripts/tools/schema_manager.py validate

🔍 验证数据模式配置: fixed_fee
============================================================
📊 字段统计:
  - 训练特征: 14 个
  - 透传字段: 11 个
  - 目标字段: 1 个
  - 总字段数: 26 个
  - 类别字段: 4 个
  - 数值字段: 9 个
  - 日期字段: 1 个

✅ 验证通过: 数据模式配置正确
```

## 📊 输出结果格式

### **预测结果格式**
配置化字段管理确保预测结果按照正确的顺序输出：

```
训练特征(14列) + 透传字段(11列) + 目标字段(1列) + 预测字段(1列) = 27列
```

### **列顺序示例**
```csv
cal_type,unit_type,rate_unit,final_eff_year,final_eff_mon,final_eff_day,final_exp_year,final_exp_mon,final_exp_day,cur_year_month,charge_day_count,month_day_count,should_fee,busi_flag,offer_inst_id,prod_inst_id,prod_id,offer_id,sub_prod_id,event_pricing_strategy_id,event_type_id,calc_priority,pricing_section_id,calc_method_id,role_id,amount,prediction
```

## 🚀 高级功能

### **动态模式管理器**
```python
from src.config.dynamic_schema_manager import get_dynamic_schema_manager

# 获取管理器
schema_manager = get_dynamic_schema_manager()

# 获取所有字段
all_fields = schema_manager.get_all_fields()

# 获取字段元数据
metadata = schema_manager.get_field_metadata('cal_type')

# 验证模式一致性
validation = schema_manager.validate_schema_consistency()

# 生成文档
doc = schema_manager.export_schema_documentation()
```

### **配置管理器增强**
```python
from src.config.production_config_manager import get_config_manager

config = get_config_manager()

# 获取三类字段
training_features = config.get_training_features()
target_column = config.get_target_column()
passthrough_columns = config.get_passthrough_columns()

# 获取所有字段
all_fields = config.get_all_fields()

# 验证字段配置
validation = config.validate_field_configuration()
```

## 🔧 故障排除

### **常见问题**

#### **1. 配置验证失败**
```bash
❌ 验证失败: 发现配置问题
🚨 错误:
  - 必需字段列表缺少: {'new_field'}
```

**解决方案**: 将新字段添加到 `required_columns` 列表中。

#### **2. 字段类型未定义**
```bash
⚠️ 警告:
  - 训练特征缺少数据类型定义: {'new_field'}
```

**解决方案**: 将字段添加到相应的类型列表中 (`categorical_columns`, `numerical_columns`, `date_columns`)。

#### **3. 数据文件字段不匹配**
```bash
❌ 缺失必需列: {'missing_field'}
```

**解决方案**: 确保输入数据包含配置中定义的所有字段，或更新配置以匹配数据。

### **调试技巧**
```bash
# 查看详细字段信息
python scripts/tools/schema_manager.py list

# 生成当前配置文档
python scripts/tools/schema_manager.py doc

# 验证配置并查看详细错误
python scripts/tools/schema_manager.py validate
```

## 📋 最佳实践

### **字段管理建议**
1. **谨慎修改训练特征**: 影响模型性能，建议重新训练
2. **透传字段灵活**: 可以随时增减，不影响模型
3. **保持配置一致**: 修改后及时验证配置
4. **文档同步**: 使用工具自动生成最新文档

### **版本控制**
1. **配置文件版本化**: 将配置文件纳入版本控制
2. **变更记录**: 记录字段变更的原因和影响
3. **回滚准备**: 保留配置文件的历史版本

### **测试流程**
1. **配置验证**: 修改后立即验证
2. **小数据测试**: 使用小数据集测试新配置
3. **完整流程测试**: 确保端到端流程正常
4. **性能验证**: 检查处理性能是否受影响

---

**文档版本**: v2.1.0  
**更新时间**: 2025-07-28  
**适用版本**: 山西电信出账稽核AI系统 v2.1.0+  
**维护状态**: ✅ 活跃维护
