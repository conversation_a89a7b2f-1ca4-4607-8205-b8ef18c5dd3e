# 生产环境部署指南

## 📋 **部署概述**

本指南详细说明如何将山西电信出账稽核AI系统部署到生产环境，包括配置管理、容器化部署、参数调优等。

---

## 🔧 **配置化部署**

### **配置文件结构** ⭐

系统支持完全配置化部署，使用企业级配置管理系统：

```
config/                             # 配置文件目录
├── billing_audit_config.json       # 开发环境配置 (v1.0.0)
└── production_config.json          # 生产环境配置 (v2.0.0) ⭐

src/config/                         # 配置管理器
└── production_config_manager.py    # 生产级配置管理器 ⭐

scripts/production/                 # 环境脚本
├── setup_env.sh                   # 基础环境设置
└── setup_production_env.sh        # 生产环境变量设置 ⭐
```

### **配置管理特性** ⭐

#### **企业级配置管理**
- ✅ **环境变量支持**: `${VAR_NAME}` 语法替换
- ✅ **配置验证**: 自动验证配置完整性
- ✅ **多环境支持**: 开发/测试/生产环境切换
- ✅ **目录管理**: 自动创建必要目录
- ✅ **错误处理**: 完善的配置加载错误处理

#### **配置管理器使用**
```python
from src.config.production_config_manager import get_config_manager

# 获取配置管理器
config_manager = get_config_manager()

# 获取配置值
batch_size = config_manager.get_batch_size()
thresholds = config_manager.get_judgment_thresholds()
feature_columns = config_manager.get_feature_columns('fixed_fee')
```

### **主要配置项**

#### **1. 数据路径配置**
```json
{
  "data_paths": {
    "input_data_dir": "/data/input",      // 输入数据目录
    "output_data_dir": "/data/output",    // 输出结果目录
    "model_dir": "/models",               // 模型文件目录
    "logs_dir": "/logs",                  // 日志文件目录
    "temp_dir": "/tmp/billing_audit",     // 临时文件目录
    "backup_dir": "/data/backup"          // 备份文件目录
  }
}
```

#### **2. 大规模处理配置**
```json
{
  "large_scale_processing": {
    "batch_size": 50000,                  // 批处理大小
    "max_memory_gb": 8,                   // 最大内存限制
    "n_jobs": -1,                         // 并行作业数
    "chunk_size": 10000,                  // 数据块大小
    "save_intermediate_results": true,    // 保存中间结果
    "enable_progress_monitoring": true    // 启用进度监控
  }
}
```

#### **3. 模型训练配置**
```json
{
  "model_training": {
    "algorithms": ["random_forest", "xgboost", "lightgbm"],
    "default_algorithm": "random_forest",
    "hyperparameters": {
      "random_forest": {
        "n_estimators": 100,
        "max_depth": 10,
        "min_samples_split": 5,
        "random_state": 42,
        "n_jobs": -1
      }
    }
  }
}
```

#### **4. 收费判定配置**
```json
{
  "billing_judgment": {
    "thresholds": {
      "absolute_threshold": 50.0,         // 绝对误差阈值(元)
      "relative_threshold": 0.1,          // 相对误差阈值(10%)
      "use_mixed_threshold": true,        // 使用混合阈值
      "uncertainty_factor": 2.0           // 不确定区间因子
    }
  }
}
```

### **环境变量支持**

配置文件支持环境变量替换，使用 `${VAR_NAME}` 格式：

```json
{
  "data_sources": {
    "training_data": "${DATA_INPUT_DIR}/training_data.csv",
    "prediction_data": "${DATA_INPUT_DIR}/prediction_data.csv"
  },
  "model_paths": {
    "large_scale_model": "${MODEL_DIR}/large_scale_model_latest.pkl"
  }
}
```

---

## 🐳 **Docker容器化部署**

### **1. 构建镜像**

```bash
# 从deployment目录构建生产镜像
docker build -f deployment/docker/Dockerfile -t billing-audit-ai:latest .

# 查看镜像
docker images | grep billing-audit-ai
```

### **2. 使用Docker Compose部署**

```bash
# 进入Docker目录
cd deployment/docker

# 基础部署
docker-compose up -d

# 包含数据库的完整部署
docker-compose --profile database up -d

# 包含监控的部署
docker-compose --profile monitoring up -d
```

### **3. 一键部署脚本**

```bash
# 基础部署
./deployment/scripts/deploy_production.sh

# 完整部署（包含数据库和监控）
./deployment/scripts/deploy_production.sh --with-database --with-monitoring
```

---

## 📁 **目录结构规划**

### **生产环境目录结构**

```
/opt/billing-audit-ai/
├── data/
│   ├── input/                    # 输入数据
│   │   ├── training_data.csv
│   │   ├── prediction_data.csv
│   │   └── billing_data.csv
│   ├── output/                   # 输出结果
│   │   ├── predictions.csv
│   │   ├── billing_judgments.csv
│   │   └── reports/
│   └── backup/                   # 数据备份
├── models/                       # 模型文件
│   ├── large_scale_model_latest.pkl
│   ├── large_scale_feature_engineer_latest.pkl
│   └── backup/
├── logs/                         # 日志文件
│   ├── billing_audit.log
│   ├── model_training.log
│   └── api.log
├── config/                       # 配置文件
│   └── production_config.json
└── scripts/                      # 部署脚本
```

### **权限设置**

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash billing-audit

# 设置目录权限
sudo chown -R billing-audit:billing-audit /opt/billing-audit-ai
sudo chmod -R 755 /opt/billing-audit-ai
sudo chmod -R 644 /opt/billing-audit-ai/config/
```

---

## 🚀 **使用示例**

### **1. 配置化模型训练**

```bash
# 使用配置文件训练模型
docker exec billing-audit-ai python scripts/production/train_with_config.py

# 指定特定配置文件
docker exec billing-audit-ai python scripts/production/train_with_config.py \
    --config /app/config/production_config.json

# 覆盖配置文件中的参数
docker exec billing-audit-ai python scripts/production/train_with_config.py \
    --input /data/input/new_training_data.csv \
    --algorithm xgboost

# 仅检查配置，不执行训练
docker exec billing-audit-ai python scripts/production/train_with_config.py --dry-run
```

### **2. 批量预测**

```bash
# 使用最新模型进行预测
docker exec billing-audit-ai python scripts/production/predict_large_scale.py \
    --input /data/input/prediction_data.csv \
    --model /models/large_scale_model_latest.pkl \
    --feature-engineer /models/large_scale_feature_engineer_latest.pkl \
    --output /data/output/predictions.csv
```

### **3. 收费合理性判定**

```bash
# 大规模收费合理性判定
docker exec billing-audit-ai python scripts/production/large_scale_billing_judge.py \
    --input /data/input/billing_data.csv \
    --model /models/large_scale_model_latest.pkl \
    --feature-engineer /models/large_scale_feature_engineer_latest.pkl \
    --output /data/output/billing_judgments.csv
```

---

## 📊 **监控和维护**

### **1. 日志监控**

```bash
# 查看实时日志
docker-compose logs -f billing-audit-ai

# 查看特定日志文件
docker exec billing-audit-ai tail -f /logs/billing_audit.log

# 日志轮转配置
# 在production_config.json中配置日志轮转
```

### **2. 性能监控**

```bash
# 查看容器资源使用
docker stats billing-audit-ai

# 查看系统资源
docker exec billing-audit-ai python -c "
import psutil
print(f'CPU使用率: {psutil.cpu_percent()}%')
print(f'内存使用: {psutil.virtual_memory().percent}%')
print(f'磁盘使用: {psutil.disk_usage(\"/\").percent}%')
"
```

### **3. 健康检查**

```bash
# 检查容器健康状态
docker inspect --format='{{.State.Health.Status}}' billing-audit-ai

# 手动执行健康检查
docker exec billing-audit-ai python -c "
from src.config.production_config_manager import get_config_manager
config = get_config_manager()
print('✅ 系统健康检查通过')
"
```

---

## 🔄 **更新和备份**

### **1. 模型更新**

```bash
# 备份当前模型
docker exec billing-audit-ai cp /models/large_scale_model_latest.pkl \
    /data/backup/model_backup_$(date +%Y%m%d_%H%M%S).pkl

# 训练新模型
docker exec billing-audit-ai python scripts/production/train_with_config.py \
    --input /data/input/new_training_data.csv

# 验证新模型
docker exec billing-audit-ai python scripts/production/large_scale_model_evaluation.py \
    --test-data /data/input/test_data.csv \
    --model /models/large_scale_model_latest.pkl
```

### **2. 配置更新**

```bash
# 备份配置
cp config/production_config.json config/production_config_backup_$(date +%Y%m%d).json

# 更新配置后重启服务
docker-compose restart billing-audit-ai
```

### **3. 数据备份**

```bash
# 自动备份脚本
docker exec billing-audit-ai python -c "
import shutil
from datetime import datetime
from pathlib import Path

backup_dir = Path('/data/backup')
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

# 备份模型
shutil.copy('/models/large_scale_model_latest.pkl', 
           backup_dir / f'model_{timestamp}.pkl')

# 备份重要数据
shutil.copytree('/data/output', backup_dir / f'output_{timestamp}')

print(f'✅ 备份完成: {timestamp}')
"
```

---

## ⚠️ **故障排除**

### **常见问题**

1. **内存不足**
   ```bash
   # 减少批处理大小
   # 在production_config.json中调整batch_size
   "large_scale_processing": {
     "batch_size": 20000  // 从50000减少到20000
   }
   ```

2. **磁盘空间不足**
   ```bash
   # 清理临时文件
   docker exec billing-audit-ai rm -rf /tmp/billing_audit/*
   
   # 清理旧日志
   docker exec billing-audit-ai find /logs -name "*.log" -mtime +7 -delete
   ```

3. **配置文件错误**
   ```bash
   # 验证配置文件
   docker exec billing-audit-ai python -c "
   from src.config.production_config_manager import ProductionConfigManager
   config = ProductionConfigManager()
   config.load_config()
   print('✅ 配置文件验证通过')
   "
   ```

### **紧急恢复**

```bash
# 停止服务
docker-compose down

# 恢复备份
cp /data/backup/model_backup_latest.pkl /models/large_scale_model_latest.pkl
cp config/production_config_backup_latest.json config/production_config.json

# 重启服务
docker-compose up -d
```

---

## 🎯 **最佳实践**

### **1. 配置管理**
- ✅ 使用环境变量管理敏感信息
- ✅ 定期备份配置文件
- ✅ 使用版本控制管理配置变更
- ✅ 在测试环境验证配置变更

### **2. 数据管理**
- ✅ 定期备份模型和重要数据
- ✅ 监控磁盘空间使用
- ✅ 实施数据保留策略
- ✅ 使用数据校验确保质量

### **3. 性能优化**
- ✅ 根据硬件资源调整批处理大小
- ✅ 监控内存和CPU使用率
- ✅ 使用SSD存储提升I/O性能
- ✅ 定期清理临时文件

### **4. 安全考虑**
- ✅ 使用非root用户运行容器
- ✅ 限制网络访问
- ✅ 定期更新基础镜像
- ✅ 监控系统日志

**🎊 通过配置化部署，系统可以灵活适应不同的生产环境需求，确保稳定可靠的运行！**

---

**文档维护**: 技术团队  
**最后更新**: 2025-07-25  
**版本**: v2.0.0
