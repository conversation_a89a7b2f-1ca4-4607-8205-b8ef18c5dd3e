# Scripts 目录说明

本目录包含山西电信出账稽核AI系统的所有脚本文件，已按功能和用途进行分类组织。

## 📁 目录结构

```
scripts/
├── production/          # 🚀 生产环境脚本
│   ├── setup_env.sh               # 环境设置脚本
│   └── setup_production_env.sh    # 生产环境变量脚本
├── testing/            # 🧪 测试脚本
│   ├── end_to_end_test.py         # 端到端系统测试
│   ├── large_scale_end_to_end_test.py # 大规模端到端测试
│   ├── test_model_evaluation.py   # 模型评估测试
│   ├── test_preprocessing.py      # 数据预处理测试
│   └── test_inference_judgment.py # 推理和判定测试
├── validation/         # ✅ 验证脚本
│   ├── validate_config.py         # 配置文件验证
│   ├── validate_evaluation_logic.py    # 评估逻辑验证
│   ├── validate_preprocessing_logic.py # 预处理逻辑验证
│   └── validate_training_logic.py      # 训练逻辑验证
└── tools/             # 🛠️ 分析和工具脚本
    ├── analyze_data_samples.py    # 数据样例分析
    ├── simple_data_analysis.py   # 简化数据分析
    └── init_project_structure.py # 项目结构初始化
```

## 🚀 生产环境脚本 (Production)

**注意**: 生产脚本已重构到src目录下对应模块中，提供更好的代码组织：

### 训练脚本 → `src/billing_audit/training/`
- `train_large_scale_model.py` - 大规模模型训练
- `train_billing_models.py` - 传统模型训练
- `train_with_config.py` - 配置化训练

### 预处理脚本 → `src/billing_audit/preprocessing/`
- `large_scale_feature_engineer.py` - 大规模特征工程

### 推理脚本 → `src/billing_audit/inference/`
- `predict_large_scale.py` - 大规模预测
- `large_scale_billing_judge.py` - 大规模收费判定

### 评估脚本 → `src/billing_audit/models/`
- `large_scale_model_evaluation.py` - 大规模模型评估

### setup_env.sh
- **功能**: 设置OpenMP库路径，激活虚拟环境，测试ML库可用性
- **用途**: 生产环境部署前的基础环境配置
- **使用方法**:
  ```bash
  source scripts/production/setup_env.sh
  ```

### setup_production_env.sh ⭐
- **功能**: 设置生产环境变量，配置系统路径和参数
- **用途**: 生产环境配置管理，支持环境变量替换
- **使用方法**:
  ```bash
  source scripts/production/setup_production_env.sh
  ```
- **环境变量**:
  ```bash
  export DATA_INPUT_DIR="/data/input"
  export DATA_OUTPUT_DIR="/data/output"
  export MODEL_DIR="/models"
  export LOGS_DIR="/logs"
  export BILLING_AUDIT_CONFIG="/path/to/production_config.json"
  ```

## 🧪 测试脚本 (Testing)

### end_to_end_test.py
- **功能**: 演示完整的收费稽核AI系统工作流程
- **用途**: 验证整个系统的集成和功能完整性
- **使用方法**:
  ```bash
  python scripts/testing/end_to_end_test.py
  ```

### test_model_evaluation.py
- **功能**: 测试模型评估和验证功能
- **用途**: 验证模型评估逻辑的正确性
- **使用方法**:
  ```bash
  python scripts/testing/test_model_evaluation.py
  ```

### test_preprocessing.py
- **功能**: 测试数据预处理和特征工程功能
- **用途**: 验证数据预处理流程
- **使用方法**:
  ```bash
  python scripts/testing/test_preprocessing.py
  ```

### test_inference_judgment.py
- **功能**: 测试模型预测和收费判定功能
- **用途**: 验证推理和判定逻辑
- **使用方法**:
  ```bash
  python scripts/testing/test_inference_judgment.py
  ```

## ✅ 验证脚本 (Validation)

### validate_config.py
- **功能**: 验证配置文件的完整性和正确性
- **用途**: 确保配置文件格式正确
- **使用方法**:
  ```bash
  python scripts/validation/validate_config.py
  ```

### validate_evaluation_logic.py
- **功能**: 验证模型评估逻辑（不依赖numpy/pandas）
- **用途**: 轻量级的评估逻辑检查
- **使用方法**:
  ```bash
  python scripts/validation/validate_evaluation_logic.py
  ```

### validate_preprocessing_logic.py
- **功能**: 验证数据预处理逻辑（不依赖外部库）
- **用途**: 轻量级的预处理逻辑检查
- **使用方法**:
  ```bash
  python scripts/validation/validate_preprocessing_logic.py
  ```

### validate_training_logic.py
- **功能**: 验证模型训练逻辑（不依赖外部ML库）
- **用途**: 轻量级的训练逻辑检查
- **使用方法**:
  ```bash
  python scripts/validation/validate_training_logic.py
  ```

## 🛠️ 分析和工具脚本 (Tools)

### analyze_data_samples.py
- **功能**: 分析固费、优惠和话单增量三个Excel样例文件
- **用途**: 数据探索和理解
- **使用方法**:
  ```bash
  python scripts/tools/analyze_data_samples.py
  ```

### simple_data_analysis.py
- **功能**: 简化版的数据分析工具
- **用途**: 快速数据概览
- **使用方法**:
  ```bash
  python scripts/tools/simple_data_analysis.py
  ```

### init_project_structure.py
- **功能**: 创建完整的项目目录结构
- **用途**: 项目初始化和目录创建
- **使用方法**:
  ```bash
  python scripts/tools/init_project_structure.py
  ```

## 🔄 脚本依赖关系

### 执行顺序建议

1. **环境准备**:
   ```bash
   source scripts/production/setup_env.sh
   ```

2. **验证系统**:
   ```bash
   python scripts/validation/validate_config.py
   python scripts/validation/validate_preprocessing_logic.py
   python scripts/validation/validate_training_logic.py
   python scripts/validation/validate_evaluation_logic.py
   ```

3. **测试功能**:
   ```bash
   python scripts/testing/test_preprocessing.py
   python scripts/testing/test_model_evaluation.py
   python scripts/testing/test_inference_judgment.py
   ```

4. **端到端测试**:
   ```bash
   python scripts/testing/end_to_end_test.py
   ```

5. **生产训练**:
   ```bash
   # 大规模训练
   python src/billing_audit/training/train_large_scale_model.py --input data.csv --output models/

   # 配置化训练
   python src/billing_audit/training/train_with_config.py --config config.json
   ```

## 📝 注意事项

1. **路径引用**: 所有脚本都使用相对路径引用项目根目录，移动后仍能正常运行
2. **执行权限**: `setup_env.sh` 脚本具有执行权限
3. **依赖关系**: 生产脚本依赖核心业务模块，测试脚本可独立运行
4. **环境要求**: 所有脚本都需要在项目虚拟环境中运行

## 🔧 故障排除

如果脚本运行出现路径问题，请确保：
1. 在项目根目录下执行脚本
2. 已激活虚拟环境
3. 已设置正确的PYTHONPATH

---

**版本**: v1.1.0  
**更新日期**: 2025-07-22  
**重组完成**: ✅
