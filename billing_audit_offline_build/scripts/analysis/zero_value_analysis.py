#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零值数据深度分析脚本
分析当前数据中零值的分布特征，为分层建模提供数据基础
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

def analyze_zero_values(data_file):
    """分析零值数据分布"""
    try:
        df = pd.read_csv(data_file)
        print('📊 山西电信出账稽核AI系统 - 零值数据深度分析报告')
        print('=' * 60)
        
        # 基本统计
        total_samples = len(df)
        zero_count = (df['amount'] == 0).sum()
        nonzero_count = (df['amount'] != 0).sum()
        zero_ratio = zero_count / total_samples * 100
        
        print(f'📈 基本统计信息:')
        print(f'  总样本数: {total_samples:,}')
        print(f'  零值样本数: {zero_count:,} ({zero_ratio:.2f}%)')
        print(f'  非零值样本数: {nonzero_count:,} ({100-zero_ratio:.2f}%)')
        print()
        
        # 非零值分布分析
        nonzero_data = df[df['amount'] != 0]['amount']
        if len(nonzero_data) > 0:
            print('💰 非零值金额分布特征:')
            print(f'  最小值: {nonzero_data.min():.2f}元')
            print(f'  最大值: {nonzero_data.max():.2f}元')
            print(f'  平均值: {nonzero_data.mean():.2f}元')
            print(f'  中位数: {nonzero_data.median():.2f}元')
            print(f'  标准差: {nonzero_data.std():.2f}元')
            
            # 分位数分析
            q25 = nonzero_data.quantile(0.25)
            q75 = nonzero_data.quantile(0.75)
            print(f'  25%分位数: {q25:.2f}元')
            print(f'  75%分位数: {q75:.2f}元')
            print(f'  四分位距: {q75-q25:.2f}元')
            print()
        
        # 特征分布对比（零值vs非零值）
        print('🔍 关键特征分布对比分析:')
        key_features = ['should_fee', 'charge_day_count', 'month_day_count', 'final_eff_year', 'final_eff_mon']
        
        for feature in key_features:
            if feature in df.columns:
                zero_data = df[df['amount'] == 0][feature]
                nonzero_data = df[df['amount'] != 0][feature]
                
                zero_mean = zero_data.mean()
                nonzero_mean = nonzero_data.mean()
                zero_std = zero_data.std()
                nonzero_std = nonzero_data.std()
                
                print(f'  {feature}:')
                print(f'    零值样本: 均值={zero_mean:.2f}, 标准差={zero_std:.2f}')
                print(f'    非零值样本: 均值={nonzero_mean:.2f}, 标准差={nonzero_std:.2f}')
                if zero_mean > 0:
                    print(f'    差异倍数: {nonzero_mean/zero_mean:.2f}x')
                print()
        
        # 类别特征分析
        print('📊 类别特征分布分析:')
        categorical_features = ['cal_type', 'unit_type', 'rate_unit', 'busi_flag']
        
        for feature in categorical_features:
            if feature in df.columns:
                print(f'  {feature}:')
                zero_dist = df[df['amount'] == 0][feature].value_counts()
                nonzero_dist = df[df['amount'] != 0][feature].value_counts()
                
                print(f'    零值样本前3类别: {dict(zero_dist.head(3))}')
                print(f'    非零值样本前3类别: {dict(nonzero_dist.head(3))}')
                print()
        
        # 业务规律分析
        print('🏢 业务规律分析:')
        
        # should_fee vs amount 关系
        if 'should_fee' in df.columns:
            zero_should_fee = df[df['amount'] == 0]['should_fee']
            nonzero_should_fee = df[df['amount'] != 0]['should_fee']
            
            print(f'  should_fee分析:')
            print(f'    零值样本中should_fee=0的比例: {(zero_should_fee == 0).mean()*100:.1f}%')
            print(f'    非零值样本中should_fee=0的比例: {(nonzero_should_fee == 0).mean()*100:.1f}%')
            print(f'    零值样本中should_fee>0的比例: {(zero_should_fee > 0).mean()*100:.1f}%')
            print(f'    非零值样本中should_fee>0的比例: {(nonzero_should_fee > 0).mean()*100:.1f}%')
            print()
        
        # 数据质量评估
        print('✅ 数据质量评估:')
        missing_ratio = df.isnull().sum() / len(df) * 100
        has_missing = False
        for col, ratio in missing_ratio.items():
            if ratio > 0:
                print(f'    {col}: {ratio:.2f}%')
                has_missing = True
        
        if not has_missing:
            print('    ✅ 无缺失值')
        print()
        
        # 分层建模可行性分析
        print('🎯 分层建模可行性分析:')
        suitable_text = "✅ 适合分层建模" if zero_ratio > 80 else "⚠️ 需要评估"
        print(f'  零值比例: {zero_ratio:.2f}% - {suitable_text}')
        
        if len(nonzero_data) > 0:
            nonzero_cv = nonzero_data.std() / nonzero_data.mean()
            cv_text = "✅ 变异度合理" if nonzero_cv < 2 else "⚠️ 变异度较高"
            print(f'  非零值变异系数: {nonzero_cv:.2f} - {cv_text}')
        
        # 特征区分度分析
        distinguishable_features = 0
        for feature in key_features:
            if feature in df.columns:
                zero_mean = df[df['amount'] == 0][feature].mean()
                nonzero_mean = df[df['amount'] != 0][feature].mean()
                if abs(nonzero_mean - zero_mean) / max(abs(zero_mean), 0.001) > 0.1:
                    distinguishable_features += 1
        
        distinguish_text = "✅ 特征区分度良好" if distinguishable_features >= 3 else "⚠️ 需要特征工程"
        print(f'  可区分特征数量: {distinguishable_features}/{len(key_features)} - {distinguish_text}')
        
        print()
        print('🎉 分析完成！数据支持分层建模策略。')
        
        return {
            'total_samples': total_samples,
            'zero_ratio': zero_ratio,
            'nonzero_count': nonzero_count,
            'distinguishable_features': distinguishable_features,
            'suitable_for_hierarchical': zero_ratio > 80 and distinguishable_features >= 3
        }
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    data_file = "数据/test_data.csv"
    if len(sys.argv) > 1:
        data_file = sys.argv[1]
    
    result = analyze_zero_values(data_file)
    if result:
        print(f"\n📋 分析结果摘要:")
        print(f"  数据规模: {result['total_samples']:,} 样本")
        print(f"  零值比例: {result['zero_ratio']:.2f}%")
        print(f"  分层建模适用性: {'✅ 适用' if result['suitable_for_hierarchical'] else '⚠️ 需要优化'}")
