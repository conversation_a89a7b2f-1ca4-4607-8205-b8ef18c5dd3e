#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
山西电信出账稽核AI系统主脚本演示
展示主脚本的各种使用方式
"""

import subprocess
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(
            cmd,
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✅ 执行成功")
            if result.stdout:
                print("输出:")
                print(result.stdout[-1000:])  # 显示最后1000字符
        else:
            print("❌ 执行失败")
            print(f"返回码: {result.returncode}")
            if result.stderr:
                print("错误:")
                print(result.stderr[-1000:])
                
    except subprocess.TimeoutExpired:
        print("⏰ 执行超时")
    except Exception as e:
        print(f"💥 执行异常: {str(e)}")

def main():
    """主演示函数"""
    print("🎯 山西电信出账稽核AI系统主脚本演示")
    print("=" * 80)
    
    # 检查数据文件
    data_file = project_root / "数据" / "test_data.csv"
    if not data_file.exists():
        print(f"❌ 测试数据文件不存在: {data_file}")
        print("请确保测试数据文件存在后再运行演示")
        return
    
    print(f"✅ 测试数据文件: {data_file}")
    
    # 主脚本路径
    main_script = "scripts/production/billing_audit_main.py"
    
    # 演示1: 查看帮助信息
    run_command(
        [sys.executable, main_script, "--help"],
        "查看主脚本帮助信息"
    )
    
    # 演示2: 单独执行特征工程
    run_command(
        [sys.executable, main_script, "feature-engineering", 
         "--input", str(data_file), "--batch-size", "100"],
        "单独执行特征工程"
    )
    
    # 演示3: 单独执行模型训练
    run_command(
        [sys.executable, main_script, "training",
         "--input", str(data_file), "--batch-size", "100"],
        "单独执行模型训练"
    )
    
    # 演示4: 单独执行模型预测
    run_command(
        [sys.executable, main_script, "prediction",
         "--input", str(data_file), "--batch-size", "100"],
        "单独执行模型预测"
    )
    
    # 演示5: 单独执行收费判定
    run_command(
        [sys.executable, main_script, "judgment",
         "--input", str(data_file), "--batch-size", "100"],
        "单独执行收费合理性判定"
    )
    
    # 演示6: 完整流程执行
    run_command(
        [sys.executable, main_script, "full",
         "--input", str(data_file), "--batch-size", "100"],
        "执行完整流程"
    )
    
    print("\n" + "=" * 80)
    print("🎊 主脚本演示完成")
    print("=" * 80)
    
    # 显示生成的文件
    print("\n📁 生成的文件:")
    
    outputs_dir = project_root / "outputs"
    if outputs_dir.exists():
        for subdir in ["models", "data", "reports"]:
            subdir_path = outputs_dir / subdir
            if subdir_path.exists():
                files = list(subdir_path.glob("*"))
                if files:
                    print(f"\n{subdir}/ 目录:")
                    for file in sorted(files)[-3:]:  # 显示最新的3个文件
                        print(f"  - {file.name}")
    
    logs_dir = project_root / "logs"
    if logs_dir.exists():
        log_files = list(logs_dir.glob("billing_audit_main_*.log"))
        if log_files:
            print(f"\nlogs/ 目录:")
            for file in sorted(log_files)[-2:]:  # 显示最新的2个日志文件
                print(f"  - {file.name}")
    
    print("\n💡 提示:")
    print("- 查看详细日志: tail -f logs/billing_audit_main_*.log")
    print("- 查看执行报告: cat outputs/reports/execution_report_*.json")
    print("- 查看预测结果: head outputs/data/predictions_*.csv")
    print("- 查看判定结果: head outputs/data/billing_judgments_*.csv")

if __name__ == "__main__":
    main()
