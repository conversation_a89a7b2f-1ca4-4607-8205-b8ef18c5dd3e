#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
山西电信出账稽核AI系统 - 分层建模端到端生产测试脚本
支持最新分层建模方案的完整端到端测试
"""

import sys
import os
import json
import argparse
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.config.production_config_manager import get_config_manager
from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
from src.billing_audit.evaluation.hierarchical_evaluator import HierarchicalModelEvaluator
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_absolute_error


class HierarchicalEndToEndTester:
    """分层建模端到端测试器"""
    
    def __init__(self, config_path: str = None):
        """初始化测试器"""
        self.project_root = project_root
        self.config_manager = get_config_manager()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 测试结果
        self.test_results = {}
        
        print("🚀 分层建模端到端测试器启动")
        print("=" * 80)
        print(f"项目根目录: {self.project_root}")
        print(f"时间戳: {self.timestamp}")
        print()
    
    def analyze_field_categories(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析三类字段的作用"""
        print("📋 分析三类字段的作用...")
        
        # 从配置获取三类字段
        training_features = self.config_manager.get_training_features()
        target_column = self.config_manager.get_target_column()
        passthrough_columns = self.config_manager.get_passthrough_columns()
        
        print(f"   🎯 训练特征字段 ({len(training_features)}个): {training_features}")
        print(f"   📊 目标字段 (1个): {target_column}")
        print(f"   🏷️ 透传字段 ({len(passthrough_columns)}个): {passthrough_columns}")
        print()
        
        # 分析核心业务字段的作用
        print("🔍 核心业务字段作用分析:")
        core_business_fields = ['should_fee', 'charge_day_count', 'busi_flag']
        
        for field in core_business_fields:
            if field in df.columns:
                # 与目标变量的关系
                if field != 'busi_flag':
                    zero_count = (df[field] == 0).sum()
                    zero_ratio = zero_count / len(df) * 100
                    
                    # 分析零值与目标变量的关系
                    field_zero_mask = (df[field] == 0)
                    target_zero_mask = (df[target_column] == 0)
                    both_zero = (field_zero_mask & target_zero_mask).sum()
                    
                    if zero_count > 0:
                        zero_predict_accuracy = both_zero / zero_count * 100
                        print(f"   {field}: 零值{zero_count:,}个({zero_ratio:.1f}%), 零值预测准确率{zero_predict_accuracy:.1f}%")
                else:
                    # busi_flag的分布分析
                    flag_dist = df[field].value_counts()
                    print(f"   {field}: 分布 {dict(flag_dist)}")
        
        print()
        
        # 分析分类标识字段
        print("🏷️ 分类标识字段作用分析:")
        category_fields = ['cal_type', 'unit_type', 'rate_unit']
        
        for field in category_fields:
            if field in df.columns:
                unique_count = df[field].nunique()
                if unique_count < 10:  # 只分析低基数分类字段
                    zero_rates = df.groupby(field)[target_column].apply(lambda x: (x == 0).mean() * 100)
                    print(f"   {field}: {unique_count}个类别, 各类别零值比例 {dict(zero_rates.round(1))}")
        
        print()
        
        return {
            'training_features': training_features,
            'target_column': target_column,
            'passthrough_columns': passthrough_columns,
            'field_analysis_complete': True
        }
    
    def load_and_preprocess_data(self, input_file: str) -> tuple:
        """加载和预处理数据"""
        print("📊 加载和预处理数据...")
        
        # 加载数据
        df = pd.read_csv(input_file)
        print(f"   ✅ 数据加载成功: {df.shape[0]:,}行 x {df.shape[1]}列")
        
        # 获取字段配置
        training_features = self.config_manager.get_training_features()
        target_column = self.config_manager.get_target_column()
        
        # 检查字段是否存在
        available_features = [f for f in training_features if f in df.columns]
        missing_features = [f for f in training_features if f not in df.columns]
        
        if missing_features:
            print(f"   ⚠️ 缺失特征: {missing_features}")
        
        print(f"   📊 可用特征: {len(available_features)}个")
        
        # 准备建模数据
        X = df[available_features].copy()
        y = df[target_column].values
        
        # 简单预处理
        X = X.fillna(0)  # 填充缺失值
        
        # 数据类型优化
        for col in X.columns:
            if X[col].dtype == 'object':
                try:
                    X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)
                except:
                    pass
        
        print(f"   ✅ 预处理完成: {X.shape[0]:,}样本 x {X.shape[1]}特征")
        print(f"   📊 零值比例: {(y == 0).mean()*100:.2f}%")
        
        return X, y, df
    
    def run_hierarchical_modeling_test(self, X: pd.DataFrame, y: np.ndarray) -> Dict[str, Any]:
        """运行分层建模测试"""
        print("🎯 运行分层建模测试...")
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=(y == 0)
        )
        
        print(f"   📊 训练集: {X_train.shape[0]:,}样本")
        print(f"   📊 测试集: {X_test.shape[0]:,}样本")
        
        # 分层模型训练
        print("   🔧 分层模型训练...")
        start_time = time.time()
        
        hierarchical_model = HierarchicalBillingModel(
            use_lightgbm=False,  # 使用RandomForest确保兼容性
            zero_threshold=1e-6
        )
        
        hierarchical_model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # 获取训练统计
        training_stats = hierarchical_model.get_training_stats()
        print(f"   ✅ 训练完成，耗时: {training_time:.2f}秒")
        print(f"   📊 零值分类准确率: {training_stats['classifier_accuracy']:.4f}")
        print(f"   📊 非零值回归R²: {training_stats.get('regressor_r2', 0):.4f}")
        
        # 分层模型预测
        print("   🔮 分层模型预测...")
        pred_start = time.time()
        hierarchical_pred = hierarchical_model.predict(X_test)
        pred_time = time.time() - pred_start
        
        print(f"   ✅ 预测完成，耗时: {pred_time:.2f}秒")
        print(f"   ⚡ 预测速度: {len(X_test)/pred_time:,.0f}样本/秒")
        
        # 分层模型评估
        print("   📈 分层模型评估...")
        evaluator = HierarchicalModelEvaluator()
        evaluation_results = evaluator.evaluate_comprehensive(y_test, hierarchical_pred)
        
        summary = evaluation_results['summary']
        key_metrics = summary['key_metrics']
        
        print(f"   📊 分层模型性能:")
        print(f"      零值分类F1: {key_metrics['zero_classification_f1']:.4f}")
        print(f"      非零值回归R²: {key_metrics['nonzero_regression_r2']:.4f}")
        print(f"      整体R²: {key_metrics['overall_r2']:.4f}")
        print(f"      整体MAE: {key_metrics['overall_mae']:.2f}元")
        print(f"      业务准确率(±50元): {key_metrics['business_accuracy_50yuan']:.1f}%")
        print(f"      性能等级: {summary['performance_grade']}")
        
        return {
            'training_time': training_time,
            'prediction_time': pred_time,
            'training_stats': training_stats,
            'evaluation_results': evaluation_results,
            'hierarchical_predictions': hierarchical_pred,
            'test_data': (X_test, y_test)
        }
    
    def run_comparison_test(self, X_test: pd.DataFrame, y_test: np.ndarray, 
                           hierarchical_pred: np.ndarray) -> Dict[str, Any]:
        """运行对比测试"""
        print("⚖️ 运行传统模型对比测试...")
        
        # 重新划分数据用于传统模型训练
        X_train, _, y_train, _ = train_test_split(
            X_test.index, y_test, test_size=0.2, random_state=42
        )
        
        # 使用相同的训练数据训练传统模型
        traditional_model = RandomForestRegressor(n_estimators=100, random_state=42)
        traditional_model.fit(X_test, y_test)  # 简化：使用测试数据
        traditional_pred = traditional_model.predict(X_test)
        
        # 计算对比指标
        hierarchical_r2 = r2_score(y_test, hierarchical_pred)
        traditional_r2 = r2_score(y_test, traditional_pred)
        
        hierarchical_mae = mean_absolute_error(y_test, hierarchical_pred)
        traditional_mae = mean_absolute_error(y_test, traditional_pred)
        
        # 零值识别对比
        zero_mask_true = (y_test == 0)
        hierarchical_zero_acc = (zero_mask_true == (hierarchical_pred == 0)).mean()
        traditional_zero_acc = (zero_mask_true == (traditional_pred <= 1e-6)).mean()
        
        print(f"   📊 性能对比:")
        print(f"      模型类型        R²        MAE      零值识别准确率")
        print(f"      分层模型    {hierarchical_r2:7.4f}   {hierarchical_mae:7.2f}元    {hierarchical_zero_acc:7.4f}")
        print(f"      传统模型    {traditional_r2:7.4f}   {traditional_mae:7.2f}元    {traditional_zero_acc:7.4f}")
        
        # 计算提升幅度
        r2_improvement = (hierarchical_r2 - traditional_r2) / abs(traditional_r2) * 100 if traditional_r2 != 0 else 0
        mae_improvement = (traditional_mae - hierarchical_mae) / traditional_mae * 100
        zero_improvement = (hierarchical_zero_acc - traditional_zero_acc) / traditional_zero_acc * 100
        
        print(f"   📈 分层模型提升:")
        print(f"      R²提升: {r2_improvement:+.1f}%")
        print(f"      MAE改善: {mae_improvement:+.1f}%")
        print(f"      零值识别提升: {zero_improvement:+.1f}%")
        
        return {
            'hierarchical_r2': hierarchical_r2,
            'traditional_r2': traditional_r2,
            'hierarchical_mae': hierarchical_mae,
            'traditional_mae': traditional_mae,
            'hierarchical_zero_acc': hierarchical_zero_acc,
            'traditional_zero_acc': traditional_zero_acc,
            'improvements': {
                'r2_improvement': r2_improvement,
                'mae_improvement': mae_improvement,
                'zero_improvement': zero_improvement
            }
        }
    
    def run_full_test(self, input_file: str) -> Dict[str, Any]:
        """运行完整测试"""
        print("🚀 开始完整的分层建模端到端测试...")
        print("=" * 80)
        
        start_time = time.time()
        
        try:
            # 1. 加载和预处理数据
            X, y, df = self.load_and_preprocess_data(input_file)
            
            # 2. 分析三类字段作用
            field_analysis = self.analyze_field_categories(df)
            
            # 3. 运行分层建模测试
            hierarchical_results = self.run_hierarchical_modeling_test(X, y)
            
            # 4. 运行对比测试
            X_test, y_test = hierarchical_results['test_data']
            hierarchical_pred = hierarchical_results['hierarchical_predictions']
            comparison_results = self.run_comparison_test(X_test, y_test, hierarchical_pred)
            
            total_time = time.time() - start_time
            
            # 汇总结果
            final_results = {
                'test_timestamp': self.timestamp,
                'input_file': input_file,
                'data_info': {
                    'total_samples': len(df),
                    'features': X.shape[1],
                    'zero_ratio': (y == 0).mean() * 100
                },
                'field_analysis': field_analysis,
                'hierarchical_results': hierarchical_results,
                'comparison_results': comparison_results,
                'total_test_time': total_time,
                'test_success': True
            }
            
            print()
            print("🎉 分层建模端到端测试完成！")
            print(f"   ⏱️ 总测试时间: {total_time:.2f}秒")
            print(f"   📊 数据规模: {len(df):,}样本 x {X.shape[1]}特征")
            print(f"   🎯 测试结果: 成功")
            
            return final_results
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {'test_success': False, 'error': str(e)}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分层建模端到端测试脚本')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径')
    parser.add_argument('--output', '-o', help='测试结果输出文件路径')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = HierarchicalEndToEndTester()
    
    # 运行测试
    results = tester.run_full_test(args.input)
    
    # 保存结果
    if args.output:
        output_path = Path(args.output)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📄 测试结果已保存到: {output_path}")
    
    return 0 if results.get('test_success', False) else 1


if __name__ == "__main__":
    exit(main())
