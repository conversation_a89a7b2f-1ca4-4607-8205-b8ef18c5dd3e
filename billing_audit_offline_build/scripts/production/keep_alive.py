#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
山西电信出账稽核AI系统 - 容器后台常驻运行脚本
版本: v2.1.0-slim
用途: 保持容器运行，提供基础服务和监控功能
"""

import time
import signal
import sys
import os
import logging
from datetime import datetime
import threading
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/app/logs/keep_alive.log', mode='a')
    ]
)
logger = logging.getLogger('keep_alive')

class BillingAuditKeepAlive:
    """容器后台常驻服务"""
    
    def __init__(self):
        self.running = True
        self.start_time = datetime.now()
        self.heartbeat_interval = 60  # 心跳间隔60秒
        self.status_file = '/tmp/billing_audit/service_status.json'
        
        # 确保状态文件目录存在
        os.makedirs(os.path.dirname(self.status_file), exist_ok=True)
        
        # 注册信号处理器
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，准备优雅关闭...")
        self.running = False
        
    def _update_status(self):
        """更新服务状态"""
        try:
            status = {
                'service': '山西电信出账稽核AI系统',
                'version': 'v2.1.0-slim',
                'status': 'running',
                'start_time': self.start_time.isoformat(),
                'current_time': datetime.now().isoformat(),
                'uptime_seconds': int((datetime.now() - self.start_time).total_seconds()),
                'pid': os.getpid(),
                'timezone': os.environ.get('TZ', 'UTC')
            }
            
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"更新状态文件失败: {e}")
    
    def _heartbeat(self):
        """心跳监控"""
        while self.running:
            try:
                uptime = datetime.now() - self.start_time
                logger.info(f"💓 服务心跳 - 运行时间: {uptime}")
                
                # 更新状态文件
                self._update_status()
                
                # 检查系统资源（简单检查）
                self._check_system_health()
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"心跳监控异常: {e}")
                time.sleep(10)
    
    def _check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查关键目录
            critical_dirs = ['/app/src', '/app/scripts', '/app/config']
            for dir_path in critical_dirs:
                if not os.path.exists(dir_path):
                    logger.warning(f"关键目录不存在: {dir_path}")
            
            # 检查磁盘空间
            import shutil
            total, used, free = shutil.disk_usage('/app')
            free_gb = free // (1024**3)
            if free_gb < 1:
                logger.warning(f"磁盘空间不足: {free_gb}GB")
            
        except Exception as e:
            logger.error(f"系统健康检查异常: {e}")
    
    def _print_welcome(self):
        """打印欢迎信息"""
        welcome_msg = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 山西电信出账稽核AI系统 v2.1.0-slim                      ║
║                              容器后台常驻服务                                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ 🕐 启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}                                    ║
║ 🌏 时区设置: {os.environ.get('TZ', 'UTC')}                                           ║
║ 📦 镜像版本: 精简版 (体积优化)                                               ║
║ 🔧 常用工具: ps, ping, telnet, htop, vim 等已安装                           ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ ⭐ 主要功能:                                                                 ║
║   • 全流程: python scripts/production/billing_audit_main.py full            ║
║   • 训练: python scripts/production/billing_audit_main.py training          ║
║   • 预测: python scripts/production/billing_audit_main.py prediction        ║
║   • 评估: python scripts/production/billing_audit_main.py evaluation        ║
║   • 判定: python scripts/production/billing_audit_main.py judgment          ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ 🔍 监控信息:                                                                 ║
║   • 服务状态: {self.status_file}                    ║
║   • 日志文件: /app/logs/keep_alive.log                                       ║
║   • 心跳间隔: {self.heartbeat_interval}秒                                                   ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ 💡 使用提示:                                                                 ║
║   • 查看进程: ps -ef                                                         ║
║   • 网络测试: ping <host> 或 telnet <host> <port>                           ║
║   • 系统监控: htop                                                           ║
║   • 查看帮助: python scripts/production/billing_audit_main.py --help        ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(welcome_msg)
        logger.info("山西电信出账稽核AI系统后台常驻服务已启动")
    
    def run(self):
        """运行主服务"""
        try:
            # 打印欢迎信息
            self._print_welcome()
            
            # 启动心跳监控线程
            heartbeat_thread = threading.Thread(target=self._heartbeat, daemon=True)
            heartbeat_thread.start()
            
            # 主循环
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("接收到键盘中断信号")
        except Exception as e:
            logger.error(f"服务运行异常: {e}")
        finally:
            self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        logger.info("正在清理资源...")
        
        # 更新状态为停止
        try:
            status = {
                'service': '山西电信出账稽核AI系统',
                'version': 'v2.1.0-slim',
                'status': 'stopped',
                'stop_time': datetime.now().isoformat(),
                'uptime_seconds': int((datetime.now() - self.start_time).total_seconds())
            }
            
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"更新停止状态失败: {e}")
        
        logger.info("🛑 山西电信出账稽核AI系统后台常驻服务已停止")
        print("\n🛑 容器服务已优雅关闭")

def main():
    """主函数"""
    try:
        service = BillingAuditKeepAlive()
        service.run()
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
