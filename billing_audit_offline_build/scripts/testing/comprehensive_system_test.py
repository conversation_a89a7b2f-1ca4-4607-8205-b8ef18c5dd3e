#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
山西电信出账稽核AI系统 - 综合系统测试
运行所有可用的测试脚本，生成完整的系统健康报告
"""

import sys
import os
import subprocess
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class ComprehensiveSystemTester:
    """综合系统测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_results = {}
        self.overall_results = {}
        
        print("山西电信出账稽核AI系统 - 综合系统测试")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"项目根目录: {self.project_root}")
        print()
    
    def run_test_script(self, script_path, test_name):
        """运行单个测试脚本"""
        print(f"\n🔍 运行 {test_name}...")
        print("-" * 40)
        
        try:
            full_path = self.project_root / script_path
            if not full_path.exists():
                self.test_results[test_name] = {
                    'status': 'SKIP',
                    'message': '测试脚本不存在',
                    'path': script_path
                }
                print(f"⏭️ {test_name}: 脚本不存在，跳过")
                return False
            
            # 运行测试脚本
            result = subprocess.run([
                sys.executable, str(full_path)
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.test_results[test_name] = {
                    'status': 'PASS',
                    'message': '测试通过',
                    'path': script_path,
                    'output': result.stdout[-1000:] if len(result.stdout) > 1000 else result.stdout  # 保留最后1000字符
                }
                print(f"✅ {test_name}: 测试通过")
                return True
            else:
                self.test_results[test_name] = {
                    'status': 'FAIL',
                    'message': f'测试失败 (返回码: {result.returncode})',
                    'path': script_path,
                    'error': result.stderr[-1000:] if len(result.stderr) > 1000 else result.stderr
                }
                print(f"❌ {test_name}: 测试失败")
                return False
                
        except subprocess.TimeoutExpired:
            self.test_results[test_name] = {
                'status': 'TIMEOUT',
                'message': '测试超时',
                'path': script_path
            }
            print(f"⏰ {test_name}: 测试超时")
            return False
            
        except Exception as e:
            self.test_results[test_name] = {
                'status': 'ERROR',
                'message': f'测试异常: {str(e)}',
                'path': script_path
            }
            print(f"💥 {test_name}: 测试异常 - {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始运行综合系统测试...\n")
        
        # 定义所有测试脚本
        test_scripts = [
            ("scripts/testing/config_only_test.py", "配置和基础功能测试"),
            ("scripts/testing/scripts_validation_test.py", "脚本验证测试"),
            ("scripts/validation/validate_config.py", "配置验证测试"),
            ("scripts/tools/verify_documentation_structure.py", "文档结构验证"),
        ]
        
        # 运行每个测试
        passed_tests = 0
        total_tests = len(test_scripts)
        
        for script_path, test_name in test_scripts:
            if self.run_test_script(script_path, test_name):
                passed_tests += 1
        
        # 计算总体结果
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        self.overall_results = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': round(success_rate, 2)
        }
        
        return success_rate >= 75  # 75%以上算成功
    
    def check_system_components(self):
        """检查系统组件状态"""
        print("\n🔧 检查系统组件状态...")
        
        components = {
            '配置管理器': self.check_config_manager(),
            '项目结构': self.check_project_structure(),
            '文档完整性': self.check_documentation(),
            '脚本可用性': self.check_scripts_availability()
        }
        
        self.overall_results['components'] = components
        
        working_components = sum(1 for status in components.values() if status)
        total_components = len(components)
        
        print(f"\n组件状态总结:")
        for component, status in components.items():
            status_icon = "✅" if status else "❌"
            print(f"  {status_icon} {component}: {'正常' if status else '异常'}")
        
        print(f"\n组件健康度: {working_components}/{total_components} ({working_components/total_components*100:.1f}%)")
        
        return working_components / total_components >= 0.75
    
    def check_config_manager(self):
        """检查配置管理器"""
        try:
            from src.config.production_config_manager import get_config_manager
            config_manager = get_config_manager()
            
            # 基本功能测试
            batch_size = config_manager.get_batch_size()
            thresholds = config_manager.get_judgment_thresholds()
            
            return batch_size > 0 and isinstance(thresholds, dict) and len(thresholds) > 0
        except:
            return False
    
    def check_project_structure(self):
        """检查项目结构"""
        required_dirs = [
            "src/config/",
            "src/billing_audit/",
            "config/",
            "scripts/",
            "docs/",
            "outputs/"
        ]
        
        for dir_path in required_dirs:
            if not (self.project_root / dir_path).exists():
                return False
        
        return True
    
    def check_documentation(self):
        """检查文档完整性"""
        required_docs = [
            "README.md",
            "docs/文档中心.md",
            "docs/core/文档索引.md"
        ]
        
        for doc_path in required_docs:
            if not (self.project_root / doc_path).exists():
                return False
        
        return True
    
    def check_scripts_availability(self):
        """检查脚本可用性"""
        key_scripts = [
            "scripts/testing/config_only_test.py",
            "scripts/tools/verify_documentation_structure.py",
            "scripts/validation/validate_config.py"
        ]
        
        for script_path in key_scripts:
            if not (self.project_root / script_path).exists():
                return False
        
        return True
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print("\n📊 生成综合系统测试报告...")
        
        report = {
            'test_info': {
                'test_name': '综合系统测试',
                'timestamp': self.timestamp,
                'test_time': datetime.now().isoformat(),
                'project_root': str(self.project_root),
                'system_info': {
                    'python_version': sys.version,
                    'platform': sys.platform
                }
            },
            'overall_results': self.overall_results,
            'individual_tests': self.test_results,
            'recommendations': self.generate_recommendations()
        }
        
        # 保存报告
        report_dir = self.project_root / "outputs" / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        report_file = report_dir / f"comprehensive_system_test_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 生成简化的文本报告
        text_report_file = report_dir / f"system_health_summary_{self.timestamp}.txt"
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write(f"山西电信出账稽核AI系统 - 系统健康报告\n")
            f.write(f"=" * 50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试成功率: {self.overall_results.get('success_rate', 0)}%\n")
            f.write(f"通过测试: {self.overall_results.get('passed_tests', 0)}/{self.overall_results.get('total_tests', 0)}\n\n")
            
            f.write("组件状态:\n")
            for component, status in self.overall_results.get('components', {}).items():
                f.write(f"  {'✅' if status else '❌'} {component}\n")
            
            f.write(f"\n详细报告: {report_file}\n")
        
        print(f"综合报告已保存: {report_file}")
        print(f"简化报告已保存: {text_report_file}")
        
        return report
    
    def generate_recommendations(self):
        """生成改进建议"""
        recommendations = []
        
        # 基于测试结果生成建议
        failed_tests = [name for name, result in self.test_results.items() if result['status'] != 'PASS']
        
        if failed_tests:
            recommendations.append(f"修复失败的测试: {', '.join(failed_tests)}")
        
        # 基于组件状态生成建议
        if 'components' in self.overall_results:
            failed_components = [name for name, status in self.overall_results['components'].items() if not status]
            if failed_components:
                recommendations.append(f"修复异常组件: {', '.join(failed_components)}")
        
        if not recommendations:
            recommendations.append("系统状态良好，建议定期运行测试以确保持续稳定")
        
        return recommendations
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        # 运行所有测试脚本
        tests_passed = self.run_all_tests()
        
        # 检查系统组件
        components_healthy = self.check_system_components()
        
        # 生成报告
        report = self.generate_comprehensive_report()
        
        # 输出总结
        print("\n" + "=" * 60)
        print("综合系统测试总结")
        print("=" * 60)
        print(f"测试脚本成功率: {self.overall_results.get('success_rate', 0)}%")
        print(f"系统组件健康度: {sum(self.overall_results.get('components', {}).values())}/{len(self.overall_results.get('components', {}))}")
        
        overall_health = tests_passed and components_healthy
        
        if overall_health:
            print("\n🎉 系统综合测试通过！系统状态良好")
            print("✅ 所有核心功能正常工作")
            print("✅ 系统组件状态健康")
            print("✅ 脚本和配置完整")
        else:
            print("\n⚠️ 系统存在一些问题")
            if not tests_passed:
                print("❌ 部分测试脚本失败")
            if not components_healthy:
                print("❌ 部分系统组件异常")
        
        print(f"\n📋 改进建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"  {i}. {rec}")
        
        return overall_health

def main():
    """主函数"""
    tester = ComprehensiveSystemTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n✅ 综合系统测试完成，系统整体健康")
        return 0
    else:
        print("\n❌ 综合系统测试发现问题，请查看报告详情")
        return 1

if __name__ == "__main__":
    exit(main())
