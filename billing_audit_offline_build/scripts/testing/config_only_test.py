#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅配置和基础功能测试 - 避开numpy依赖问题
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class ConfigOnlyTester:
    """仅配置和基础功能测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_results = {}
        self.passed_tests = 0
        self.total_tests = 0
        
        print("山西电信出账稽核AI系统 - 配置和基础功能测试")
        print("=" * 55)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def test_config_manager_only(self):
        """仅测试配置管理器"""
        print("🔧 测试配置管理器...")
        self.total_tests += 1
        
        try:
            from src.config.production_config_manager import get_config_manager
            
            # 测试配置管理器基本功能
            config_manager = get_config_manager()
            
            # 测试基本配置获取
            batch_size = config_manager.get_batch_size()
            thresholds = config_manager.get_judgment_thresholds()
            feature_columns = config_manager.get_feature_columns('fixed_fee')
            model_params = config_manager.get_model_hyperparameters('random_forest')
            env_vars = config_manager.get_environment_setup()
            
            # 验证结果
            assert batch_size > 0, f"批次大小异常: {batch_size}"
            assert isinstance(thresholds, dict) and len(thresholds) > 0, "判定阈值异常"
            assert isinstance(feature_columns, list) and len(feature_columns) > 0, "特征列异常"
            assert isinstance(model_params, dict) and len(model_params) > 0, "模型参数异常"
            assert isinstance(env_vars, dict), "环境变量异常"
            
            self.test_results['config_manager'] = {
                'status': 'PASS',
                'message': '配置管理器功能正常',
                'details': {
                    'batch_size': batch_size,
                    'thresholds_count': len(thresholds),
                    'feature_columns_count': len(feature_columns),
                    'model_params_count': len(model_params),
                    'env_vars_count': len(env_vars)
                }
            }
            self.passed_tests += 1
            print("✅ 配置管理器测试通过")
            print(f"   - 批次大小: {batch_size}")
            print(f"   - 判定阈值: {len(thresholds)}个")
            print(f"   - 特征列: {len(feature_columns)}个")
            print(f"   - 模型参数: {len(model_params)}个")
            print(f"   - 环境变量: {len(env_vars)}个")
            return True
            
        except Exception as e:
            self.test_results['config_manager'] = {
                'status': 'FAIL',
                'message': f'配置管理器测试失败: {str(e)}'
            }
            print(f"❌ 配置管理器测试失败: {str(e)}")
            return False
    
    def test_config_file_structure(self):
        """测试配置文件结构"""
        print("\n⚙️ 测试配置文件结构...")
        self.total_tests += 1
        
        try:
            # 测试生产配置文件
            prod_config_path = self.project_root / "config" / "production_config.json"
            assert prod_config_path.exists(), "生产配置文件不存在"
            
            with open(prod_config_path, 'r', encoding='utf-8') as f:
                prod_config = json.load(f)
            
            # 验证配置文件结构
            required_sections = ['project', 'data_sources', 'model_training', 'billing_judgment']
            for section in required_sections:
                assert section in prod_config, f"配置文件缺少{section}节"
            
            # 验证项目信息
            project_info = prod_config['project']
            assert 'name' in project_info, "项目信息缺少名称"
            assert 'version' in project_info, "项目信息缺少版本"
            
            # 验证模型训练配置
            model_training = prod_config['model_training']
            assert 'hyperparameters' in model_training, "模型训练配置缺少超参数"
            assert 'random_forest' in model_training['hyperparameters'], "缺少随机森林超参数"
            
            self.test_results['config_file_structure'] = {
                'status': 'PASS',
                'message': '配置文件结构正确',
                'details': {
                    'sections_count': len(prod_config),
                    'project_name': project_info.get('name', 'Unknown'),
                    'project_version': project_info.get('version', 'Unknown'),
                    'algorithms_count': len(model_training.get('algorithms', []))
                }
            }
            self.passed_tests += 1
            print("✅ 配置文件结构测试通过")
            print(f"   - 配置节数: {len(prod_config)}")
            print(f"   - 项目名称: {project_info.get('name', 'Unknown')}")
            print(f"   - 项目版本: {project_info.get('version', 'Unknown')}")
            print(f"   - 支持算法: {len(model_training.get('algorithms', []))}个")
            return True
            
        except Exception as e:
            self.test_results['config_file_structure'] = {
                'status': 'FAIL',
                'message': f'配置文件结构测试失败: {str(e)}'
            }
            print(f"❌ 配置文件结构测试失败: {str(e)}")
            return False
    
    def test_project_directories(self):
        """测试项目目录结构"""
        print("\n📁 测试项目目录结构...")
        self.total_tests += 1
        
        # 关键目录
        key_directories = [
            "src/",
            "src/config/",
            "src/billing_audit/",
            "src/billing_audit/preprocessing/",
            "src/billing_audit/training/",
            "src/billing_audit/inference/",
            "src/billing_audit/models/",
            "config/",
            "scripts/",
            "scripts/testing/",
            "scripts/tools/",
            "docs/",
            "outputs/"
        ]
        
        existing_dirs = []
        missing_dirs = []
        
        for dir_path in key_directories:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                existing_dirs.append(dir_path)
            else:
                missing_dirs.append(dir_path)
        
        if len(missing_dirs) == 0:
            self.test_results['project_directories'] = {
                'status': 'PASS',
                'message': '项目目录结构完整',
                'existing_dirs': existing_dirs
            }
            self.passed_tests += 1
            print(f"✅ 项目目录结构完整 ({len(existing_dirs)}个目录)")
        else:
            self.test_results['project_directories'] = {
                'status': 'FAIL',
                'message': f'项目目录结构不完整，缺少{len(missing_dirs)}个目录',
                'missing_dirs': missing_dirs,
                'existing_dirs': existing_dirs
            }
            print(f"❌ 项目目录结构不完整")
            for missing in missing_dirs:
                print(f"   - 缺少: {missing}")
    
    def test_key_files_existence(self):
        """测试关键文件存在性"""
        print("\n📄 测试关键文件存在性...")
        self.total_tests += 1
        
        # 关键文件
        key_files = [
            "src/config/production_config_manager.py",
            "src/billing_audit/inference/judgment_result.py",
            "config/production_config.json",
            "README.md",
            "docs/文档中心.md",
            "scripts/testing/core_functionality_test.py",
            "scripts/testing/config_only_test.py"
        ]
        
        existing_files = []
        missing_files = []
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists() and full_path.is_file():
                existing_files.append(file_path)
            else:
                missing_files.append(file_path)
        
        if len(missing_files) == 0:
            self.test_results['key_files_existence'] = {
                'status': 'PASS',
                'message': '关键文件完整',
                'existing_files': existing_files
            }
            self.passed_tests += 1
            print(f"✅ 关键文件完整 ({len(existing_files)}个文件)")
        else:
            self.test_results['key_files_existence'] = {
                'status': 'FAIL',
                'message': f'关键文件不完整，缺少{len(missing_files)}个文件',
                'missing_files': missing_files,
                'existing_files': existing_files
            }
            print(f"❌ 关键文件不完整")
            for missing in missing_files:
                print(f"   - 缺少: {missing}")
    
    def test_environment_setup(self):
        """测试环境设置"""
        print("\n🌍 测试环境设置...")
        self.total_tests += 1
        
        try:
            from src.config.production_config_manager import get_config_manager
            
            config_manager = get_config_manager()
            
            # 测试环境变量导出
            env_vars = config_manager.get_environment_setup()
            
            # 验证关键环境变量
            expected_env_vars = ['DATA_INPUT_DIR', 'MODEL_DIR', 'LOGS_DIR']
            missing_env_vars = []
            
            for var in expected_env_vars:
                if var not in env_vars:
                    missing_env_vars.append(var)
            
            if len(missing_env_vars) == 0:
                self.test_results['environment_setup'] = {
                    'status': 'PASS',
                    'message': '环境设置正常',
                    'env_vars_count': len(env_vars),
                    'env_vars': list(env_vars.keys())
                }
                self.passed_tests += 1
                print(f"✅ 环境设置正常 ({len(env_vars)}个环境变量)")
                print(f"   - 包含关键变量: {', '.join(expected_env_vars)}")
            else:
                self.test_results['environment_setup'] = {
                    'status': 'FAIL',
                    'message': f'环境设置不完整，缺少{len(missing_env_vars)}个关键变量',
                    'missing_env_vars': missing_env_vars,
                    'existing_env_vars': list(env_vars.keys())
                }
                print(f"❌ 环境设置不完整")
                for missing in missing_env_vars:
                    print(f"   - 缺少: {missing}")
            
        except Exception as e:
            self.test_results['environment_setup'] = {
                'status': 'FAIL',
                'message': f'环境设置测试失败: {str(e)}'
            }
            print(f"❌ 环境设置测试失败: {str(e)}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        report = {
            'test_info': {
                'test_name': '配置和基础功能测试',
                'timestamp': self.timestamp,
                'test_time': datetime.now().isoformat(),
                'project_root': str(self.project_root)
            },
            'summary': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.total_tests - self.passed_tests,
                'success_rate': round(success_rate, 2)
            },
            'test_results': self.test_results
        }
        
        # 保存报告
        report_dir = self.project_root / "outputs" / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        report_file = report_dir / f"config_only_test_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"测试报告已保存: {report_file}")
        return report
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始运行配置和基础功能测试...\n")
        
        # 运行各项测试
        self.test_project_directories()
        self.test_key_files_existence()
        self.test_config_file_structure()
        self.test_config_manager_only()
        self.test_environment_setup()
        
        # 生成报告
        report = self.generate_test_report()
        
        # 输出总结
        print("\n" + "=" * 55)
        print("配置和基础功能测试总结")
        print("=" * 55)
        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.total_tests - self.passed_tests}")
        print(f"成功率: {report['summary']['success_rate']}%")
        
        if report['summary']['success_rate'] >= 95:
            print("\n🎉 配置和基础功能完全正常！")
            return True
        elif report['summary']['success_rate'] >= 80:
            print("\n✅ 配置和基础功能基本正常，有少量问题")
            return True
        else:
            print("\n⚠️ 配置和基础功能存在问题，需要修复")
            return False

def main():
    """主函数"""
    tester = ConfigOnlyTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 配置和基础功能测试完成，系统基础功能正常")
        return 0
    else:
        print("\n❌ 配置和基础功能测试发现问题，请检查失败项")
        return 1

if __name__ == "__main__":
    exit(main())
