#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千万级数据处理系统端到端测试脚本
使用现有模拟数据进行完整的千万级数据处理系统测试
验证：特征工程 → 模型训练 → 模型评估 → 预测服务
"""

import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime
import subprocess
import shutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


class LargeScaleEndToEndTester:
    """千万级数据处理系统端到端测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_data_path = self.project_root / "数据" / "test_data.csv"
        # 使用标准的outputs目录，并添加时间戳
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_output_dir = self.project_root / "outputs" / "testing" / f"large_scale_test_{self.timestamp}"

        # 创建各种输出目录
        self.models_dir = self.project_root / "outputs" / "models"
        self.data_dir = self.project_root / "outputs" / "data"
        self.reports_dir = self.project_root / "outputs" / "reports"
        self.visualizations_dir = self.project_root / "outputs" / "visualizations"
        self.test_results = {}
        
        # 创建所有输出目录
        self.test_output_dir.mkdir(parents=True, exist_ok=True)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        self.visualizations_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"千万级数据处理系统端到端测试初始化")
        print(f"  - 项目根目录: {self.project_root}")
        print(f"  - 测试数据: {self.test_data_path}")
        print(f"  - 输出目录: {self.test_output_dir}")
    
    def check_prerequisites(self):
        """检查测试前置条件"""
        print(f"\n检查测试前置条件...")
        
        # 检查测试数据
        if not self.test_data_path.exists():
            print(f"测试数据不存在: {self.test_data_path}")
            return False
        
        # 检查数据格式
        try:
            df = pd.read_csv(self.test_data_path)
            print(f"  测试数据: {df.shape[0]} 行 × {df.shape[1]} 列")
            
            # 检查必需列
            config_path = self.project_root / "config" / "billing_audit_config.json"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            required_columns = config['billing_audit']['fixed_fee']['feature_columns']
            target_column = config['billing_audit']['fixed_fee']['target_column']
            
            missing_columns = set(required_columns + [target_column]) - set(df.columns)
            if missing_columns:
                print(f"缺失必需列: {missing_columns}")
                return False
            
            print(f"  数据格式检查通过")
            
        except Exception as e:
            print(f"数据格式检查失败: {e}")
            return False
        
        # 检查脚本文件
        scripts_to_check = [
            "src/billing_audit/preprocessing/large_scale_feature_engineer.py",
            "src/billing_audit/training/train_large_scale_model.py",
            "src/billing_audit/models/large_scale_model_evaluation.py",
            "src/billing_audit/inference/predict_large_scale.py"
        ]
        
        for script in scripts_to_check:
            script_path = self.project_root / script
            if not script_path.exists():
                print(f"脚本不存在: {script}")
                return False
        
        print(f"  所有脚本文件存在")
        return True
    
    def prepare_test_data(self):
        """准备测试数据"""
        print(f"\n准备测试数据...")
        
        # 读取原始数据
        df = pd.read_csv(self.test_data_path)
        
        # 分割训练和测试数据 (80/20)
        train_size = int(len(df) * 0.8)
        
        train_df = df[:train_size].copy()
        test_df = df[train_size:].copy()
        
        # 保存分割后的数据到data目录，带时间戳
        train_path = self.data_dir / f"train_data_{self.timestamp}.csv"
        test_path = self.data_dir / f"test_data_{self.timestamp}.csv"
        
        train_df.to_csv(train_path, index=False)
        test_df.to_csv(test_path, index=False)
        
        print(f"  训练数据: {len(train_df)} 行 → {train_path}")
        print(f"  测试数据: {len(test_df)} 行 → {test_path}")
        
        return train_path, test_path
    
    def run_script(self, script_name, args, description):
        """运行脚本并记录结果"""
        print(f"\n{description}...")
        
        script_path = self.project_root / script_name
        cmd = ["python", str(script_path)] + args
        
        print(f"  执行命令: {' '.join(cmd)}")
        
        start_time = datetime.now()
        
        try:
            result = subprocess.run(
                cmd,
                cwd=str(self.project_root),
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if result.returncode == 0:
                print(f"  {description}成功，耗时: {duration:.2f}秒")
                print(f"  📄 输出摘要:")
                # 显示最后几行输出
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines[-5:]:
                    if line.strip():
                        print(f"    {line}")
                
                return True, duration, result.stdout
            else:
                print(f"  {description}失败")
                print(f"  📄 错误信息:")
                error_lines = result.stderr.strip().split('\n')
                for line in error_lines[-10:]:
                    if line.strip():
                        print(f"    {line}")
                
                return False, duration, result.stderr
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {description}超时 (5分钟)")
            return False, 300, "Timeout"
        except Exception as e:
            print(f"  {description}异常: {e}")
            return False, 0, str(e)
    
    def test_feature_engineering(self, train_path):
        """测试特征工程"""
        # 输出到models目录，带时间戳
        feature_engineer_file = self.models_dir / f"large_scale_feature_engineer_{self.timestamp}.pkl"
        args = [
            "--input", str(train_path),
            "--output", str(feature_engineer_file),
            "--batch-size", "100"  # 小批次测试
        ]
        
        success, duration, output = self.run_script(
            "src/billing_audit/preprocessing/large_scale_feature_engineer.py",
            args,
            "特征工程测试"
        )
        
        self.test_results['feature_engineering'] = {
            'success': success,
            'duration': duration,
            'output_file': str(self.models_dir / f"large_scale_feature_engineer_{self.timestamp}.pkl")
        }
        
        return success
    
    def test_model_training(self, train_path):
        """测试模型训练"""
        # 输出到models目录
        args = [
            "--input", str(train_path),
            "--output", str(self.models_dir),
            "--batch-size", "100"  # 小批次测试
        ]
        
        success, duration, output = self.run_script(
            "src/billing_audit/training/train_large_scale_model.py",
            args,
            "模型训练测试"
        )
        
        # 查找生成的模型文件（现在在models目录）
        model_files = list(self.models_dir.glob("large_scale_model_*.pkl"))
        feature_engineer_files = list(self.models_dir.glob("large_scale_feature_engineer_*.pkl"))
        
        self.test_results['model_training'] = {
            'success': success,
            'duration': duration,
            'model_files': [str(f) for f in model_files],
            'feature_engineer_files': [str(f) for f in feature_engineer_files]
        }
        
        return success and len(model_files) > 0 and len(feature_engineer_files) > 0
    
    def test_model_evaluation(self, test_path):
        """测试模型评估"""
        # 获取最新的模型文件（现在在models目录）
        model_files = list(self.models_dir.glob("large_scale_model_*.pkl"))
        feature_engineer_files = list(self.models_dir.glob("large_scale_feature_engineer_*.pkl"))
        
        if not model_files or not feature_engineer_files:
            print(f"找不到模型文件进行评估")
            return False
        
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        latest_feature_engineer = max(feature_engineer_files, key=lambda x: x.stat().st_mtime)
        
        args = [
            "--test-data", str(test_path),
            "--model", str(latest_model),
            "--feature-engineer", str(latest_feature_engineer),
            "--target-column", "amount",
            "--batch-size", "50",  # 小批次测试
            "--output", str(self.reports_dir / f"evaluation_report_{self.timestamp}.json")
        ]
        
        success, duration, output = self.run_script(
            "src/billing_audit/models/large_scale_model_evaluation.py",
            args,
            "模型评估测试"
        )
        
        self.test_results['model_evaluation'] = {
            'success': success,
            'duration': duration,
            'model_used': str(latest_model),
            'feature_engineer_used': str(latest_feature_engineer),
            'report_file': str(self.project_root / "outputs" / "reports" / f"evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        }
        
        return success
    
    def test_prediction(self, test_path):
        """测试预测服务"""
        # 获取最新的模型文件（现在在models目录）
        model_files = list(self.models_dir.glob("large_scale_model_*.pkl"))
        feature_engineer_files = list(self.models_dir.glob("large_scale_feature_engineer_*.pkl"))
        
        if not model_files or not feature_engineer_files:
            print(f"找不到模型文件进行预测")
            return False
        
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        latest_feature_engineer = max(feature_engineer_files, key=lambda x: x.stat().st_mtime)
        
        args = [
            "--input", str(test_path),
            "--model", str(latest_model),
            "--feature-engineer", str(latest_feature_engineer),
            "--output", str(self.data_dir / f"predictions_{self.timestamp}.csv"),
            "--batch-size", "50",  # 小批次测试
            "--include-features"  # 包含原始特征
        ]
        
        success, duration, output = self.run_script(
            "src/billing_audit/inference/predict_large_scale.py",
            args,
            "预测服务测试"
        )
        
        # 检查预测结果
        prediction_file = self.test_output_dir / "predictions.csv"
        if success and prediction_file.exists():
            try:
                pred_df = pd.read_csv(prediction_file)
                print(f"  预测结果: {len(pred_df)} 行")
                if 'prediction' in pred_df.columns:
                    predictions = pred_df['prediction']
                    print(f"    - 预测范围: {predictions.min():.2f} - {predictions.max():.2f}")
                    print(f"    - 平均值: {predictions.mean():.2f}")
                    print(f"    - 零值比例: {(predictions == 0).mean()*100:.1f}%")
            except Exception as e:
                print(f"  预测结果分析失败: {e}")
        
        self.test_results['prediction'] = {
            'success': success,
            'duration': duration,
            'model_used': str(latest_model),
            'feature_engineer_used': str(latest_feature_engineer),
            'prediction_file': str(prediction_file)
        }
        
        return success

    def test_billing_judgment(self, test_path):
        """测试收费合理性判定"""
        # 获取最新的模型文件（现在在models目录）
        model_files = list(self.models_dir.glob("large_scale_model_*.pkl"))
        feature_engineer_files = list(self.models_dir.glob("large_scale_feature_engineer_*.pkl"))

        if not model_files or not feature_engineer_files:
            print(f"找不到模型文件进行收费合理性判定")
            return False

        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        latest_feature_engineer = max(feature_engineer_files, key=lambda x: x.stat().st_mtime)

        args = [
            "--input", str(test_path),
            "--model", str(latest_model),
            "--feature-engineer", str(latest_feature_engineer),
            "--output", str(self.data_dir / f"billing_judgments_{self.timestamp}.csv"),
            "--target-column", "amount",
            "--batch-size", "50",  # 小批次测试
            "--abs-threshold", "50.0",  # 50元绝对阈值
            "--rel-threshold", "0.1"   # 10%相对阈值
        ]

        success, duration, output = self.run_script(
            "src/billing_audit/inference/large_scale_billing_judge.py",
            args,
            "收费合理性判定测试"
        )

        # 检查判定结果
        judgment_file = self.test_output_dir / "billing_judgments.csv"
        if success and judgment_file.exists():
            try:
                judgment_df = pd.read_csv(judgment_file)
                print(f"  判定结果: {len(judgment_df)} 行")
                if 'judgment' in judgment_df.columns:
                    judgments = judgment_df['judgment'].value_counts()
                    total = len(judgment_df)
                    print(f"    - 合理收费: {judgments.get('reasonable', 0)} 条 ({judgments.get('reasonable', 0)/total*100:.1f}%)")
                    print(f"    - 不合理收费: {judgments.get('unreasonable', 0)} 条 ({judgments.get('unreasonable', 0)/total*100:.1f}%)")
                    print(f"    - 不确定收费: {judgments.get('uncertain', 0)} 条 ({judgments.get('uncertain', 0)/total*100:.1f}%)")

                if 'confidence_score' in judgment_df.columns:
                    avg_confidence = judgment_df['confidence_score'].mean()
                    print(f"    - 平均置信度: {avg_confidence:.3f}")

            except Exception as e:
                print(f"  判定结果分析失败: {e}")

        self.test_results['billing_judgment'] = {
            'success': success,
            'duration': duration,
            'model_used': str(latest_model),
            'feature_engineer_used': str(latest_feature_engineer),
            'judgment_file': str(judgment_file)
        }

        return success

    def generate_test_report(self):
        """生成测试报告"""
        print(f"\n生成测试报告...")
        
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'test_data_path': str(self.test_data_path),
            'test_output_dir': str(self.test_output_dir),
            'results': self.test_results
        }
        
        # 保存详细报告到reports目录
        report_file = self.reports_dir / f"large_scale_end_to_end_test_report_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要报告
        print(f"\n" + "="*60)
        print(f"千万级数据处理系统端到端测试报告")
        print(f"="*60)
        
        total_duration = sum(result.get('duration', 0) for result in self.test_results.values())
        success_count = sum(1 for result in self.test_results.values() if result.get('success', False))
        total_count = len(self.test_results)
        
        print(f"\n总体结果:")
        print(f"  - 测试项目: {total_count}")
        print(f"  - 成功项目: {success_count}")
        print(f"  - 成功率: {success_count/total_count*100:.1f}%")
        print(f"  - 总耗时: {total_duration:.2f}秒")
        
        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "成功" if result.get('success', False) else "失败"
            duration = result.get('duration', 0)
            print(f"  - {test_name}: {status} ({duration:.2f}秒)")
        
        print(f"\n详细报告已保存: {report_file}")
        
        return success_count == total_count


def main():
    """主函数"""
    print("千万级数据处理系统端到端测试")
    print("=" * 60)
    
    try:
        # 初始化测试器
        tester = LargeScaleEndToEndTester()
        
        # 检查前置条件
        if not tester.check_prerequisites():
            print("前置条件检查失败")
            return False
        
        # 准备测试数据
        train_path, test_path = tester.prepare_test_data()
        
        # 执行测试步骤
        print(f"\n开始端到端测试...")
        
        # 步骤1: 特征工程测试
        if not tester.test_feature_engineering(train_path):
            print("特征工程测试失败，停止后续测试")
            return False
        
        # 步骤2: 模型训练测试
        if not tester.test_model_training(train_path):
            print("模型训练测试失败，停止后续测试")
            return False
        
        # 步骤3: 模型评估测试
        if not tester.test_model_evaluation(test_path):
            print("模型评估测试失败，但继续预测测试")
        
        # 步骤4: 预测服务测试
        if not tester.test_prediction(test_path):
            print("预测服务测试失败")

        # 步骤5: 收费合理性判定测试
        if not tester.test_billing_judgment(test_path):
            print("收费合理性判定测试失败")

        # 生成测试报告
        all_success = tester.generate_test_report()
        
        if all_success:
            print(f"\n端到端测试全部成功！")
            print(f"千万级数据处理系统验证通过，可以投入生产使用")
        else:
            print(f"\n端到端测试部分失败，请检查详细报告")
        
        return all_success
        
    except Exception as e:
        print(f"端到端测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
