#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据适配脚本 - 将原始26列数据映射到新的字段结构
山西电信出账稽核AI系统 v2.1.0
"""

import pandas as pd
import numpy as np
import argparse
import logging
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.config.production_config_manager import get_config_manager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/data_adapter.log')
        ]
    )
    return logging.getLogger(__name__)

def analyze_raw_data(file_path):
    """分析原始数据结构"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"分析原始数据文件: {file_path}")
    
    # 读取原始数据
    data = pd.read_csv(file_path, header=None)
    logger.info(f"原始数据形状: {data.shape}")
    
    # 分析各列的特征
    logger.info("各列统计信息:")
    for i in range(data.shape[1]):
        unique_count = data[i].nunique()
        null_count = data[i].isnull().sum()
        data_type = data[i].dtype
        sample_values = data[i].dropna().head(3).tolist()
        
        logger.info(f"列{i:2d}: 唯一值={unique_count:6d}, 空值={null_count:6d}, 类型={data_type}, 样例={sample_values}")
    
    return data

def create_column_mapping():
    """创建列映射关系"""
    # 基于数据分析和业务逻辑推断的列映射
    # 根据原始数据的实际结构进行映射
    column_mapping = {
        # 透传字段 (passthrough_columns) - 业务标识字段
        0: 'cal_type',           # 费用计算类型 (第0列)
        1: 'unit_type',          # 周期类型 (第1列)
        2: 'rate_unit',          # 周期数量 (第2列)
        3: 'final_eff_year',     # 最终生效年 (第3列)
        4: 'final_eff_mon',      # 最终生效月 (第4列)
        5: 'final_eff_day',      # 最终生效日 (第5列)
        6: 'final_exp_year',     # 最终失效年 (第6列)
        7: 'final_exp_mon',      # 最终失效月 (第7列)
        8: 'final_exp_day',      # 最终失效日 (第8列)
        9: 'cur_year_month',     # 当前年月 (第9列)
        10: 'charge_day_count',  # 计费天数 (第10列)
        11: 'month_day_count',   # 当月总天数 (第11列)
        12: 'should_fee',        # 应收费 (第12列)
        13: 'busi_flag',         # 业务标识 (第13列)
        14: 'amount',            # 账单费用金额 (第14列) - 目标字段
        15: 'offer_inst_id',     # 优惠实例ID (第15列)
        16: 'prod_inst_id',      # 产品实例ID (第16列)
        17: 'prod_id',           # 产品ID (第17列)
        18: 'offer_id',          # 优惠ID (第18列)
        19: 'sub_prod_id',       # 子产品ID (第19列)
        20: 'event_pricing_strategy_id',  # 事件定价策略ID (第20列)
        21: 'event_type_id',     # 事件类型ID (第21列)
        22: 'calc_priority',     # 计算优先级 (第22列)
        23: 'pricing_section_id', # 定价段ID (第23列)
        24: 'calc_method_id',    # 计算方法ID (第24列)
        25: 'role_id'            # 角色ID (第25列)
    }

    return column_mapping

def adapt_data(input_file, output_file):
    """适配数据格式"""
    logger = logging.getLogger(__name__)
    
    logger.info("开始数据适配...")
    
    # 分析原始数据
    raw_data = analyze_raw_data(input_file)
    
    # 获取列映射
    column_mapping = create_column_mapping()
    
    # 检查列数是否匹配
    if raw_data.shape[1] != len(column_mapping):
        logger.warning(f"列数不匹配: 原始数据{raw_data.shape[1]}列, 映射{len(column_mapping)}列")
    
    # 重命名列
    logger.info("应用列映射...")
    adapted_data = raw_data.copy()
    adapted_data.columns = [column_mapping.get(i, f'unknown_col_{i}') for i in range(adapted_data.shape[1])]
    
    # 数据类型转换和清理
    logger.info("数据类型转换...")
    
    # 数值类型字段
    numerical_fields = [
        'final_eff_year', 'final_eff_mon', 'final_eff_day',
        'final_exp_year', 'final_exp_mon', 'final_exp_day', 
        'charge_day_count', 'month_day_count', 'should_fee', 'amount'
    ]
    
    for field in numerical_fields:
        if field in adapted_data.columns:
            adapted_data[field] = pd.to_numeric(adapted_data[field], errors='coerce')
    
    # 类别类型字段
    categorical_fields = ['cal_type', 'unit_type', 'rate_unit', 'busi_flag']
    
    for field in categorical_fields:
        if field in adapted_data.columns:
            adapted_data[field] = adapted_data[field].astype('category')
    
    # 日期类型字段
    if 'cur_year_month' in adapted_data.columns:
        adapted_data['cur_year_month'] = adapted_data['cur_year_month'].astype(str)
    
    # 数据质量检查
    logger.info("数据质量检查...")
    
    # 检查空值
    null_counts = adapted_data.isnull().sum()
    if null_counts.sum() > 0:
        logger.warning(f"发现空值: \n{null_counts[null_counts > 0]}")
    
    # 检查关键字段
    config = get_config_manager()
    required_columns = config.get('data_schema.fixed_fee.required_columns', [])
    
    missing_columns = set(required_columns) - set(adapted_data.columns)
    if missing_columns:
        logger.error(f"缺失必需列: {missing_columns}")
        raise ValueError(f"缺失必需列: {missing_columns}")
    
    # 保存适配后的数据
    logger.info(f"保存适配后的数据到: {output_file}")
    adapted_data.to_csv(output_file, index=False)
    
    # 输出统计信息
    logger.info(f"适配完成:")
    logger.info(f"  - 原始数据: {raw_data.shape[0]} 行 × {raw_data.shape[1]} 列")
    logger.info(f"  - 适配数据: {adapted_data.shape[0]} 行 × {adapted_data.shape[1]} 列")
    logger.info(f"  - 字段映射: {len(column_mapping)} 个字段")
    
    return adapted_data

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据适配脚本')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出数据文件路径')
    parser.add_argument('--analyze-only', action='store_true', help='仅分析数据，不进行适配')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    try:
        # 确保输出目录存在
        output_dir = Path(args.output).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if args.analyze_only:
            # 仅分析数据
            analyze_raw_data(args.input)
        else:
            # 执行数据适配
            adapted_data = adapt_data(args.input, args.output)
            logger.info("数据适配成功完成!")
            
    except Exception as e:
        logger.error(f"数据适配失败: {e}")
        raise

if __name__ == "__main__":
    main()
