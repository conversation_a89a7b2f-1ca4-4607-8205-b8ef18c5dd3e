#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段格式迁移脚本
将旧格式的日期字段转换为新格式的年月日字段
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def convert_date_to_ymd(date_str):
    """
    将yyyymmdd格式的日期字符串转换为年、月、日
    
    Args:
        date_str: 日期字符串 (yyyymmdd格式)
        
    Returns:
        tuple: (year, month, day)
    """
    if pd.isna(date_str) or date_str == '':
        return None, None, None
    
    try:
        # 确保是字符串格式
        date_str = str(date_str).strip()
        
        # 处理不同的日期格式
        if len(date_str) == 8:  # yyyymmdd
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])
        elif len(date_str) == 6:  # yyyymm (补充01日)
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = 1
        else:
            print(f"警告: 无法解析日期格式: {date_str}")
            return None, None, None
            
        return year, month, day
        
    except Exception as e:
        print(f"错误: 日期转换失败 {date_str}: {e}")
        return None, None, None


def migrate_fixed_fee_data(input_file, output_file=None):
    """
    迁移固费数据的字段格式
    
    Args:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径，如果为None则覆盖原文件
    """
    print(f"开始迁移固费数据: {input_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(input_file)
        print(f"原始数据: {df.shape[0]} 行 × {df.shape[1]} 列")
        
        # 检查是否包含旧字段
        old_fields = ['final_eff_date', 'final_exp_date']
        missing_fields = [field for field in old_fields if field not in df.columns]
        
        if missing_fields:
            print(f"缺少必需的旧字段: {missing_fields}")
            return False
        
        # 转换生效日期
        print("转换生效日期字段...")
        eff_results = df['final_eff_date'].apply(convert_date_to_ymd)
        df['final_eff_year'] = [r[0] for r in eff_results]
        df['final_eff_mon'] = [r[1] for r in eff_results]
        df['final_eff_day'] = [r[2] for r in eff_results]
        
        # 转换失效日期
        print("转换失效日期字段...")
        exp_results = df['final_exp_date'].apply(convert_date_to_ymd)
        df['final_exp_year'] = [r[0] for r in exp_results]
        df['final_exp_mon'] = [r[1] for r in exp_results]
        df['final_exp_day'] = [r[2] for r in exp_results]
        
        # 移除旧字段
        print("🗑️ 移除旧字段...")
        df = df.drop(columns=['final_eff_date', 'final_exp_date'])
        
        # 重新排列列顺序，将新字段放在合适位置
        new_columns = []
        for col in df.columns:
            new_columns.append(col)
            if col == 'rate_unit':
                # 在rate_unit后插入新的日期字段
                new_columns.extend([
                    'final_eff_year', 'final_eff_mon', 'final_eff_day',
                    'final_exp_year', 'final_exp_mon', 'final_exp_day'
                ])
        
        # 去重并保持顺序
        seen = set()
        ordered_columns = []
        for col in new_columns:
            if col not in seen:
                ordered_columns.append(col)
                seen.add(col)
        
        df = df[ordered_columns]
        
        # 保存结果
        if output_file is None:
            output_file = input_file
        
        df.to_excel(output_file, index=False)
        print(f"迁移完成: {df.shape[0]} 行 × {df.shape[1]} 列")
        print(f"保存到: {output_file}")
        
        # 显示转换统计
        print("\n转换统计:")
        print(f"  - 生效日期转换成功: {df['final_eff_year'].notna().sum()} 条")
        print(f"  - 失效日期转换成功: {df['final_exp_year'].notna().sum()} 条")
        print(f"  - 生效日期转换失败: {df['final_eff_year'].isna().sum()} 条")
        print(f"  - 失效日期转换失败: {df['final_exp_year'].isna().sum()} 条")
        
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        return False


def validate_migrated_data(file_path):
    """
    验证迁移后的数据格式
    
    Args:
        file_path: 迁移后的Excel文件路径
    """
    print(f"\n验证迁移后的数据: {file_path}")
    
    try:
        df = pd.read_excel(file_path)
        
        # 检查新字段是否存在
        new_fields = [
            'final_eff_year', 'final_eff_mon', 'final_eff_day',
            'final_exp_year', 'final_exp_mon', 'final_exp_day'
        ]
        
        missing_new_fields = [field for field in new_fields if field not in df.columns]
        if missing_new_fields:
            print(f"缺少新字段: {missing_new_fields}")
            return False
        
        # 检查旧字段是否已移除
        old_fields = ['final_eff_date', 'final_exp_date']
        remaining_old_fields = [field for field in old_fields if field in df.columns]
        if remaining_old_fields:
            print(f"旧字段未移除: {remaining_old_fields}")
            return False
        
        # 检查数据范围
        print("数据范围检查:")
        for field in ['final_eff_year', 'final_exp_year']:
            if field in df.columns:
                valid_years = df[field].dropna()
                if len(valid_years) > 0:
                    print(f"  - {field}: {valid_years.min()} - {valid_years.max()}")
        
        for field in ['final_eff_mon', 'final_exp_mon']:
            if field in df.columns:
                valid_months = df[field].dropna()
                if len(valid_months) > 0:
                    invalid_months = valid_months[(valid_months < 1) | (valid_months > 12)]
                    if len(invalid_months) > 0:
                        print(f"{field} 包含无效月份: {invalid_months.unique()}")
                    else:
                        print(f"  - {field}: {valid_months.min()} - {valid_months.max()}")
        
        for field in ['final_eff_day', 'final_exp_day']:
            if field in df.columns:
                valid_days = df[field].dropna()
                if len(valid_days) > 0:
                    invalid_days = valid_days[(valid_days < 1) | (valid_days > 31)]
                    if len(invalid_days) > 0:
                        print(f"{field} 包含无效日期: {invalid_days.unique()}")
                    else:
                        print(f"  - {field}: {valid_days.min()} - {valid_days.max()}")
        
        print("数据验证通过")
        return True
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False


def main():
    """主函数"""
    print("固费数据字段格式迁移工具")
    print("=" * 50)
    
    # 默认处理样例数据
    input_file = project_root / "数据" / "固费预测样例@20250707.xlsx"
    backup_file = project_root / "数据" / f"固费预测样例@20250707_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    if not input_file.exists():
        print(f"输入文件不存在: {input_file}")
        return
    
    # 创建备份
    print(f"创建备份: {backup_file}")
    import shutil
    shutil.copy2(input_file, backup_file)
    
    # 执行迁移
    success = migrate_fixed_fee_data(str(input_file))
    
    if success:
        # 验证迁移结果
        validate_migrated_data(str(input_file))
        print(f"\n迁移完成！")
        print(f"原文件备份: {backup_file}")
        print(f"迁移后文件: {input_file}")
    else:
        print(f"\n迁移失败，请检查错误信息")


if __name__ == "__main__":
    main()
