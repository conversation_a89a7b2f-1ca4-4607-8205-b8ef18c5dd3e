#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量将文档文件名改为中文
"""

import os
import shutil
from pathlib import Path

def rename_documents():
    """重命名文档文件"""
    print("开始批量重命名文档为中文")
    print("=" * 50)
    
    # 项目根目录
    project_root = Path(__file__).parent.parent.parent
    docs_dir = project_root / "docs"
    
    # 重命名映射表
    rename_map = {
        # 核心文档
        "docs/core/DOCUMENT_INDEX.md": "docs/core/文档索引.md",
        "docs/core/TECHNICAL_SPECIFICATIONS.md": "docs/core/技术规格文档.md",
        
        # 指南文档
        "docs/guides/API_USAGE_GUIDE.md": "docs/guides/API使用指南.md",
        "docs/guides/LARGE_SCALE_END_TO_END_GUIDE.md": "docs/guides/大规模端到端指南.md",
        "docs/guides/PRODUCTION_SCRIPTS_GUIDE.md": "docs/guides/生产脚本指南.md",
        "docs/guides/QUICK_START_GUIDE.md": "docs/guides/快速开始指南.md",
        
        # 技术文档
        "docs/technical/BILLING_JUDGMENT_ADAPTATION_REPORT.md": "docs/technical/收费判定适配报告.md",
        "docs/technical/CONFIG_MANAGEMENT_IMPROVEMENTS.md": "docs/technical/配置管理改进.md",
        "docs/technical/CONFIG_MANAGEMENT_E2E_TEST_REPORT.md": "docs/technical/配置管理端到端测试报告.md",
        "docs/technical/CODE_SEPARATION_SUMMARY.md": "docs/technical/代码分离总结.md",
        "docs/technical/END_TO_END_TEST_REPORT.md": "docs/technical/端到端测试报告.md",
        "docs/technical/LARGE_SCALE_DATA_PROCESSING_GUIDE.md": "docs/technical/大规模数据处理指南.md",
        "docs/technical/PRODUCTION_DEPLOYMENT_GUIDE.md": "docs/technical/生产环境部署指南.md",
        "docs/technical/PRODUCTION_SCRIPTS_REFACTORING.md": "docs/technical/生产脚本重构.md",
        "docs/technical/CHANGELOG_CONFIG_MANAGEMENT.md": "docs/technical/配置管理变更日志.md",
        
        # 报告文档
        "docs/reports/MOCK_DATA_GENERATION_REPORT.md": "docs/reports/模拟数据生成报告.md",
        "docs/reports/MODEL_TRAINING_SUMMARY.md": "docs/reports/模型训练总结.md",
        "docs/reports/MULTI_ALGORITHM_COMPARISON_REPORT.md": "docs/reports/多算法对比报告.md",
        
        # 归档文档
        "docs/archive/CODE_DOCUMENTATION.md": "docs/archive/代码文档.md",
        "docs/archive/FIELD_CHANGES_LOG.md": "docs/archive/字段变更日志.md",
        "docs/archive/MODEL_RETRAINING_REPORT.md": "docs/archive/模型重训练报告.md",
        "docs/archive/MODEL_RETRAINING_SUMMARY.md": "docs/archive/模型重训练总结.md",
        "docs/archive/MULTI_ALGORITHM_TRAINING_SUMMARY.md": "docs/archive/多算法训练总结.md",
        "docs/archive/PREPROCESSING_UPDATE_REPORT.md": "docs/archive/预处理更新报告.md",
        
        # 顶级文档
        "docs/DOCS_INDEX.md": "docs/文档中心.md"
    }
    
    renamed_count = 0
    error_count = 0
    
    for old_path, new_path in rename_map.items():
        old_file = project_root / old_path
        new_file = project_root / new_path
        
        try:
            if old_file.exists():
                # 确保目标目录存在
                new_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 重命名文件
                shutil.move(str(old_file), str(new_file))
                print(f"已重命名: {old_path} -> {new_path}")
                renamed_count += 1
            else:
                print(f"文件不存在: {old_path}")
                
        except Exception as e:
            print(f"重命名失败 {old_path}: {e}")
            error_count += 1
    
    print(f"\n重命名完成:")
    print(f"  - 成功重命名: {renamed_count}")
    print(f"  - 失败: {error_count}")
    
    return renamed_count, error_count

def update_file_references():
    """更新文件中的引用"""
    print("\n开始更新文件引用...")
    
    # 引用更新映射
    reference_updates = {
        "DOCUMENT_INDEX.md": "文档索引.md",
        "TECHNICAL_SPECIFICATIONS.md": "技术规格文档.md",
        "API_USAGE_GUIDE.md": "API使用指南.md",
        "LARGE_SCALE_END_TO_END_GUIDE.md": "大规模端到端指南.md",
        "PRODUCTION_SCRIPTS_GUIDE.md": "生产脚本指南.md",
        "QUICK_START_GUIDE.md": "快速开始指南.md",
        "BILLING_JUDGMENT_ADAPTATION_REPORT.md": "收费判定适配报告.md",
        "CONFIG_MANAGEMENT_IMPROVEMENTS.md": "配置管理改进.md",
        "CONFIG_MANAGEMENT_E2E_TEST_REPORT.md": "配置管理端到端测试报告.md",
        "CODE_SEPARATION_SUMMARY.md": "代码分离总结.md",
        "END_TO_END_TEST_REPORT.md": "端到端测试报告.md",
        "LARGE_SCALE_DATA_PROCESSING_GUIDE.md": "大规模数据处理指南.md",
        "PRODUCTION_DEPLOYMENT_GUIDE.md": "生产环境部署指南.md",
        "PRODUCTION_SCRIPTS_REFACTORING.md": "生产脚本重构.md",
        "DOCS_INDEX.md": "文档中心.md"
    }
    
    project_root = Path(__file__).parent.parent.parent
    
    # 需要更新引用的文件
    files_to_update = [
        project_root / "README.md",
        project_root / "docs" / "core" / "README.md",
        project_root / "docs" / "core" / "文档索引.md"
    ]
    
    updated_files = 0
    
    for file_path in files_to_update:
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 更新所有引用
                for old_name, new_name in reference_updates.items():
                    content = content.replace(old_name, new_name)
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"已更新引用: {file_path}")
                    updated_files += 1
                    
            except Exception as e:
                print(f"更新引用失败 {file_path}: {e}")
    
    print(f"引用更新完成: {updated_files} 个文件")
    return updated_files

def main():
    """主函数"""
    try:
        # 重命名文档
        renamed_count, error_count = rename_documents()
        
        # 更新文件引用
        updated_count = update_file_references()
        
        print(f"\n总结:")
        print(f"  - 重命名文档: {renamed_count} 个")
        print(f"  - 更新引用: {updated_count} 个文件")
        print(f"  - 错误: {error_count} 个")
        
        if error_count == 0:
            print("\n文档中文化完成！")
        else:
            print(f"\n文档中文化完成，但有 {error_count} 个错误")
            
    except Exception as e:
        print(f"执行失败: {e}")

if __name__ == "__main__":
    main()
