#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用新特征集重新训练模型
基于更新后的预处理和特征工程逻辑训练模型
"""

import sys
import pandas as pd
import numpy as np
import json
import pickle
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_config():
    """加载配置文件"""
    config_path = project_root / "config" / "billing_audit_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_and_preprocess_data():
    """加载和预处理数据"""
    print("步骤1: 数据加载和预处理...")
    
    # 加载配置
    config = load_config()
    data_file = project_root / config['data_sources']['fixed_fee_sample']
    fixed_fee_config = config['billing_audit']['fixed_fee']
    
    # 加载数据
    df = pd.read_excel(data_file)
    print(f"  数据加载成功: {df.shape[0]} 行 × {df.shape[1]} 列")
    
    # 提取特征和目标变量
    feature_columns = fixed_fee_config['feature_columns']
    target_column = fixed_fee_config['target_column']
    passthrough_columns = fixed_fee_config['passthrough_columns']
    
    X = df[feature_columns].copy()
    y = df[target_column].copy()
    passthrough = df[passthrough_columns].copy()
    
    print(f"  特征提取完成: {X.shape[1]} 个特征")
    print(f"  目标变量: {len(y)} 个样本")
    print(f"  透传字段: {passthrough.shape[1]} 个字段")
    
    return X, y, passthrough, fixed_fee_config


def create_enhanced_features(X, config):
    """创建增强的特征"""
    print("步骤2: 特征工程...")
    
    X_features = X.copy()
    
    # 1. 处理类别变量
    categorical_columns = config['categorical_columns']
    label_encoders = {}
    
    for col in categorical_columns:
        if col in X_features.columns:
            le = LabelEncoder()
            X_features[col] = le.fit_transform(X_features[col].astype(str))
            label_encoders[col] = le
    
    print(f"  类别变量编码完成: {len(categorical_columns)} 个字段")
    
    # 2. 创建基于新字段的日期特征
    if all(col in X_features.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']):
        try:
            # 组合生效日期
            eff_date = pd.to_datetime(
                X_features[['final_eff_year', 'final_eff_mon', 'final_eff_day']].rename(
                    columns={'final_eff_year': 'year', 'final_eff_mon': 'month', 'final_eff_day': 'day'}
                ), errors='coerce'
            )
            
            # 创建生效日期的额外特征
            X_features['final_eff_dayofweek'] = eff_date.dt.dayofweek
            X_features['final_eff_quarter'] = eff_date.dt.quarter
            X_features['final_eff_is_weekend'] = (eff_date.dt.dayofweek >= 5).astype(int)
            X_features['final_eff_month_start'] = (eff_date.dt.day == 1).astype(int)
            
            print(f"  生效日期特征创建成功")
            
        except Exception as e:
            print(f"  生效日期特征创建失败: {e}")
    
    if all(col in X_features.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day']):
        try:
            # 组合失效日期
            exp_date = pd.to_datetime(
                X_features[['final_exp_year', 'final_exp_mon', 'final_exp_day']].rename(
                    columns={'final_exp_year': 'year', 'final_exp_mon': 'month', 'final_exp_day': 'day'}
                ), errors='coerce'
            )
            
            # 创建失效日期的额外特征
            X_features['final_exp_dayofweek'] = exp_date.dt.dayofweek
            X_features['final_exp_quarter'] = exp_date.dt.quarter
            X_features['final_exp_is_weekend'] = (exp_date.dt.dayofweek >= 5).astype(int)
            X_features['final_exp_month_end'] = (exp_date.dt.day >= 28).astype(int)
            
            print(f"  失效日期特征创建成功")
            
        except Exception as e:
            print(f"  失效日期特征创建失败: {e}")
    
    # 3. 创建组合特征
    if (all(col in X_features.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']) and
        all(col in X_features.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day'])):
        try:
            # 计算订阅时长（天数）
            X_features['subscription_duration_days'] = (exp_date - eff_date).dt.days
            
            # 计算当前月份与生效月份的差异
            if 'cur_year_month' in X_features.columns:
                cur_date = pd.to_datetime(X_features['cur_year_month'], format='%Y%m', errors='coerce')
                X_features['months_since_effective'] = (
                    (cur_date.dt.year - eff_date.dt.year) * 12 + 
                    (cur_date.dt.month - eff_date.dt.month)
                )
                
                # 计算距离失效的月数
                X_features['months_until_expiry'] = (
                    (exp_date.dt.year - cur_date.dt.year) * 12 + 
                    (exp_date.dt.month - cur_date.dt.month)
                )
            
            # 创建生效和失效的季度差异
            X_features['quarter_diff'] = (
                exp_date.dt.quarter - eff_date.dt.quarter + 
                (exp_date.dt.year - eff_date.dt.year) * 4
            )
            
            print(f"  组合日期特征创建成功")
            
        except Exception as e:
            print(f"  组合日期特征创建失败: {e}")
    
    # 4. 创建业务逻辑特征
    if 'cal_type' in X_features.columns and 'charge_day_count' in X_features.columns:
        # 计费效率特征
        X_features['billing_efficiency'] = X_features['charge_day_count'] / X_features['month_day_count']
        
        # 计费类型与天数的交互特征
        X_features['cal_type_day_interaction'] = X_features['cal_type'] * X_features['charge_day_count']
    
    if 'should_fee' in X_features.columns and 'charge_day_count' in X_features.columns:
        # 日均应收费用
        X_features['daily_should_fee'] = X_features['should_fee'] / np.maximum(X_features['charge_day_count'], 1)
    
    print(f"  业务逻辑特征创建成功")
    
    # 5. 标准化数值特征
    numerical_columns = config['numerical_columns']
    scaler = StandardScaler()
    
    # 找出实际存在的数值列
    existing_numerical_cols = [col for col in numerical_columns if col in X_features.columns]
    
    if existing_numerical_cols:
        X_features[existing_numerical_cols] = scaler.fit_transform(X_features[existing_numerical_cols])
        print(f"  数值特征标准化完成: {len(existing_numerical_cols)} 个字段")
    
    # 处理日期字段
    date_columns = config['date_columns']
    for col in date_columns:
        if col in X_features.columns:
            # 将yyyymm格式转换为数值
            X_features[col] = pd.to_numeric(X_features[col], errors='coerce')
    
    print(f"  日期字段处理完成: {len(date_columns)} 个字段")
    
    # 显示特征工程结果
    original_features = X.shape[1]
    final_features = X_features.shape[1]
    new_features = final_features - original_features
    
    print(f"  特征工程总结:")
    print(f"    - 原始特征数: {original_features}")
    print(f"    - 最终特征数: {final_features}")
    print(f"    - 新增特征数: {new_features}")
    
    return X_features, label_encoders, scaler


def train_enhanced_model(X, y):
    """训练增强的模型"""
    print("\n步骤3: 模型训练...")
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=None
    )
    
    print(f"  数据分割完成:")
    print(f"    - 训练集: {X_train.shape[0]} 样本")
    print(f"    - 测试集: {X_test.shape[0]} 样本")
    print(f"    - 特征数量: {X_train.shape[1]}")
    
    # 训练模型
    model = RandomForestRegressor(
        n_estimators=200,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    
    print("  开始训练模型...")
    start_time = datetime.now()
    model.fit(X_train, y_train)
    training_time = (datetime.now() - start_time).total_seconds()
    
    print(f"  模型训练完成，耗时: {training_time:.2f}秒")
    
    # 预测
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # 评估指标
    train_mae = mean_absolute_error(y_train, y_pred_train)
    test_mae = mean_absolute_error(y_test, y_pred_test)
    
    train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
    test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
    
    train_r2 = r2_score(y_train, y_pred_train)
    test_r2 = r2_score(y_test, y_pred_test)
    
    print(f"\n步骤4: 模型性能评估")
    print(f"  训练集指标:")
    print(f"    - MAE: {train_mae:.2f}")
    print(f"    - RMSE: {train_rmse:.2f}")
    print(f"    - R²: {train_r2:.4f}")
    
    print(f"  测试集指标:")
    print(f"    - MAE: {test_mae:.2f}")
    print(f"    - RMSE: {test_rmse:.2f}")
    print(f"    - R²: {test_r2:.4f}")
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\n步骤5: 特征重要性分析 (Top 15)")
    for i, row in feature_importance.head(15).iterrows():
        print(f"  {feature_importance.index.get_loc(i)+1:2d}. {row['feature']:<30} {row['importance']:.4f}")
    
    # 业务准确性评估
    threshold = 50.0  # 50元阈值
    accurate_predictions = np.abs(y_test - y_pred_test) <= threshold
    business_accuracy = accurate_predictions.mean() * 100
    
    print(f"\n💼 步骤6: 业务准确性评估")
    print(f"  - 阈值: ±{threshold}元")
    print(f"  - 准确率: {business_accuracy:.1f}%")
    
    # 保存模型和相关文件
    models_dir = project_root / "models" / "billing_audit"
    models_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_filename = f"fixed_fee_enhanced_model_{timestamp}"
    
    # 保存模型
    model_path = models_dir / f"{model_filename}.pkl"
    joblib.dump(model, model_path)
    
    # 保存特征重要性
    importance_path = models_dir / f"{model_filename}_feature_importance.csv"
    feature_importance.to_csv(importance_path, index=False)
    
    print(f"\n步骤7: 模型保存")
    print(f"  模型保存到: {model_path}")
    print(f"  特征重要性保存到: {importance_path}")
    
    return {
        'model': model,
        'metrics': {
            'train_mae': train_mae,
            'test_mae': test_mae,
            'train_rmse': train_rmse,
            'test_rmse': test_rmse,
            'train_r2': train_r2,
            'test_r2': test_r2,
            'business_accuracy': business_accuracy
        },
        'feature_importance': feature_importance,
        'model_path': str(model_path),
        'training_time': training_time
    }


def main():
    """主函数"""
    print("使用新特征集重新训练模型")
    print("=" * 60)
    
    try:
        # 加载和预处理数据
        X, y, passthrough, config = load_and_preprocess_data()
        
        # 创建增强特征
        X_enhanced, label_encoders, scaler = create_enhanced_features(X, config)
        
        # 训练增强模型
        result = train_enhanced_model(X_enhanced, y)
        
        print(f"\n模型重训练完成！")
        print(f"最终结果总结:")
        print(f"  - 特征数量: {X_enhanced.shape[1]} (原始: {X.shape[1]})")
        print(f"  - 样本数量: {X_enhanced.shape[0]}")
        print(f"  - 测试集MAE: {result['metrics']['test_mae']:.2f}元")
        print(f"  - 测试集R²: {result['metrics']['test_r2']:.4f}")
        print(f"  - 业务准确率: {result['metrics']['business_accuracy']:.1f}%")
        print(f"  - 训练时间: {result['training_time']:.2f}秒")
        print(f"  - 模型路径: {result['model_path']}")
        
        # 判断模型质量
        r2_score = result['metrics']['test_r2']
        if r2_score > 0.9:
            print("模型质量: 优秀！")
        elif r2_score > 0.8:
            print("模型质量: 良好！")
        elif r2_score > 0.6:
            print("模型质量: 一般，建议优化")
        else:
            print("模型质量: 较差，需要改进")
        
        return True
        
    except Exception as e:
        print(f"模型重训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
