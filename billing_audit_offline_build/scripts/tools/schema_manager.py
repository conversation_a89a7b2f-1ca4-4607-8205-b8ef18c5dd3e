#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模式管理工具
支持配置化字段管理、验证和文档生成
山西电信出账稽核AI系统 v2.1.0
"""

import argparse
import json
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.config.production_config_manager import get_config_manager
from src.config.dynamic_schema_manager import get_dynamic_schema_manager


def validate_schema(data_type='fixed_fee'):
    """验证数据模式配置"""
    print(f"🔍 验证数据模式配置: {data_type}")
    print("=" * 60)
    
    # 获取管理器
    config_manager = get_config_manager()
    schema_manager = get_dynamic_schema_manager(config_manager)
    
    # 执行验证
    validation_result = schema_manager.validate_schema_consistency(data_type)
    
    # 显示统计信息
    stats = validation_result['statistics']
    print(f"📊 字段统计:")
    print(f"  - 训练特征: {stats['training_features_count']} 个")
    print(f"  - 透传字段: {stats['passthrough_columns_count']} 个")
    print(f"  - 目标字段: 1 个")
    print(f"  - 总字段数: {stats['total_fields']} 个")
    print(f"  - 类别字段: {stats['categorical_count']} 个")
    print(f"  - 数值字段: {stats['numerical_count']} 个")
    print(f"  - 日期字段: {stats['date_count']} 个")
    print()
    
    # 显示验证结果
    if validation_result['valid']:
        print("✅ 验证通过: 数据模式配置正确")
    else:
        print("❌ 验证失败: 发现配置问题")
        
        if validation_result['errors']:
            print("\n🚨 错误:")
            for error in validation_result['errors']:
                print(f"  - {error}")
        
        if validation_result['warnings']:
            print("\n⚠️ 警告:")
            for warning in validation_result['warnings']:
                print(f"  - {warning}")
    
    return validation_result['valid']


def list_fields(data_type='fixed_fee', category=None):
    """列出字段信息"""
    print(f"📋 字段列表: {data_type}")
    if category:
        print(f"   类别: {category}")
    print("=" * 60)
    
    # 获取管理器
    config_manager = get_config_manager()
    schema_manager = get_dynamic_schema_manager(config_manager)
    
    if category:
        # 显示特定类别的字段
        fields = schema_manager.get_field_by_category(category, data_type)
        print(f"\n{category} ({len(fields)} 个字段):")
        for i, field in enumerate(fields, 1):
            metadata = schema_manager.get_field_metadata(field, data_type)
            print(f"  {i:2d}. {field} ({metadata['data_type']}) - {metadata['description']}")
    else:
        # 显示所有字段，按类别分组
        categories = [
            ('training_features', '训练特征'),
            ('target_column', '目标字段'),
            ('passthrough_columns', '透传字段')
        ]
        
        for cat_key, cat_name in categories:
            if cat_key == 'target_column':
                target = schema_manager.get_field_by_category(cat_key, data_type)
                if target and target[0]:
                    print(f"\n{cat_name} (1 个字段):")
                    metadata = schema_manager.get_field_metadata(target[0], data_type)
                    print(f"  1. {target[0]} ({metadata['data_type']}) - {metadata['description']}")
            else:
                fields = schema_manager.get_field_by_category(cat_key, data_type)
                if fields:
                    print(f"\n{cat_name} ({len(fields)} 个字段):")
                    for i, field in enumerate(fields, 1):
                        metadata = schema_manager.get_field_metadata(field, data_type)
                        print(f"  {i:2d}. {field} ({metadata['data_type']}) - {metadata['description']}")


def generate_documentation(data_type='fixed_fee', output_file=None):
    """生成模式文档"""
    print(f"📖 生成模式文档: {data_type}")
    print("=" * 60)
    
    # 获取管理器
    config_manager = get_config_manager()
    schema_manager = get_dynamic_schema_manager(config_manager)
    
    # 生成文档
    documentation = schema_manager.export_schema_documentation(data_type)
    
    if output_file:
        # 保存到文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(documentation)
        
        print(f"✅ 文档已保存: {output_path}")
    else:
        # 输出到控制台
        print(documentation)


def generate_mapping_code(data_type='fixed_fee', output_file=None):
    """生成字段映射代码"""
    print(f"🔧 生成字段映射代码: {data_type}")
    print("=" * 60)
    
    # 获取管理器
    config_manager = get_config_manager()
    schema_manager = get_dynamic_schema_manager(config_manager)
    
    # 生成代码
    mapping_code = schema_manager.generate_field_mapping_code(data_type)
    
    if output_file:
        # 保存到文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(mapping_code)
        
        print(f"✅ 映射代码已保存: {output_path}")
    else:
        # 输出到控制台
        print(mapping_code)


def show_schema_info(data_type='fixed_fee'):
    """显示模式信息概览"""
    print(f"ℹ️ 数据模式信息: {data_type}")
    print("=" * 60)
    
    # 获取管理器
    config_manager = get_config_manager()
    schema_manager = get_dynamic_schema_manager(config_manager)
    
    # 获取模式信息
    schema = schema_manager.get_field_schema(data_type)
    validation = schema_manager.validate_schema_consistency(data_type)
    
    # 显示基本信息
    print(f"📊 字段统计:")
    stats = validation['statistics']
    print(f"  - 训练特征: {stats['training_features_count']} 个")
    print(f"  - 透传字段: {stats['passthrough_columns_count']} 个")
    print(f"  - 目标字段: 1 个")
    print(f"  - 总字段数: {stats['total_fields']} 个")
    print()
    
    # 显示数据类型分布
    print(f"🏷️ 数据类型分布:")
    print(f"  - 类别字段: {stats['categorical_count']} 个")
    print(f"  - 数值字段: {stats['numerical_count']} 个")
    print(f"  - 日期字段: {stats['date_count']} 个")
    print()
    
    # 显示验证状态
    status_icon = "✅" if validation['valid'] else "❌"
    print(f"🔍 验证状态: {status_icon} {'通过' if validation['valid'] else '失败'}")
    
    if validation['errors']:
        print(f"  错误: {len(validation['errors'])} 个")
    if validation['warnings']:
        print(f"  警告: {len(validation['warnings'])} 个")
    
    print()
    
    # 显示配置路径
    print(f"📁 配置信息:")
    print(f"  - 配置文件: {getattr(config_manager, 'config_path', 'config/production_config.json')}")
    print(f"  - 数据类型: {data_type}")
    print(f"  - 模式路径: data_schema.{data_type}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据模式管理工具')
    parser.add_argument('--data-type', '-t', default='fixed_fee', help='数据类型 (默认: fixed_fee)')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 验证命令
    validate_parser = subparsers.add_parser('validate', help='验证数据模式配置')
    
    # 列出字段命令
    list_parser = subparsers.add_parser('list', help='列出字段信息')
    list_parser.add_argument('--category', '-c', help='字段类别 (training_features, target_column, passthrough_columns)')
    
    # 生成文档命令
    doc_parser = subparsers.add_parser('doc', help='生成模式文档')
    doc_parser.add_argument('--output', '-o', help='输出文件路径')
    
    # 生成代码命令
    code_parser = subparsers.add_parser('code', help='生成字段映射代码')
    code_parser.add_argument('--output', '-o', help='输出文件路径')
    
    # 信息命令
    info_parser = subparsers.add_parser('info', help='显示模式信息概览')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'validate':
            success = validate_schema(args.data_type)
            sys.exit(0 if success else 1)
        
        elif args.command == 'list':
            list_fields(args.data_type, args.category)
        
        elif args.command == 'doc':
            generate_documentation(args.data_type, args.output)
        
        elif args.command == 'code':
            generate_mapping_code(args.data_type, args.output)
        
        elif args.command == 'info':
            show_schema_info(args.data_type)
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
