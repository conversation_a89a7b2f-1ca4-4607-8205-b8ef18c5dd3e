#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单数据适配脚本 - 处理带列名的原始数据
山西电信出账稽核AI系统 v2.1.0
"""

import pandas as pd
import numpy as np
import argparse
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def adapt_data_with_headers(input_file, output_file):
    """适配带列名的数据"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"读取原始数据: {input_file}")
    
    # 读取数据，第一行作为列名
    data = pd.read_csv(input_file, header=0, low_memory=False)
    
    logger.info(f"原始数据形状: {data.shape}")
    logger.info(f"列名: {list(data.columns)}")
    
    # 数据类型转换
    logger.info("执行数据类型转换...")
    
    # 数值类型字段
    numerical_fields = [
        'final_eff_year', 'final_eff_mon', 'final_eff_day',
        'final_exp_year', 'final_exp_mon', 'final_exp_day', 
        'charge_day_count', 'month_day_count', 'should_fee', 'amount'
    ]
    
    for field in numerical_fields:
        if field in data.columns:
            data[field] = pd.to_numeric(data[field], errors='coerce')
            logger.info(f"转换 {field} 为数值类型")
    
    # 类别类型字段
    categorical_fields = ['cal_type', 'unit_type', 'rate_unit', 'busi_flag']
    
    for field in categorical_fields:
        if field in data.columns:
            data[field] = pd.to_numeric(data[field], errors='coerce').astype('Int64')
            logger.info(f"转换 {field} 为整数类型")
    
    # 字符串类型字段 (透传字段)
    string_fields = [
        'offer_inst_id', 'prod_inst_id', 'prod_id', 'offer_id', 'sub_prod_id',
        'event_pricing_strategy_id', 'event_type_id', 'calc_priority',
        'pricing_section_id', 'calc_method_id', 'role_id'
    ]
    
    for field in string_fields:
        if field in data.columns:
            data[field] = data[field].astype(str)
            logger.info(f"转换 {field} 为字符串类型")
    
    # 日期类型字段
    if 'cur_year_month' in data.columns:
        data['cur_year_month'] = data['cur_year_month'].astype(str)
        logger.info("转换 cur_year_month 为字符串类型")
    
    # 数据质量检查
    logger.info("数据质量检查...")
    
    # 检查空值
    null_counts = data.isnull().sum()
    if null_counts.sum() > 0:
        logger.warning(f"发现空值:")
        for col, count in null_counts[null_counts > 0].items():
            logger.warning(f"  {col}: {count} 个空值")
    
    # 检查关键字段的数据分布
    if 'amount' in data.columns:
        amount_stats = data['amount'].describe()
        logger.info(f"amount 统计: 均值={amount_stats['mean']:.2f}, 标准差={amount_stats['std']:.2f}")
        
        zero_count = (data['amount'] == 0).sum()
        total_count = len(data)
        zero_ratio = zero_count / total_count
        logger.info(f"amount 零值比例: {zero_ratio:.2%} ({zero_count}/{total_count})")
    
    if 'should_fee' in data.columns:
        should_fee_stats = data['should_fee'].describe()
        logger.info(f"should_fee 统计: 均值={should_fee_stats['mean']:.2f}, 标准差={should_fee_stats['std']:.2f}")
    
    # 保存适配后的数据
    logger.info(f"保存适配后的数据到: {output_file}")
    data.to_csv(output_file, index=False)
    
    # 输出统计信息
    logger.info(f"适配完成:")
    logger.info(f"  - 数据行数: {data.shape[0]:,}")
    logger.info(f"  - 数据列数: {data.shape[1]}")
    logger.info(f"  - 列名: {list(data.columns)}")
    
    return data

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简单数据适配脚本')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出数据文件路径')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    try:
        # 确保输出目录存在
        output_dir = Path(args.output).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 执行数据适配
        adapted_data = adapt_data_with_headers(args.input, args.output)
        logger.info("数据适配成功完成!")
        
    except Exception as e:
        logger.error(f"数据适配失败: {e}")
        raise

if __name__ == "__main__":
    main()
