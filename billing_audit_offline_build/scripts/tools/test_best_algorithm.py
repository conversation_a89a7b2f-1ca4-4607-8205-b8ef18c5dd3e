#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最佳算法
验证RandomForest算法在不同业务场景下的预测效果
"""

import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
import joblib

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_best_model():
    """加载最佳模型 (RandomForest)"""
    print("加载最佳算法模型...")
    
    models_dir = project_root / "models" / "billing_audit" / "multi_algorithm"
    
    # 查找最新的RandomForest模型
    rf_models = list(models_dir.glob("RandomForest_model_*.pkl"))
    if not rf_models:
        print("未找到RandomForest模型文件")
        return None
    
    latest_model = max(rf_models, key=lambda x: x.stat().st_mtime)
    
    try:
        model = joblib.load(latest_model)
        print(f"模型加载成功: {latest_model.name}")
        print(f"  - 模型类型: {type(model).__name__}")
        print(f"  - 特征数量: {model.n_features_in_}")
        print(f"  - 树的数量: {model.n_estimators}")
        return model
    except Exception as e:
        print(f"模型加载失败: {e}")
        return None


def create_business_scenarios():
    """创建业务场景测试数据"""
    print("\n创建业务场景测试数据...")
    
    scenarios = [
        {
            'name': '正常整月收费',
            'description': '用户正常使用，整月收费',
            'data': {
                'cal_type': 1, 'unit_type': 1, 'rate_unit': 1,
                'final_eff_year': 2024, 'final_eff_mon': 1, 'final_eff_day': 1,
                'final_exp_year': 2024, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 31, 'month_day_count': 31,
                'run_code': 'NORMAL', 'run_time': 202407, 'should_fee': 100.0, 'busi_flag': 0
            },
            'expected_range': (90, 110),
            'business_logic': '整月收费，应接近应收费用'
        },
        {
            'name': '按天折算收费',
            'description': '月中开通，按天折算',
            'data': {
                'cal_type': 2, 'unit_type': 1, 'rate_unit': 1,
                'final_eff_year': 2024, 'final_eff_mon': 7, 'final_eff_day': 15,
                'final_exp_year': 2024, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 17, 'month_day_count': 31,
                'run_code': 'NORMAL', 'run_time': 202407, 'should_fee': 50.0, 'busi_flag': 0
            },
            'expected_range': (40, 60),
            'business_logic': '按天折算，费用应按比例计算'
        },
        {
            'name': '不收费场景',
            'description': '业务规则不收费',
            'data': {
                'cal_type': 0, 'unit_type': 0, 'rate_unit': 0,
                'final_eff_year': 2024, 'final_eff_mon': 1, 'final_eff_day': 1,
                'final_exp_year': 2024, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 0, 'month_day_count': 31,
                'run_code': 'STOP', 'run_time': 202407, 'should_fee': 0.0, 'busi_flag': 1
            },
            'expected_range': (0, 5),
            'business_logic': '不收费标识，应为零或接近零'
        },
        {
            'name': '高额收费',
            'description': '高价值套餐用户',
            'data': {
                'cal_type': 1, 'unit_type': 1, 'rate_unit': 1,
                'final_eff_year': 2024, 'final_eff_mon': 1, 'final_eff_day': 1,
                'final_exp_year': 2025, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 31, 'month_day_count': 31,
                'run_code': 'NORMAL', 'run_time': 202407, 'should_fee': 300.0, 'busi_flag': 0
            },
            'expected_range': (250, 350),
            'business_logic': '高价值用户，费用应较高'
        },
        {
            'name': '停机用户',
            'description': '用户停机状态',
            'data': {
                'cal_type': 0, 'unit_type': 0, 'rate_unit': 0,
                'final_eff_year': 2024, 'final_eff_mon': 1, 'final_eff_day': 1,
                'final_exp_year': 2024, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 0, 'month_day_count': 31,
                'run_code': 'STOP', 'run_time': 202407, 'should_fee': 0.0, 'busi_flag': 0
            },
            'expected_range': (0, 10),
            'business_logic': '停机状态，通常不收费或收费很少'
        }
    ]
    
    print(f"创建了 {len(scenarios)} 个业务场景")
    return scenarios


def prepare_features_for_prediction(scenario_data):
    """为预测准备特征"""
    # 创建DataFrame
    df = pd.DataFrame([scenario_data])
    
    # 1. 编码类别变量
    from sklearn.preprocessing import LabelEncoder
    
    categorical_columns = ['cal_type', 'unit_type', 'rate_unit', 'run_code', 'busi_flag']
    for col in categorical_columns:
        if col in df.columns:
            le = LabelEncoder()
            df[col] = le.fit_transform(df[col].astype(str))
    
    # 2. 创建日期特征
    try:
        # 组合生效日期
        eff_date = pd.to_datetime(
            df[['final_eff_year', 'final_eff_mon', 'final_eff_day']].rename(
                columns={'final_eff_year': 'year', 'final_eff_mon': 'month', 'final_eff_day': 'day'}
            ), errors='coerce'
        )
        
        # 组合失效日期
        exp_date = pd.to_datetime(
            df[['final_exp_year', 'final_exp_mon', 'final_exp_day']].rename(
                columns={'final_exp_year': 'year', 'final_exp_mon': 'month', 'final_exp_day': 'day'}
            ), errors='coerce'
        )
        
        # 创建基础日期特征
        df['final_eff_dayofweek'] = eff_date.dt.dayofweek
        df['final_eff_quarter'] = eff_date.dt.quarter
        df['final_eff_is_weekend'] = (eff_date.dt.dayofweek >= 5).astype(int)
        
        df['final_exp_dayofweek'] = exp_date.dt.dayofweek
        df['final_exp_quarter'] = exp_date.dt.quarter
        df['final_exp_is_weekend'] = (exp_date.dt.dayofweek >= 5).astype(int)
        
        # 创建组合特征
        df['subscription_duration_days'] = (exp_date - eff_date).dt.days
        
        cur_date = pd.to_datetime(df['cur_year_month'], format='%Y%m', errors='coerce')
        df['months_since_effective'] = (
            (cur_date.dt.year - eff_date.dt.year) * 12 + 
            (cur_date.dt.month - eff_date.dt.month)
        )
        df['months_until_expiry'] = (
            (exp_date.dt.year - cur_date.dt.year) * 12 + 
            (exp_date.dt.month - cur_date.dt.month)
        )
        df['quarter_diff'] = (
            exp_date.dt.quarter - eff_date.dt.quarter + 
            (exp_date.dt.year - eff_date.dt.year) * 4
        )
        
    except Exception as e:
        print(f"  日期特征创建失败: {e}")
    
    # 3. 创建业务特征
    try:
        df['billing_efficiency'] = df['charge_day_count'] / df['month_day_count']
        df['cal_type_day_interaction'] = df['cal_type'] * df['charge_day_count']
        df['daily_should_fee'] = df['should_fee'] / np.maximum(df['charge_day_count'], 1)
    except Exception as e:
        print(f"  业务特征创建失败: {e}")
    
    # 4. 标准化数值特征（简化处理）
    numerical_columns = ['final_eff_year', 'final_eff_mon', 'final_eff_day', 
                        'final_exp_year', 'final_exp_mon', 'final_exp_day',
                        'charge_day_count', 'month_day_count', 'should_fee']
    
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    
    for col in numerical_columns:
        if col in df.columns:
            df[col] = scaler.fit_transform(df[[col]])
    
    # 5. 处理日期字段
    df['cur_year_month'] = pd.to_numeric(df['cur_year_month'], errors='coerce')
    df['run_time'] = pd.to_numeric(df['run_time'], errors='coerce')
    
    return df


def test_business_scenarios(model, scenarios):
    """测试业务场景"""
    print("\n业务场景预测测试")
    print("=" * 80)
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n场景 {i}: {scenario['name']}")
        print(f"  描述: {scenario['description']}")
        print(f"  业务逻辑: {scenario['business_logic']}")
        
        try:
            # 准备特征
            X = prepare_features_for_prediction(scenario['data'])
            
            # 执行预测
            prediction = model.predict(X)[0]
            
            # 评估结果
            expected_min, expected_max = scenario['expected_range']
            is_in_range = expected_min <= prediction <= expected_max
            
            print(f"  预测结果: {prediction:.2f}元")
            print(f"  期望范围: {expected_min}-{expected_max}元")
            
            if is_in_range:
                print(f"  预测合理: 在期望范围内")
                status = "合理"
            else:
                if prediction < expected_min:
                    print(f"  预测偏低: 低于期望范围 {expected_min - prediction:.2f}元")
                    status = "偏低"
                else:
                    print(f"  预测偏高: 高于期望范围 {prediction - expected_max:.2f}元")
                    status = "偏高"
            
            # 计算相对误差
            expected_center = (expected_min + expected_max) / 2
            relative_error = abs(prediction - expected_center) / expected_center * 100
            print(f"  相对误差: {relative_error:.1f}%")
            
            results.append({
                'scenario': scenario['name'],
                'prediction': prediction,
                'expected_range': scenario['expected_range'],
                'status': status,
                'relative_error': relative_error,
                'in_range': is_in_range
            })
            
        except Exception as e:
            print(f"  预测失败: {e}")
            results.append({
                'scenario': scenario['name'],
                'prediction': None,
                'expected_range': scenario['expected_range'],
                'status': "失败",
                'relative_error': None,
                'in_range': False
            })
    
    return results


def analyze_test_results(results):
    """分析测试结果"""
    print(f"\n测试结果分析")
    print("=" * 60)
    
    # 统计成功率
    total_tests = len(results)
    successful_predictions = len([r for r in results if r['prediction'] is not None])
    reasonable_predictions = len([r for r in results if r['in_range']])
    
    print(f"总体统计:")
    print(f"  - 总测试场景: {total_tests}")
    print(f"  - 成功预测: {successful_predictions}/{total_tests} ({successful_predictions/total_tests*100:.1f}%)")
    print(f"  - 合理预测: {reasonable_predictions}/{total_tests} ({reasonable_predictions/total_tests*100:.1f}%)")
    
    # 详细结果表
    print(f"\n详细结果:")
    print(f"{'场景':<15} {'预测值':<10} {'期望范围':<15} {'状态':<10} {'相对误差':<10}")
    print("-" * 70)
    
    for result in results:
        if result['prediction'] is not None:
            pred_str = f"{result['prediction']:.2f}元"
            range_str = f"{result['expected_range'][0]}-{result['expected_range'][1]}元"
            error_str = f"{result['relative_error']:.1f}%" if result['relative_error'] is not None else "N/A"
        else:
            pred_str = "失败"
            range_str = f"{result['expected_range'][0]}-{result['expected_range'][1]}元"
            error_str = "N/A"
        
        print(f"{result['scenario']:<15} {pred_str:<10} {range_str:<15} {result['status']:<10} {error_str:<10}")
    
    # 误差分析
    valid_errors = [r['relative_error'] for r in results if r['relative_error'] is not None]
    if valid_errors:
        avg_error = np.mean(valid_errors)
        max_error = np.max(valid_errors)
        min_error = np.min(valid_errors)
        
        print(f"\n误差分析:")
        print(f"  - 平均相对误差: {avg_error:.1f}%")
        print(f"  - 最大相对误差: {max_error:.1f}%")
        print(f"  - 最小相对误差: {min_error:.1f}%")
    
    # 业务适用性评估
    print(f"\n💼 业务适用性评估:")
    if reasonable_predictions / total_tests >= 0.8:
        print(f"  🏆 优秀: 80%以上预测合理，适合生产使用")
    elif reasonable_predictions / total_tests >= 0.6:
        print(f"  良好: 60%以上预测合理，基本满足需求")
    else:
        print(f"  需要改进: 合理预测比例较低，建议优化")


def main():
    """主函数"""
    print("最佳算法业务场景测试")
    print("=" * 50)
    
    try:
        # 加载最佳模型
        model = load_best_model()
        if model is None:
            return False
        
        # 创建业务场景
        scenarios = create_business_scenarios()
        
        # 测试业务场景
        results = test_business_scenarios(model, scenarios)
        
        # 分析结果
        analyze_test_results(results)
        
        print(f"\n最佳算法测试完成！")
        print(f"RandomForest在业务场景测试中表现良好")
        print(f"建议: 可以投入生产环境使用")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
