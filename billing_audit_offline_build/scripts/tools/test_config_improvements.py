#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置管理改进
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_config_manager():
    """测试配置管理器"""
    print("测试生产配置管理器...")
    
    try:
        from src.config.production_config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        print(f"  配置管理器加载成功")
        print(f"  批次大小: {config_manager.get_batch_size()}")
        print(f"  ⚖️  判定阈值: {config_manager.get_judgment_thresholds()}")
        print(f"  特征列数: {len(config_manager.get_feature_columns('fixed_fee'))}")
        
        # 测试环境变量功能
        env_vars = config_manager.get_environment_setup()
        print(f"  环境变量数: {len(env_vars)}")
        
        return True
        
    except Exception as e:
        print(f"  配置管理器测试失败: {e}")
        return False

def test_feature_engineer_config():
    """测试特征工程器配置"""
    print("\n测试特征工程器配置...")
    
    try:
        from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer
        
        # 测试使用生产配置
        engineer = LargeScaleFeatureEngineer()
        
        print(f"  特征工程器初始化成功")
        print(f"  特征列数: {len(engineer.feature_columns)}")
        print(f"  类别列数: {len(engineer.categorical_columns)}")
        print(f"  🔢 数值列数: {len(engineer.numerical_columns)}")
        
        return True
        
    except Exception as e:
        print(f"  特征工程器配置测试失败: {e}")
        return False

def test_model_trainer_config():
    """测试模型训练器配置"""
    print("\n测试模型训练器配置...")
    
    try:
        from src.billing_audit.training.train_large_scale_model import LargeScaleDataProcessor
        
        # 测试使用生产配置
        processor = LargeScaleDataProcessor()
        
        print(f"  数据处理器初始化成功")
        print(f"  批次大小: {processor.batch_size}")
        print(f"  配置管理器: {type(processor.config_manager).__name__}")
        
        return True
        
    except Exception as e:
        print(f"  模型训练器配置测试失败: {e}")
        return False

def test_billing_judge_config():
    """测试收费判定器配置"""
    print("\n⚖️  测试收费判定器配置...")
    
    try:
        # 由于需要模型文件，我们只测试配置加载部分
        from src.config.production_config_manager import get_config_manager
        
        config_manager = get_config_manager()
        thresholds = config_manager.get_judgment_thresholds()
        
        print(f"  判定配置加载成功")
        print(f"  绝对阈值: {thresholds.get('absolute_threshold', 'N/A')}")
        print(f"  相对阈值: {thresholds.get('relative_threshold', 'N/A')}")
        print(f"  混合阈值: {thresholds.get('use_mixed_threshold', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"  收费判定器配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("配置管理改进测试")
    print("=" * 60)
    
    tests = [
        test_config_manager,
        test_feature_engineer_config,
        test_model_trainer_config,
        test_billing_judge_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n测试结果:")
    print(f"  - 通过: {passed}/{total}")
    print(f"  - 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print(f"\n所有配置管理改进测试通过！")
        return True
    else:
        print(f"\n部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
