#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强模型
验证新训练的增强模型是否能正常进行预测
"""

import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
import joblib

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_config():
    """加载配置文件"""
    config_path = project_root / "config" / "billing_audit_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_enhanced_model():
    """加载增强模型"""
    print("加载增强模型...")
    
    config = load_config()
    model_path = project_root / config['model_paths']['fixed_fee_model']
    
    if not model_path.exists():
        print(f"模型文件不存在: {model_path}")
        return None
    
    try:
        model = joblib.load(model_path)
        print(f"模型加载成功: {model_path.name}")
        print(f"  - 模型类型: {type(model).__name__}")
        print(f"  - 特征数量: {model.n_features_in_}")
        return model
    except Exception as e:
        print(f"模型加载失败: {e}")
        return None


def prepare_test_data():
    """准备测试数据"""
    print("\n准备测试数据...")
    
    # 创建测试样例
    test_data = {
        # 原始特征
        'cal_type': [1, 2, 0, 1],
        'unit_type': [1, 1, 0, 1],
        'rate_unit': [1, 1, 0, 1],
        'final_eff_year': [2024, 2024, 2024, 2024],
        'final_eff_mon': [1, 6, 12, 3],
        'final_eff_day': [15, 1, 31, 10],
        'final_exp_year': [2024, 2025, 2024, 2025],
        'final_exp_mon': [12, 6, 12, 3],
        'final_exp_day': [31, 30, 31, 10],
        'cur_year_month': [202407, 202407, 202407, 202407],
        'charge_day_count': [31, 15, 0, 31],
        'month_day_count': [31, 31, 31, 31],
        'run_code': ['NORMAL', 'NORMAL', 'STOP', 'NORMAL'],
        'run_time': [202407, 202407, 202407, 202407],
        'should_fee': [100.0, 50.0, 0.0, 200.0],
        'busi_flag': [0, 0, 1, 0]
    }
    
    df = pd.DataFrame(test_data)
    print(f"测试数据创建成功: {df.shape}")
    
    return df


def create_enhanced_features_for_prediction(df):
    """为预测创建增强特征（简化版）"""
    print("创建增强特征...")
    
    df_features = df.copy()
    
    # 1. 编码类别变量
    from sklearn.preprocessing import LabelEncoder
    
    categorical_columns = ['cal_type', 'unit_type', 'rate_unit', 'run_code', 'busi_flag']
    for col in categorical_columns:
        if col in df_features.columns:
            le = LabelEncoder()
            df_features[col] = le.fit_transform(df_features[col].astype(str))
    
    # 2. 创建日期特征
    try:
        # 组合生效日期
        eff_date = pd.to_datetime(
            df_features[['final_eff_year', 'final_eff_mon', 'final_eff_day']].rename(
                columns={'final_eff_year': 'year', 'final_eff_mon': 'month', 'final_eff_day': 'day'}
            ), errors='coerce'
        )
        
        # 组合失效日期
        exp_date = pd.to_datetime(
            df_features[['final_exp_year', 'final_exp_mon', 'final_exp_day']].rename(
                columns={'final_exp_year': 'year', 'final_exp_mon': 'month', 'final_exp_day': 'day'}
            ), errors='coerce'
        )
        
        # 创建基础日期特征
        df_features['final_eff_dayofweek'] = eff_date.dt.dayofweek
        df_features['final_eff_quarter'] = eff_date.dt.quarter
        df_features['final_eff_is_weekend'] = (eff_date.dt.dayofweek >= 5).astype(int)
        df_features['final_eff_month_start'] = (eff_date.dt.day == 1).astype(int)
        
        df_features['final_exp_dayofweek'] = exp_date.dt.dayofweek
        df_features['final_exp_quarter'] = exp_date.dt.quarter
        df_features['final_exp_is_weekend'] = (exp_date.dt.dayofweek >= 5).astype(int)
        df_features['final_exp_month_end'] = (exp_date.dt.day >= 28).astype(int)
        
        # 创建组合特征
        df_features['subscription_duration_days'] = (exp_date - eff_date).dt.days
        
        cur_date = pd.to_datetime(df_features['cur_year_month'], format='%Y%m', errors='coerce')
        df_features['months_since_effective'] = (
            (cur_date.dt.year - eff_date.dt.year) * 12 + 
            (cur_date.dt.month - eff_date.dt.month)
        )
        df_features['months_until_expiry'] = (
            (exp_date.dt.year - cur_date.dt.year) * 12 + 
            (exp_date.dt.month - cur_date.dt.month)
        )
        df_features['quarter_diff'] = (
            exp_date.dt.quarter - eff_date.dt.quarter + 
            (exp_date.dt.year - eff_date.dt.year) * 4
        )
        
        print("  日期特征创建成功")
        
    except Exception as e:
        print(f"  日期特征创建失败: {e}")
    
    # 3. 创建业务特征
    try:
        df_features['billing_efficiency'] = df_features['charge_day_count'] / df_features['month_day_count']
        df_features['cal_type_day_interaction'] = df_features['cal_type'] * df_features['charge_day_count']
        df_features['daily_should_fee'] = df_features['should_fee'] / np.maximum(df_features['charge_day_count'], 1)
        
        print("  业务特征创建成功")
        
    except Exception as e:
        print(f"  业务特征创建失败: {e}")
    
    # 4. 标准化数值特征（简化处理）
    numerical_columns = ['final_eff_year', 'final_eff_mon', 'final_eff_day', 
                        'final_exp_year', 'final_exp_mon', 'final_exp_day',
                        'charge_day_count', 'month_day_count', 'should_fee']
    
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    
    for col in numerical_columns:
        if col in df_features.columns:
            df_features[col] = scaler.fit_transform(df_features[[col]])
    
    # 5. 处理日期字段
    df_features['cur_year_month'] = pd.to_numeric(df_features['cur_year_month'], errors='coerce')
    df_features['run_time'] = pd.to_numeric(df_features['run_time'], errors='coerce')
    
    print(f"特征工程完成: {df_features.shape[1]} 个特征")
    
    return df_features


def test_model_prediction(model, X_test):
    """测试模型预测"""
    print("\n测试模型预测...")
    
    try:
        # 执行预测
        predictions = model.predict(X_test)
        
        print(f"预测成功: {len(predictions)} 个预测结果")
        
        # 显示预测结果
        print(f"\n预测结果:")
        for i, pred in enumerate(predictions):
            print(f"  样本 {i+1}: {pred:.2f}元")
        
        # 统计信息
        print(f"\n预测统计:")
        print(f"  - 最小值: {predictions.min():.2f}元")
        print(f"  - 最大值: {predictions.max():.2f}元")
        print(f"  - 平均值: {predictions.mean():.2f}元")
        print(f"  - 标准差: {predictions.std():.2f}元")
        
        return predictions
        
    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_business_scenarios():
    """测试业务场景"""
    print("\n💼 业务场景测试:")
    
    scenarios = [
        {
            'name': '正常整月收费',
            'data': {
                'cal_type': 1, 'unit_type': 1, 'rate_unit': 1,
                'final_eff_year': 2024, 'final_eff_mon': 1, 'final_eff_day': 1,
                'final_exp_year': 2024, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 31, 'month_day_count': 31,
                'run_code': 'NORMAL', 'run_time': 202407, 'should_fee': 100.0, 'busi_flag': 0
            },
            'expected': '接近100元'
        },
        {
            'name': '按天折算收费',
            'data': {
                'cal_type': 2, 'unit_type': 1, 'rate_unit': 1,
                'final_eff_year': 2024, 'final_eff_mon': 7, 'final_eff_day': 15,
                'final_exp_year': 2024, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 17, 'month_day_count': 31,
                'run_code': 'NORMAL', 'run_time': 202407, 'should_fee': 50.0, 'busi_flag': 0
            },
            'expected': '接近50元'
        },
        {
            'name': '不收费场景',
            'data': {
                'cal_type': 0, 'unit_type': 0, 'rate_unit': 0,
                'final_eff_year': 2024, 'final_eff_mon': 1, 'final_eff_day': 1,
                'final_exp_year': 2024, 'final_exp_mon': 12, 'final_exp_day': 31,
                'cur_year_month': 202407, 'charge_day_count': 0, 'month_day_count': 31,
                'run_code': 'STOP', 'run_time': 202407, 'should_fee': 0.0, 'busi_flag': 1
            },
            'expected': '接近0元'
        }
    ]
    
    return scenarios


def main():
    """主函数"""
    print("增强模型测试")
    print("=" * 40)
    
    try:
        # 加载模型
        model = load_enhanced_model()
        if model is None:
            return False
        
        # 准备测试数据
        test_df = prepare_test_data()
        
        # 创建增强特征
        X_test = create_enhanced_features_for_prediction(test_df)
        
        # 测试预测
        predictions = test_model_prediction(model, X_test)
        
        if predictions is not None:
            print(f"\n增强模型测试成功！")
            print(f"模型状态: 正常工作")
            print(f"特征数量: {X_test.shape[1]}")
            print(f"预测范围: {predictions.min():.2f} - {predictions.max():.2f}元")
            return True
        else:
            print(f"\n增强模型测试失败")
            return False
        
    except Exception as e:
        print(f"测试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
