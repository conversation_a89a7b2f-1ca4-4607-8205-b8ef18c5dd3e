#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的数据预处理逻辑
验证新字段结构的预处理是否正常工作
"""

import sys
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from src.billing_audit.preprocessing import BillingDataPreprocessor, FeatureEngineer
    from src.utils.config_manager import get_config
except ImportError as e:
    print(f"模块导入失败: {e}")
    print("请确保项目路径正确且所有依赖已安装")

    # 尝试简化测试
    print("尝试简化测试...")
    try:
        import json
        config_path = project_root / "config" / "billing_audit_config.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("配置文件加载成功，继续简化测试")
    except Exception as e2:
        print(f"配置文件加载也失败: {e2}")
        sys.exit(1)


def test_data_preprocessor():
    """测试数据预处理器"""
    print("测试数据预处理器...")
    
    try:
        # 初始化预处理器
        preprocessor = BillingDataPreprocessor('fixed_fee')
        print("预处理器初始化成功")
        
        # 加载数据
        df = preprocessor.load_data()
        print(f"数据加载成功: {df.shape}")
        
        # 验证数据
        validation_result = preprocessor.validate_data(df)
        print(f"数据验证完成")
        
        if validation_result['is_valid']:
            print("  数据验证通过")
        else:
            print("  数据验证有警告:")
            for error in validation_result['errors']:
                print(f"    - {error}")
        
        # 检查新字段验证
        new_field_validations = [key for key in validation_result.keys() 
                               if any(field in key for field in ['final_eff_year', 'final_eff_mon', 'final_eff_day',
                                                               'final_exp_year', 'final_exp_mon', 'final_exp_day'])]
        if new_field_validations:
            print(f"  新字段验证已执行: {len(new_field_validations)} 个字段")
        else:
            print("  未找到新字段验证结果")
        
        # 清洗数据
        df_cleaned = preprocessor.clean_data(df)
        print(f"数据清洗完成: {df_cleaned.shape}")
        
        # 准备特征和目标变量
        X, y = preprocessor.prepare_features_and_target(df_cleaned)
        print(f"特征准备完成: X{X.shape}, y{y.shape}")
        
        return True, df_cleaned, X, y
        
    except Exception as e:
        print(f"数据预处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None


def test_feature_engineer(df, X, y):
    """测试特征工程器"""
    print("\n测试特征工程器...")
    
    try:
        # 初始化特征工程器
        engineer = FeatureEngineer('fixed_fee')
        print("特征工程器初始化成功")
        
        # 创建日期特征
        df_with_date_features = engineer.create_date_features(X)
        print(f"日期特征创建完成: {df_with_date_features.shape}")
        
        # 检查新创建的特征
        original_cols = set(X.columns)
        new_cols = set(df_with_date_features.columns) - original_cols
        if new_cols:
            print(f"  新增特征: {len(new_cols)} 个")
            for col in sorted(list(new_cols))[:10]:  # 显示前10个
                print(f"    - {col}")
            if len(new_cols) > 10:
                print(f"    ... 还有 {len(new_cols) - 10} 个特征")
        else:
            print("  未创建新特征")
        
        # 编码类别特征
        df_encoded = engineer.encode_categorical_features(df_with_date_features, fit=True)
        print(f"类别特征编码完成: {df_encoded.shape}")
        
        # 标准化数值特征
        df_scaled = engineer.scale_numerical_features(df_encoded, fit=True)
        print(f"数值特征标准化完成: {df_scaled.shape}")
        
        # 完整的特征工程流程
        df_final = engineer.fit_transform(X)
        print(f"完整特征工程完成: {df_final.shape}")
        
        # 显示最终特征列表
        print(f"  最终特征数量: {len(df_final.columns)}")
        print(f"  特征名称示例: {list(df_final.columns)[:10]}")
        
        return True, df_final
        
    except Exception as e:
        print(f"特征工程器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def test_complete_pipeline():
    """测试完整的预处理流程"""
    print("\n测试完整预处理流程...")
    
    try:
        # 初始化预处理器
        preprocessor = BillingDataPreprocessor('fixed_fee')
        
        # 执行完整流程
        X, y, passthrough = preprocessor.process()
        print(f"完整预处理流程成功: X{X.shape}, y{y.shape}, passthrough{passthrough.shape}")
        
        # 初始化特征工程器并处理
        engineer = FeatureEngineer('fixed_fee')
        X_engineered = engineer.fit_transform(X)
        print(f"特征工程完成: {X_engineered.shape}")
        
        # 数据质量检查
        print("\n数据质量检查:")
        print(f"  - 特征矩阵形状: {X_engineered.shape}")
        print(f"  - 目标变量形状: {y.shape}")
        print(f"  - 透传数据形状: {passthrough.shape}")
        print(f"  - 缺失值数量: {X_engineered.isnull().sum().sum()}")
        print(f"  - 目标变量范围: {y.min():.2f} - {y.max():.2f}")
        print(f"  - 目标变量均值: {y.mean():.2f}")
        
        return True, X_engineered, y, passthrough
        
    except Exception as e:
        print(f"完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None


def main():
    """主函数"""
    print("更新后的数据预处理逻辑测试")
    print("=" * 60)
    
    # 测试数据预处理器
    success1, df_cleaned, X, y = test_data_preprocessor()
    
    if success1:
        # 测试特征工程器
        success2, df_final = test_feature_engineer(df_cleaned, X, y)
        
        # 测试完整流程
        success3, X_final, y_final, passthrough = test_complete_pipeline()
        
        if success1 and success2 and success3:
            print(f"\n所有测试通过！")
            print(f"最终结果:")
            print(f"  - 预处理后特征数: {X_final.shape[1]}")
            print(f"  - 样本数量: {X_final.shape[0]}")
            print(f"  - 透传字段数: {passthrough.shape[1]}")
            print(f"  - 数据质量: 良好")
            
            # 检查是否包含新字段相关的特征
            new_field_features = [col for col in X_final.columns 
                                if any(field in col for field in ['final_eff', 'final_exp', 'subscription', 'months_'])]
            if new_field_features:
                print(f"  新字段相关特征: {len(new_field_features)} 个")
                print(f"    示例: {new_field_features[:5]}")
            else:
                print(f"  未发现新字段相关特征")
            
            return True
        else:
            print(f"\n部分测试失败")
            return False
    else:
        print(f"\n数据预处理器测试失败，跳过后续测试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
