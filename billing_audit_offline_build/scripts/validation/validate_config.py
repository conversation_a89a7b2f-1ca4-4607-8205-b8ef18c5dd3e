#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件验证脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.utils.config_manager import ConfigManager


def main():
    """主函数"""
    print("开始验证配置文件...")
    
    try:
        # 创建配置管理器
        config = ConfigManager()
        
        # 验证配置
        errors = config.validate_config()
        
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        else:
            print("配置验证通过!")
            
            # 显示配置摘要
            print("\n配置摘要:")
            print(f"  项目名称: {config.get('project.name')}")
            print(f"  版本: {config.get('project.version')}")
            
            # 显示数据源
            print(f"\n数据源:")
            for name, path in config.get('data_sources', {}).items():
                print(f"  - {name}: {path}")
            
            # 显示模型配置
            print(f"\n模型配置:")
            fixed_fee_config = config.get_model_config('billing_audit', 'fixed_fee')
            print(f"  固费模型特征数: {len(fixed_fee_config.feature_columns)}")
            
            discount_config = config.get_model_config('billing_audit', 'discount')
            print(f"  优惠模型特征数: {len(discount_config.feature_columns)}")
            
            call_config = config.get_model_config('behavior_analysis', 'call_detail')
            print(f"  话单分析特征数: {len(call_config.feature_columns)}")
            
            return True
            
    except Exception as e:
        print(f"配置验证出错: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
