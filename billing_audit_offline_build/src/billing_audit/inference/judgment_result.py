"""
收费合理性判定结果定义
"""

from enum import Enum


class JudgmentResult(Enum):
    """判定结果枚举"""
    REASONABLE = "reasonable"      # 合理
    UNREASONABLE = "unreasonable"  # 不合理
    UNCERTAIN = "uncertain"        # 不确定


class JudgmentThresholds:
    """判定阈值配置"""
    def __init__(self, absolute_threshold=50.0, relative_threshold=0.1):
        self.absolute_threshold = absolute_threshold  # 绝对误差阈值
        self.relative_threshold = relative_threshold  # 相对误差阈值


class JudgmentMetrics:
    """判定指标"""
    def __init__(self):
        self.total_count = 0
        self.reasonable_count = 0
        self.unreasonable_count = 0
        self.uncertain_count = 0
        
    def add_result(self, result: JudgmentResult):
        """添加判定结果"""
        self.total_count += 1
        if result == JudgmentResult.REASONABLE:
            self.reasonable_count += 1
        elif result == JudgmentResult.UNREASONABLE:
            self.unreasonable_count += 1
        elif result == JudgmentResult.UNCERTAIN:
            self.uncertain_count += 1
    
    def get_statistics(self):
        """获取统计信息"""
        if self.total_count == 0:
            return {
                'total': 0,
                'reasonable_rate': 0.0,
                'unreasonable_rate': 0.0,
                'uncertain_rate': 0.0
            }
        
        return {
            'total': self.total_count,
            'reasonable_count': self.reasonable_count,
            'unreasonable_count': self.unreasonable_count,
            'uncertain_count': self.uncertain_count,
            'reasonable_rate': self.reasonable_count / self.total_count,
            'unreasonable_rate': self.unreasonable_count / self.total_count,
            'uncertain_rate': self.uncertain_count / self.total_count
        }
