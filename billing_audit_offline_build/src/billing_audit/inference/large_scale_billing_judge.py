#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模收费合理性判定脚本
专门针对千万级数据优化的收费合理性判定系统
支持分批判定、内存优化、详细统计
"""

import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime
import argparse
import gc
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 导入大规模特征工程器和判定逻辑
from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer
from src.billing_audit.inference.judgment_result import JudgmentResult
from src.config.production_config_manager import get_config_manager
from src.utils.logger import get_logger
import joblib

# 导入分层模型
from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel


class LargeScaleBillingJudge:
    """大规模收费合理性判定器"""
    
    def __init__(self, model_path, feature_engineer_path, batch_size=None):
        # 使用生产配置管理器
        self.config_manager = get_config_manager()
        self.batch_size = batch_size or self.config_manager.get_batch_size()
        self.model = None
        self.feature_engineer = None
        self.is_hierarchical = False  # 分层模型标识

        # 初始化日志器
        self.logger = get_logger('large_scale_billing_judge')

        # 从配置管理器获取判定配置
        self.judgment_thresholds = self.config_manager.get_judgment_thresholds()

        # 如果配置为空，使用默认值
        if not self.judgment_thresholds:
            self.judgment_thresholds = {
                'absolute_threshold': 50.0,  # 绝对误差阈值（元）
                'relative_threshold': 0.1,   # 相对误差阈值（10%）
                'use_mixed_threshold': True,  # 使用混合阈值
                'uncertainty_factor': 2.0    # 不确定区间因子
            }
        
        # 判定统计
        self.judgment_stats = {
            'total_judgments': 0,
            'reasonable_count': 0,
            'unreasonable_count': 0,
            'uncertain_count': 0,
            'error_count': 0
        }
        
        # 加载模型和特征工程器
        self.load_model_and_feature_engineer(model_path, feature_engineer_path)
        
        self.logger.info(f"大规模收费合理性判定器初始化完成")
        self.logger.info(f"批次大小: {batch_size:,}")
        self.logger.info(f"绝对阈值: ±{self.judgment_thresholds['absolute_threshold']}元")
        self.logger.info(f"相对阈值: ±{self.judgment_thresholds['relative_threshold']*100}%")

        print(f"🏛️ 大规模收费合理性判定器初始化完成")
        print(f"  - 批次大小: {batch_size:,}")
        print(f"  - 绝对阈值: ±{self.judgment_thresholds['absolute_threshold']}元")
        print(f"  - 相对阈值: ±{self.judgment_thresholds['relative_threshold']*100}%")
    
    def load_model_and_feature_engineer(self, model_path, feature_engineer_path):
        """加载模型和特征工程器"""
        self.logger.info(f"开始加载模型和特征工程器: 模型={model_path}, 特征工程器={feature_engineer_path}")
        print(f"加载模型和特征工程器...")

        try:
            # 尝试加载分层模型
            if str(model_path).find('hierarchical') != -1:
                self.model = HierarchicalBillingModel.load(model_path)
                self.is_hierarchical = True
                self.logger.info(f"分层模型加载成功: {type(self.model).__name__}")
                print(f"  分层模型加载成功: {type(self.model).__name__}")
            else:
                # 加载传统模型
                self.model = joblib.load(model_path)
                self.is_hierarchical = False
                self.logger.info(f"传统模型加载成功: {type(self.model).__name__}")
                print(f"  传统模型加载成功: {type(self.model).__name__}")

                # 显示特征数（仅对传统模型）
                if hasattr(self.model, 'n_features_in_'):
                    print(f"  模型特征数: {self.model.n_features_in_}")

            # 加载特征工程器
            self.feature_engineer = LargeScaleFeatureEngineer.load_preprocessor(feature_engineer_path)
            print(f"  特征工程器加载成功")

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            print(f"模型加载失败: {e}")
            raise
    
    def read_large_csv(self, file_path, chunksize=None):
        """分批读取大型CSV文件"""
        if chunksize is None:
            chunksize = self.batch_size
            
        self.logger.info(f"开始分批读取数据文件: {file_path}, 批次大小: {chunksize:,}")
        print(f"开始分批读取数据文件: {file_path}")
        print(f"  - 批次大小: {chunksize:,} 行")

        try:
            # 检测分隔符
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline()
                sep = ',' if ',' in first_line else '\t'

            self.logger.info(f"检测到分隔符: '{sep}'")
            print(f"  - 检测到分隔符: '{sep}'")
            
            # 分批读取
            chunk_reader = pd.read_csv(
                file_path, 
                sep=sep,
                chunksize=chunksize,
                encoding='utf-8',
                low_memory=False
            )
            
            return chunk_reader, sep
            
        except Exception as e:
            self.logger.error(f"文件读取失败: {e}")
            print(f"文件读取失败: {e}")
            raise
    
    def judge_chunk(self, chunk, target_column='amount'):
        """判定单个数据块"""
        try:
            # 提取特征和实际金额
            feature_columns = self.feature_engineer.feature_columns
            X = chunk[feature_columns].copy()
            actual_amounts = chunk[target_column].copy()
            
            # 处理缺失值
            X = X.fillna(0)
            actual_amounts = actual_amounts.fillna(0)
            
            # 应用特征工程
            X_processed = self.feature_engineer.transform_chunk(X)
            
            # 预测
            predicted_amounts = self.model.predict(X_processed)
            
            # 计算误差
            absolute_errors = np.abs(predicted_amounts - actual_amounts)
            relative_errors = np.where(
                actual_amounts != 0,
                np.abs(predicted_amounts - actual_amounts) / actual_amounts,
                np.inf
            )
            
            # 批量判定
            judgments = self._make_batch_judgment(absolute_errors, relative_errors, actual_amounts)
            
            # 计算置信度
            confidence_scores = self._calculate_batch_confidence(absolute_errors, relative_errors, actual_amounts)
            
            return {
                'predicted_amounts': predicted_amounts,
                'actual_amounts': actual_amounts.values,
                'absolute_errors': absolute_errors,
                'relative_errors': relative_errors,
                'judgments': judgments,
                'confidence_scores': confidence_scores
            }
            
        except Exception as e:
            self.logger.error(f"数据块判定失败: {e}")
            print(f"数据块判定失败: {e}")
            raise
    
    def _make_batch_judgment(self, absolute_errors, relative_errors, actual_amounts):
        """批量执行判定逻辑"""
        abs_threshold = self.judgment_thresholds['absolute_threshold']
        rel_threshold = self.judgment_thresholds['relative_threshold']
        use_mixed = self.judgment_thresholds['use_mixed_threshold']
        uncertainty_factor = self.judgment_thresholds['uncertainty_factor']
        
        judgments = []
        
        for abs_err, rel_err, actual in zip(absolute_errors, relative_errors, actual_amounts):
            if use_mixed:
                # 混合阈值判定：满足任一条件即为合理
                is_reasonable = (abs_err <= abs_threshold) or (rel_err <= rel_threshold)
                
                # 不确定区间：误差在阈值的1-2倍之间
                is_uncertain = (
                    (abs_threshold < abs_err <= abs_threshold * uncertainty_factor) or
                    (rel_threshold < rel_err <= rel_threshold * uncertainty_factor)
                )
            else:
                # 仅使用绝对阈值
                is_reasonable = abs_err <= abs_threshold
                is_uncertain = abs_threshold < abs_err <= abs_threshold * uncertainty_factor
            
            if is_reasonable:
                judgments.append('reasonable')
            elif is_uncertain:
                judgments.append('uncertain')
            else:
                judgments.append('unreasonable')
        
        return judgments
    
    def _calculate_batch_confidence(self, absolute_errors, relative_errors, actual_amounts):
        """批量计算判定置信度"""
        abs_threshold = self.judgment_thresholds['absolute_threshold']
        rel_threshold = self.judgment_thresholds['relative_threshold']
        
        # 基于误差大小计算置信度
        abs_confidences = np.maximum(0, 1 - (absolute_errors / abs_threshold))
        rel_confidences = np.maximum(0, 1 - (relative_errors / rel_threshold))
        
        # 取较高的置信度
        confidences = np.maximum(abs_confidences, rel_confidences)
        
        # 确保置信度在0-1范围内
        return np.clip(confidences, 0.0, 1.0)
    
    def judge_large_file(self, input_file, output_file, target_column='amount'):
        """判定大型文件"""
        self.logger.info(f"开始大规模收费合理性判定: 输入={input_file}, 输出={output_file}, 目标列={target_column}")
        print(f"⚖️  开始大规模收费合理性判定")
        print(f"  输入文件: {input_file}")
        print(f"  输出文件: {output_file}")
        print(f"  目标列: {target_column}")
        print("=" * 60)
        
        # 读取数据
        chunk_reader, _ = self.read_large_csv(input_file, self.batch_size)
        
        # 准备输出文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        chunk_count = 0
        total_judgments = 0
        all_results = []
        
        start_time = datetime.now()
        
        for chunk in chunk_reader:
            chunk_count += 1
            self.logger.info(f"开始判定第 {chunk_count} 批数据，样本数: {len(chunk)}")
            print(f"\n  判定第 {chunk_count} 批数据...")

            try:
                # 判定数据块
                result = self.judge_chunk(chunk, target_column)

                # 构建结果DataFrame
                chunk_results = chunk.copy()
                chunk_results['predicted_amount'] = result['predicted_amounts']
                chunk_results['absolute_error'] = result['absolute_errors']
                chunk_results['relative_error'] = result['relative_errors']
                chunk_results['judgment'] = result['judgments']
                chunk_results['confidence_score'] = result['confidence_scores']
                chunk_results['judgment_timestamp'] = datetime.now().isoformat()

                all_results.append(chunk_results)

                # 更新统计
                self._update_batch_stats(result['judgments'])

                total_judgments += len(result['judgments'])

                self.logger.info(f"第 {chunk_count} 批判定完成: {len(result['judgments']):,} 条，累计: {total_judgments:,} 条")
                print(f"    判定完成: {len(result['judgments']):,} 条")
                print(f"    累计判定: {total_judgments:,} 条")

                # 显示批次统计
                batch_stats = self._calculate_batch_stats(result['judgments'])
                print(f"    批次统计: 合理{batch_stats['reasonable']:.1f}% | "
                      f"不合理{batch_stats['unreasonable']:.1f}% | "
                      f"不确定{batch_stats['uncertain']:.1f}%")

                # 内存管理：每处理一定数量的批次就保存一次
                if chunk_count % 20 == 0:
                    self.logger.info(f"执行中间保存，已处理 {chunk_count} 批次")
                    print(f"  中间保存...")
                    self._save_intermediate_results(all_results, output_file, chunk_count)
                    all_results = []  # 清理内存
                    gc.collect()

            except Exception as e:
                self.logger.error(f"第 {chunk_count} 批判定失败: {e}")
                print(f"    第 {chunk_count} 批判定失败: {e}")
                self.judgment_stats['error_count'] += len(chunk)
                continue
        
        # 保存最终结果
        self.logger.info(f"开始保存最终判定结果到: {output_file}")
        print(f"\n保存最终判定结果...")
        if all_results:
            self._save_final_results(all_results, output_file)
        
        total_time = (datetime.now() - start_time).total_seconds()
        
        # 生成判定报告
        self._generate_judgment_report(total_judgments, total_time, output_file)
        
        return {
            'total_judgments': total_judgments,
            'total_time': total_time,
            'judgments_per_second': total_judgments/total_time if total_time > 0 else 0,
            'output_file': output_file,
            'judgment_stats': self.get_judgment_stats()
        }
    
    def _update_batch_stats(self, judgments):
        """更新批次统计"""
        for judgment in judgments:
            self.judgment_stats['total_judgments'] += 1
            if judgment == 'reasonable':
                self.judgment_stats['reasonable_count'] += 1
            elif judgment == 'unreasonable':
                self.judgment_stats['unreasonable_count'] += 1
            elif judgment == 'uncertain':
                self.judgment_stats['uncertain_count'] += 1
    
    def _calculate_batch_stats(self, judgments):
        """计算批次统计"""
        total = len(judgments)
        if total == 0:
            return {'reasonable': 0, 'unreasonable': 0, 'uncertain': 0}
        
        reasonable = judgments.count('reasonable') / total * 100
        unreasonable = judgments.count('unreasonable') / total * 100
        uncertain = judgments.count('uncertain') / total * 100
        
        return {
            'reasonable': reasonable,
            'unreasonable': unreasonable,
            'uncertain': uncertain
        }
    
    def _save_intermediate_results(self, results, output_file, chunk_count):
        """保存中间结果"""
        if results:
            combined_results = pd.concat(results, ignore_index=True)
            temp_file = str(output_file).replace('.csv', f'_temp_{chunk_count}.csv')
            combined_results.to_csv(temp_file, index=False)
            self.logger.info(f"中间结果已保存: {temp_file}")
            print(f"    中间结果已保存: {temp_file}")

    def _save_final_results(self, results, output_file):
        """保存最终结果"""
        combined_results = pd.concat(results, ignore_index=True)
        combined_results.to_csv(output_file, index=False)
        self.logger.info(f"最终结果已保存: {output_file}, 总记录数: {len(combined_results)}")
        print(f"  最终结果已保存: {output_file}")
    
    def _generate_judgment_report(self, total_judgments, total_time, output_file):
        """生成判定报告"""
        self.logger.info(f"大规模收费合理性判定完成: 总数={total_judgments:,}, 耗时={total_time:.2f}秒, 速度={total_judgments/total_time:.0f}条/秒")
        print(f"\n⚖️  大规模收费合理性判定完成！")
        print(f"  总判定数: {total_judgments:,} 条")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  判定速度: {total_judgments/total_time:.0f} 条/秒")
        print(f"  结果文件: {output_file}")

        # 详细统计
        stats = self.get_judgment_stats()
        self.logger.info(f"判定统计: 合理={stats['reasonable_count']:,}({stats.get('reasonable_rate', 0):.1f}%), "
                        f"不合理={stats['unreasonable_count']:,}({stats.get('unreasonable_rate', 0):.1f}%), "
                        f"不确定={stats['uncertain_count']:,}({stats.get('uncertain_rate', 0):.1f}%)")
        print(f"\n判定统计:")
        print(f"  - 合理收费: {stats['reasonable_count']:,} 条 ({stats.get('reasonable_rate', 0):.1f}%)")
        print(f"  - 不合理收费: {stats['unreasonable_count']:,} 条 ({stats.get('unreasonable_rate', 0):.1f}%)")
        print(f"  - 不确定收费: {stats['uncertain_count']:,} 条 ({stats.get('uncertain_rate', 0):.1f}%)")
        if stats['error_count'] > 0:
            self.logger.warning(f"判定错误: {stats['error_count']:,} 条")
            print(f"  - 判定错误: {stats['error_count']:,} 条")
    
    def get_judgment_stats(self):
        """获取判定统计信息"""
        total = self.judgment_stats['total_judgments']
        
        if total == 0:
            return self.judgment_stats.copy()
        
        stats = self.judgment_stats.copy()
        stats.update({
            'reasonable_rate': self.judgment_stats['reasonable_count'] / total * 100,
            'unreasonable_rate': self.judgment_stats['unreasonable_count'] / total * 100,
            'uncertain_rate': self.judgment_stats['uncertain_count'] / total * 100
        })
        
        return stats


def main():
    """主函数"""
    import logging
    logger = logging.getLogger(__name__)

    parser = argparse.ArgumentParser(description='大规模收费合理性判定')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径 (CSV/TXT)')
    parser.add_argument('--model', '-m', required=True, help='模型文件路径 (.pkl)')
    parser.add_argument('--feature-engineer', '-f', required=True, help='特征工程器文件路径 (.pkl)')
    parser.add_argument('--output', '-o', required=True, help='判定结果输出文件路径 (.csv)')
    parser.add_argument('--target-column', '-c', default='amount', help='目标列名 (默认: amount)')
    parser.add_argument('--batch-size', '-b', type=int, default=50000, help='批处理大小')
    parser.add_argument('--abs-threshold', type=float, default=50.0, help='绝对误差阈值 (元)')
    parser.add_argument('--rel-threshold', type=float, default=0.1, help='相对误差阈值 (比例)')
    
    args = parser.parse_args()
    
    try:
        # 初始化判定器
        judge = LargeScaleBillingJudge(
            model_path=args.model,
            feature_engineer_path=args.feature_engineer,
            batch_size=args.batch_size
        )
        
        # 更新判定阈值
        judge.judgment_thresholds['absolute_threshold'] = args.abs_threshold
        judge.judgment_thresholds['relative_threshold'] = args.rel_threshold
        
        # 执行判定
        result = judge.judge_large_file(
            input_file=args.input,
            output_file=args.output,
            target_column=args.target_column
        )
        
        logger.info(f"大规模收费合理性判定任务完成")
        print(f"\n大规模收费合理性判定完成！")
        return True

    except Exception as e:
        logger.error(f"判定失败: {e}")
        print(f"判定失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
