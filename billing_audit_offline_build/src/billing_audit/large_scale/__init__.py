#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模数据处理模块

专门针对千万级数据规模优化的处理模块，包括：
- 大规模特征工程
- 大规模模型训练
- 大规模模型评估
- 大规模预测服务
- 大规模收费判定

所有模块都支持分批处理、内存优化、错误恢复等特性。
"""

from .feature_engineer import LargeScaleFeatureEngineer
from .model_trainer import LargeScaleModelTrainer
from .model_evaluator import LargeScaleModelEvaluator
from .predictor import LargeScalePrediction
from .billing_judge import LargeScaleBillingJudge

__all__ = [
    'LargeScaleFeatureEngineer',
    'LargeScaleModelTrainer', 
    'LargeScaleModelEvaluator',
    'LargeScalePrediction',
    'LargeScaleBillingJudge'
]

__version__ = '2.0.0'
__author__ = '技术团队'
__description__ = '山西电信出账稽核AI系统 - 大规模数据处理模块'
