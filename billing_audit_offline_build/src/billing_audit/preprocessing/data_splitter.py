#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据拆分脚本
将特征工程后的数据拆分为训练集和测试集
"""

import sys
import pandas as pd
import numpy as np
import argparse
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.config.production_config_manager import get_config_manager


class DataSplitter:
    """数据拆分器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def split_data(self, input_file: str, output_dir: str, 
                   test_size: float = 0.2, random_state: int = 42,
                   target_column: str = 'amount') -> tuple:
        """
        拆分数据为训练集和测试集
        
        Args:
            input_file: 输入数据文件（特征工程后的数据）
            output_dir: 输出目录
            test_size: 测试集比例
            random_state: 随机种子
            target_column: 目标列名
            
        Returns:
            (train_file_path, test_file_path): 训练集和测试集文件路径
        """
        print(f"🔀 开始数据拆分")
        print(f"  输入文件: {input_file}")
        print(f"  输出目录: {output_dir}")
        print(f"  测试集比例: {test_size}")
        print(f"  目标列: {target_column}")
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 读取数据
        print(f"\n读取数据...")
        df = pd.read_csv(input_file)
        print(f"  数据形状: {df.shape}")
        
        # 检查目标列
        if target_column not in df.columns:
            raise ValueError(f"目标列 '{target_column}' 不存在于数据中")
        
        # 分离特征和目标
        X = df.drop(columns=[target_column])
        y = df[target_column]
        
        print(f"  特征数: {X.shape[1]}")
        print(f"  样本数: {len(y)}")
        
        # 拆分数据
        print(f"\n拆分数据...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
        
        print(f"  训练集: {X_train.shape[0]:,} 样本")
        print(f"  测试集: {X_test.shape[0]:,} 样本")
        
        # 重新组合数据
        train_df = pd.concat([X_train, y_train], axis=1)
        test_df = pd.concat([X_test, y_test], axis=1)
        
        # 保存文件
        train_file = output_path / f"train_data_{self.timestamp}.csv"
        test_file = output_path / f"test_data_{self.timestamp}.csv"
        
        print(f"\n保存拆分后的数据...")
        train_df.to_csv(train_file, index=False)
        test_df.to_csv(test_file, index=False)
        
        print(f"  训练集文件: {train_file}")
        print(f"  测试集文件: {test_file}")
        
        # 验证保存的文件
        train_check = pd.read_csv(train_file)
        test_check = pd.read_csv(test_file)
        
        print(f"\n验证保存的文件:")
        print(f"  训练集: {train_check.shape}")
        print(f"  测试集: {test_check.shape}")
        print(f"  总样本数: {len(train_check) + len(test_check)} (原始: {len(df)})")
        
        # 统计信息
        print(f"\n数据拆分统计:")
        print(f"  训练集目标值范围: {y_train.min():.2f} - {y_train.max():.2f}")
        print(f"  测试集目标值范围: {y_test.min():.2f} - {y_test.max():.2f}")
        print(f"  训练集目标值均值: {y_train.mean():.2f}")
        print(f"  测试集目标值均值: {y_test.mean():.2f}")
        
        print(f"\n✅ 数据拆分完成")
        
        return str(train_file), str(test_file)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据拆分脚本')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件')
    parser.add_argument('--output', '-o', required=True, help='输出目录')
    parser.add_argument('--test-size', type=float, default=0.2, help='测试集比例')
    parser.add_argument('--random-state', type=int, default=42, help='随机种子')
    parser.add_argument('--target-column', default='amount', help='目标列名')
    
    args = parser.parse_args()
    
    try:
        splitter = DataSplitter()
        train_file, test_file = splitter.split_data(
            input_file=args.input,
            output_dir=args.output,
            test_size=args.test_size,
            random_state=args.random_state,
            target_column=args.target_column
        )
        
        print(f"\n🎯 数据拆分成功完成")
        print(f"训练集: {train_file}")
        print(f"测试集: {test_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 数据拆分失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
