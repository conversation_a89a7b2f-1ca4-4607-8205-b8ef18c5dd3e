#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的模型训练器 - 支持多算法选择和容错机制
"""

import sys
import os
import pickle
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.config.production_config_manager import get_config_manager

class EnhancedModelTrainer:
    """增强的模型训练器"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager or get_config_manager()
        self.logger = logging.getLogger(__name__)
        self.available_algorithms = self._check_algorithm_availability()
        self.trained_models = {}
        
    def _check_algorithm_availability(self) -> Dict[str, bool]:
        """检查算法可用性"""
        availability = {}
        
        # 检查RandomForest
        try:
            from sklearn.ensemble import RandomForestRegressor
            availability['random_forest'] = True
            self.logger.info("RandomForest 可用")
        except ImportError:
            availability['random_forest'] = False
            self.logger.warning("RandomForest 不可用")
        
        # 检查XGBoost
        try:
            import xgboost as xgb
            availability['xgboost'] = True
            self.logger.info(f"XGBoost 可用 (v{xgb.__version__})")
        except ImportError:
            availability['xgboost'] = False
            self.logger.warning("XGBoost 不可用")
        
        # 检查LightGBM
        try:
            import lightgbm as lgb
            availability['lightgbm'] = True
            self.logger.info(f"LightGBM 可用 (v{lgb.__version__})")
        except ImportError:
            availability['lightgbm'] = False
            self.logger.warning("LightGBM 不可用")
        
        return availability
    
    def create_model(self, algorithm: str) -> Any:
        """创建指定算法的模型"""
        if not self.available_algorithms.get(algorithm, False):
            raise ValueError(f"算法 {algorithm} 不可用")
        
        hyperparams = self.config_manager.get_model_hyperparameters(algorithm)
        
        if algorithm == 'random_forest':
            from sklearn.ensemble import RandomForestRegressor
            return RandomForestRegressor(**hyperparams)
        
        elif algorithm == 'xgboost':
            import xgboost as xgb
            return xgb.XGBRegressor(**hyperparams)
        
        elif algorithm == 'lightgbm':
            import lightgbm as lgb
            return lgb.LGBMRegressor(**hyperparams)
        
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
    
    def train_single_algorithm(self, X_train: pd.DataFrame, y_train: pd.Series,
                             X_test: pd.DataFrame, y_test: pd.Series,
                             algorithm: str) -> Dict[str, Any]:
        """训练单个算法"""
        self.logger.info(f"开始训练 {algorithm} 模型...")
        
        try:
            # 创建模型
            model = self.create_model(algorithm)
            
            # 训练模型
            start_time = datetime.now()
            model.fit(X_train, y_train)
            training_time = (datetime.now() - start_time).total_seconds()
            
            # 预测
            y_pred = model.predict(X_test)
            
            # 计算指标
            metrics = {
                'mae': mean_absolute_error(y_test, y_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred)),
                'r2': r2_score(y_test, y_pred),
                'training_time': training_time
            }
            
            # 特征重要性
            feature_importance = None
            if hasattr(model, 'feature_importances_'):
                feature_importance = dict(zip(X_train.columns, model.feature_importances_))
            
            result = {
                'model': model,
                'metrics': metrics,
                'feature_importance': feature_importance,
                'algorithm': algorithm,
                'success': True
            }
            
            self.logger.info(f"{algorithm} 训练成功 - R²: {metrics['r2']:.4f}, MAE: {metrics['mae']:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"{algorithm} 训练失败: {str(e)}")
            return {
                'model': None,
                'metrics': None,
                'feature_importance': None,
                'algorithm': algorithm,
                'success': False,
                'error': str(e)
            }
    
    def train_with_fallback(self, X_train: pd.DataFrame, y_train: pd.Series,
                          X_test: pd.DataFrame, y_test: pd.Series,
                          preferred_algorithm: str = None) -> Dict[str, Any]:
        """带容错机制的训练"""
        # 获取算法优先级
        config_algorithms = self.config_manager.config.get('model_training', {}).get('algorithms', [])
        default_algorithm = self.config_manager.config.get('model_training', {}).get('default_algorithm', 'random_forest')
        
        # 确定尝试顺序
        if preferred_algorithm:
            algorithm_order = [preferred_algorithm] + [a for a in config_algorithms if a != preferred_algorithm]
        else:
            algorithm_order = [default_algorithm] + [a for a in config_algorithms if a != default_algorithm]
        
        self.logger.info(f"算法尝试顺序: {algorithm_order}")
        
        # 依次尝试算法
        for algorithm in algorithm_order:
            if not self.available_algorithms.get(algorithm, False):
                self.logger.warning(f"跳过不可用的算法: {algorithm}")
                continue
            
            result = self.train_single_algorithm(X_train, y_train, X_test, y_test, algorithm)
            
            if result['success']:
                self.logger.info(f"成功使用算法: {algorithm}")
                return result
            else:
                self.logger.warning(f"算法 {algorithm} 失败，尝试下一个算法")
        
        # 所有算法都失败
        raise RuntimeError("所有可用算法都训练失败")
    
    def train_multiple_algorithms(self, X_train: pd.DataFrame, y_train: pd.Series,
                                X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Dict[str, Any]]:
        """训练多个算法并对比"""
        self.logger.info("开始多算法对比训练...")
        
        config_algorithms = self.config_manager.config.get('model_training', {}).get('algorithms', [])
        results = {}
        
        for algorithm in config_algorithms:
            if not self.available_algorithms.get(algorithm, False):
                self.logger.warning(f"跳过不可用的算法: {algorithm}")
                continue
            
            result = self.train_single_algorithm(X_train, y_train, X_test, y_test, algorithm)
            results[algorithm] = result
        
        # 选择最佳算法
        best_algorithm = self._select_best_algorithm(results)
        if best_algorithm:
            self.logger.info(f"最佳算法: {best_algorithm}")
            results['best_algorithm'] = best_algorithm
        
        return results
    
    def _select_best_algorithm(self, results: Dict[str, Dict[str, Any]]) -> Optional[str]:
        """选择最佳算法"""
        successful_results = {k: v for k, v in results.items() if v.get('success', False)}
        
        if not successful_results:
            return None
        
        # 基于R²得分选择最佳算法
        best_algorithm = max(successful_results.keys(), 
                           key=lambda k: successful_results[k]['metrics']['r2'])
        
        return best_algorithm
    
    def save_model(self, model: Any, algorithm: str, metrics: Dict[str, Any],
                  feature_importance: Dict[str, float] = None,
                  output_dir: str = None) -> str:
        """保存模型"""
        if output_dir is None:
            output_dir = project_root / "outputs" / "models"
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"{algorithm}_model_{timestamp}.pkl"
        model_path = output_dir / model_filename
        
        # 保存模型
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        # 保存模型信息
        model_info = {
            'algorithm': algorithm,
            'timestamp': timestamp,
            'metrics': metrics,
            'feature_importance': feature_importance,
            'model_file': str(model_path)
        }
        
        info_filename = f"{algorithm}_model_info_{timestamp}.json"
        info_path = output_dir / info_filename
        
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"模型已保存: {model_path}")
        self.logger.info(f"模型信息已保存: {info_path}")
        
        return str(model_path)
    
    def train_and_save(self, X_train: pd.DataFrame, y_train: pd.Series,
                      X_test: pd.DataFrame, y_test: pd.Series,
                      algorithm: str = None, mode: str = 'fallback',
                      output_dir: str = None) -> Dict[str, Any]:
        """训练并保存模型"""
        self.logger.info(f"开始训练模式: {mode}")
        
        if mode == 'single':
            if not algorithm:
                algorithm = self.config_manager.config.get('model_training', {}).get('default_algorithm', 'random_forest')
            result = self.train_single_algorithm(X_train, y_train, X_test, y_test, algorithm)
            
            if result['success']:
                model_path = self.save_model(
                    result['model'], result['algorithm'], 
                    result['metrics'], result['feature_importance'], 
                    output_dir
                )
                result['model_path'] = model_path
            
            return result
        
        elif mode == 'fallback':
            result = self.train_with_fallback(X_train, y_train, X_test, y_test, algorithm)
            
            model_path = self.save_model(
                result['model'], result['algorithm'], 
                result['metrics'], result['feature_importance'], 
                output_dir
            )
            result['model_path'] = model_path
            
            return result
        
        elif mode == 'compare':
            results = self.train_multiple_algorithms(X_train, y_train, X_test, y_test)
            
            # 保存所有成功的模型
            for algorithm, result in results.items():
                if algorithm != 'best_algorithm' and result.get('success', False):
                    model_path = self.save_model(
                        result['model'], result['algorithm'], 
                        result['metrics'], result['feature_importance'], 
                        output_dir
                    )
                    result['model_path'] = model_path
            
            return results
        
        else:
            raise ValueError(f"不支持的训练模式: {mode}")

def main():
    """主函数 - 演示用法"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)

    # 创建训练器
    trainer = EnhancedModelTrainer()

    print("增强模型训练器演示")
    print("=" * 40)
    print(f"可用算法: {list(trainer.available_algorithms.keys())}")
    print(f"算法状态: {trainer.available_algorithms}")

    # 显示配置信息
    config_algorithms = trainer.config_manager.config.get('model_training', {}).get('algorithms', [])
    default_algorithm = trainer.config_manager.config.get('model_training', {}).get('default_algorithm', 'random_forest')

    print(f"配置中的算法: {config_algorithms}")
    print(f"默认算法: {default_algorithm}")

    # 检查每个算法的超参数
    for algo in config_algorithms:
        try:
            params = trainer.config_manager.get_model_hyperparameters(algo)
            print(f"{algo} 超参数: {len(params)}个")
        except Exception as e:
            print(f"{algo} 超参数获取失败: {e}")

    print("\n✅ 增强模型训练器初始化成功")

if __name__ == "__main__":
    main()
