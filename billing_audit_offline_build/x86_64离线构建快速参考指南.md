# x86_64离线构建快速参考指南

**系统版本**: 山西电信出账稽核AI系统 v2.1.0  
**构建日期**: 2025-08-03  
**目标架构**: x86_64 (amd64)  

---

## 🚀 快速开始

### 在x86_64 Linux主机上执行：

#### 1. 解压离线包
```bash
tar -xzf billing_audit_x86_64_offline_complete_*.tar.gz
cd billing_audit_offline_build
```

#### 2. 执行离线构建
```bash
bash deployment/scripts/build_x86_64_offline.sh
```

#### 3. 验证构建结果
```bash
bash deployment/scripts/verify_offline_build.sh
```

#### 4. 制作部署包
```bash
bash deployment/scripts/create_x86_64_deployment.sh
```

---

## 📋 系统要求

### 必需条件
- **系统**: x86_64 Linux
- **Docker**: 20.10+
- **磁盘**: 2GB+ 可用空间
- **内存**: 4GB+ RAM

### 检查命令
```bash
# 检查架构
uname -m  # 必须输出: x86_64

# 检查Docker
docker --version
docker info

# 检查磁盘空间
df -h
```

---

## ⚠️ 重要注意事项

### 架构限制
- **必须在x86_64系统上构建**
- ARM64系统无法构建真正的x86_64镜像
- 构建前会自动验证系统架构

### 网络要求
- **基本离线**: 大部分构建过程无需网络
- **基础镜像**: 可能需要网络下载x86_64版本
- **系统包**: apt-get可能需要网络连接

---

## 🔧 故障排除

### 常见问题

#### 架构不匹配
```bash
# 问题: 镜像仍然是ARM64
# 解决: 确保在x86_64系统上构建
uname -m  # 必须是 x86_64
```

#### 基础镜像问题
```bash
# 重新下载正确架构镜像
docker pull --platform linux/amd64 python:3.9-slim
```

#### 权限问题
```bash
# 添加Docker权限
sudo usermod -aG docker $USER
# 重新登录生效
```

#### 磁盘空间不足
```bash
# 清理Docker缓存
docker system prune -f
```

---

## 📊 构建结果

### 预期输出
- **镜像名称**: `billing-audit-ai:v2.1.0-x86_64`
- **镜像大小**: ~1.2GB
- **架构**: amd64 (x86_64兼容)
- **构建时间**: 5-10分钟

### 验证命令
```bash
# 检查镜像
docker images | grep billing-audit-ai

# 验证架构
docker inspect billing-audit-ai:v2.1.0-x86_64 --format '{{.Architecture}}'
# 期望输出: amd64

# 测试功能
docker run --rm billing-audit-ai:v2.1.0-x86_64 python --version
```

---

## 📁 文件结构

### 离线包内容 (273MB)
```
billing_audit_offline_build/
├── python-3.9-slim-current.tar.gz     # 基础镜像 (88MB)
├── offline_packages/                   # Python依赖 (94MB)
├── src/                               # 源代码 (~15MB)
├── scripts/production/                # 生产脚本 (~3MB)
├── config/                           # 配置文件 (~1MB)
├── deployment/                       # 部署文件 (~2MB)
├── docs/                             # 文档 (~5MB)
└── README_OFFLINE_BUILD.md           # 使用说明
```

### 核心脚本
- `deployment/scripts/build_x86_64_offline.sh` - 构建脚本
- `deployment/scripts/verify_offline_build.sh` - 验证脚本
- `deployment/docker/Dockerfile.x86_64.offline` - 离线Dockerfile

---

## 🎯 成功标志

### 构建成功
```
🎉 x86_64离线构建完成！
📦 构建结果:
  镜像标签: billing-audit-ai:v2.1.0-x86_64
  构建方法: 离线构建
  目标架构: x86_64 (amd64)
  镜像大小: ~1.2GB
```

### 验证通过
```
🎉 x86_64离线构建验证完成！
📊 验证总结：
  ✅ 镜像存在性检查
  ✅ 架构正确性验证
  ✅ 系统环境检查
  ✅ 基本功能测试
  ✅ 依赖包验证
  ✅ 配置管理测试
```

---

## 📞 支持信息

### 详细文档
- **完整技术文档**: `山西电信出账稽核AI系统v2.1.0_x86_64离线构建技术文档_20250803.md`
- **使用说明**: `README_OFFLINE_BUILD.md`

### 技术支持
- **团队**: 九思计费专家团队
- **版本**: v2.1.0
- **更新日期**: 2025-08-03

---

**快速参考指南结束**

💡 **提示**: 如遇复杂问题，请参考完整技术文档获取详细解决方案。
