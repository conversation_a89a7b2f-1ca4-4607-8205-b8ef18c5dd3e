{"project": {"name": "山西电信出账稽核AI系统", "version": "v2.1.0", "description": "基于机器学习的电信出账稽核系统，支持千万级数据处理", "author": "技术团队", "created_date": "2025-07-27", "last_updated": "2025-07-28"}, "data_paths": {"input_data_dir": "./data/input", "output_data_dir": "./data/output", "model_dir": "./models", "logs_dir": "./logs", "temp_dir": "./tmp/billing_audit", "backup_dir": "./data/backup"}, "large_scale_processing": {"batch_size": 50000, "max_memory_usage_gb": 8, "enable_parallel_processing": true, "num_workers": 4, "chunk_overlap": 0, "progress_reporting_interval": 10000}, "billing_judgment": {"thresholds": {"absolute_threshold": 10.0, "relative_threshold": 0.1, "use_mixed_threshold": true, "uncertainty_factor": 2.0}, "judgment_rules": {"enable_business_rules": true, "enable_statistical_analysis": true, "enable_outlier_detection": true}}, "data_schema": {"fixed_fee": {"training_features": ["cal_type", "unit_type", "rate_unit", "final_eff_year", "final_eff_mon", "final_eff_day", "final_exp_year", "final_exp_mon", "final_exp_day", "cur_year_month", "charge_day_count", "month_day_count", "should_fee", "busi_flag"], "passthrough_columns": ["prod_id", "offer_id", "acct_id", "user_id", "bill_id", "eff_date", "exp_date", "bill_cycle_id", "region_code", "create_time", "update_time"], "target_column": "amount", "categorical_columns": ["cal_type", "unit_type", "rate_unit", "busi_flag"], "numerical_columns": ["final_eff_year", "final_eff_mon", "final_eff_day", "final_exp_year", "final_exp_mon", "final_exp_day", "charge_day_count", "month_day_count", "should_fee"]}}, "environment_variables": {"DATA_INPUT_DIR": "/data/input", "DATA_OUTPUT_DIR": "/data/output", "MODEL_DIR": "/models", "LOGS_DIR": "/logs", "DB_PASSWORD": "your_db_password_here", "PYTHONPATH": "/app", "OMP_NUM_THREADS": "4"}}