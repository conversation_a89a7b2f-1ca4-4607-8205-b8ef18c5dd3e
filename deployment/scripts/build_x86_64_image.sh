#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - x86_64架构Docker镜像构建脚本
# 支持跨架构构建，适用于Linux x86_64主机部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置变量
VERSION="v2.1.0"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
IMAGE_NAME="billing-audit-ai"
TARGET_ARCH="linux/amd64"
BUILDER_NAME="x86_64_builder"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 检查Docker环境
check_docker() {
    log_step "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    # 检查buildx是否可用
    if ! docker buildx version &> /dev/null; then
        log_error "Docker buildx不可用，请更新Docker到最新版本"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
    docker version --format "Docker版本: {{.Server.Version}}"
    docker buildx version
}

# 创建多架构构建器
create_builder() {
    log_step "创建多架构构建器..."

    # 检查构建器是否已存在
    if docker buildx ls | grep -q "$BUILDER_NAME"; then
        log_info "构建器 $BUILDER_NAME 已存在，删除旧构建器..."
        docker buildx rm "$BUILDER_NAME" || true
    fi

    # 重置Docker上下文到默认
    docker context use default || true

    # 创建新的构建器（使用更稳定的配置）
    docker buildx create --name "$BUILDER_NAME" --driver docker-container --use

    # 启动构建器
    docker buildx inspect --bootstrap

    log_success "多架构构建器创建成功"
}

# 构建x86_64镜像
build_x86_64_image() {
    log_step "构建x86_64架构镜像..."

    # 获取项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

    cd "$PROJECT_ROOT"

    # 构建精简版镜像（推荐用于生产）
    log_info "构建精简版x86_64镜像..."

    # 尝试使用buildx构建，如果失败则使用传统方法
    if docker buildx build \
        --platform "$TARGET_ARCH" \
        --file deployment/docker/Dockerfile.slim \
        --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
        --tag "${IMAGE_NAME}:${VERSION}-x86_64-slim" \
        --tag "${IMAGE_NAME}:latest-x86_64" \
        --load \
        . 2>/dev/null; then
        log_success "使用buildx构建成功"
    else
        log_warn "buildx构建失败，尝试使用传统Docker构建..."

        # 使用传统Docker构建（在x86_64系统上运行时）
        if [ "$(uname -m)" = "x86_64" ]; then
            docker build \
                --file deployment/docker/Dockerfile.slim \
                --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
                --tag "${IMAGE_NAME}:${VERSION}-x86_64-slim" \
                --tag "${IMAGE_NAME}:latest-x86_64" \
                .
        else
            log_error "当前系统不是x86_64，且buildx构建失败"
            log_error "请检查Docker buildx配置或在x86_64系统上运行"
            exit 1
        fi
    fi

    if [ $? -eq 0 ]; then
        log_success "x86_64镜像构建成功"
    else
        log_error "x86_64镜像构建失败"
        exit 1
    fi

    # 显示镜像信息
    log_info "镜像信息："
    docker images | grep "$IMAGE_NAME" | grep "x86_64"
}

# 验证镜像架构
verify_image_arch() {
    log_step "验证镜像架构..."
    
    # 检查镜像架构
    ARCH_INFO=$(docker inspect "${IMAGE_NAME}:${VERSION}-x86_64" --format '{{.Architecture}}')
    
    if [ "$ARCH_INFO" = "amd64" ]; then
        log_success "镜像架构验证通过: $ARCH_INFO"
    else
        log_error "镜像架构验证失败: $ARCH_INFO (期望: amd64)"
        exit 1
    fi
}

# 测试镜像功能
test_image_functionality() {
    log_step "测试镜像基本功能..."
    
    # 测试Python环境
    log_info "测试Python环境..."
    docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" python --version
    
    # 测试核心依赖
    log_info "测试核心依赖..."
    docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" python -c "
import pandas as pd
import numpy as np
import sklearn
import xgboost
import lightgbm
print('✅ 核心依赖测试通过')
print(f'pandas: {pd.__version__}')
print(f'numpy: {np.__version__}')
print(f'sklearn: {sklearn.__version__}')
print(f'xgboost: {xgboost.__version__}')
print(f'lightgbm: {lightgbm.__version__}')
"
    
    # 测试配置管理
    log_info "测试配置管理..."
    docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" python -c "
from src.config.production_config_manager import get_config_manager
config = get_config_manager()
print('✅ 配置管理测试通过')
print(f'项目: {config.get(\"project.name\")}')
print(f'版本: {config.get(\"project.version\")}')
"
    
    log_success "镜像功能测试通过"
}

# 导出镜像
export_image() {
    log_step "导出x86_64镜像..."
    
    # 创建导出目录
    EXPORT_DIR="docker_images_x86_64_${TIMESTAMP}"
    mkdir -p "$EXPORT_DIR"
    
    # 导出主镜像
    log_info "导出主应用镜像..."
    docker save "${IMAGE_NAME}:${VERSION}-x86_64" | gzip > "${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64.tar.gz"
    
    # 导出基础镜像（x86_64版本）
    log_info "拉取并导出x86_64基础镜像..."
    docker pull --platform "$TARGET_ARCH" python:3.9-slim
    docker save python:3.9-slim | gzip > "${EXPORT_DIR}/python-3.9-slim-x86_64.tar.gz"
    
    # 计算文件大小
    MAIN_SIZE=$(du -sh "${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64.tar.gz" | cut -f1)
    BASE_SIZE=$(du -sh "${EXPORT_DIR}/python-3.9-slim-x86_64.tar.gz" | cut -f1)
    TOTAL_SIZE=$(du -sh "$EXPORT_DIR" | cut -f1)
    
    log_success "镜像导出完成"
    log_info "主镜像大小: $MAIN_SIZE"
    log_info "基础镜像大小: $BASE_SIZE"
    log_info "总大小: $TOTAL_SIZE"
    log_info "导出目录: $EXPORT_DIR"
}

# 清理构建器
cleanup_builder() {
    log_step "清理构建器..."
    
    # 切换回默认构建器
    docker buildx use default
    
    # 删除自定义构建器
    docker buildx rm "$BUILDER_NAME" || true
    
    log_info "构建器清理完成"
}

# 显示完成信息
show_completion_info() {
    log_success "🎉 x86_64架构镜像构建完成！"
    echo ""
    echo "📦 构建结果:"
    echo "  镜像标签: ${IMAGE_NAME}:${VERSION}-x86_64"
    echo "  目标架构: x86_64 (amd64)"
    echo "  镜像大小: $(docker images ${IMAGE_NAME}:${VERSION}-x86_64 --format 'table {{.Size}}' | tail -1)"
    echo ""
    echo "📁 导出文件:"
    if [ -d "$EXPORT_DIR" ]; then
        echo "  导出目录: $EXPORT_DIR"
        echo "  主镜像: ${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64.tar.gz"
        echo "  基础镜像: ${EXPORT_DIR}/python-3.9-slim-x86_64.tar.gz"
    fi
    echo ""
    echo "🚀 下一步操作:"
    echo "  1. 运行部署包制作脚本:"
    echo "     bash deployment/scripts/create_x86_64_deployment.sh"
    echo "  2. 或直接使用镜像:"
    echo "     docker run --rm ${IMAGE_NAME}:${VERSION}-x86_64 python --version"
    echo ""
    echo "💡 注意事项:"
    echo "  - 此镜像专为x86_64 Linux主机设计"
    echo "  - 在ARM64系统上仅可构建，不可直接运行"
    echo "  - 部署到目标主机后可正常运行"
}

# 主函数
main() {
    log_info "开始构建山西电信出账稽核AI系统 x86_64架构镜像..."
    
    # 设置错误处理
    trap cleanup_builder EXIT
    
    # 执行构建步骤
    check_docker
    create_builder
    build_x86_64_image
    verify_image_arch
    test_image_functionality
    export_image
    show_completion_info
    
    log_success "x86_64架构镜像构建完成！"
}

# 运行主函数
main "$@"
