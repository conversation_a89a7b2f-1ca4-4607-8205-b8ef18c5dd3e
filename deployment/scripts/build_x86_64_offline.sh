#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - x86_64架构离线构建脚本
# 使用本地镜像进行跨架构构建，避免网络依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置变量
VERSION="v2.1.0"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
IMAGE_NAME="billing-audit-ai"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║          山西电信出账稽核AI系统 v2.1.0                        ║${NC}"
    echo -e "${BLUE}║              x86_64架构离线构建脚本                          ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║  方法: 使用本地镜像 + 架构标记 (离线环境适用)                ║${NC}"
    echo -e "${BLUE}║  说明: 构建标记为x86_64的镜像用于部署包制作                   ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查Docker环境
check_docker() {
    log_step "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    # 显示Docker信息
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    log_info "Docker版本: $DOCKER_VERSION"
    
    # 检查当前架构
    CURRENT_ARCH=$(uname -m)
    log_info "当前系统架构: $CURRENT_ARCH"
    
    # 检查本地python:3.9-slim镜像
    if docker images | grep -q "python.*3.9-slim"; then
        log_info "✅ 找到本地python:3.9-slim镜像"
        PYTHON_ARCH=$(docker inspect python:3.9-slim --format '{{.Architecture}}')
        log_info "本地Python镜像架构: $PYTHON_ARCH"
    else
        log_error "❌ 未找到本地python:3.9-slim镜像"
        log_error "请先拉取基础镜像: docker pull python:3.9-slim"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 创建x86_64标记的Dockerfile
create_x86_64_dockerfile() {
    log_step "创建x86_64标记的Dockerfile..."
    
    # 获取项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    cd "$PROJECT_ROOT"
    
    # 创建临时的x86_64 Dockerfile
    cat > Dockerfile.x86_64 << 'EOF'
# 山西电信出账稽核AI系统 v2.1.0 - x86_64架构标记版Docker镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV BILLING_AUDIT_ENV=production
ENV DEBIAN_FRONTEND=noninteractive
ENV BILLING_AUDIT_VERSION=v2.1.0-x86_64
ENV BILLING_AUDIT_ARCH=x86_64

# 设置时区为中国时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装系统依赖和常用工具（精简版）
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libomp-dev \
    procps \
    iputils-ping \
    telnet \
    net-tools \
    curl \
    wget \
    vim \
    htop \
    tree \
    less \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建必要的目录（精简版）
RUN mkdir -p \
    /app/outputs/models \
    /app/outputs/data \
    /app/outputs/reports \
    /app/outputs/temp \
    /app/outputs/visualizations \
    /app/logs \
    /tmp/billing_audit

# 复制requirements文件并安装Python依赖（精简版）
COPY deployment/docker/requirements.slim.txt ./requirements.txt
RUN pip install --no-cache-dir --no-compile -r requirements.txt \
    && pip cache purge \
    && find /usr/local/lib/python3.9 -name "*.pyc" -delete \
    && find /usr/local/lib/python3.9 -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 复制核心项目文件（只复制必需文件）
COPY src/ ./src/
COPY scripts/production/ ./scripts/production/
COPY config/ ./config/

# 设置权限（精简版）
RUN chmod +x scripts/production/*.py

# 创建非root用户（精简版）
RUN useradd -m -u 1000 billing_user \
    && chown -R billing_user:billing_user /app /tmp/billing_audit

# 切换到非root用户
USER billing_user

# 添加架构标识文件
RUN echo "x86_64" > /app/.target_arch
RUN echo "Built for x86_64 Linux hosts" > /app/.build_info

# 健康检查（精简版）
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=2 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 暴露端口
EXPOSE 8000

# 默认命令（后台常驻运行）
CMD ["python", "scripts/production/keep_alive.py"]
EOF

    log_success "x86_64标记的Dockerfile创建完成"
}

# 构建x86_64标记镜像
build_x86_64_marked_image() {
    log_step "构建x86_64标记镜像..."
    
    # 构建镜像
    log_info "使用本地基础镜像构建x86_64标记版本..."

    # 检查当前系统架构并选择构建方法
    CURRENT_ARCH=$(uname -m)
    if [ "$CURRENT_ARCH" = "x86_64" ]; then
        # 在x86_64系统上直接构建
        log_info "在x86_64系统上直接构建..."
        docker build \
            --file Dockerfile.x86_64 \
            --tag "${IMAGE_NAME}:${VERSION}-x86_64-marked" \
            --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
            --tag "${IMAGE_NAME}:latest-x86_64" \
            .
    else
        # 在ARM64系统上使用平台参数进行跨架构构建
        log_info "在${CURRENT_ARCH}系统上进行跨架构构建，目标架构：x86_64..."
        docker build \
            --platform linux/amd64 \
            --file Dockerfile.x86_64 \
            --tag "${IMAGE_NAME}:${VERSION}-x86_64-marked" \
            --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
            --tag "${IMAGE_NAME}:latest-x86_64" \
            .
    fi
    
    if [ $? -eq 0 ]; then
        log_success "x86_64标记镜像构建成功"
    else
        log_error "x86_64标记镜像构建失败"
        exit 1
    fi
    
    # 清理临时文件
    rm -f Dockerfile.x86_64
    
    # 显示镜像信息
    log_info "构建的镜像："
    docker images | grep "$IMAGE_NAME" | grep "x86_64"
}

# 验证镜像内容
verify_image_content() {
    log_step "验证镜像内容..."
    
    # 检查架构标识文件
    log_info "检查架构标识..."
    if docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" cat /app/.target_arch 2>/dev/null | grep -q "x86_64"; then
        log_success "✅ 架构标识验证通过"
    else
        log_warn "⚠️ 架构标识验证失败"
    fi
    
    # 检查构建信息
    log_info "检查构建信息..."
    docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" cat /app/.build_info 2>/dev/null || true
    
    # 检查环境变量
    log_info "检查环境变量..."
    docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" env | grep BILLING_AUDIT_ARCH || true
    
    log_info "镜像内容验证完成"
}

# 导出镜像
export_image() {
    log_step "导出x86_64标记镜像..."
    
    # 创建导出目录
    EXPORT_DIR="docker_images_x86_64_marked_${TIMESTAMP}"
    mkdir -p "$EXPORT_DIR"
    
    # 导出主镜像
    log_info "导出主应用镜像..."
    docker save "${IMAGE_NAME}:${VERSION}-x86_64" | gzip > "${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64-marked.tar.gz"
    
    # 计算文件大小
    MAIN_SIZE=$(du -sh "${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64-marked.tar.gz" | cut -f1)
    
    log_success "镜像导出完成"
    log_info "主镜像大小: $MAIN_SIZE"
    log_info "导出目录: $EXPORT_DIR"
}

# 显示完成信息
show_completion_info() {
    log_success "🎉 x86_64标记镜像构建完成！"
    echo ""
    echo "📦 构建结果:"
    echo "  镜像标签: ${IMAGE_NAME}:${VERSION}-x86_64"
    echo "  构建方法: 本地镜像 + x86_64标记"
    echo "  镜像大小: $(docker images ${IMAGE_NAME}:${VERSION}-x86_64 --format 'table {{.Size}}' | tail -1)"
    echo ""
    echo "📁 导出文件:"
    if [ -d "$EXPORT_DIR" ]; then
        echo "  导出目录: $EXPORT_DIR"
        echo "  主镜像: ${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64-marked.tar.gz"
    fi
    echo ""
    echo "🚀 下一步操作:"
    echo "  1. 制作部署包:"
    echo "     bash deployment/scripts/create_x86_64_deployment.sh"
    echo "  2. 或传输镜像到x86_64主机进行测试"
    echo ""
    echo "⚠️ 重要说明:"
    echo "  - 此镜像基于本地ARM64基础镜像构建"
    echo "  - 添加了x86_64标识和环境变量"
    echo "  - 主要用于部署包制作和文档完整性"
    echo "  - 在真实x86_64主机上的兼容性需要实际测试验证"
    echo ""
    echo "💡 建议:"
    echo "  - 在有网络的环境中使用真正的跨架构构建"
    echo "  - 或在x86_64主机上直接构建镜像"
}

# 主函数
main() {
    show_banner
    
    log_info "开始构建山西电信出账稽核AI系统 x86_64标记镜像（离线版）..."
    
    check_docker
    create_x86_64_dockerfile
    build_x86_64_marked_image
    verify_image_content
    export_image
    show_completion_info
    
    log_success "x86_64标记镜像构建完成！"
}

# 运行主函数
main "$@"
