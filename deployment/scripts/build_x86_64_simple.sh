#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - x86_64架构简化构建脚本
# 使用传统Docker构建方法，避免buildx复杂性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置变量
VERSION="v2.1.0"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
IMAGE_NAME="billing-audit-ai"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║          山西电信出账稽核AI系统 v2.1.0                        ║${NC}"
    echo -e "${BLUE}║              x86_64架构简化构建脚本                          ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║  方法: 传统Docker构建 (避免buildx复杂性)                     ║${NC}"
    echo -e "${BLUE}║  目标: 生成可在x86_64 Linux主机运行的镜像                    ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查Docker环境
check_docker() {
    log_step "检查Docker环境..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi

    # 显示Docker信息
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    log_info "Docker版本: $DOCKER_VERSION"

    # 检查buildx支持
    if docker buildx version &> /dev/null; then
        log_info "✅ Docker buildx 可用"
        BUILDX_AVAILABLE=true
    else
        log_warn "⚠️ Docker buildx 不可用，将使用传统构建方法"
        BUILDX_AVAILABLE=false
    fi

    # 检查当前架构
    CURRENT_ARCH=$(uname -m)
    log_info "当前系统架构: $CURRENT_ARCH"

    if [ "$CURRENT_ARCH" = "x86_64" ]; then
        log_info "当前系统是x86_64，可直接构建"
    else
        log_warn "当前系统是$CURRENT_ARCH，需要跨架构构建"
    fi

    log_success "Docker环境检查通过"
}

# 初始化buildx环境
setup_buildx() {
    log_step "初始化buildx环境..."

    if [ "$BUILDX_AVAILABLE" = true ]; then
        # 检查是否已有多架构构建器
        if ! docker buildx ls | grep -q "multiarch"; then
            log_info "创建多架构构建器..."
            docker buildx create --name multiarch --driver docker-container --use
            docker buildx inspect --bootstrap
        else
            log_info "使用现有多架构构建器..."
            docker buildx use multiarch
        fi
        log_success "✅ buildx环境初始化完成"
    else
        log_info "跳过buildx初始化（不可用）"
    fi
}

# 准备x86_64基础镜像
prepare_x86_64_base_image() {
    log_step "准备x86_64基础镜像..."

    if [ "$BUILDX_AVAILABLE" = true ]; then
        # 使用buildx拉取多架构镜像
        log_info "使用buildx拉取x86_64基础镜像..."
        if docker buildx imagetools inspect python:3.9-slim &>/dev/null; then
            log_success "✅ 基础镜像可用"
        else
            log_warn "⚠️ 基础镜像检查失败，构建时将自动拉取"
        fi
    else
        # 传统方式检查本地镜像
        if docker images | grep -q "python.*3.9-slim"; then
            log_info "✅ 找到本地python:3.9-slim基础镜像"
        else
            log_warn "⚠️ 未找到本地基础镜像，构建时将自动拉取"
        fi
    fi
}

# 构建x86_64镜像
build_x86_64_image() {
    log_step "构建x86_64架构镜像..."

    # 获取项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

    cd "$PROJECT_ROOT"

    # 初始化buildx环境
    setup_buildx

    # 准备基础镜像
    if ! prepare_x86_64_base_image; then
        log_warn "基础镜像准备失败，尝试使用现有镜像继续构建..."
    fi

    # 构建精简版镜像
    log_info "构建精简版镜像..."

    # 检查当前系统架构并选择构建方法
    CURRENT_ARCH=$(uname -m)
    if [ "$CURRENT_ARCH" = "x86_64" ]; then
        # 在x86_64系统上直接构建
        log_info "在x86_64系统上直接构建..."
        docker build \
            --file deployment/docker/Dockerfile.x86_64 \
            --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
            --tag "${IMAGE_NAME}:${VERSION}-x86_64-slim" \
            --tag "${IMAGE_NAME}:latest-x86_64" \
            .
    else
        # 在非x86_64系统上使用跨架构构建
        log_info "在${CURRENT_ARCH}系统上进行跨架构构建，目标架构：x86_64..."

        BUILD_SUCCESS=false

        if [ "$BUILDX_AVAILABLE" = true ]; then
            # 使用buildx进行跨架构构建（推荐）
            log_info "使用Docker buildx进行跨架构构建..."
            if docker buildx build \
                --platform linux/amd64 \
                --file deployment/docker/Dockerfile.x86_64.local \
                --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
                --tag "${IMAGE_NAME}:${VERSION}-x86_64-slim" \
                --tag "${IMAGE_NAME}:latest-x86_64" \
                --load \
                . 2>/dev/null; then
                BUILD_SUCCESS=true
                log_success "✅ buildx构建成功"
            else
                log_warn "⚠️ buildx构建失败，回退到传统Docker构建..."
                # 回退到传统Docker构建
                if docker build \
                    --platform linux/amd64 \
                    --file deployment/docker/Dockerfile.x86_64.local \
                    --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
                    --tag "${IMAGE_NAME}:${VERSION}-x86_64-slim" \
                    --tag "${IMAGE_NAME}:latest-x86_64" \
                    .; then
                    BUILD_SUCCESS=true
                    log_success "✅ 传统Docker构建成功"
                else
                    log_error "❌ 传统Docker构建也失败"
                fi
            fi
        else
            # 直接使用传统Docker构建
            log_info "使用传统Docker构建..."
            if docker build \
                --platform linux/amd64 \
                --file deployment/docker/Dockerfile.x86_64.local \
                --tag "${IMAGE_NAME}:${VERSION}-x86_64" \
                --tag "${IMAGE_NAME}:${VERSION}-x86_64-slim" \
                --tag "${IMAGE_NAME}:latest-x86_64" \
                .; then
                BUILD_SUCCESS=true
                log_success "✅ 传统Docker构建成功"
            else
                log_error "❌ 传统Docker构建失败"
            fi
        fi
    fi

    # 检查构建结果
    if [ "$BUILD_SUCCESS" = true ]; then
        log_success "🎉 x86_64镜像构建成功"
    else
        log_error "❌ x86_64镜像构建失败"
        log_error "可能的原因："
        log_error "1. Docker版本不支持跨架构构建"
        log_error "2. 网络连接问题，无法拉取基础镜像"
        log_error "3. Dockerfile.x86_64文件不存在或有错误"
        exit 1
    fi
    
    # 显示镜像信息
    log_info "构建的镜像："
    docker images | grep "$IMAGE_NAME" | grep "x86_64"
}

# 验证镜像架构
verify_image_architecture() {
    log_step "验证镜像架构..."

    # 检查镜像架构
    IMAGE_ARCH=$(docker inspect "${IMAGE_NAME}:${VERSION}-x86_64" --format '{{.Architecture}}')
    IMAGE_OS=$(docker inspect "${IMAGE_NAME}:${VERSION}-x86_64" --format '{{.Os}}')

    log_info "镜像架构信息："
    log_info "  架构: $IMAGE_ARCH"
    log_info "  操作系统: $IMAGE_OS"

    # 验证架构是否正确
    if [ "$IMAGE_ARCH" = "amd64" ]; then
        log_success "✅ 镜像架构验证通过：$IMAGE_ARCH (x86_64兼容)"

        # 额外验证：检查镜像内的架构信息
        log_info "验证镜像内部架构信息..."
        if docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" /app/verify_arch.sh 2>/dev/null; then
            log_success "✅ 镜像内部架构信息验证通过"
        else
            log_warn "⚠️ 镜像内部架构信息验证失败（可能是跨架构运行限制）"
        fi
    else
        log_error "❌ 镜像架构验证失败：期望 amd64，实际 $IMAGE_ARCH"
        log_error "这个镜像无法在x86_64 Linux主机上运行！"
        return 1
    fi
}

# 测试镜像功能
test_image_functionality() {
    log_step "测试镜像基本功能..."

    # 先验证架构
    if ! verify_image_architecture; then
        log_error "架构验证失败，跳过功能测试"
        return 1
    fi

    # 测试Python环境（仅在架构匹配时）
    CURRENT_ARCH=$(uname -m)
    if [ "$CURRENT_ARCH" = "x86_64" ]; then
        log_info "测试Python环境..."
        if docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" python --version; then
            log_info "✅ Python环境测试通过"
        else
            log_warn "⚠️ Python环境测试失败"
        fi

        log_info "测试核心依赖..."
        if docker run --rm "${IMAGE_NAME}:${VERSION}-x86_64" python -c "
import pandas as pd
import numpy as np
import sklearn
print('✅ 核心依赖测试通过')
print(f'pandas: {pd.__version__}')
print(f'numpy: {np.__version__}')
print(f'sklearn: {sklearn.__version__}')
"; then
            log_success "核心依赖测试通过"
        else
            log_warn "核心依赖测试失败"
        fi
    else
        log_info "当前系统架构($CURRENT_ARCH)与目标架构(x86_64)不匹配，跳过运行测试"
        log_info "镜像将在x86_64 Linux主机上进行功能验证"
    fi
}

# 导出镜像
export_image() {
    log_step "导出x86_64镜像..."
    
    # 创建导出目录
    EXPORT_DIR="docker_images_x86_64_simple_${TIMESTAMP}"
    mkdir -p "$EXPORT_DIR"
    
    # 导出主镜像
    log_info "导出主应用镜像..."
    docker save "${IMAGE_NAME}:${VERSION}-x86_64" | gzip > "${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64.tar.gz"
    
    # 计算文件大小
    MAIN_SIZE=$(du -sh "${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64.tar.gz" | cut -f1)
    
    log_success "镜像导出完成"
    log_info "主镜像大小: $MAIN_SIZE"
    log_info "导出目录: $EXPORT_DIR"
}

# 显示完成信息
show_completion_info() {
    log_success "🎉 x86_64架构镜像构建完成！"
    echo ""
    echo "📦 构建结果:"
    echo "  镜像标签: ${IMAGE_NAME}:${VERSION}-x86_64"
    echo "  构建方法: 传统Docker构建"
    echo "  镜像大小: $(docker images ${IMAGE_NAME}:${VERSION}-x86_64 --format 'table {{.Size}}' | tail -1)"
    echo ""
    echo "📁 导出文件:"
    if [ -d "$EXPORT_DIR" ]; then
        echo "  导出目录: $EXPORT_DIR"
        echo "  主镜像: ${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64.tar.gz"
    fi
    echo ""
    echo "🚀 下一步操作:"
    echo "  1. 制作部署包:"
    echo "     bash deployment/scripts/create_x86_64_deployment.sh"
    echo "  2. 或传输镜像到x86_64主机:"
    echo "     scp ${EXPORT_DIR}/${IMAGE_NAME}-${VERSION}-x86_64.tar.gz user@host:/path/"
    echo "     # 在目标主机上: docker load < ${IMAGE_NAME}-${VERSION}-x86_64.tar.gz"
    echo ""
    echo "💡 注意事项:"
    echo "  - 此镜像使用传统Docker构建方法"
    echo "  - 在x86_64 Linux主机上应该可以正常运行"
    echo "  - 如果当前系统不是x86_64，请在目标主机上测试"
}

# 主函数
main() {
    show_banner
    
    log_info "开始构建山西电信出账稽核AI系统 x86_64架构镜像（简化版）..."
    
    check_docker
    build_x86_64_image
    test_image_functionality
    export_image
    show_completion_info
    
    log_success "x86_64架构镜像构建完成！"
}

# 运行主函数
main "$@"
