#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - Docker镜像导出脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 版本信息
VERSION="v2.1.0"
IMAGE_NAME="billing-audit-ai"
BUILD_DATE=$(date +"%Y%m%d_%H%M%S")
EXPORT_DIR="./docker_export"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "📦 山西电信出账稽核AI系统 ${VERSION}"
    echo "   Docker镜像导出工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "📅 导出时间: $(date)"
    echo "🏷️  镜像名称: ${IMAGE_NAME}"
    echo "📁 导出目录: ${EXPORT_DIR}"
    echo ""
}

# 检查Docker环境
check_docker() {
    log_step "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! docker ps &> /dev/null; then
        log_error "Docker服务未运行或权限不足"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 构建Docker镜像
build_image() {
    log_step "构建Docker镜像..."
    
    # 获取脚本所在目录的项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    cd "$PROJECT_ROOT"
    
    # 构建镜像
    IMAGE_TAG="${VERSION}-${BUILD_DATE}"
    docker build -f deployment/docker/Dockerfile -t "${IMAGE_NAME}:${IMAGE_TAG}" -t "${IMAGE_NAME}:latest" .
    
    if [ $? -eq 0 ]; then
        log_info "Docker镜像构建成功: ${IMAGE_NAME}:${IMAGE_TAG}"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
    
    # 显示镜像信息
    docker images | grep "$IMAGE_NAME"
}

# 创建导出目录
create_export_directory() {
    log_step "创建导出目录..."
    
    mkdir -p "$EXPORT_DIR"
    
    # 创建子目录
    mkdir -p "$EXPORT_DIR/images"
    mkdir -p "$EXPORT_DIR/configs"
    mkdir -p "$EXPORT_DIR/scripts"
    mkdir -p "$EXPORT_DIR/docs"
    
    log_info "导出目录创建完成: $EXPORT_DIR"
}

# 导出Docker镜像
export_docker_images() {
    log_step "导出Docker镜像..."
    
    # 导出主镜像
    IMAGE_FILE="$EXPORT_DIR/images/${IMAGE_NAME}-${VERSION}-${BUILD_DATE}.tar"
    log_info "导出镜像到: $IMAGE_FILE"
    
    docker save -o "$IMAGE_FILE" "${IMAGE_NAME}:latest"
    
    if [ $? -eq 0 ]; then
        log_info "镜像导出成功"
        
        # 显示文件大小
        file_size=$(du -h "$IMAGE_FILE" | cut -f1)
        log_info "镜像文件大小: $file_size"
    else
        log_error "镜像导出失败"
        exit 1
    fi
    
    # 导出依赖镜像
    log_info "导出依赖镜像..."
    
    # Python基础镜像
    docker pull python:3.9-slim
    docker save -o "$EXPORT_DIR/images/python-3.9-slim.tar" python:3.9-slim
    
    # PostgreSQL镜像
    docker pull postgres:13
    docker save -o "$EXPORT_DIR/images/postgres-13.tar" postgres:13
    
    # Redis镜像
    docker pull redis:6-alpine
    docker save -o "$EXPORT_DIR/images/redis-6-alpine.tar" redis:6-alpine
    
    log_info "依赖镜像导出完成"
}

# 复制配置文件
copy_configs() {
    log_step "复制配置文件..."
    
    # 获取项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    # 复制Docker配置
    cp -r "$PROJECT_ROOT/deployment/docker" "$EXPORT_DIR/configs/"
    cp -r "$PROJECT_ROOT/deployment/config" "$EXPORT_DIR/configs/"
    
    # 复制requirements
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        cp "$PROJECT_ROOT/requirements.txt" "$EXPORT_DIR/configs/"
    else
        cp "$PROJECT_ROOT/deployment/requirements.txt" "$EXPORT_DIR/configs/"
    fi
    
    log_info "配置文件复制完成"
}

# 复制部署脚本
copy_scripts() {
    log_step "复制部署脚本..."
    
    # 获取项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    # 复制部署脚本
    cp "$PROJECT_ROOT/deployment/scripts/deploy_linux_production.sh" "$EXPORT_DIR/scripts/"
    cp "$PROJECT_ROOT/deployment/scripts/deploy_production.sh" "$EXPORT_DIR/scripts/"
    
    # 设置执行权限
    chmod +x "$EXPORT_DIR/scripts"/*.sh
    
    log_info "部署脚本复制完成"
}

# 复制文档
copy_docs() {
    log_step "复制文档..."
    
    # 获取项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    # 复制关键文档
    cp "$PROJECT_ROOT/README.md" "$EXPORT_DIR/docs/"
    cp -r "$PROJECT_ROOT/docs/guides" "$EXPORT_DIR/docs/"
    cp -r "$PROJECT_ROOT/docs/technical" "$EXPORT_DIR/docs/"
    
    log_info "文档复制完成"
}

# 创建导入脚本
create_import_script() {
    log_step "创建导入脚本..."
    
    cat > "$EXPORT_DIR/import_images.sh" << 'EOF'
#!/bin/bash
# Docker镜像导入脚本

set -e

echo "🚀 开始导入Docker镜像..."

# 导入主镜像
echo "📦 导入主镜像..."
for image_file in images/*.tar; do
    if [ -f "$image_file" ]; then
        echo "导入: $image_file"
        docker load -i "$image_file"
    fi
done

echo "✅ 镜像导入完成"
echo ""
echo "📋 已导入的镜像:"
docker images | grep -E "(billing-audit-ai|python|postgres|redis)"
echo ""
echo "🚀 下一步:"
echo "1. 运行部署脚本: ./scripts/deploy_linux_production.sh"
echo "2. 或者使用docker-compose: docker-compose -f configs/docker/docker-compose.yml up -d"
EOF
    
    chmod +x "$EXPORT_DIR/import_images.sh"
    
    log_info "导入脚本创建完成"
}

# 创建README文件
create_readme() {
    log_step "创建README文件..."
    
    cat > "$EXPORT_DIR/README.md" << EOF
# 山西电信出账稽核AI系统 ${VERSION} - Docker部署包

## 📦 包内容

\`\`\`
docker_export/
├── images/                          # Docker镜像文件
│   ├── billing-audit-ai-${VERSION}-${BUILD_DATE}.tar  # 主镜像
│   ├── python-3.9-slim.tar         # Python基础镜像
│   ├── postgres-13.tar             # PostgreSQL镜像
│   └── redis-6-alpine.tar          # Redis镜像
├── configs/                         # 配置文件
│   ├── docker/                      # Docker配置
│   ├── config/                      # 应用配置
│   └── requirements.txt             # Python依赖
├── scripts/                         # 部署脚本
│   ├── deploy_linux_production.sh  # Linux生产环境部署
│   └── deploy_production.sh        # 通用部署脚本
├── docs/                           # 文档
│   ├── README.md                   # 项目说明
│   ├── guides/                     # 使用指南
│   └── technical/                  # 技术文档
├── import_images.sh                # 镜像导入脚本
└── README.md                       # 本文件
\`\`\`

## 🚀 快速部署

### 1. 导入镜像
\`\`\`bash
chmod +x import_images.sh
./import_images.sh
\`\`\`

### 2. 运行部署脚本
\`\`\`bash
chmod +x scripts/deploy_linux_production.sh
./scripts/deploy_linux_production.sh
\`\`\`

### 3. 验证部署
\`\`\`bash
docker ps | grep billing-audit
\`\`\`

## 📋 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **内存**: 8GB+ (推荐16GB+)
- **磁盘**: 20GB+ 可用空间
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

## 🔧 使用方法

### 生产级主脚本 (推荐)
\`\`\`bash
# 完整流程
docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \\
  --input /data/input/data.csv \\
  --batch-size 1000

# 模块化执行
docker exec billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \\
  --input /data/input/raw_data.csv
\`\`\`

### 传统方式
\`\`\`bash
# 模型训练
docker exec billing-audit-ai python src/billing_audit/training/train_large_scale_model.py \\
  --input /data/input/training_data.csv \\
  --output /models/ \\
  --batch-size 50000

# 批量预测
docker exec billing-audit-ai python src/billing_audit/inference/predict_large_scale.py \\
  --input /data/input/predict_data.csv \\
  --model /models/large_scale_model_latest.pkl \\
  --feature-engineer /models/large_scale_feature_engineer_latest.pkl \\
  --output /data/output/predictions.csv
\`\`\`

## 📁 重要目录

- **输入数据**: \`/opt/billing-audit-ai/data/input/\`
- **输出结果**: \`/opt/billing-audit-ai/data/output/\`
- **执行报告**: \`/opt/billing-audit-ai/data/output/reports/markdown/\`
- **模型文件**: \`/opt/billing-audit-ai/models/\`
- **日志文件**: \`/opt/billing-audit-ai/logs/\`

## 🆘 故障排除

### 查看日志
\`\`\`bash
docker logs billing-audit-ai
\`\`\`

### 进入容器
\`\`\`bash
docker exec -it billing-audit-ai bash
\`\`\`

### 重启服务
\`\`\`bash
docker restart billing-audit-ai
\`\`\`

## 📞 技术支持

如有问题，请查看 \`docs/\` 目录下的详细文档。

---

**版本**: ${VERSION}  
**构建时间**: ${BUILD_DATE}  
**镜像大小**: $(du -h images/${IMAGE_NAME}-${VERSION}-${BUILD_DATE}.tar 2>/dev/null | cut -f1 || echo "未知")
EOF
    
    log_info "README文件创建完成"
}

# 创建压缩包
create_archive() {
    log_step "创建压缩包..."
    
    ARCHIVE_NAME="billing-audit-ai-${VERSION}-${BUILD_DATE}.tar.gz"
    
    tar -czf "$ARCHIVE_NAME" -C "$(dirname "$EXPORT_DIR")" "$(basename "$EXPORT_DIR")"
    
    if [ $? -eq 0 ]; then
        archive_size=$(du -h "$ARCHIVE_NAME" | cut -f1)
        log_info "压缩包创建成功: $ARCHIVE_NAME ($archive_size)"
    else
        log_error "压缩包创建失败"
        exit 1
    fi
}

# 显示导出信息
show_export_info() {
    log_step "导出信息"
    
    echo ""
    echo -e "${GREEN}=================================================="
    echo "📦 Docker镜像导出完成"
    echo "==================================================${NC}"
    echo ""
    echo -e "${BLUE}📊 导出信息:${NC}"
    echo "  版本: ${VERSION}"
    echo "  构建时间: ${BUILD_DATE}"
    echo "  导出目录: ${EXPORT_DIR}"
    echo ""
    echo -e "${BLUE}📁 文件列表:${NC}"
    find "$EXPORT_DIR" -type f -exec ls -lh {} \; | awk '{print "  " $9 " (" $5 ")"}'
    echo ""
    echo -e "${BLUE}🚀 部署步骤:${NC}"
    echo "  1. 将导出目录复制到目标服务器"
    echo "  2. 运行: chmod +x import_images.sh && ./import_images.sh"
    echo "  3. 运行: chmod +x scripts/deploy_linux_production.sh && ./scripts/deploy_linux_production.sh"
    echo ""
    echo -e "${GREEN}✅ 导出完成！可以将 ${EXPORT_DIR} 目录传输到目标服务器。${NC}"
    echo ""
}

# 主函数
main() {
    show_banner
    check_docker
    build_image
    create_export_directory
    export_docker_images
    copy_configs
    copy_scripts
    copy_docs
    create_import_script
    create_readme
    create_archive
    show_export_info
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --export-dir)
            EXPORT_DIR="$2"
            shift 2
            ;;
        --no-build)
            NO_BUILD=true
            shift
            ;;
        --no-archive)
            NO_ARCHIVE=true
            shift
            ;;
        --help)
            echo "Docker镜像导出工具"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --export-dir DIR   指定导出目录 (默认: ./docker_export)"
            echo "  --no-build         跳过镜像构建"
            echo "  --no-archive       不创建压缩包"
            echo "  --help             显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            exit 1
            ;;
    esac
done

# 运行主函数
main
