# 🚀 山西电信出账稽核AI系统 v2.1.0 - 容器使用指南

## 📋 容器自动退出问题说明

### **问题原因**
Docker容器的默认行为是：当主进程执行完毕后，容器会自动退出。原始的Dockerfile使用了一个简单的打印命令作为默认命令，执行完就退出了。

### **解决方案**
我们提供了多种方式来保持容器运行并执行AI处理任务。

## 🔧 **容器运行方式**

### **方式一: 使用启动脚本 (推荐)** ⭐

```bash
# 一键启动，自动处理所有配置
chmod +x start_container.sh
./start_container.sh
```

**优势**:
- ✅ 自动创建数据目录
- ✅ 自动设置环境变量
- ✅ 自动检查容器状态
- ✅ 提供详细的使用指导

### **方式二: 后台服务模式 (生产环境推荐)**

```bash
# 启动后台容器，保持运行
docker run -d \
  --name billing-audit-ai \
  -v ~/billing-audit-ai/data/input:/data/input \
  -v ~/billing-audit-ai/data/output:/data/output \
  -v ~/billing-audit-ai/data/models:/models \
  -v ~/billing-audit-ai/data/logs:/logs \
  -p 8000:8000 \
  -e DATA_INPUT_DIR=/data/input \
  -e MODEL_DIR=/models \
  -e LOGS_DIR=/logs \
  -e DB_PASSWORD=default_password \
  billing-audit-ai:v2.1.0

# 然后通过exec执行命令
docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help
```

**优势**:
- ✅ 容器持续运行，可重复使用
- ✅ 适合生产环境长期部署
- ✅ 支持多次任务执行

### **方式三: 交互式模式 (开发/调试推荐)**

```bash
# 交互式进入容器
docker run -it --name billing-audit-ai \
  -v ~/billing-audit-ai/data:/data \
  billing-audit-ai:v2.1.0 bash

# 在容器内直接执行命令
python scripts/production/billing_audit_main.py --help
python scripts/production/billing_audit_main.py full --input /data/input/data.csv
```

**优势**:
- ✅ 直接在容器内操作
- ✅ 便于调试和开发
- ✅ 可以查看详细的执行过程

### **方式四: 一次性任务模式 (批处理推荐)**

```bash
# 执行完整流程后自动删除容器
docker run --rm \
  -v ~/billing-audit-ai/data:/data \
  billing-audit-ai:v2.1.0 \
  python scripts/production/billing_audit_main.py full \
  --input /data/input/your_data.csv \
  --output /data/output/results.csv

# 执行单个步骤
docker run --rm \
  -v ~/billing-audit-ai/data:/data \
  billing-audit-ai:v2.1.0 \
  python scripts/production/billing_audit_main.py feature-engineering \
  --input /data/input/your_data.csv
```

**优势**:
- ✅ 执行完自动清理
- ✅ 适合批处理任务
- ✅ 不占用系统资源

## 📊 **常用操作命令**

### **容器管理**

```bash
# 查看容器状态
docker ps | grep billing-audit-ai

# 查看所有容器（包括停止的）
docker ps -a | grep billing-audit-ai

# 启动已停止的容器
docker start billing-audit-ai

# 停止容器
docker stop billing-audit-ai

# 重启容器
docker restart billing-audit-ai

# 删除容器
docker rm billing-audit-ai

# 查看容器日志
docker logs billing-audit-ai

# 查看容器资源使用情况
docker stats billing-audit-ai
```

### **数据操作**

```bash
# 复制数据到容器
docker cp your_data.csv billing-audit-ai:/data/input/

# 从容器复制结果
docker cp billing-audit-ai:/data/output/results.csv ./

# 查看容器内文件
docker exec billing-audit-ai ls -la /data/input/
docker exec billing-audit-ai ls -la /data/output/

# 查看日志文件
docker exec billing-audit-ai ls -la /logs/
docker exec billing-audit-ai cat /logs/billing_audit_main.log
```

### **AI任务执行**

```bash
# 查看帮助
docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help

# 完整流程
docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \
  --input /data/input/data.csv \
  --output /data/output/results.csv

# 单独步骤
docker exec billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \
  --input /data/input/data.csv

docker exec billing-audit-ai python scripts/production/billing_audit_main.py training \
  --input /data/input/features.csv

docker exec billing-audit-ai python scripts/production/billing_audit_main.py prediction \
  --input /data/input/features.csv

docker exec billing-audit-ai python scripts/production/billing_audit_main.py judgment \
  --input /data/output/predictions.csv
```

## 🔍 **故障排除**

### **容器无法启动**

```bash
# 检查镜像是否存在
docker images | grep billing-audit-ai

# 检查端口是否被占用
netstat -tulpn | grep 8000

# 查看详细错误信息
docker logs billing-audit-ai
```

### **容器自动退出**

```bash
# 检查容器退出状态
docker ps -a | grep billing-audit-ai

# 查看退出原因
docker logs billing-audit-ai

# 使用后台模式重新启动
docker run -d --name billing-audit-ai-new \
  -v ~/billing-audit-ai/data:/data \
  billing-audit-ai:v2.1.0 \
  tail -f /dev/null
```

### **权限问题**

```bash
# 检查数据目录权限
ls -la ~/billing-audit-ai/data/

# 修改权限
chmod -R 755 ~/billing-audit-ai/data/
chown -R $USER:$USER ~/billing-audit-ai/data/
```

## 📋 **最佳实践**

### **生产环境部署**
1. 使用方式二（后台服务模式）
2. 设置环境变量消除警告
3. 定期备份数据目录
4. 监控容器资源使用情况

### **开发环境使用**
1. 使用方式三（交互式模式）
2. 挂载源代码目录便于调试
3. 使用小数据集进行测试

### **批处理任务**
1. 使用方式四（一次性任务模式）
2. 设置合适的资源限制
3. 记录执行日志

---

**文档维护**: 技术团队  
**最后更新**: 2025-07-28  
**适用版本**: v2.1.0
