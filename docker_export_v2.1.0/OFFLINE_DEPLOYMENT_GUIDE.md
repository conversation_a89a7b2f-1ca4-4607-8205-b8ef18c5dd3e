# 山西电信出账稽核AI系统 v2.1.0 - 离线部署指南

## 📋 文档信息

**文档版本**: v1.0  
**创建时间**: 2025-07-28  
**系统版本**: v2.1.0 (流程修正版)  
**适用环境**: 内网服务器 (无外网连接)  

## 🚀 部署包内容

本离线部署包包含以下内容：

```
docker_export_v2.1.0/
├── images/                           # Docker镜像文件
│   ├── billing-audit-ai-v2.1.0-*.tar # 主应用镜像 (~342MB)
│   ├── python-3.9-slim.tar           # Python基础镜像 (~44MB)
│   ├── postgres-13.tar               # PostgreSQL数据库镜像 (~142MB)
│   └── redis-6-alpine.tar            # Redis缓存镜像 (~12MB)
├── configs/                          # 配置文件
│   ├── config/                       # 应用配置
│   │   └── production_config.json    # 生产环境配置
│   ├── docker/                       # Docker配置
│   │   ├── Dockerfile                # 应用Dockerfile
│   │   └── docker-compose.yml        # 容器编排配置
│   └── requirements.txt              # Python依赖清单
├── scripts/                          # 部署脚本
│   ├── deploy_production.sh          # 生产环境部署脚本
│   ├── deploy_linux_production.sh    # Linux专用部署脚本
│   ├── export_docker_image.sh        # 镜像导出脚本
│   └── validate_deployment.sh        # 部署验证脚本
├── import_images.sh                  # 镜像导入脚本
├── OFFLINE_DEPLOYMENT_GUIDE.md       # 本文档
└── README.md                         # 部署包说明
```

## 🔧 部署前准备

### 系统要求

- **操作系统**: CentOS 7+, Ubuntu 18.04+, 或其他现代Linux发行版
- **Docker**: 20.10.0+
- **硬件要求**:
  - **CPU**: 8核+ (推荐16核)
  - **内存**: 16GB+ (推荐32GB)
  - **存储**: 50GB+ 可用空间
  - **网络**: 内网连接 (无需外网)

### 环境检查

在部署前，请确认目标服务器满足以下条件：

1. Docker已安装并正常运行
   ```bash
   docker --version
   docker info
   ```

2. 磁盘空间充足
   ```bash
   df -h
   ```

3. 系统资源充足
   ```bash
   free -h
   nproc
   ```

## 📥 部署步骤

### 1. 传输部署包

将整个`docker_export_v2.1.0`目录传输到目标服务器，可以使用以下方法之一：

- 物理介质 (U盘、硬盘)
- 内网文件传输 (SCP、SFTP)
- 内网文件服务器

```bash
# 示例: 使用SCP传输
scp -r docker_export_v2.1.0 user@target-server:/path/to/destination
```

### 2. 导入Docker镜像

在目标服务器上，进入部署包目录并执行导入脚本：

```bash
cd /path/to/docker_export_v2.1.0
chmod +x import_images.sh
./import_images.sh
```

导入成功后，可以验证镜像是否正确导入：

```bash
docker images | grep -E "(billing-audit-ai|python|postgres|redis)"
```

### 3. 启动容器

#### 方式一: 使用启动脚本 (推荐)

```bash
# 使用提供的启动脚本
chmod +x start_container.sh
./start_container.sh
```

#### 方式二: 手动启动容器

```bash
# 创建数据目录
mkdir -p ~/billing-audit-ai/data/{input,output,models,logs}

# 启动容器 (保持运行)
docker run -d \
  --name billing-audit-ai \
  -v ~/billing-audit-ai/data/input:/data/input \
  -v ~/billing-audit-ai/data/output:/data/output \
  -v ~/billing-audit-ai/data/models:/models \
  -v ~/billing-audit-ai/data/logs:/logs \
  -p 8000:8000 \
  -e DATA_INPUT_DIR=/data/input \
  -e MODEL_DIR=/models \
  -e LOGS_DIR=/logs \
  -e DB_PASSWORD=default_password \
  billing-audit-ai:v2.1.0
```

#### 方式三: 交互式运行 (开发/调试)

```bash
# 交互式进入容器
docker run -it --name billing-audit-ai \
  -v ~/billing-audit-ai/data:/data \
  billing-audit-ai:v2.1.0 bash

# 在容器内执行命令
python scripts/production/billing_audit_main.py --help
```

#### 方式四: 直接执行命令 (一次性任务)

```bash
# 执行完整流程后自动删除容器
docker run --rm \
  -v ~/billing-audit-ai/data:/data \
  billing-audit-ai:v2.1.0 \
  python scripts/production/billing_audit_main.py full \
  --input /data/input/your_data.csv \
  --output /data/output/results.csv
```

### 4. 验证部署

#### 基本验证

```bash
# 检查容器状态
docker ps | grep billing-audit-ai

# 查看容器日志
docker logs billing-audit-ai
```

#### 功能验证

```bash
# 运行端到端测试
docker exec billing-audit-ai python scripts/testing/end_to_end_test.py

# 查看帮助信息
docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help
```

## 🚀 使用系统

### 基本操作

```bash
# 进入容器
docker exec -it billing-audit-ai bash

# 查看帮助
docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help

# 检查容器状态
docker ps | grep billing-audit-ai

# 查看容器日志
docker logs billing-audit-ai
```

### 主脚本使用

```bash
# 完整流程执行
docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \
  --input /data/input/your_data.csv \
  --output /data/output/results.csv

# 单独执行特征工程
docker exec billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \
  --input /data/input/your_data.csv \
  --output /data/output/features.csv

# 单独执行模型训练
docker exec billing-audit-ai python scripts/production/billing_audit_main.py training \
  --input /data/input/features.csv

# 单独执行预测
docker exec billing-audit-ai python scripts/production/billing_audit_main.py prediction \
  --input /data/input/features.csv \
  --output /data/output/predictions.csv

# 单独执行判定
docker exec billing-audit-ai python scripts/production/billing_audit_main.py judgment \
  --input /data/output/predictions.csv \
  --output /data/output/judgments.csv
```

### 数据输入/输出

```bash
# 复制数据到容器
docker cp your_local_data.csv billing-audit-ai:/data/input/

# 从容器复制结果
docker cp billing-audit-ai:/data/output/results.csv ./
```

## 🔄 配置管理

### 查看当前配置

```bash
docker exec billing-audit-ai cat config/production_config.json
```

### 修改配置

```bash
# 从容器复制配置文件
docker cp billing-audit-ai:/app/config/production_config.json ./

# 编辑配置文件
vi production_config.json

# 将修改后的配置文件复制回容器
docker cp production_config.json billing-audit-ai:/app/config/

# 重启容器使配置生效
docker restart billing-audit-ai
```

## 🔍 故障排除

### 常见问题

#### 1. 容器无法启动

```bash
# 检查Docker日志
docker logs billing-audit-ai

# 检查系统资源
free -h
df -h
```

#### 2. 模型训练失败

```bash
# 检查训练日志
docker exec billing-audit-ai cat /logs/train_large_scale_model.log

# 检查数据格式
docker exec -it billing-audit-ai python -c "
import pandas as pd
data = pd.read_csv('/data/input/your_data.csv', nrows=5)
print(data.columns)
print(data.dtypes)
"
```

#### 3. 预测结果异常

```bash
# 检查模型文件
docker exec billing-audit-ai ls -la /models/

# 检查预测日志
docker exec billing-audit-ai cat /logs/predict_large_scale.log
```

### 日志查看

```bash
# 查看所有日志文件
docker exec billing-audit-ai ls -la /logs/

# 查看特定日志
docker exec billing-audit-ai cat /logs/billing_audit_main.log
```

## 🔄 更新部署

如需更新到新版本，请按以下步骤操作：

1. 备份当前数据
   ```bash
   docker cp billing-audit-ai:/data/output ./backup_output
   docker cp billing-audit-ai:/models ./backup_models
   ```

2. 停止并移除当前容器
   ```bash
   docker stop billing-audit-ai
   docker rm billing-audit-ai
   ```

3. 导入新版本镜像并启动新容器 (参考前述步骤)

## 📞 支持与帮助

如遇到无法解决的问题，请联系系统管理员或技术支持团队，并提供以下信息：

1. 部署环境详情 (操作系统、Docker版本)
2. 错误日志和截图
3. 重现步骤

---

**文档维护**: 技术团队  
**最后更新**: 2025-07-28  
**状态**: ✅ 正式版
