# 🚀 山西电信出账稽核AI系统 v2.1.0 - 离线部署包

## 📋 概述

本部署包包含山西电信出账稽核AI系统v2.1.0的完整Docker离线部署文件，专为内网环境设计，无需外网连接即可完成部署。

**系统版本**: v2.1.0 (流程修正版)  
**打包时间**: 2025-07-28  
**部署环境**: 内网服务器 (Linux)  
**总大小**: ~540MB (压缩后)  

## 🎯 v2.1.0 新功能特性

### ⭐ 核心增强
- **完整流程自动化**: 原始数据→特征工程→数据拆分→训练→评估→预测→判定
- **配置化字段管理**: 支持动态增减字段，无需修改代码
- **预测结果优化**: 27列标准格式，154,286条/秒处理速度
- **Markdown执行报告**: 详细业务分析和智能评级
- **日志标准化**: 完整的日志记录和错误处理

### 🔧 技术优化
- **流程修正**: 修正了数据处理流程的逻辑错误
- **模块化执行**: 支持任意步骤独立运行
- **配置化参数**: 所有参数通过配置文件管理
- **容错机制**: 智能降级和错误恢复
- **性能监控**: 实时性能指标和资源监控

## 📦 部署包内容

```
docker_export_v2.1.0/
├── 📁 images/                        # Docker镜像文件 (540MB)
│   ├── billing-audit-ai-v2.1.0-*.tar # 主应用镜像 (342MB)
│   ├── python-3.9-slim.tar           # Python基础镜像 (44MB)
│   ├── postgres-13.tar               # PostgreSQL数据库 (142MB)
│   └── redis-6-alpine.tar            # Redis缓存 (12MB)
├── 📁 configs/                       # 配置文件
│   ├── config/production_config.json # 生产环境配置
│   ├── docker/                       # Docker配置
│   └── requirements.txt              # Python依赖
├── 📁 scripts/                       # 部署脚本
│   ├── deploy_production.sh          # 生产环境部署
│   ├── deploy_linux_production.sh    # Linux专用部署
│   └── validate_deployment.sh        # 部署验证
├── 🔧 import_images.sh               # 镜像导入脚本
├── 📖 OFFLINE_DEPLOYMENT_GUIDE.md    # 详细部署指南
└── 📄 README.md                      # 本文档
```

## ⚡ 快速开始

### 1. 系统要求

- **操作系统**: CentOS 7+, Ubuntu 18.04+
- **Docker**: 20.10.0+
- **硬件**: 8核CPU, 16GB内存, 50GB存储

### 2. 一键部署

```bash
# 1. 解压部署包
tar -xzf billing-audit-ai-v2.1.0-offline.tar.gz
cd docker_export_v2.1.0

# 2. 导入镜像
chmod +x import_images.sh
./import_images.sh

# 3. 启动容器 (推荐使用启动脚本)
chmod +x start_container.sh
./start_container.sh

# 或者手动启动
docker run -d \
  --name billing-audit-ai \
  -v ~/billing-audit-ai/data:/data \
  -p 8000:8000 \
  -e DATA_INPUT_DIR=/data/input \
  -e MODEL_DIR=/models \
  -e LOGS_DIR=/logs \
  billing-audit-ai:v2.1.0

# 4. 验证部署
docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help
```

### 3. 使用系统

```bash
# 查看帮助
docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help

# 完整流程执行
docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \
  --input /data/input/your_data.csv \
  --output /data/output/results.csv
```

## 🔧 核心功能

### 生产级主脚本
- **完整流程自动化**: 一键执行所有处理步骤
- **模块化执行**: 支持单独执行任意步骤
- **配置化管理**: 所有参数通过配置文件控制
- **智能报告**: 自动生成Markdown格式的执行报告

### 大规模数据处理
- **千万级数据支持**: 优化的内存管理和分批处理
- **高性能特征工程**: 154,286条/秒的处理速度
- **智能容错**: 自动错误检测和恢复机制
- **实时监控**: 处理进度和性能指标实时显示

### 智能模型系统
- **多算法支持**: RandomForest/XGBoost/LightGBM
- **自动模型选择**: 基于数据特征自动选择最优算法
- **增量训练**: 支持模型的增量更新
- **模型评估**: 全面的模型性能评估和报告

### 收费合理性判定
- **业务规则引擎**: 基于电信业务规则的智能判定
- **多维度分析**: 从多个角度评估收费合理性
- **详细报告**: 生成详细的判定报告和建议
- **可配置规则**: 支持自定义业务规则

## 📊 性能指标

### 处理性能
- **特征工程**: 86,220条/秒
- **模型训练**: 支持千万级数据
- **批量预测**: 80K-120K样本/秒
- **收费判定**: 1.5K-2K样本/秒

### 系统性能
- **内存使用**: <2GB (60万数据)
- **CPU利用率**: 多核并行处理
- **存储需求**: 动态扩展
- **响应时间**: 毫秒级响应

## 🔍 故障排除

### 常见问题

1. **容器无法启动**
   ```bash
   docker logs billing-audit-ai
   ```

2. **内存不足**
   ```bash
   # 调整批处理大小
   docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \
     --input /data/input/data.csv --batch-size 1000
   ```

3. **数据格式错误**
   ```bash
   # 检查数据格式
   docker exec billing-audit-ai python -c "
   import pandas as pd
   data = pd.read_csv('/data/input/data.csv', nrows=5)
   print(data.columns)
   "
   ```

### 日志查看

```bash
# 查看系统日志
docker exec billing-audit-ai ls -la /logs/

# 查看主脚本日志
docker exec billing-audit-ai cat /logs/billing_audit_main.log
```

## 📖 详细文档

- **[离线部署指南](OFFLINE_DEPLOYMENT_GUIDE.md)** - 完整的部署步骤和配置说明
- **[配置文件说明](configs/config/production_config.json)** - 详细的配置参数说明
- **[Docker配置](configs/docker/)** - Docker相关配置文件

## 🔄 版本历史

### v2.1.0 (2025-07-28) - 流程修正版
- ✅ 修正了完整流程的数据流向
- ✅ 实现配置化字段管理
- ✅ 优化预测结果格式
- ✅ 标准化日志记录
- ✅ 增强错误处理机制

### v2.0.0 (2025-07-27) - 千万级数据版
- ✅ 支持千万级数据处理
- ✅ 实现大规模批量处理
- ✅ 优化内存使用效率
- ✅ 增加性能监控

## 📞 技术支持

如遇到问题，请按以下步骤获取支持：

1. **查看日志**: 检查容器和应用日志
2. **参考文档**: 查阅详细部署指南
3. **联系支持**: 提供错误日志和环境信息

---

**开发团队**: 山西电信出账稽核AI项目组  
**技术支持**: 系统管理员  
**文档版本**: v1.0  
**最后更新**: 2025-07-28
