#!/bin/bash

# 山西电信出账稽核AI系统 v2.1.0 - Docker镜像导入脚本
# 用于在内网环境中导入离线Docker镜像

set -e

echo "🚀 山西电信出账稽核AI系统 v2.1.0 - Docker镜像导入脚本"
echo "=================================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker服务是否运行
if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker服务未运行，请启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"
echo ""

# 导入镜像函数
import_image() {
    local image_file=$1
    local image_name=$2
    
    if [ -f "$image_file" ]; then
        echo "📦 导入镜像: $image_name"
        docker load -i "$image_file"
        echo "✅ 导入完成: $image_name"
        echo ""
    else
        echo "⚠️  警告: 镜像文件不存在: $image_file"
    fi
}

# 导入基础镜像
echo "🔧 导入基础镜像..."
import_image "images/python-3.9-slim.tar" "python:3.9-slim"
import_image "images/postgres-13.tar" "postgres:13"
import_image "images/redis-6-alpine.tar" "redis:6-alpine"

# 导入主应用镜像
echo "🎯 导入主应用镜像..."
MAIN_IMAGE=$(ls images/billing-audit-ai-v2.1.0-*.tar 2>/dev/null | head -1)
if [ -n "$MAIN_IMAGE" ]; then
    import_image "$MAIN_IMAGE" "billing-audit-ai:v2.1.0"
else
    echo "❌ 错误: 找不到主应用镜像文件"
    exit 1
fi

# 验证镜像导入
echo "🔍 验证镜像导入..."
echo "已导入的镜像:"
docker images | grep -E "(billing-audit-ai|python|postgres|redis)"

echo ""
echo "🎉 所有镜像导入完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 启动容器: docker run -d --name billing-audit-ai billing-audit-ai:v2.1.0"
echo "2. 进入容器: docker exec -it billing-audit-ai bash"
echo "3. 运行测试: docker exec billing-audit-ai python scripts/testing/end_to_end_test.py"
echo "4. 查看帮助: docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help"
echo ""
echo "📖 详细部署指南请参考: OFFLINE_DEPLOYMENT_GUIDE.md"
