#!/bin/bash

# 山西电信出账稽核AI系统 v2.1.0 - 容器启动脚本
# 用于启动并保持容器运行

set -e

echo "🚀 山西电信出账稽核AI系统 v2.1.0 - 容器启动脚本"
echo "=================================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker服务是否运行
if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker服务未运行，请启动Docker服务"
    exit 1
fi

# 检查镜像是否存在
if ! docker images | grep -q "billing-audit-ai"; then
    echo "❌ 错误: billing-audit-ai镜像不存在，请先导入镜像"
    echo "提示: 执行 ./import_images.sh 导入镜像"
    exit 1
fi

# 创建数据目录
DATA_DIR="$HOME/billing-audit-ai/data"
mkdir -p "$DATA_DIR/input" "$DATA_DIR/output" "$DATA_DIR/models" "$DATA_DIR/logs"
echo "✅ 数据目录已创建: $DATA_DIR"

# 检查容器是否已存在
if docker ps -a | grep -q "billing-audit-ai"; then
    echo "⚠️ 容器 billing-audit-ai 已存在"
    
    # 检查容器是否正在运行
    if docker ps | grep -q "billing-audit-ai"; then
        echo "✅ 容器已在运行中"
    else
        echo "🔄 启动已存在的容器..."
        docker start billing-audit-ai
        echo "✅ 容器已启动"
    fi
else
    echo "🔄 创建并启动新容器..."
    
    # 启动容器
    docker run -d \
        --name billing-audit-ai \
        -v "$DATA_DIR/input:/data/input" \
        -v "$DATA_DIR/output:/data/output" \
        -v "$DATA_DIR/models:/models" \
        -v "$DATA_DIR/logs:/logs" \
        -p 8000:8000 \
        -e DATA_INPUT_DIR=/data/input \
        -e MODEL_DIR=/models \
        -e LOGS_DIR=/logs \
        -e DB_PASSWORD=default_password \
        billing-audit-ai:v2.1.0
    
    echo "✅ 容器已创建并启动"
fi

# 显示容器状态
echo ""
echo "📊 容器状态:"
docker ps | grep billing-audit-ai

echo ""
echo "🎯 使用方法:"
echo "1. 进入容器: docker exec -it billing-audit-ai bash"
echo "2. 执行命令: docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help"
echo "3. 查看日志: docker logs billing-audit-ai"
echo "4. 停止容器: docker stop billing-audit-ai"
echo "5. 删除容器: docker rm billing-audit-ai"

echo ""
echo "📋 示例命令:"
echo "# 执行完整流程"
echo "docker exec billing-audit-ai python scripts/production/billing_audit_main.py full \\"
echo "  --input /data/input/your_data.csv \\"
echo "  --output /data/output/results.csv"
echo ""
echo "# 复制数据到容器"
echo "docker cp your_data.csv billing-audit-ai:/data/input/"
echo ""
echo "# 从容器复制结果"
echo "docker cp billing-audit-ai:/data/output/results.csv ./"

echo ""
echo "🎉 容器已成功启动！"
