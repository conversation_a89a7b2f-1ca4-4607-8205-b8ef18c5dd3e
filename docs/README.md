# 山西电信出账稽核AI系统 - 生产部署文档

## 📋 系统简介

山西电信出账稽核AI系统是一个基于机器学习的智能收费合理性判定系统，专为电信运营商设计。系统通过分析用户的定价特征和历史数据，自动判定收费的合理性。

### 🎯 核心功能
- **智能稽核**: 基于分层建模的收费合理性自动判定
- **高性能处理**: 支持千万级数据批量处理，154,286条/秒
- **分层建模**: 零值识别准确率96.77%，业务准确率94.6%
- **完整流程**: 特征工程 → 数据拆分 → 训练 → 评估 → 预测 → 判定
- **容器化部署**: Docker一键部署，支持数据挂载

### 📊 技术指标
- **处理速度**: 154,286条/秒
- **零值识别准确率**: 96.77%
- **业务准确率**: 94.6%
- **预测结果格式**: 27列完整输出
- **支持数据规模**: 千万级
- **部署方式**: Docker容器化

## 🚀 快速部署

### 1️⃣ 系统要求
- **操作系统**: Linux/macOS/Windows (支持Docker)
- **Docker**: 版本 20.10+
- **内存**: 建议 8GB+
- **存储**: 建议 20GB+ 可用空间
- **CPU**: 建议 4核+

### 2️⃣ 部署步骤

#### **解压部署包**
```bash
tar -xzf billing_audit_production_v2.1.0_20250802_slim.tar.gz
cd billing_audit_production_v2.1.0_20250730_083326
```

#### **准备数据文件**
```bash
# 将您的数据文件放到 data/input 目录
cp /path/to/your/data.csv data/input/
```

#### **一键部署运行**
```bash
# 执行一键部署脚本
bash deploy.sh
```

### 3️⃣ 验证部署
部署成功后，检查以下内容：
- Docker容器正常运行
- 输出目录创建成功
- 日志文件正常生成

## 📖 操作手册

### 🔄 全流程操作

#### **方式1: 一键全流程**
```bash
# 在容器内执行完整流程
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py full \
  --input /app/data/input/your_data.csv \
  --algorithm hierarchical \
  --batch-size 1000
```

#### **方式2: 主机挂载执行**
```bash
# 使用数据挂载方式执行
docker run --rm \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/outputs:/app/outputs \
  billing-audit-ai:v2.1.0-slim-fixed \
  python scripts/production/billing_audit_main.py full \
  --input data/input/your_data.csv \
  --algorithm hierarchical \
  --batch-size 1000
```

### 🔧 单流程操作

#### **1. 特征工程**
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py feature-engineering \
  --input /app/data/input/your_data.csv \
  --batch-size 1000
```

#### **2. 模型训练**
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py training \
  --input /app/data/input/your_data.csv \
  --algorithm hierarchical \
  --batch-size 1000
```

#### **3. 模型预测**
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py prediction \
  --input /app/data/input/your_data.csv \
  --batch-size 1000
```

#### **4. 模型评估**
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py evaluation \
  --input /app/data/input/your_data.csv \
  --batch-size 1000
```

#### **5. 收费判定**
```bash
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py judgment \
  --input /app/data/input/your_data.csv \
  --batch-size 1000 \
  --abs-threshold 50.0 \
  --rel-threshold 0.1
```

## 🔧 运维操作

### 📊 监控检查

#### **容器状态检查**
```bash
# 检查容器运行状态
docker ps | grep billing-audit

# 检查容器健康状态
docker inspect billing-audit-ai | grep Health -A 10

# 查看容器资源使用
docker stats billing-audit-ai
```

#### **日志查看**
```bash
# 查看容器日志
docker logs billing-audit-ai

# 实时查看日志
docker logs -f billing-audit-ai

# 查看应用日志
docker exec -it billing-audit-ai tail -f /app/logs/billing_audit.log
```

#### **数据检查**
```bash
# 检查输入数据
ls -la data/input/

# 检查输出结果
ls -la outputs/

# 检查模型文件
ls -la outputs/models/

# 检查预测结果
head outputs/data/*predictions*.csv
```

### 🔄 运维操作

#### **重启服务**
```bash
# 重启容器
docker restart billing-audit-ai

# 重新部署
bash deploy.sh
```

#### **清理操作**
```bash
# 清理输出文件
rm -rf outputs/*

# 清理日志文件
rm -rf logs/*

# 清理临时文件
docker exec -it billing-audit-ai rm -rf /tmp/billing_audit/*
```

#### **备份操作**
```bash
# 备份模型文件
cp -r outputs/models/ backup/models_$(date +%Y%m%d_%H%M%S)/

# 备份配置文件
cp production_config.json backup/config_$(date +%Y%m%d_%H%M%S).json

# 备份预测结果
cp -r outputs/data/ backup/data_$(date +%Y%m%d_%H%M%S)/
```

## 🚨 故障排除

### 常见问题

#### **1. 容器启动失败**
```bash
# 检查镜像是否存在
docker images | grep billing-audit-ai

# 检查端口占用
netstat -tlnp | grep 8000

# 重新加载镜像
docker load < images/billing-audit-ai-v2.1.0-slim-fixed.tar.gz
```

#### **2. 数据处理失败**
```bash
# 检查数据文件格式
head -5 data/input/your_data.csv

# 检查数据文件权限
ls -la data/input/

# 检查容器内数据挂载
docker exec -it billing-audit-ai ls -la /app/data/input/
```

#### **3. 内存不足**
```bash
# 调整批处理大小
--batch-size 500  # 减少批处理大小

# 检查系统内存
free -h

# 调整Docker内存限制
# 修改 docker-compose.yml 中的 memory 配置
```

#### **4. 预测结果异常**
```bash
# 检查模型文件
ls -la outputs/models/

# 检查特征工程器
ls -la outputs/models/*feature_engineer*.pkl

# 重新训练模型
docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py training \
  --input /app/data/input/your_data.csv --algorithm hierarchical
```

## 📞 技术支持

### 联系方式
- **技术文档**: 参考本文档
- **日志分析**: 查看 `/app/logs/` 目录下的日志文件
- **配置文件**: `production_config.json`

### 重要文件位置
- **主脚本**: `scripts/production/billing_audit_main.py`
- **配置文件**: `production_config.json`
- **部署脚本**: `deploy.sh`
- **输出目录**: `outputs/`
- **日志目录**: `logs/`
