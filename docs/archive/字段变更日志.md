# 字段变更记录

## 📋 变更概述

**变更日期**: 2025-07-22  
**变更类型**: 固费收入稽核预测字段调整  
**影响模块**: billing_audit.fixed_fee  
**变更原因**: 业务需求调整，日期字段拆分为年月日独立字段

## 🔄 字段变更详情

### ✅ 新增字段

| 字段名称 | 字段描述 | 字段类型 | 字段说明 | 备注 |
|---------|---------|---------|---------|------|
| `final_eff_year` | 最终生效年 | 数值 | yyyy格式，订购的多个时间取交集 | 替代原final_eff_date |
| `final_eff_mon` | 最终生效月 | 数值 | mm格式 | 替代原final_eff_date |
| `final_eff_day` | 最终生效日 | 数值 | dd格式 | 替代原final_eff_date |
| `final_exp_year` | 最终失效年 | 数值 | yyyy格式，订购的多个时间取交集 | 替代原final_exp_date |
| `final_exp_mon` | 最终失效月 | 数值 | mm格式 | 替代原final_exp_date |
| `final_exp_day` | 最终失效日 | 数值 | dd格式 | 替代原final_exp_date |

### ❌ 移除字段

| 字段名称 | 移除原因 | 替代方案 |
|---------|---------|---------|
| `final_eff_date` | 拆分为年月日独立字段 | `final_eff_year` + `final_eff_mon` + `final_eff_day` |
| `final_exp_date` | 拆分为年月日独立字段 | `final_exp_year` + `final_exp_mon` + `final_exp_day` |

### 🔄 字段描述更新

| 字段名称 | 原描述 | 新描述 | 变更说明 |
|---------|-------|-------|---------|
| `cal_type` | 就用计算类型 | 费用计算类型 | 描述更准确 |
| `rate_unit` | 周期数量 | 周期数量 (0-非多周期, >1为周期数量) | 增加详细说明 |
| `charge_day_count` | 计费天数 | 计费天数 (综合考虑生效和状态变化) | 增加业务逻辑说明 |
| `should_fee` | 应收费 | 应收费 (如果是协议价，从协议价获取) | 增加协议价说明 |
| `busi_flag` | 业务规则确定不收费标识 | 根据业务规则确定不收费标识 (0-正常收费, 1-不收费) | 增加枚举值说明 |

### 🔄 透传字段更新

**移除的透传字段**:
- `user_id` - 用户标识
- `phone_no` - 用户号码  
- `strategy_id` - 策略标识
- `life_cycle_id` - 生命周期
- `region_str` - 地市

**新增的透传字段**:
- `event_pricing_strategy_id` - 策略标识 (根据策略条件匹配到的策略)
- `event_type_id` - 事件类型
- `calc_priority` - 策略优先级
- `pricing_section_id` - 段落标识 (根据段落条件匹配到的段落)
- `calc_method_id` - 计算标识
- `role_id` - 角色标识

## 📊 数据类型分类变更

### 数值字段 (numerical_columns)
**新增**:
- `final_eff_year`, `final_eff_mon`, `final_eff_day`
- `final_exp_year`, `final_exp_mon`, `final_exp_day`

### 日期字段 (date_columns)  
**移除**:
- `final_eff_date`, `final_exp_date`

**保留**:
- `cur_year_month`, `run_time`

### 类别字段 (categorical_columns)
**无变更**: 保持原有5个字段不变

## 🎯 业务规则补充

### 新增业务规则说明

1. **171事件类型处理**:
   - 如果用户订购多个171事件，只收取优先级最高的订购
   - 其他订购不收费

2. **精品班成员处理**:
   - 如果用户是精品班成员，则不收取固费

3. **协议价处理**:
   - 如果是协议价，从协议价获取应收费用

## 🔧 配置文件影响

### 更新的配置文件
- `config/billing_audit_config.json` - 主配置文件

### 更新的文档
- `docs/技术规格文档.md` - 技术规格文档
- `docs/数据分析报告.md` - 数据分析报告
- `docs/字段变更日志.md` - 字段变更记录 (新增)

## ✅ 验证结果

```
✅ 配置文件JSON格式正确
✅ 固费特征字段数量: 16 (原12个 + 新增6个 - 移除2个)
✅ 固费透传字段数量: 11 (原10个 + 新增6个 - 移除5个)
✅ 固费类别字段数量: 5 (无变更)
✅ 固费数值字段数量: 9 (原3个 + 新增6个)
✅ 固费日期字段数量: 2 (原4个 - 移除2个)
✅ 所有新字段已正确添加
✅ 所有旧字段已正确移除
```

## 🚨 注意事项

### 数据兼容性
1. **现有数据**: 需要将现有的 `final_eff_date` 和 `final_exp_date` 字段拆分为年月日字段
2. **数据预处理**: 需要更新数据预处理逻辑以处理新的字段结构
3. **特征工程**: 可能需要调整特征工程逻辑，考虑年月日字段的组合使用

### 模型重训练
1. **必须重训练**: 由于特征字段发生变化，现有模型无法直接使用
2. **特征数量**: 从12个特征增加到16个特征，模型复杂度可能增加
3. **性能评估**: 需要重新评估模型性能指标

### 代码更新
1. **数据加载**: 需要更新数据加载逻辑以处理新字段
2. **验证逻辑**: 需要更新数据验证逻辑
3. **API接口**: 需要更新API接口的输入参数说明

## 📝 后续行动项

### 立即执行
- [x] 更新配置文件
- [x] 更新技术文档
- [x] 验证配置正确性

### 待执行
- [ ] 更新数据预处理代码
- [ ] 更新数据验证逻辑
- [ ] 准备新格式的训练数据
- [ ] 重新训练模型
- [ ] 更新API接口文档
- [ ] 执行端到端测试

---

**变更记录版本**: v1.0.0  
**记录人**: 山西电信AI团队  
**审核状态**: 待审核
