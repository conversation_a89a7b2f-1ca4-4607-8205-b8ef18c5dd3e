# 📚 山西电信出账稽核AI系统 - 文档中心

欢迎来到山西电信出账稽核AI系统文档中心！这里包含了系统的完整文档和使用指南。

## 🚀 **最新版本**: v2.1.0 (流程修正版)
- ✅ **生产级主脚本**: 完整的端到端自动化流程
- ✅ **Markdown执行报告**: 详细的业务分析和智能评级
- ✅ **流程修正**: 正确的数据流向
- ✅ **日志标准化**: 175个问题点全部修复

## 📚 文档导航

### 🚀 快速开始
- **[项目总览 (README.md)](../README.md)** - 项目介绍、安装和基本使用
- **[快速使用指南](../guides/快速开始指南.md)** - 5分钟快速上手指南
- **[生产环境主脚本使用指南](../guides/生产环境主脚本使用指南.md)** ⭐ **推荐** - 生产级主脚本完整使用指南

### 🔧 开发文档
- **[技术规格文档](技术规格文档.md)** - 完整技术规格与操作流程
- **[API使用指南](../guides/API使用指南.md)** - API接口详细使用说明
- **[大规模端到端指南](../guides/大规模端到端指南.md)** - 千万级数据处理指南
- **[生产脚本指南](../guides/生产脚本指南.md)** - 生产环境脚本使用指南

### 📊 系统信息
- **[配置说明](../config/)** - 系统配置文件说明
- **[数据格式](../数据/)** - 输入数据格式和样例
- **[模型信息](../models/)** - 训练好的模型文件

### 🔧 技术文档
- **[主脚本流程修正说明](../technical/主脚本流程修正说明.md)** - 流程修正详细说明
- **[生产脚本日志修复报告](../technical/生产脚本日志修复报告.md)** - 日志标准化修复报告
- **[大规模数据处理指南](../technical/大规模数据处理指南.md)** - 千万级数据处理技术指南
- **[生产环境部署指南](../technical/生产环境部署指南.md)** - 完整的生产部署方案

## 🎯 按用户角色分类

### 👨‍💼 业务用户
如果您是业务用户，想要了解系统功能和使用方法：

1. **[项目总览](../README.md)** - 了解系统能做什么
2. **[快速使用指南](快速开始指南.md)** - 学习基本操作
3. **[API使用指南](API使用指南.md)** - 了解接口调用方法

### 👨‍💻 开发人员
如果您是开发人员，想要了解代码实现和扩展系统：

1. **[技术规格文档](技术规格文档.md)** - 完整技术规格和操作流程
2. **[代码功能说明](代码文档.md)** - 深入了解代码架构
3. **[快速使用指南](快速开始指南.md)** - 环境搭建和开发环境
4. **[API使用指南](API使用指南.md)** - API开发和集成

### 🔧 运维人员
如果您负责系统部署和运维：

1. **[技术规格文档](技术规格文档.md)** - 完整的部署和操作流程
2. **[项目总览](../README.md)** - 了解系统架构和部署要求
3. **[快速使用指南](快速开始指南.md)** - 环境配置和故障排除
4. **[代码功能说明](代码文档.md)** - 监控和性能优化

## 📋 文档详情

### 1. 项目总览 (README.md)
- **内容**: 项目介绍、功能特性、安装指南、基本使用
- **适合**: 所有用户
- **阅读时间**: 10-15分钟

### 2. 快速使用指南 (快速开始指南.md)
- **内容**: 5分钟快速上手、基础示例、常见问题
- **适合**: 新用户、快速验证
- **阅读时间**: 5-10分钟

### 3. 代码功能说明 (代码文档.md)
- **内容**: 详细代码架构、API设计、性能优化
- **适合**: 开发人员、系统集成
- **阅读时间**: 30-45分钟

### 4. API使用指南 (API使用指南.md)
- **内容**: 完整API接口文档、使用示例、错误处理
- **适合**: API集成、前端开发
- **阅读时间**: 20-30分钟

### 5. 技术规格文档 (技术规格文档.md)
- **内容**: 完整技术规格、数据格式、操作流程、脚本说明
- **适合**: 开发人员、运维人员、系统集成
- **阅读时间**: 45-60分钟

## 🔍 快速查找

### 常见问题
- **安装问题** → [README.md](../README.md#故障排除) 或 [快速开始指南.md](快速开始指南.md#故障排除)
- **使用示例** → [快速开始指南.md](快速开始指南.md#基础使用示例)
- **API调用** → [API使用指南.md](API使用指南.md#使用示例)
- **代码理解** → [代码文档.md](代码文档.md#核心模块架构)

### 功能查找
- **数据预处理** → [代码文档.md](代码文档.md#数据预处理模块)
- **模型训练** → [代码文档.md](代码文档.md#模型训练模块)
- **收费预测** → [API使用指南.md](API使用指南.md#收费预测接口)
- **合理性判定** → [API使用指南.md](API使用指南.md#收费判定接口)

### 配置相关
- **环境配置** → [快速开始指南.md](快速开始指南.md#环境准备)
- **参数调整** → [快速开始指南.md](快速开始指南.md#常用配置)
- **性能优化** → [代码文档.md](代码文档.md#性能优化)

## 📈 学习路径

### 新手入门路径
1. 阅读 [项目总览](../README.md) 了解系统概况
2. 按照 [快速使用指南](快速开始指南.md) 搭建环境
3. 运行示例验证系统功能
4. 根据需要查阅 [API使用指南](API使用指南.md)

### 开发者进阶路径
1. 完成新手入门路径
2. 深入阅读 [代码功能说明](代码文档.md)
3. 理解系统架构和设计模式
4. 尝试扩展和定制功能

### 集成部署路径
1. 了解系统架构和依赖
2. 配置生产环境
3. 集成API接口
4. 设置监控和日志

## 🛠️ 文档维护

### 文档更新
- **版本**: 所有文档都标注了版本号
- **更新频率**: 随系统版本同步更新
- **变更记录**: 重要变更会在文档中标注

### 反馈和改进
- **问题反馈**: 发现文档问题请提交Issue
- **改进建议**: 欢迎提出文档改进建议
- **贡献文档**: 欢迎提交文档改进的Pull Request

## 📞 获取帮助

### 技术支持
- **文档问题**: 查看相关文档或提交Issue
- **使用问题**: 参考故障排除章节
- **开发问题**: 查看代码文档或联系开发团队

### 联系方式
- **项目维护者**: 山西电信AI团队
- **技术支持**: [技术支持邮箱]
- **项目仓库**: [项目链接]

---

**文档中心版本**: v1.1.0
**最后更新**: 2025-07-22
**维护者**: 山西电信AI团队

## 📋 文档清单

- ✅ [README.md](../README.md) - 项目总览
- ✅ [快速开始指南.md](快速开始指南.md) - 快速使用指南
- ✅ [代码文档.md](代码文档.md) - 代码功能说明
- ✅ [API使用指南.md](API使用指南.md) - API使用指南
- ✅ [技术规格文档.md](技术规格文档.md) - 技术规格文档
- ✅ [字段变更日志.md](字段变更日志.md) - 字段变更记录
- ✅ [docs/README.md](README.md) - 文档中心 (本文档)

**文档完整性**: 100% ✅
