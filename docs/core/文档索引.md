# 📚 山西电信出账稽核AI系统 - 文档索引

## 🚀 **v2.1.0 (流程修正版)** - 最新文档导航

### ⭐ **推荐入口**
- **[生产环境主脚本使用指南](../guides/生产环境主脚本使用指南.md)** - 🎯 **最佳入口** - 完整的生产级使用指南
- **[模型性能优化方案](../technical/模型性能优化方案_v2.1.0.md)** - 🚀 **v2.1.0 重磅** - 全面模型优化策略
- **[问题修复和功能增强报告](../technical/问题修复和功能增强报告.md)** - 🔧 **v2.1.0 最新** - 三大问题修复报告
- **[配置化字段管理文档](../technical/字段映射和配置重构文档.md)** - ⚙️ **v2.1.0 新增** - 动态字段管理指南
- **[项目总览](../README.md)** - 系统介绍和核心功能概述

## 🎯 按需求快速查找

### 我想了解系统功能
- **[项目总览](../README.md)** - 系统介绍和核心功能
- **[快速使用指南](../guides/快速开始指南.md)** - 5分钟快速上手
- **[主脚本流程修正说明](../technical/主脚本流程修正说明.md)** - 最新流程改进

### 我想使用系统 (生产环境)
- **[生产环境主脚本使用指南](../guides/生产环境主脚本使用指南.md)** ⭐ **推荐** - 支持全流程+单环节运行
- **[大规模端到端指南](../guides/大规模端到端指南.md)** - 千万级数据处理
- **[生产脚本指南](../guides/生产脚本指南.md)** - 各脚本详细使用方法
- **[技术规格文档](技术规格文档.md)** - 完整操作流程和脚本说明

### 我想开发扩展
- **[技术规格文档](技术规格文档.md)** - 数据格式和接口规格
- **[API使用指南](../guides/API使用指南.md)** - API接口调用方法
- **[大规模数据处理指南](../technical/大规模数据处理指南.md)** - 技术实现细节

### 我想部署运维
- **[生产环境部署指南](../technical/生产环境部署指南.md)** - 完整部署方案
- **[x86_64架构构建指南](../guides/x86_64架构构建指南.md)** - 🔧 **v2.1.0 新增** - 跨架构构建和部署
- **[生产脚本日志修复报告](../technical/生产脚本日志修复报告.md)** - 日志标准化
- **[技术规格文档](技术规格文档.md)** - 部署流程和故障排除

## 🔍 按内容类型查找

### 📋 规格文档
| 文档 | 内容 | 适用人群 |
|-----|------|---------|
| [技术规格文档](技术规格文档.md) | 数据格式、API接口、操作流程 | 开发、运维、集成 |
| [API使用指南](API使用指南.md) | API接口详细说明 | 前端、集成开发 |

### 🚀 使用指南
| 文档 | 内容 | 适用人群 |
|-----|------|---------|
| [快速使用指南](快速开始指南.md) | 快速上手和基础示例 | 新用户、测试 |
| [项目总览](../README.md) | 安装部署和基本使用 | 所有用户 |
| [千万级数据处理指南](大规模数据处理指南.md) | 大规模数据端到端处理 | 生产环境用户 ⭐ |
| [生产脚本使用指南](生产脚本指南.md) | 生产环境脚本详解 | 运维人员 |
| [端到端测试报告](端到端测试报告.md) | 千万级系统测试验证 | 技术团队 ⭐ |
| [收费合理性判定适配报告](收费判定适配报告.md) | 千万级数据判定功能适配 | 技术团队 ⭐ |
| [生产环境部署指南](生产环境部署指南.md) | 配置化部署和容器化 | 技术团队 ⭐ |
| [生产环境训练脚本选择指南](../guides/生产环境训练脚本选择指南.md) | 训练脚本选择和使用建议 | 开发、运维 |
| [生产环境主脚本使用指南](../guides/生产环境主脚本使用指南.md) | 主脚本完整使用指南 | 开发、运维 ⭐ |
| [文件管理规范](../guides/文件管理规范.md) | 项目文件组织规范、归档策略、命名规范 | 开发、运维 ⭐ 新增 |

### 🔧 开发文档
| 文档 | 内容 | 适用人群 |
|-----|------|---------|
| [模型性能优化方案](../technical/模型性能优化方案_v2.1.0.md) | 全面模型优化策略和实施方案 | 技术团队 ⭐ 重磅 |
| [问题修复和功能增强报告](../technical/问题修复和功能增强报告.md) | 三大问题修复和功能增强详解 | 技术团队 ⭐ 最新 |
| [字段映射和配置重构文档](../technical/字段映射和配置重构文档.md) | 配置化字段管理完整说明 | 技术团队 ⭐ 最新 |
| [真实生产数据端到端测试报告](../technical/真实生产数据端到端测试报告_v2.1.0.md) | 真实数据完整测试验证 | 技术团队 ⭐ 最新 |
| [特征工程处理分析报告](../technical/特征工程处理分析报告.md) | 特征工程详细处理分析 | 技术团队 ⭐ 最新 |
| [主脚本流程修正说明](../technical/主脚本流程修正说明.md) | 流程修正详细说明 | 技术团队 ⭐ 新增 |
| [生产脚本日志修复报告](../technical/生产脚本日志修复报告.md) | 日志标准化修复报告 | 技术团队 ⭐ 新增 |
| [代码功能说明](../archive/代码文档.md) | 代码架构和模块设计 | 开发人员 |
| [模型训练总结](../reports/模型训练总结.md) | 模型重训练和算法对比完整总结 | 技术团队 ⭐ |
| [代码分离总结](../technical/代码分离总结.md) | 新旧代码分离和架构重构 | 技术团队 ⭐ |
| [配置管理改进](../technical/配置管理改进.md) | 生产级配置管理和环境变量支持 | 技术团队 ⭐ |
| [配置管理端到端测试](../technical/配置管理端到端测试报告.md) | 配置管理改进功能验证报告 | 技术团队 ⭐ |
| [文档中文化总结](../technical/文档中文化总结.md) | 文档重命名和中文化过程记录 | 技术团队 ⭐ |
| [端到端测试总结报告](../technical/端到端测试总结报告.md) | 系统端到端测试完整报告 | 技术团队 ⭐ |
| [完整端到端测试报告](../technical/完整端到端测试报告.md) | 修复numpy后的完整测试报告 | 技术团队 ⭐ |
| [算法支持与训练策略分析报告](../technical/算法支持与训练策略分析报告.md) | 详细的算法支持分析和训练策略 | 技术团队 ⭐ |
| [LightGBM状态异常技术分析](../technical/LightGBM状态异常技术分析.md) | LightGBM状态异常深度技术分析 | 技术团队 ⭐ 最新 |
| [预测数据处理不一致问题综合技术分析报告](../technical/预测数据处理不一致问题综合技术分析报告.md) | 预测不一致问题完整技术分析 | 技术团队 ⭐ 最新 |
| [最终技术解决方案总结](../technical/最终技术解决方案总结.md) | 完整技术解决方案和实施指南 | 技术团队 ⭐ 最新 |
| [技术规格文档](技术规格文档.md) | 脚本说明和依赖关系 | 开发、运维 |

## 📊 按功能模块查找

### 数据处理
- **数据格式规格** → [技术规格文档 - 数据输入规格](技术规格文档.md#1-数据输入规格)
- **预处理流程** → [代码功能说明 - 数据预处理模块](CODE_DOCUMENTATION.md#数据预处理模块)
- **千万级数据处理** → [千万级数据处理指南](大规模数据处理指南.md) ⭐ 新增

### 模型训练
- **训练流程** → [技术规格文档 - 模型训练阶段](技术规格文档.md#43-模型训练阶段)
- **训练脚本** → [技术规格文档 - 关键脚本说明](技术规格文档.md#5-关键脚本说明)

### 预测判定
- **API调用** → [技术规格文档 - API调用接口](技术规格文档.md#3-api调用接口)
- **输出格式** → [技术规格文档 - 系统输出规格](技术规格文档.md#2-系统输出规格)

### 系统运维
- **部署流程** → [技术规格文档 - 完整工作流程](技术规格文档.md#4-完整工作流程脚本执行顺序)
- **故障排除** → [技术规格文档 - 故障排除指南](技术规格文档.md#7-故障排除指南)

## 🎓 学习路径推荐

### 新手用户 (0-30分钟)
1. [项目总览](../README.md) (10分钟)
2. [快速使用指南](快速开始指南.md) (15分钟)
3. 运行端到端测试验证 (5分钟)

### 开发集成 (1-2小时)
1. 完成新手路径
2. [技术规格文档](技术规格文档.md) (45分钟)
3. [API使用指南](API使用指南.md) (30分钟)
4. [代码功能说明](CODE_DOCUMENTATION.md) (30分钟)

### 深度开发 (2-4小时)
1. 完成开发集成路径
2. 深入研读代码实现
3. 尝试扩展功能模块
4. 贡献代码和文档

## 📞 获取帮助

### 常见问题
- **环境问题** → [项目总览 - 故障排除](../README.md#故障排除)
- **使用问题** → [快速使用指南 - 故障排除](快速开始指南.md#故障排除)
- **API问题** → [API使用指南 - 错误处理](API使用指南.md#错误处理)
- **部署问题** → [技术规格文档 - 故障排除指南](技术规格文档.md#7-故障排除指南)

### 联系方式
- **技术支持**: 山西电信AI团队
- **文档问题**: 提交Issue或Pull Request
- **功能建议**: 项目仓库讨论区

---

**索引版本**: v1.0.0  
**创建日期**: 2025-07-22  
**维护者**: 山西电信AI团队
