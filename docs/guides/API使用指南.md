# API使用说明

## 📋 概述

本文档详细说明了山西电信出账稽核AI系统的API接口使用方法，包括接口定义、请求格式、响应格式和使用示例。

## 🌐 API基础信息

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: API Key (Header: `X-API-Key`)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔧 核心API接口

### 1. 收费预测接口

#### 1.1 单条预测

**接口地址**: `POST /api/v1/predict`

**请求参数**:
```json
{
    "fee_type": "fixed_fee",
    "billing_data": {
        "user_id": "12345",
        "final_eff_date": "2024-01-01",
        "final_exp_date": "2024-01-31",
        "should_fee": 100.0,
        "busi_flag": 1,
        "unit_type": 2,
        "charge_day_count": 31
    }
}
```

**响应格式**:
```json
{
    "success": true,
    "code": 200,
    "message": "预测成功",
    "data": {
        "predicted_amount": 106.17,
        "confidence_score": 0.95,
        "processing_time_ms": 45,
        "model_version": "v1.0.0",
        "feature_count": 37
    },
    "timestamp": "2025-07-16T15:30:00Z"
}
```

#### 1.2 批量预测

**接口地址**: `POST /api/v1/predict/batch`

**请求参数**:
```json
{
    "fee_type": "fixed_fee",
    "billing_data": [
        {
            "user_id": "12345",
            "final_eff_date": "2024-01-01",
            "final_exp_date": "2024-01-31",
            "should_fee": 100.0,
            "busi_flag": 1,
            "unit_type": 2,
            "charge_day_count": 31
        },
        {
            "user_id": "12346",
            "final_eff_date": "2024-01-01",
            "final_exp_date": "2024-01-31",
            "should_fee": 200.0,
            "busi_flag": 2,
            "unit_type": 1,
            "charge_day_count": 31
        }
    ]
}
```

**响应格式**:
```json
{
    "success": true,
    "code": 200,
    "message": "批量预测成功",
    "data": {
        "total_records": 2,
        "predictions": [
            {
                "user_id": "12345",
                "predicted_amount": 106.17,
                "confidence_score": 0.95
            },
            {
                "user_id": "12346",
                "predicted_amount": 205.32,
                "confidence_score": 0.92
            }
        ],
        "processing_time_ms": 78,
        "average_confidence": 0.935
    },
    "timestamp": "2025-07-16T15:30:00Z"
}
```

### 2. 收费判定接口

#### 2.1 单条判定

**接口地址**: `POST /api/v1/judge`

**请求参数**:
```json
{
    "fee_type": "fixed_fee",
    "billing_data": {
        "user_id": "12345",
        "final_eff_date": "2024-01-01",
        "final_exp_date": "2024-01-31",
        "should_fee": 100.0,
        "busi_flag": 1,
        "unit_type": 2,
        "charge_day_count": 31
    },
    "actual_amount": 95.0
}
```

**响应格式**:
```json
{
    "success": true,
    "code": 200,
    "message": "判定成功",
    "data": {
        "judgment": "reasonable",
        "confidence_score": 0.85,
        "predicted_amount": 106.17,
        "actual_amount": 95.0,
        "absolute_error": 11.17,
        "relative_error": 0.1175,
        "threshold_used": {
            "absolute_threshold": 50.0,
            "relative_threshold": 0.1,
            "method": "hybrid"
        },
        "reason": "预测误差在可接受范围内"
    },
    "timestamp": "2025-07-16T15:30:00Z"
}
```

#### 2.2 批量判定

**接口地址**: `POST /api/v1/judge/batch`

**请求参数**:
```json
{
    "fee_type": "fixed_fee",
    "records": [
        {
            "billing_data": {
                "user_id": "12345",
                "final_eff_date": "2024-01-01",
                "final_exp_date": "2024-01-31",
                "should_fee": 100.0,
                "busi_flag": 1,
                "unit_type": 2,
                "charge_day_count": 31
            },
            "actual_amount": 95.0
        },
        {
            "billing_data": {
                "user_id": "12346",
                "final_eff_date": "2024-01-01",
                "final_exp_date": "2024-01-31",
                "should_fee": 200.0,
                "busi_flag": 2,
                "unit_type": 1,
                "charge_day_count": 31
            },
            "actual_amount": 250.0
        }
    ]
}
```

**响应格式**:
```json
{
    "success": true,
    "code": 200,
    "message": "批量判定成功",
    "data": {
        "total_records": 2,
        "results": [
            {
                "user_id": "12345",
                "judgment": "reasonable",
                "confidence_score": 0.85,
                "predicted_amount": 106.17,
                "actual_amount": 95.0,
                "relative_error": 0.1175
            },
            {
                "user_id": "12346",
                "judgment": "unreasonable",
                "confidence_score": 0.92,
                "predicted_amount": 205.32,
                "actual_amount": 250.0,
                "relative_error": 0.2177
            }
        ],
        "summary": {
            "reasonable_count": 1,
            "unreasonable_count": 1,
            "uncertain_count": 0,
            "reasonable_rate": 0.5
        },
        "processing_time_ms": 156
    },
    "timestamp": "2025-07-16T15:30:00Z"
}
```

### 3. 模型信息接口

#### 3.1 获取模型状态

**接口地址**: `GET /api/v1/model/status`

**响应格式**:
```json
{
    "success": true,
    "code": 200,
    "message": "获取成功",
    "data": {
        "models": {
            "fixed_fee": {
                "status": "active",
                "version": "v1.0.0",
                "last_trained": "2025-07-16T10:30:00Z",
                "performance": {
                    "mae": 28.49,
                    "r2": 0.9778,
                    "business_accuracy": 0.5385
                },
                "feature_count": 37
            }
        },
        "system_status": "healthy",
        "uptime_seconds": 86400
    },
    "timestamp": "2025-07-16T15:30:00Z"
}
```

#### 3.2 获取模型性能

**接口地址**: `GET /api/v1/model/{fee_type}/performance`

**响应格式**:
```json
{
    "success": true,
    "code": 200,
    "message": "获取成功",
    "data": {
        "fee_type": "fixed_fee",
        "performance_metrics": {
            "regression_metrics": {
                "mae": 28.49,
                "rmse": 34.57,
                "r2": 0.9778,
                "mape": 103.71
            },
            "business_metrics": {
                "accuracy_rate": 53.85,
                "total_samples": 52,
                "accurate_samples": 28
            },
            "overall_score": 65.30
        },
        "last_evaluation": "2025-07-16T10:30:00Z"
    },
    "timestamp": "2025-07-16T15:30:00Z"
}
```

## 🔧 使用示例

### Python示例

```python
import requests
import json

# API配置
BASE_URL = "http://localhost:8000/api/v1"
API_KEY = "your-api-key"
HEADERS = {
    "Content-Type": "application/json",
    "X-API-Key": API_KEY
}

# 单条预测示例
def predict_single(billing_data):
    url = f"{BASE_URL}/predict"
    payload = {
        "fee_type": "fixed_fee",
        "billing_data": billing_data
    }
    
    response = requests.post(url, json=payload, headers=HEADERS)
    return response.json()

# 使用示例
billing_data = {
    "user_id": "12345",
    "final_eff_date": "2024-01-01",
    "final_exp_date": "2024-01-31",
    "should_fee": 100.0,
    "busi_flag": 1,
    "unit_type": 2,
    "charge_day_count": 31
}

result = predict_single(billing_data)
print(f"预测金额: {result['data']['predicted_amount']}")
```

### JavaScript示例

```javascript
// API配置
const BASE_URL = "http://localhost:8000/api/v1";
const API_KEY = "your-api-key";

// 收费判定示例
async function judgeBilling(billingData, actualAmount) {
    const response = await fetch(`${BASE_URL}/judge`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-API-Key': API_KEY
        },
        body: JSON.stringify({
            fee_type: 'fixed_fee',
            billing_data: billingData,
            actual_amount: actualAmount
        })
    });
    
    return await response.json();
}

// 使用示例
const billingData = {
    user_id: "12345",
    final_eff_date: "2024-01-01",
    final_exp_date: "2024-01-31",
    should_fee: 100.0,
    busi_flag: 1,
    unit_type: 2,
    charge_day_count: 31
};

judgeBilling(billingData, 95.0)
    .then(result => {
        console.log(`判定结果: ${result.data.judgment}`);
        console.log(`置信度: ${result.data.confidence_score}`);
    });
```

### cURL示例

```bash
# 单条预测
curl -X POST "http://localhost:8000/api/v1/predict" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "fee_type": "fixed_fee",
    "billing_data": {
      "user_id": "12345",
      "final_eff_date": "2024-01-01",
      "final_exp_date": "2024-01-31",
      "should_fee": 100.0,
      "busi_flag": 1,
      "unit_type": 2,
      "charge_day_count": 31
    }
  }'

# 收费判定
curl -X POST "http://localhost:8000/api/v1/judge" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "fee_type": "fixed_fee",
    "billing_data": {
      "user_id": "12345",
      "final_eff_date": "2024-01-01",
      "final_exp_date": "2024-01-31",
      "should_fee": 100.0,
      "busi_flag": 1,
      "unit_type": 2,
      "charge_day_count": 31
    },
    "actual_amount": 95.0
  }'
```

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求格式和必需字段 |
| 401 | 认证失败 | 检查API Key是否正确 |
| 404 | 接口不存在 | 检查URL路径 |
| 422 | 数据验证失败 | 检查数据格式和类型 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误响应格式

```json
{
    "success": false,
    "code": 400,
    "message": "请求参数错误",
    "error": {
        "type": "ValidationError",
        "details": "字段 'should_fee' 不能为空",
        "field": "should_fee"
    },
    "timestamp": "2025-07-16T15:30:00Z"
}
```

## 📊 性能和限制

### API限制

- **请求频率**: 1000次/分钟
- **批量大小**: 最大100条记录
- **超时时间**: 30秒
- **文件大小**: 最大10MB

### 性能指标

- **平均响应时间**: < 100ms (单条)
- **批量处理**: < 5ms/条
- **并发支持**: 100个并发连接
- **可用性**: 99.9%

## 🔒 安全说明

### API Key管理

```python
# 环境变量方式
import os
API_KEY = os.getenv('BILLING_AUDIT_API_KEY')

# 配置文件方式
import json
with open('config.json') as f:
    config = json.load(f)
    API_KEY = config['api_key']
```

### 数据安全

- **传输加密**: 支持HTTPS
- **数据脱敏**: 敏感字段自动脱敏
- **访问日志**: 完整的API访问日志
- **权限控制**: 基于角色的访问控制

## 📈 监控和调试

### 请求日志

```json
{
    "request_id": "req_123456789",
    "timestamp": "2025-07-16T15:30:00Z",
    "method": "POST",
    "endpoint": "/api/v1/predict",
    "user_id": "api_user_001",
    "processing_time_ms": 45,
    "status_code": 200
}
```

### 调试模式

```python
# 启用调试模式
headers = {
    "Content-Type": "application/json",
    "X-API-Key": API_KEY,
    "X-Debug": "true"  # 启用调试信息
}
```

---

**API文档版本**: v1.0.0  
**最后更新**: 2025-07-16  
**维护者**: 山西电信AI团队
