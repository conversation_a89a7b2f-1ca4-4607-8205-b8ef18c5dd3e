# 山西电信出账稽核AI系统 v2.1.0 - x86_64架构构建指南

## 📋 概述

本指南介绍如何在ARM64系统（如Apple Silicon Mac）上构建适用于x86_64 Linux主机的Docker镜像和部署包。

### 🎯 使用场景
- **开发环境**: ARM64 (Apple Silicon Mac, ARM64 Linux)
- **生产环境**: x86_64 Linux主机（无外网连接）
- **部署需求**: 跨架构构建 + 离线部署

## 🚀 快速开始

### 1. 一键完整构建（推荐）
```bash
# 完整构建：镜像构建 + 部署包制作
bash deployment/scripts/build_x86_64_complete.sh
```

### 2. 分步构建
```bash
# 仅构建x86_64镜像
bash deployment/scripts/build_x86_64_complete.sh --build-only

# 仅制作部署包（需要已存在x86_64镜像）
bash deployment/scripts/build_x86_64_complete.sh --package-only
```

### 3. 环境清理
```bash
# 清理所有x86_64相关镜像和文件
bash deployment/scripts/build_x86_64_complete.sh --clean
```

## 📦 构建脚本说明

### 主控制脚本
- **文件**: `deployment/scripts/build_x86_64_complete.sh`
- **功能**: 一键完成整个x86_64构建流程
- **特性**: 
  - 自动环境检查
  - 跨架构构建支持
  - 智能错误处理
  - 详细进度显示

### 镜像构建脚本
- **文件**: `deployment/scripts/build_x86_64_image.sh`
- **功能**: 专门构建x86_64架构Docker镜像
- **特性**:
  - 使用Docker buildx多架构构建
  - 自动创建和管理构建器
  - 镜像架构验证
  - 功能测试验证

### 部署包制作脚本
- **文件**: `deployment/scripts/create_x86_64_deployment.sh`
- **功能**: 制作x86_64架构离线部署包
- **特性**:
  - 精简部署包结构
  - 智能挂载运行脚本
  - 一键部署脚本
  - 完整文档支持

## 🔧 系统要求

### 构建环境（ARM64）
- **操作系统**: macOS (Apple Silicon) 或 ARM64 Linux
- **Docker**: 20.10+ (支持buildx)
- **内存**: 8GB+
- **磁盘**: 20GB+

### 目标环境（x86_64）
- **操作系统**: Linux x86_64
- **Docker**: 20.10+
- **内存**: 8GB+
- **磁盘**: 10GB+
- **网络**: 无需外网连接

## 📊 构建流程详解

### 1. 环境检查阶段
```bash
✅ 检查当前系统架构
✅ 验证Docker环境
✅ 确认buildx可用性
✅ 检查磁盘空间
```

### 2. 镜像构建阶段
```bash
✅ 创建多架构构建器
✅ 构建x86_64精简镜像
✅ 验证镜像架构
✅ 测试镜像功能
✅ 导出镜像文件
```

### 3. 部署包制作阶段
```bash
✅ 创建部署包目录结构
✅ 导出x86_64镜像
✅ 创建配置文件
✅ 生成一键部署脚本
✅ 生成智能运行脚本
✅ 创建完整文档
✅ 打包压缩
```

## 📁 输出文件结构

### 构建输出
```
# 镜像文件
docker_images_x86_64_YYYYMMDD_HHMMSS/
├── billing-audit-ai-v2.1.0-x86_64.tar.gz
└── python-3.9-slim-x86_64.tar.gz

# 部署包
billing_audit_x86_64_v2.1.0_YYYYMMDD_HHMMSS.tar.gz
```

### 部署包内容
```
billing_audit_x86_64_v2.1.0_YYYYMMDD_HHMMSS/
├── deploy.sh                    # 一键部署脚本
├── run_with_mount.sh            # 智能挂载运行脚本
├── production_config.json       # 生产配置文件
├── images/                      # Docker镜像文件
│   ├── billing-audit-ai-v2.1.0-x86_64.tar.gz
│   └── python-3.9-slim.tar.gz
├── data/                        # 数据目录
│   ├── input/                   # 输入数据
│   ├── output/                  # 输出结果
│   └── logs/                    # 日志文件
└── README.md                    # 使用说明
```

## 🎯 部署到x86_64主机

### 1. 传输部署包
```bash
# 使用scp传输到目标主机
scp billing_audit_x86_64_v2.1.0_*.tar.gz user@target-host:/path/to/deploy/
```

### 2. 解压和部署
```bash
# 在目标主机上执行
tar -xzf billing_audit_x86_64_v2.1.0_*.tar.gz
cd billing_audit_x86_64_v2.1.0_*

# 准备数据文件
cp /path/to/your/data.txt data/input/

# 一键部署
bash deploy.sh
```

### 3. 运行系统
```bash
# 完整流程
bash run_with_mount.sh full --algorithm hierarchical --batch-size 1000

# 单个功能
bash run_with_mount.sh training --algorithm hierarchical
bash run_with_mount.sh prediction --batch-size 2000
```

## ⚠️ 注意事项

### 构建环境
1. **Docker版本**: 确保Docker版本支持buildx
2. **磁盘空间**: 构建过程需要较多临时空间
3. **网络连接**: 首次构建需要下载基础镜像
4. **构建时间**: 跨架构构建比本地构建耗时更长

### 目标环境
1. **架构匹配**: 确保目标主机是x86_64架构
2. **Docker版本**: 目标主机Docker版本需支持所构建的镜像
3. **权限设置**: 确保有足够权限运行Docker容器
4. **资源配置**: 根据数据规模调整内存和批处理大小

## 🔍 故障排除

### 常见构建问题

#### 1. buildx不可用
```bash
# 错误: Docker buildx不可用
# 解决: 更新Docker到最新版本
brew upgrade docker  # macOS
```

#### 2. 构建器创建失败
```bash
# 错误: 构建器创建失败
# 解决: 清理旧构建器
docker buildx rm x86_64_builder
docker buildx prune
```

#### 3. 镜像架构验证失败
```bash
# 错误: 镜像架构不是amd64
# 解决: 检查构建命令中的--platform参数
docker inspect billing-audit-ai:v2.1.0-x86_64 --format '{{.Architecture}}'
```

### 常见部署问题

#### 1. 镜像加载失败
```bash
# 错误: 镜像文件损坏或不存在
# 解决: 重新构建镜像
bash deployment/scripts/build_x86_64_complete.sh --build-only
```

#### 2. 容器启动失败
```bash
# 错误: 容器无法启动
# 解决: 检查系统架构和Docker版本
uname -m  # 应该显示 x86_64
docker --version
```

#### 3. 权限问题
```bash
# 错误: 文件权限不足
# 解决: 修复权限
sudo chown -R $USER:$USER data/
```

## 📞 技术支持

### 获取帮助
```bash
# 查看帮助信息
bash deployment/scripts/build_x86_64_complete.sh --help

# 查看构建日志
docker logs <container_name>

# 检查镜像信息
docker images | grep billing-audit-ai
```

### 联系支持
如遇问题，请提供：
- 构建环境信息（uname -a, docker --version）
- 错误日志信息
- 构建命令和参数
- 目标环境信息

---

**山西电信出账稽核AI系统 v2.1.0**  
**x86_64架构构建指南**  
**技术支持: 九思计费专家团队**
