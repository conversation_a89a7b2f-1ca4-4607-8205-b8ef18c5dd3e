# 山西电信出账稽核AI系统 - 文件管理规范

## 📁 目录结构规范

### 核心目录结构
```
山西电信出账稽核AI/
├── src/                    # 源代码
├── scripts/               # 脚本文件
│   ├── production/        # 生产脚本
│   └── debug/            # 调试脚本 ⭐
├── config/               # 配置文件
├── docs/                 # 文档
├── data/                 # 数据文件
├── models/               # 模型文件
├── outputs/              # 输出文件
├── logs/                 # 日志文件
└── deployment/           # 部署文件
```

## 🚫 禁止行为

### ❌ 不允许的文件创建位置
1. **项目根目录**: 不允许随意创建临时脚本、测试文件
2. **源代码目录**: 不允许在src/下创建非业务代码
3. **配置目录**: 不允许创建临时配置文件

### ❌ 不允许的文件命名
- 使用中文文件名（除文档外）
- 包含空格的文件名
- 过长或无意义的文件名
- 重复或冲突的文件名

## ✅ 文件归档规范

### 📝 调试和测试脚本
**目标目录**: `scripts/debug/`
**文件类型**:
- `debug_*.py` - 调试脚本
- `test_*.py` - 测试脚本
- `analyze_*.py` - 分析脚本

**示例**:
```bash
# ✅ 正确
scripts/debug/debug_data_reading.py
scripts/debug/test_chunk_reader.py
scripts/debug/analyze_performance.py

# ❌ 错误
debug_data_reading.py          # 根目录
src/debug_prediction.py       # 源代码目录
test_something.py              # 根目录
```

### 🔧 工具脚本
**目标目录**: `scripts/tools/`
**文件类型**:
- 数据处理工具
- 模型转换工具
- 批处理脚本

### 📊 输出文件
**目标目录**: `outputs/`
**子目录结构**:
```
outputs/
├── data/          # 预测结果、处理后数据
├── models/        # 训练好的模型
├── reports/       # 评估报告、分析报告
├── visualizations/# 图表、可视化文件
└── temp/          # 临时文件
```

### 📋 文档文件
**目标目录**: `docs/`
**子目录结构**:
```
docs/
├── core/          # 核心文档
├── technical/     # 技术文档
├── guides/        # 使用指南
├── reports/       # 项目报告
└── archive/       # 归档文档
```

### 🗂️ 配置文件
**目标目录**: `config/`
**文件类型**:
- `*.json` - JSON配置
- `*.yaml` - YAML配置
- `*.env` - 环境变量

## 📋 文件命名规范

### 通用规范
- 使用英文小写字母
- 单词间用下划线连接
- 包含版本号或时间戳（如需要）
- 扩展名明确表示文件类型

### 具体规范

#### 脚本文件
```bash
# 调试脚本
debug_[功能]_[版本].py
test_[组件]_[场景].py

# 示例
debug_data_reading_v1.py
test_model_prediction.py
```

#### 输出文件
```bash
# 预测结果
[模型类型]_predictions_[时间戳].csv
hierarchical_predictions_20250804_173325.csv

# 评估报告
[模型类型]_evaluation_[时间戳].json
model_performance_report_20250804.md
```

#### 配置文件
```bash
# 生产配置
production_config.json
[环境]_config.json

# 示例
development_config.json
staging_config.json
```

## 🔄 文件生命周期管理

### 临时文件处理
1. **创建**: 统一在 `outputs/temp/` 目录
2. **命名**: 包含时间戳，便于清理
3. **清理**: 定期清理超过7天的临时文件

### 版本管理
1. **重要文件**: 保留历史版本
2. **配置文件**: 使用版本控制
3. **输出文件**: 按时间戳归档

### 归档策略
1. **活跃文件**: 保留在主目录
2. **历史文件**: 移动到archive子目录
3. **过期文件**: 定期清理或压缩

## 🛠️ 自动化工具

### 文件整理脚本
```bash
# 清理临时文件
scripts/tools/cleanup_temp_files.sh

# 归档旧文件
scripts/tools/archive_old_files.sh

# 检查文件规范
scripts/tools/check_file_standards.sh
```

### Git忽略规则
```gitignore
# 临时文件
*.tmp
*.temp
*_temp.*

# 调试输出
debug_output_*
test_result_*

# 大文件
*.pkl
*.model
*.h5
```

## 📊 监控和维护

### 定期检查项目
- [ ] 根目录是否有临时文件
- [ ] 各目录文件是否按规范命名
- [ ] 临时文件是否及时清理
- [ ] 文档是否及时更新

### 违规处理
1. **发现违规文件**: 立即移动到正确位置
2. **更新文档**: 记录变更和原因
3. **通知相关人员**: 说明规范要求

## 🎯 最佳实践

### 创建新文件前
1. **确定目标目录**: 根据文件类型选择正确目录
2. **检查命名规范**: 确保文件名符合规范
3. **考虑生命周期**: 是否需要版本管理或定期清理

### 文件操作建议
1. **批量操作**: 使用脚本而非手动操作
2. **备份重要文件**: 操作前先备份
3. **记录变更**: 在文档中记录重要变更

### 协作规范
1. **统一标准**: 所有人员遵循相同规范
2. **及时沟通**: 变更前与团队沟通
3. **定期review**: 定期检查和改进规范

---
**制定日期**: 2025-08-05
**适用范围**: 山西电信出账稽核AI系统v2.1.0及后续版本
**维护责任**: 项目开发团队
