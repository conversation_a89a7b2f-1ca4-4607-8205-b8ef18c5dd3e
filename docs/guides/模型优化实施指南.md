# 🚀 山西电信出账稽核AI系统 - 模型优化实施指南

## 📋 快速开始

**目标**: 将模型R²从0.0880提升至0.7+，±50元准确率从39.72%提升至90%+  
**核心问题**: 92.62%零值数据导致模型性能不足  
**解决策略**: 分层建模 + 零值感知算法 + 业务导向评估  

## 🎯 **第一阶段：核心问题解决 (1周)** ⭐ 立即开始

### **Day 1-2: 零值数据处理**

#### **实施分层建模**
```bash
# 1. 创建分层模型脚本
cp src/billing_audit/models/large_scale_model_evaluation.py \
   src/billing_audit/models/hierarchical_billing_model.py

# 2. 修改模型架构
# 编辑 hierarchical_billing_model.py，实现零值分类器 + 非零值回归器
```

#### **核心代码实现**
```python
# 在 hierarchical_billing_model.py 中添加
class HierarchicalBillingModel:
    def __init__(self):
        self.zero_classifier = lgb.LGBMClassifier()
        self.nonzero_regressor = lgb.LGBMRegressor()
    
    def fit(self, X, y):
        # 训练零值分类器
        y_binary = (y > 0).astype(int)
        self.zero_classifier.fit(X, y_binary)
        
        # 训练非零值回归器
        nonzero_mask = y > 0
        if nonzero_mask.sum() > 0:
            self.nonzero_regressor.fit(X[nonzero_mask], y[nonzero_mask])
    
    def predict(self, X):
        zero_proba = self.zero_classifier.predict_proba(X)[:, 1]
        nonzero_pred = self.nonzero_regressor.predict(X)
        return zero_proba * nonzero_pred
```

#### **验证效果**
```bash
# 运行测试
python src/billing_audit/models/hierarchical_billing_model.py \
  --test-data data/input/ofrm_result_adapted.csv \
  --output outputs/reports/hierarchical_test.json
```

**预期效果**: R² 0.0880 → 0.35+

### **Day 3-4: 算法替换为LightGBM**

#### **安装依赖**
```bash
pip install lightgbm==4.1.0
```

#### **替换主脚本中的算法**
```python
# 在 scripts/production/billing_audit_main.py 中修改
# 将 RandomForestRegressor 替换为 LightGBMRegressor

from lightgbm import LGBMRegressor

model = LGBMRegressor(
    objective='regression',
    metric='rmse',
    num_leaves=31,
    learning_rate=0.05,
    feature_fraction=0.9,
    bagging_fraction=0.8,
    random_state=42
)
```

#### **测试新算法**
```bash
python scripts/production/billing_audit_main.py full \
  --input data/input/ofrm_result_adapted.csv \
  --batch-size 1000
```

**预期效果**: 进一步提升R²至0.4+

### **Day 5-7: 分层评估实施**

#### **创建分层评估器**
```python
# 创建 src/billing_audit/evaluation/stratified_evaluator.py
class StratifiedEvaluator:
    def evaluate_by_amount_level(self, y_true, y_pred):
        results = {}
        
        # 零值评估
        zero_mask = (y_true == 0)
        if zero_mask.sum() > 0:
            results['zero'] = self._calculate_metrics(
                y_true[zero_mask], y_pred[zero_mask]
            )
        
        # 非零值评估
        nonzero_mask = (y_true > 0)
        if nonzero_mask.sum() > 0:
            results['nonzero'] = self._calculate_metrics(
                y_true[nonzero_mask], y_pred[nonzero_mask]
            )
        
        return results
```

#### **集成到主脚本**
```bash
# 修改主脚本，添加分层评估
# 在模型评估步骤后添加分层分析
```

**第一阶段目标**: R² 0.0880 → 0.4+, ±50元准确率 39.72% → 70%+

---

## ⚡ **第二阶段：特征和参数优化 (1-2周)**

### **Week 2: 零值特征工程**

#### **创建零值特征工程器**
```python
# 创建 src/billing_audit/preprocessing/zero_value_features.py
class ZeroValueFeatureEngineer:
    def create_zero_features(self, df):
        # 零值概率评分
        df['zero_probability_score'] = (
            (df['busi_flag'] == 1) * 0.3 +
            (df['charge_day_count'] == 0) * 0.4 +
            (df['should_fee'] == 0) * 0.3
        )
        
        # 免费服务标识
        df['is_free_service'] = (
            (df['should_fee'] == 0) & 
            (df['busi_flag'] == 1) &
            (df['charge_day_count'] >= 0)
        ).astype(int)
        
        return df
```

#### **集成到特征工程流程**
```bash
# 修改 large_scale_feature_engineer.py
# 在 create_efficient_features 方法中添加零值特征
```

### **Week 3: 超参数优化**

#### **安装优化工具**
```bash
pip install scikit-optimize==0.9.0
```

#### **贝叶斯优化实施**
```python
# 创建 scripts/optimization/hyperparameter_tuning.py
from skopt import gp_minimize
from skopt.space import Real, Integer

def optimize_lightgbm():
    search_space = [
        Integer(10, 100, name='num_leaves'),
        Real(0.01, 0.3, name='learning_rate'),
        Integer(3, 10, name='max_depth'),
        Real(0.1, 1.0, name='feature_fraction')
    ]
    
    result = gp_minimize(
        func=objective_function,
        dimensions=search_space,
        n_calls=50
    )
    
    return result.x
```

**第二阶段目标**: R² 0.4+ → 0.55+, ±50元准确率 70% → 80%+

---

## 📈 **第三阶段：集成和稳定性 (2-4周)**

### **Week 4-5: 集成学习**

#### **实施Stacking集成**
```python
# 创建 src/billing_audit/models/ensemble_models.py
from sklearn.ensemble import VotingRegressor

ensemble = VotingRegressor([
    ('lgb', lgb.LGBMRegressor(**best_params)),
    ('xgb', xgb.XGBRegressor()),
    ('cat', CatBoostRegressor(verbose=False))
])
```

### **Week 6: 生产部署优化**

#### **性能监控**
```python
# 创建 scripts/monitoring/performance_monitor.py
class ModelPerformanceMonitor:
    def monitor_realtime(self, y_true, y_pred):
        metrics = {
            'r2': r2_score(y_true, y_pred),
            'mae': mean_absolute_error(y_true, y_pred),
            'accuracy_50': np.mean(np.abs(y_true - y_pred) <= 50)
        }
        
        # 检查告警
        if metrics['r2'] < 0.4:
            self.send_alert("R²过低")
        
        return metrics
```

**第三阶段目标**: R² 0.55+ → 0.65+, ±50元准确率 80% → 85%+

---

## 🛠️ **快速实施命令**

### **环境准备**
```bash
# 1. 安装新依赖
pip install lightgbm==4.1.0 scikit-optimize==0.9.0 catboost==1.2

# 2. 备份当前模型
cp -r outputs/models outputs/models_backup_$(date +%Y%m%d)

# 3. 创建优化分支
git checkout -b model_optimization_v2.1.0
```

### **第一阶段快速实施**
```bash
# 1. 实施分层建模
python scripts/optimization/implement_hierarchical_model.py

# 2. 替换算法
python scripts/optimization/replace_with_lightgbm.py

# 3. 分层评估
python scripts/optimization/implement_stratified_evaluation.py

# 4. 验证效果
python scripts/production/billing_audit_main.py full \
  --input data/input/ofrm_result_adapted.csv \
  --optimization-mode hierarchical
```

### **效果验证**
```bash
# 对比优化前后效果
python scripts/evaluation/compare_models.py \
  --baseline outputs/models/baseline_model.pkl \
  --optimized outputs/models/hierarchical_model.pkl \
  --test-data data/input/test_data.csv
```

## 📊 **监控指标**

### **关键指标监控**
| 指标 | 当前值 | 第一阶段目标 | 第二阶段目标 | 第三阶段目标 |
|------|--------|-------------|-------------|-------------|
| R² | 0.0880 | 0.35+ | 0.55+ | 0.65+ |
| MAE | 137.45元 | <100元 | <80元 | <70元 |
| ±50元准确率 | 39.72% | 70%+ | 80%+ | 85%+ |
| 零值识别准确率 | - | 95%+ | 97%+ | 98%+ |

### **告警阈值**
```python
ALERT_THRESHOLDS = {
    'r2_min': 0.3,           # R²最低阈值
    'mae_max': 120,          # MAE最高阈值
    'accuracy_50_min': 0.6,  # ±50元准确率最低阈值
    'zero_accuracy_min': 0.9 # 零值识别准确率最低阈值
}
```

## 🚨 **常见问题和解决方案**

### **问题1: 分层模型训练失败**
```bash
# 检查数据分布
python -c "
import pandas as pd
data = pd.read_csv('data/input/ofrm_result_adapted.csv')
print('零值比例:', (data['amount'] == 0).mean())
print('非零值数量:', (data['amount'] > 0).sum())
"

# 解决方案：确保非零值样本足够
# 如果非零值样本<1000，使用数据增强
```

### **问题2: LightGBM安装失败**
```bash
# 方案1: 使用conda安装
conda install -c conda-forge lightgbm

# 方案2: 从源码编译
git clone --recursive https://github.com/microsoft/LightGBM
cd LightGBM && python setup.py install
```

### **问题3: 内存不足**
```bash
# 减少批处理大小
python scripts/production/billing_audit_main.py full \
  --input data/input/ofrm_result_adapted.csv \
  --batch-size 500  # 从1000减少到500
```

## 📋 **实施检查清单**

### **第一阶段 (1周)**
- [ ] 分层建模代码实现
- [ ] LightGBM算法替换
- [ ] 分层评估器创建
- [ ] 端到端测试通过
- [ ] R²提升至0.35+
- [ ] ±50元准确率提升至70%+

### **第二阶段 (1-2周)**
- [ ] 零值特征工程实施
- [ ] 缺失值处理改进
- [ ] 超参数贝叶斯优化
- [ ] 业务指标集成
- [ ] R²提升至0.55+
- [ ] ±50元准确率提升至80%+

### **第三阶段 (2-4周)**
- [ ] 集成学习实施
- [ ] 性能监控部署
- [ ] A/B测试框架
- [ ] 生产环境验证
- [ ] R²提升至0.65+
- [ ] ±50元准确率提升至85%+

## 🎯 **成功标准**

### **技术指标**
- **R²决定系数**: >0.6 (当前0.0880)
- **平均绝对误差**: <70元 (当前137.45元)
- **±50元准确率**: >85% (当前39.72%)
- **零值识别准确率**: >98%

### **业务指标**
- **预测准确性**: 业务可接受度>90%
- **处理性能**: 保持>10万条/秒
- **系统稳定性**: 99.9%可用性
- **成本效益**: ROI>2000%

### **部署标准**
- **A/B测试**: 新模型显著优于基线
- **生产验证**: 连续7天稳定运行
- **业务验收**: 业务团队确认效果
- **文档完整**: 操作手册和故障排除指南

---

**实施负责人**: 技术团队  
**预计完成时间**: 4-6周  
**风险等级**: 中等 (分阶段实施降低风险)  
**预期ROI**: 2300-5900%  
**下一步**: 开始第一阶段实施
