# 生产环境脚本使用指南

## 📋 **生产环境核心脚本清单**

### **🎯 必需脚本 (千万级数据优化版)**

#### **1. 大规模模型训练脚本**
```bash
scripts/production/train_large_scale_model.py
```

**功能**: 专门针对千万级数据优化的模型训练  
**输入格式**: CSV/TXT文件，英文逗号分割  
**输出**: 训练好的模型文件 + 预处理器文件  

**使用方法**:
```bash
# 基本用法
python scripts/production/train_large_scale_model.py \
    --input /path/to/your/data.csv \
    --output /path/to/output/dir \
    --batch-size 50000

# 参数说明
--input, -i     : 输入数据文件路径 (必需)
--output, -o    : 输出目录路径 (可选，默认: models/billing_audit/large_scale)
--batch-size, -b: 批处理大小 (可选，默认: 50000)
```

**输入数据格式要求**:
```csv
cal_type,unit_type,rate_unit,final_eff_year,final_eff_mon,final_eff_day,final_exp_year,final_exp_mon,final_exp_day,cur_year_month,charge_day_count,month_day_count,run_code,run_time,should_fee,busi_flag,amount
1,1,1,2024,1,1,2024,12,31,202407,31,31,NORMAL,202407,100.0,0,95.5
2,1,1,2024,7,15,2024,12,31,202407,17,31,NORMAL,202407,50.0,0,48.2
...
```

**输出文件**:
- `large_scale_model_YYYYMMDD_HHMMSS.pkl` - 训练好的模型
- `large_scale_preprocessor_YYYYMMDD_HHMMSS.pkl` - 预处理器

#### **2. 大规模数据预测脚本**
```bash
scripts/production/predict_large_scale.py
```

**功能**: 专门针对千万级数据优化的批量预测  
**输入格式**: CSV/TXT文件，英文逗号分割  
**输出**: 预测结果CSV文件  

**使用方法**:
```bash
# 基本用法
python scripts/production/predict_large_scale.py \
    --input /path/to/predict_data.csv \
    --model /path/to/model.pkl \
    --preprocessor /path/to/preprocessor.pkl \
    --output /path/to/predictions.csv \
    --batch-size 50000

# 包含原始特征的预测
python scripts/production/predict_large_scale.py \
    --input /path/to/predict_data.csv \
    --model /path/to/model.pkl \
    --preprocessor /path/to/preprocessor.pkl \
    --output /path/to/predictions_with_features.csv \
    --include-features

# 参数说明
--input, -i         : 输入数据文件路径 (必需)
--model, -m         : 模型文件路径 (必需)
--preprocessor, -p  : 预处理器文件路径 (必需)
--output, -o        : 输出预测结果文件路径 (必需)
--batch-size, -b    : 批处理大小 (可选，默认: 50000)
--include-features  : 在输出中包含原始特征 (可选)
```

**输出格式**:
```csv
# 仅预测结果
row_id,prediction
0,95.5
1,48.2
...

# 包含原始特征
cal_type,unit_type,...,prediction
1,1,...,95.5
2,1,...,48.2
...
```

### **🔧 辅助脚本**

#### **3. 环境设置脚本**
```bash
scripts/production/setup_env.sh
scripts/production/setup_production_env.sh  # ⭐ 新增
```

**功能**: 设置Python环境和依赖
**使用方法**:
```bash
# 基础环境设置
source scripts/production/setup_env.sh

# 生产环境变量设置 ⭐
source scripts/production/setup_production_env.sh
```

#### **4. 配置管理系统** ⭐
**配置文件**:
- `config/billing_audit_config.json` - 开发配置 (v1.0.0)
- `config/production_config.json` - 生产配置 (v2.0.0) ⭐

**配置管理器**:
- `src/config/production_config_manager.py` - 生产级配置管理

**环境变量支持**:
```bash
# 关键环境变量
export DATA_INPUT_DIR="/data/input"
export DATA_OUTPUT_DIR="/data/output"
export MODEL_DIR="/models"
export LOGS_DIR="/logs"
export BILLING_AUDIT_CONFIG="/path/to/production_config.json"
```

**配置验证**:
```bash
# 验证配置文件
python -c "from src.config.production_config_manager import get_config_manager; cm = get_config_manager(); print('配置OK')"
```

#### **5. 数据验证脚本**
```bash
scripts/validation/validate_preprocessing_logic.py
```

**功能**: 验证数据格式和质量  
**使用方法**:
```bash
python scripts/validation/validate_preprocessing_logic.py
```

---

## 🚀 **千万级数据优化特性**

### **内存优化**
- ✅ **分批处理**: 默认50,000行一批，避免内存溢出
- ✅ **增量加载**: 逐批读取，不一次性加载全部数据
- ✅ **内存清理**: 自动垃圾回收，释放不用的内存
- ✅ **中间保存**: 每20批自动保存中间结果

### **性能优化**
- ✅ **并行处理**: RandomForest使用所有CPU核心
- ✅ **特征简化**: 只创建最重要的特征，避免特征爆炸
- ✅ **数据类型优化**: 自动选择最优数据类型
- ✅ **预处理缓存**: 预处理器只拟合一次，重复使用

### **稳定性优化**
- ✅ **错误恢复**: 单批失败不影响整体处理
- ✅ **进度监控**: 实时显示处理进度和状态
- ✅ **自动检测**: 自动检测文件分隔符和编码
- ✅ **异常处理**: 完善的异常捕获和错误提示

---

## 📊 **性能基准**

### **训练性能** (基于测试环境)
- **数据规模**: 1000万行 × 16列
- **内存使用**: 峰值 < 8GB
- **训练时间**: 约 30-60分钟
- **批处理大小**: 50,000行/批
- **并发处理**: 使用所有CPU核心

### **预测性能** (基于测试环境)
- **预测速度**: 约 50,000-100,000 条/秒
- **内存使用**: 峰值 < 4GB
- **批处理大小**: 50,000行/批
- **输出速度**: 实时保存，无延迟

---

## 🔄 **完整工作流程**

### **步骤1: 环境准备**
```bash
# 设置基础环境
source scripts/production/setup_env.sh

# 设置生产环境变量 ⭐
source scripts/production/setup_production_env.sh

# 验证环境
python -c "import pandas, sklearn, joblib; print('环境OK')"

# 验证配置管理器 ⭐
python -c "from src.config.production_config_manager import get_config_manager; cm = get_config_manager(); print('配置OK')"
```

### **步骤2: 数据准备**
```bash
# 确保数据格式正确
head -5 /path/to/your/data.csv

# 检查数据大小
wc -l /path/to/your/data.csv
```

### **步骤3: 模型训练**
```bash
# 训练模型 (千万级数据)
python scripts/production/train_large_scale_model.py \
    --input /path/to/training_data.csv \
    --output ./models/production \
    --batch-size 50000
```

### **步骤4: 批量预测**
```bash
# 批量预测 (千万级数据)
python scripts/production/predict_large_scale.py \
    --input /path/to/predict_data.csv \
    --model ./models/production/large_scale_model_20250725_160000.pkl \
    --preprocessor ./models/production/large_scale_preprocessor_20250725_160000.pkl \
    --output ./results/predictions.csv \
    --batch-size 50000
```

### **步骤5: 结果验证**
```bash
# 检查预测结果
head -10 ./results/predictions.csv
wc -l ./results/predictions.csv

# 简单统计
python -c "
import pandas as pd
df = pd.read_csv('./results/predictions.csv')
print(f'预测数量: {len(df):,}')
print(f'预测范围: {df.prediction.min():.2f} - {df.prediction.max():.2f}')
print(f'平均值: {df.prediction.mean():.2f}')
"
```

---

## ⚠️ **注意事项**

### **数据格式要求**
1. **文件格式**: CSV或TXT，UTF-8编码
2. **分隔符**: 英文逗号 (,) 或制表符 (\t)
3. **列名**: 必须包含配置文件中定义的所有特征列
4. **数据类型**: 数值列为数字，类别列为字符串
5. **缺失值**: 用空值表示，系统会自动填充为0

### **系统资源要求**
1. **内存**: 建议 16GB+ (千万级数据)
2. **存储**: 确保有足够空间存储模型和结果
3. **CPU**: 多核CPU可显著提升训练速度
4. **Python**: 3.8+ 版本

### **常见问题解决**

#### **内存不足**
```bash
# 减小批处理大小
--batch-size 20000

# 或者使用更小的批次
--batch-size 10000
```

#### **文件读取错误**
```bash
# 检查文件编码
file -I /path/to/your/data.csv

# 转换编码 (如果需要)
iconv -f GBK -t UTF-8 input.csv > output.csv
```

#### **列名不匹配**
```bash
# 检查列名
head -1 /path/to/your/data.csv

# 对比配置文件中的feature_columns
cat config/billing_audit_config.json | grep -A 20 "feature_columns"
```

---

## 📈 **监控和日志**

### **训练过程监控**
- 实时显示批处理进度
- 内存使用情况提示
- 训练时间和性能统计
- 模型评估指标

### **预测过程监控**
- 预测进度和速度
- 中间结果自动保存
- 异常批次跳过提示
- 最终统计报告

### **日志文件**
- 所有操作都有详细日志输出
- 错误信息包含具体位置和原因
- 性能统计便于优化调整

---

## 🎯 **最佳实践**

1. **数据预处理**: 训练前先用小样本验证数据格式
2. **批次大小**: 根据内存情况调整，通常50,000行较合适
3. **模型保存**: 训练完成后及时备份模型文件
4. **结果验证**: 预测完成后检查结果的合理性
5. **资源监控**: 关注内存和CPU使用情况
6. **定期重训练**: 根据新数据定期更新模型

---

**📞 如有问题，请参考错误日志或联系技术支持**
