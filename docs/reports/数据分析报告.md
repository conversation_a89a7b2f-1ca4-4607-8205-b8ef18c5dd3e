# 数据样例分析报告

## 📊 数据概览

基于需求文档和样例文件，本项目包含三类数据：

### 1. 固定费用预测样例数据
**文件**: `数据/固费预测样例@20250707.xlsx`

**用途**: 用于训练固定费用收费合理性判定模型

**特征字段** (用于AI训练):
- `cal_type`: 费用计算类型 (枚举类型: 0-非月租, 1-整月收, 2-按天折算, 3-日收)
- `unit_type`: 周期类型 (枚举类型: 0-非多周期, 1-月, 2-天)
- `rate_unit`: 周期数量 (枚举类型: 0-非多周期, >1为周期数量)
- `final_eff_year`: 最终生效年 (数值: yyyy, 订购的多个时间取交集)
- `final_eff_mon`: 最终生效月 (数值: mm)
- `final_eff_day`: 最终生效日 (数值: dd)
- `final_exp_year`: 最终失效年 (数值: yyyy, 订购的多个时间取交集)
- `final_exp_mon`: 最终失效月 (数值: mm)
- `final_exp_day`: 最终失效日 (数值: dd)
- `cur_year_month`: 当前年月 (日期类型: yyyymm)
- `charge_day_count`: 计费天数 (数值: 根据业务规则计算出来的当月应收费天数，综合考虑生效和状态变化)
- `month_day_count`: 当月总天数 (数值)
- `run_code`: 当前状态 (字符串: 结合用户状态和停机类型输出)
- `run_time`: 状态变更时间 (日期类型: yyyymm)
- `should_fee`: 应收费 (数值: 定价应收金额，如果是协议价，从协议价获取)
- `busi_flag`: 根据业务规则确定不收费标识 (枚举类型: 0-正常收费, 1-不收费)

**结果字段**:
- `amount`: 账单费用金额 (数值: 账单中的应收费) - **目标变量**

**原始数据字段** (用于分析问题使用):
- `offer_inst_id`: 销售品实例标识
- `prod_inst_id`: 产品实例标识
- `prod_id`: 产品标识
- `offer_id`: 销售品标识
- `sub_prod_id`: 功能产品标识
- `event_pricing_strategy_id`: 策略标识 (根据策略条件匹配到的策略)
- `event_type_id`: 事件类型
- `calc_priority`: 策略优先级
- `pricing_section_id`: 段落标识 (根据段落条件匹配到的段落)
- `calc_method_id`: 计算标识
- `role_id`: 角色标识

**业务规则说明**:
- 对于171事件类型，如果用户订购多个171事件，只收取优先级最高的订购，其他订购不收费
- 如果用户是精品班成员，则不收取固费

### 2. 优惠费用预测样例数据
**文件**: `数据/优惠预测样例@20250707.xlsx`

**用途**: 用于训练优惠费用收费合理性判定模型

**特征字段** (用于AI训练):
- `fav_type`: 优惠类型 (枚举类型: 0-非优惠, 1-打折, 2-送费, 3-免费)
- `final_eff_date`: 最终生效时间 (日期类型: yyyymmdd)
- `final_exp_date`: 最终失效时间 (日期类型: yyyymmdd)
- `cur_year_month`: 当前年月 (日期类型: yyyymm)
- `run_code`: 当前状态 (字符串: 结合用户状态和停机类型输出)
- `run_time`: 状态变更时间 (日期类型: yyyymm)
- `fav_cal_fee`: 优惠计算金额 (数值: 根据优惠计算对象，统计优惠计算金额)
- `fav_value`: 优惠费 (数值: 定价优惠值，如果是协议价，从协议价获取)
- `busi_flag`: 业务规则确定不优惠标识 (枚举类型: 0-正常优惠, 1-不优惠)

**结果字段**:
- `amount`: 账单费用金额 (数值: 账单中的优惠费无法直接关联到优惠订购) - **目标变量**

**原始数据字段** (用于分析问题使用):
- `offer_inst_id`: 销售品实例标识
- `prod_inst_id`: 产品实例标识
- `user_id`: 用户标识
- `phone_no`: 用户号码
- `prod_id`: 产品标识
- `offer_id`: 销售品标识
- `sub_prod_id`: 功能产品标识
- `final_eff_date`: 最终生效时间 (订购的多个时间段交集)
- `final_exp_date`: 最终失效时间 (订购的多个时间段交集)
- `strategy_id`: 策略标识
- `life_cycle_id`: 生命周期
- `eff_type`: 生命周期偏移类型
- `eff_value`: 生效时间偏移量
- `exp_value`: 失效时间偏移量
- `event_type_id`: 事件类型
- `section_id`: 段落标识
- `calc_method_id`: 计算标识
- `role_id`: 角色标识
- `region_str`: 地市

### 3. 话单增量样例数据
**文件**: `数据/话单增量样例.xlsx`

**用途**: 用于用户行为分析和变化检测

**特征字段** (用于AI训练):
- `system_type`: 子系统代码
- `call_type`: 通话类型
- `cdr_type`: 话单类型
- `dial_type`: 拨打类型
- `fee_type`: 费用类型
- `ism_type`: 业务类型
- `record_type`: 记录类型
- `roam_type`: 漫游类型
- `sm_type`: 短消息类型
- `struct_type`: 对端号码类型
- `call_duration`: 通话时长
- `down_flow`: 下行流量
- `up_flow`: 上行流量
- `item1`: fee1账目项
- `item2`: fee2账目项
- `item3`: fee3账目项
- `fee1`: 一批后fee1
- `fee2`: 一批后fee2
- `fee3`: 一批后fee3

**原始数据字段** (用于分析问题使用):
- `msisdn`: 用户号码
- `start_time`: 通话时间

## 🎯 数据特征分析

### 固费和优惠数据特征
1. **时间特征**:
   - `final_eff_year`, `final_eff_mon`, `final_eff_day`: 可组合为日期特征或计算持续时间
   - `final_exp_year`, `final_exp_mon`, `final_exp_day`: 可组合为日期特征或计算持续时间
   - `cur_year_month`: 可提取月份、季度等时间特征

2. **类别特征**:
   - `cal_type`, `unit_type`, `rate_unit`, `run_code`, `busi_flag`: 需要进行One-Hot编码

3. **数值特征**:
   - `final_eff_year`, `final_eff_mon`, `final_eff_day`: 时间数值特征
   - `final_exp_year`, `final_exp_mon`, `final_exp_day`: 时间数值特征
   - `month_day_count`, `charge_day_count`, `should_fee`: 计费相关数值，需要标准化处理

4. **目标变量**:
   - `amount`: 实际收费金额，用于训练和验证

### 话单数据特征
1. **时间特征**:
   - `start_time`: 可提取小时、星期几、是否周末等特征

2. **类别特征**:
   - `system_type`, `call_type`: 需要进行One-Hot编码

3. **数值特征**:
   - `call_duration`, `down_flow`, `up_flow`: 需要标准化处理

## 📋 数据质量评估

### 需要关注的问题
1. **缺失值处理**: 检查各字段的缺失情况
2. **异常值检测**: 识别金额、时长等字段的异常值
3. **数据一致性**: 确保日期格式、数值范围的一致性
4. **业务逻辑验证**: 验证计费逻辑的合理性

## 🛠 特征工程策略

### 1. 收费稽核系统特征工程
- **日期处理**: 计算订阅时长、生效天数比例
- **类别编码**: One-Hot编码处理类别变量
- **数值标准化**: StandardScaler标准化数值特征
- **特征组合**: 创建交互特征，如费用密度等

### 2. 行为分析系统特征工程
- **时间特征**: 提取通话时段、工作日/周末标识
- **流量特征**: 计算上下行流量比、总流量等
- **行为模式**: 基于时间和类型的行为聚合特征

## 💡 建议

1. **数据预处理优先级**:
   - 首先处理缺失值和异常值
   - 统一数据格式和编码
   - 验证业务逻辑一致性

2. **模型训练策略**:
   - 固费和优惠分别训练独立模型
   - 使用时间序列交叉验证
   - 重点关注模型可解释性

3. **系统设计考虑**:
   - 支持配置化字段管理
   - 实现增量学习能力
   - 建立模型监控机制
