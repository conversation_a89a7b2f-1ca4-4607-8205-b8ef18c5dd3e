# 📚 山西电信出账稽核AI系统 - 文档中心

## 🚀 **v2.1.0 (流程修正版)** - 文档导航

本目录包含山西电信出账稽核AI系统的完整文档，按照重要性和用途进行分类组织。

### ⭐ **推荐入口**
- **[生产环境主脚本使用指南](guides/生产环境主脚本使用指南.md)** - 🎯 **最佳入口** - 完整的生产级使用指南
- **[模型性能优化方案](technical/模型性能优化方案_v2.1.0.md)** - 🚀 **v2.1.0 重磅** - 全面模型优化策略
- **[问题修复和功能增强报告](technical/问题修复和功能增强报告.md)** - 🔧 **v2.1.0 最新** - 三大问题修复报告
- **[配置化字段管理文档](technical/字段映射和配置重构文档.md)** - ⚙️ **v2.1.0 新增** - 动态字段管理指南
- **[主脚本流程修正说明](technical/主脚本流程修正说明.md)** - 🔄 **v2.1.0 新增** - 流程修正详细说明

---

## 📁 **目录结构**

```
docs/
├── 文档中心.md                 # 本文档导航
├── core/                         # 核心文档 ⭐ 最重要
│   ├── 技术规格文档.md  # 完整技术规格 (752行)
│   ├── 文档索引.md            # 文档快速索引
│   └── README.md                    # 文档导航（旧版）
├── technical/                    # 技术文档
│   ├── 模型性能优化方案_v2.1.0.md ⭐ # v2.1.0 重磅 - 全面模型优化策略
│   ├── 问题修复和功能增强报告.md ⭐ # v2.1.0 新增 - 三大问题修复报告
│   ├── 字段映射和配置重构文档.md ⭐ # v2.1.0 新增 - 配置化字段管理文档
│   ├── 真实生产数据端到端测试报告_v2.1.0.md ⭐ # v2.1.0 新增 - 真实数据测试报告
│   ├── 特征工程处理分析报告.md ⭐ # v2.1.0 新增 - 特征工程详细分析
│   ├── 主脚本流程修正说明.md ⭐ # v2.1.0 新增 - 流程修正详细说明
│   ├── 主脚本功能架构分析.md ⭐ # v2.1.0 新增 - 全流程+单环节运行完整分析
│   ├── 生产脚本日志修复报告.md ⭐ # v2.1.0 新增 - 日志标准化修复报告
│   ├── 大规模数据处理指南.md    # 千万级数据处理指南
│   ├── 生产环境部署指南.md          # 生产环境部署指南
│   ├── 收费判定适配报告.md   # 收费合理性判定适配报告
│   └── 端到端测试报告.md               # 端到端测试报告
├── guides/                       # 用户指南
│   ├── 快速开始指南.md         # 快速开始指南
│   ├── 生产环境主脚本使用指南.md ⭐ # 生产级主脚本完整使用指南
│   ├── 模型优化实施指南.md ⭐ # v2.1.0 重磅 - 模型优化快速实施指南
│   ├── 配置化字段管理使用指南.md ⭐ # v2.1.0 新增 - 动态字段管理指南
│   ├── API使用指南.md           # API使用指南
│   ├── 大规模端到端指南.md # 千万级端到端指南
│   └── 生产脚本指南.md  # 生产脚本指南
├── reports/                      # 报告文档
│   ├── 模型训练总结.md    # 模型训练总结
│   ├── 模拟数据生成报告.md # 模拟数据生成报告
│   ├── 多算法对比报告.md # 多算法对比报告
│   └── 数据分析报告.md              # 数据分析报告
├── archive/                      # 归档文档
│   ├── 模型重训练报告.md   # 模型重训练报告（旧版）
│   ├── 预处理更新报告.md # 预处理更新报告（旧版）
│   └── ...                          # 其他历史文档
├── api/                          # API文档
└── user_guide/                   # 用户指南（旧版）
```

---

## 🎯 **快速导航**

### **🔥 必读文档**
1. **[技术规格文档](core/技术规格文档.md)** - 系统完整技术规格 (752行)
2. **[千万级数据处理指南](technical/大规模数据处理指南.md)** - 大规模数据处理
3. **[生产环境部署指南](technical/生产环境部署指南.md)** - 配置化部署
4. **[快速开始指南](guides/快速开始指南.md)** - 快速上手

### **📊 系统能力**
- **[收费合理性判定适配报告](technical/收费判定适配报告.md)** - 千万级判定功能
- **[端到端测试报告](technical/端到端测试报告.md)** - 系统验证报告
- **[模型训练总结](reports/模型训练总结.md)** - 模型性能报告

### **🚀 使用指南**
- **[API使用指南](guides/API使用指南.md)** - API接口使用
- **[生产脚本指南](guides/生产脚本指南.md)** - 生产环境脚本
- **[千万级端到端指南](guides/大规模端到端指南.md)** - 大规模数据处理

---

## 📖 **按用户角色导航**

### **👨‍💻 开发人员**
- [技术规格文档](core/技术规格文档.md) - 了解系统架构
- [API使用指南](guides/API使用指南.md) - 接口开发
- [快速开始指南](guides/快速开始指南.md) - 开发环境搭建

### **🔧 运维人员**
- [生产环境部署指南](technical/生产环境部署指南.md) - 部署配置
- [千万级数据处理指南](technical/大规模数据处理指南.md) - 性能调优
- [生产脚本指南](guides/生产脚本指南.md) - 运维脚本

### **📊 业务人员**
- [快速开始指南](guides/快速开始指南.md) - 系统使用
- [收费合理性判定适配报告](technical/收费判定适配报告.md) - 业务功能
- [模型训练总结](reports/模型训练总结.md) - 系统性能

### **🧪 测试人员**
- [端到端测试报告](technical/端到端测试报告.md) - 测试验证
- [千万级端到端指南](guides/大规模端到端指南.md) - 测试流程
- [多算法对比报告](reports/多算法对比报告.md) - 性能对比

---

## 🔍 **按功能导航**

### **🤖 模型训练**
- [模型训练总结](reports/模型训练总结.md)
- [多算法对比报告](reports/多算法对比报告.md)
- [千万级数据处理指南](technical/大规模数据处理指南.md)

### **⚖️ 收费判定**
- [收费合理性判定适配报告](technical/收费判定适配报告.md)
- [技术规格文档](core/技术规格文档.md)
- [API使用指南](guides/API使用指南.md)

### **🚀 生产部署**
- [生产环境部署指南](technical/生产环境部署指南.md)
- [生产脚本指南](guides/生产脚本指南.md)
- [端到端测试报告](technical/端到端测试报告.md)

### **📈 性能优化**
- [千万级数据处理指南](technical/大规模数据处理指南.md)
- [端到端测试报告](technical/端到端测试报告.md)
- [多算法对比报告](reports/多算法对比报告.md)

---

## 📚 **文档版本说明**

### **当前版本 (v2.0.0)**
- ✅ 支持千万级数据处理
- ✅ 收费合理性判定功能完整
- ✅ 生产环境配置化部署
- ✅ 完整的端到端测试验证

### **文档更新记录**
- **2025-07-25**: 完成文档重新组织和分类
- **2025-07-25**: 新增收费合理性判定适配报告
- **2025-07-25**: 新增生产环境部署指南
- **2025-07-25**: 完成千万级数据处理功能文档

---

## 🔗 **相关链接**

### **项目文件**
- [项目根目录README](../README.md) - 项目总览
- [部署文件](../deployment/) - 生产环境部署
- [配置文件](../config/) - 系统配置

### **外部资源**
- [技术规格详细索引](core/文档索引.md) - 文档快速索引
- [API文档](api/) - 接口文档
- [用户指南](user_guide/) - 详细用户指南

---

## 📞 **文档维护**

### **文档反馈**
如发现文档问题或需要补充，请：
1. 检查是否有相关文档已存在
2. 查看归档文档是否有历史版本
3. 联系技术团队进行更新

### **文档贡献**
- 新增文档请放在合适的分类目录
- 更新现有文档请保持格式一致
- 重要变更请更新本导航文档

---

**📊 文档统计**: 20+ 个文档，按5个分类组织  
**🎯 覆盖范围**: 技术规格、使用指南、部署运维、测试报告  
**📅 最后更新**: 2025-07-25  
**📝 维护团队**: 技术团队

**🎉 现在文档结构清晰，便于查找和使用！**
