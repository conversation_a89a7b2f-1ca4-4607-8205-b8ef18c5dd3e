# 🗂️ 旧版代码存档

## 📋 目录说明

本目录包含山西电信出账稽核AI系统的旧版代码，这些代码已被新的千万级数据处理系统替代。

## 📁 目录结构

```
legacy_code/
└── src/
    └── billing_audit/
        ├── preprocessing/
        │   ├── feature_engineer.py          # 旧版特征工程
        │   └── data_preprocessor.py         # 旧版数据预处理
        ├── training/
        │   ├── model_trainer.py             # 旧版模型训练
        │   ├── hyperparameter_tuner.py      # 旧版超参数调优
        │   ├── train_billing_models.py      # 旧版训练脚本
        │   └── train_with_config.py         # 旧版配置训练
        ├── inference/
        │   ├── model_predictor.py           # 旧版模型预测
        │   └── billing_judge.py             # 旧版收费判定
        └── models/
            ├── model_evaluator.py           # 旧版模型评估
            └── model_validator.py           # 旧版模型验证
```

## 🆚 新旧代码对比

### **旧版代码特点**
- 适用于小规模数据处理（万级别）
- 基础的机器学习流程
- 简单的错误处理
- 有限的性能优化

### **新版代码特点**
- 适用于千万级数据处理
- 分批处理和内存优化
- 完善的日志记录系统
- 实时进度条和性能监控
- 生产级错误处理
- 规范化文件输出

## 🚀 当前生产代码

当前生产环境使用的是以下新版代码：

- `src/billing_audit/preprocessing/large_scale_feature_engineer.py`
- `src/billing_audit/training/train_large_scale_model.py`
- `src/billing_audit/inference/predict_large_scale.py`
- `src/billing_audit/inference/large_scale_billing_judge.py`
- `src/billing_audit/models/large_scale_model_evaluation.py`

## ⚠️ 重要说明

1. **不建议使用旧版代码**：旧版代码已不适用于当前的千万级数据处理需求
2. **仅供参考**：这些代码保留仅用于历史参考和学习目的
3. **性能差异**：新版代码性能提升显著（1800+条/秒 vs 原版的数百条/秒）
4. **功能完整性**：新版代码具备完整的生产级特性

## 📅 存档时间

- 存档日期：2025-07-25
- 存档原因：系统升级到千万级数据处理能力
- 新版本特性：生产级性能、完善监控、规范化管理

## 🔄 如需恢复

如果因特殊原因需要使用旧版代码，可以将相应文件复制回原位置，但强烈建议：

1. 先备份当前新版代码
2. 评估是否真的需要使用旧版
3. 考虑性能和功能差异
4. 进行充分测试

---

**建议：继续使用新版千万级数据处理系统，以获得最佳性能和完整功能。**
