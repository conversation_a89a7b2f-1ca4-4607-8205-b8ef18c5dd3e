#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收费判定器
实现基于模型预测的收费合理性判定功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
from enum import Enum
import json
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger
from .model_predictor import ModelPredictor

logger = get_logger(__name__)


class JudgmentResult(Enum):
    """判定结果枚举"""
    REASONABLE = "reasonable"      # 合理
    UNREASONABLE = "unreasonable"  # 不合理
    UNCERTAIN = "uncertain"        # 不确定


class BillingJudge:
    """收费判定器"""
    
    def __init__(self, fee_type: str = 'fixed_fee'):
        """
        初始化收费判定器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.judgment_thresholds = self.config.get_judgment_thresholds()
        
        # 初始化模型预测器
        self.predictor = ModelPredictor(fee_type)
        
        # 判定统计
        self.judgment_stats = {
            'total_judgments': 0,
            'reasonable_count': 0,
            'unreasonable_count': 0,
            'uncertain_count': 0
        }
        
        logger.info(f"初始化{fee_type}收费判定器")
    
    def judge_single(self, billing_data: Dict[str, Any], actual_amount: float) -> Dict[str, Any]:
        """
        单个收费记录判定
        
        Args:
            billing_data: 计费数据
            actual_amount: 实际收费金额
            
        Returns:
            判定结果字典
        """
        logger.debug("开始单个收费判定")
        
        # 模型预测
        try:
            prediction_result = self.predictor.predict_with_details(billing_data)
            predicted_amount = prediction_result['predictions'][0]
        except Exception as e:
            logger.error(f"模型预测失败: {e}")
            return self._create_error_result(billing_data, actual_amount, str(e))
        
        # 计算误差
        absolute_error = abs(predicted_amount - actual_amount)
        relative_error = abs(predicted_amount - actual_amount) / actual_amount if actual_amount != 0 else float('inf')
        
        # 判定逻辑
        judgment_result = self._make_judgment(absolute_error, relative_error, actual_amount)
        
        # 构建结果
        result = {
            'billing_data': billing_data,
            'actual_amount': actual_amount,
            'predicted_amount': predicted_amount,
            'absolute_error': absolute_error,
            'relative_error': relative_error,
            'judgment': judgment_result.value,
            'confidence_score': self._calculate_confidence(absolute_error, relative_error, actual_amount),
            'threshold_info': {
                'absolute_threshold': self.judgment_thresholds.get('absolute_threshold', 0.01),
                'relative_threshold': self.judgment_thresholds.get('relative_threshold', 0.01),
                'use_mixed_threshold': self.judgment_thresholds.get('use_mixed_threshold', True)
            },
            'judgment_timestamp': datetime.now().isoformat()
        }
        
        # 更新统计
        self._update_stats(judgment_result)
        
        logger.debug(f"判定完成: {judgment_result.value}")
        return result
    
    def judge_batch(self, billing_records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量收费判定
        
        Args:
            billing_records: 计费记录列表，每个记录包含billing_data和actual_amount
            
        Returns:
            判定结果列表
        """
        logger.info(f"开始批量收费判定，记录数: {len(billing_records)}")
        
        results = []
        for i, record in enumerate(billing_records):
            try:
                billing_data = record.get('billing_data', {})
                actual_amount = record.get('actual_amount', 0.0)
                
                result = self.judge_single(billing_data, actual_amount)
                result['record_index'] = i
                results.append(result)
                
            except Exception as e:
                logger.error(f"记录 {i} 判定失败: {e}")
                error_result = self._create_error_result(
                    record.get('billing_data', {}),
                    record.get('actual_amount', 0.0),
                    str(e)
                )
                error_result['record_index'] = i
                results.append(error_result)
        
        logger.info(f"批量判定完成，成功: {len([r for r in results if r.get('judgment') != 'error'])}")
        return results
    
    def judge_from_file(self, input_file: str, output_file: str = None) -> str:
        """
        从文件读取数据进行判定
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            输出文件路径
        """
        logger.info(f"从文件进行收费判定: {input_file}")
        
        # 读取输入文件
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        if input_path.suffix.lower() == '.xlsx':
            df = pd.read_excel(input_file)
        elif input_path.suffix.lower() == '.csv':
            df = pd.read_csv(input_file)
        else:
            raise ValueError(f"不支持的文件格式: {input_path.suffix}")
        
        # 验证必要列
        required_columns = self.predictor.model_config.feature_columns + ['amount']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"输入文件缺少必要列: {missing_columns}")
        
        # 准备批量判定数据
        billing_records = []
        for _, row in df.iterrows():
            billing_data = row.drop('amount').to_dict()
            actual_amount = row['amount']
            billing_records.append({
                'billing_data': billing_data,
                'actual_amount': actual_amount
            })
        
        # 批量判定
        results = self.judge_batch(billing_records)
        
        # 构建结果DataFrame
        result_rows = []
        for i, result in enumerate(results):
            row = df.iloc[i].to_dict()
            row.update({
                'predicted_amount': result.get('predicted_amount', 0),
                'absolute_error': result.get('absolute_error', 0),
                'relative_error': result.get('relative_error', 0),
                'judgment': result.get('judgment', 'error'),
                'confidence_score': result.get('confidence_score', 0),
                'judgment_timestamp': result.get('judgment_timestamp', '')
            })
            result_rows.append(row)
        
        result_df = pd.DataFrame(result_rows)
        
        # 设置输出文件路径
        if output_file is None:
            output_dir = project_root / "outputs" / "judgments"
            output_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = output_dir / f"{self.fee_type}_judgments_{timestamp}.csv"
        
        # 保存结果
        result_df.to_csv(output_file, index=False, encoding='utf-8')
        
        logger.info(f"文件判定完成，结果保存到: {output_file}")
        return str(output_file)
    
    def _make_judgment(self, absolute_error: float, relative_error: float, 
                      actual_amount: float) -> JudgmentResult:
        """
        执行判定逻辑
        
        Args:
            absolute_error: 绝对误差
            relative_error: 相对误差
            actual_amount: 实际金额
            
        Returns:
            判定结果
        """
        abs_threshold = self.judgment_thresholds.get('absolute_threshold', 0.01)
        rel_threshold = self.judgment_thresholds.get('relative_threshold', 0.01)
        use_mixed = self.judgment_thresholds.get('use_mixed_threshold', True)
        uncertainty_factor = self.judgment_thresholds.get('uncertainty_factor', 2.0)
        
        if use_mixed:
            # 混合阈值判定：满足任一条件即为合理
            is_reasonable = (absolute_error <= abs_threshold) or (relative_error <= rel_threshold)
            
            # 不确定区间：误差在阈值的1-2倍之间
            is_uncertain = (
                (abs_threshold < absolute_error <= abs_threshold * uncertainty_factor) or
                (rel_threshold < relative_error <= rel_threshold * uncertainty_factor)
            )
        else:
            # 仅使用绝对阈值
            is_reasonable = absolute_error <= abs_threshold
            is_uncertain = abs_threshold < absolute_error <= abs_threshold * uncertainty_factor
        
        if is_reasonable:
            return JudgmentResult.REASONABLE
        elif is_uncertain:
            return JudgmentResult.UNCERTAIN
        else:
            return JudgmentResult.UNREASONABLE
    
    def _calculate_confidence(self, absolute_error: float, relative_error: float, 
                            actual_amount: float) -> float:
        """
        计算判定置信度
        
        Args:
            absolute_error: 绝对误差
            relative_error: 相对误差
            actual_amount: 实际金额
            
        Returns:
            置信度 (0-1)
        """
        abs_threshold = self.judgment_thresholds.get('absolute_threshold', 0.01)
        rel_threshold = self.judgment_thresholds.get('relative_threshold', 0.01)
        
        # 基于误差大小计算置信度
        abs_confidence = max(0, 1 - (absolute_error / abs_threshold))
        rel_confidence = max(0, 1 - (relative_error / rel_threshold))
        
        # 取较高的置信度
        confidence = max(abs_confidence, rel_confidence)
        
        # 确保置信度在0-1范围内
        return max(0.0, min(1.0, confidence))
    
    def _create_error_result(self, billing_data: Dict[str, Any], actual_amount: float, 
                           error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'billing_data': billing_data,
            'actual_amount': actual_amount,
            'predicted_amount': 0.0,
            'absolute_error': 0.0,
            'relative_error': 0.0,
            'judgment': 'error',
            'confidence_score': 0.0,
            'error_message': error_message,
            'judgment_timestamp': datetime.now().isoformat()
        }
    
    def _update_stats(self, judgment_result: JudgmentResult) -> None:
        """更新判定统计"""
        self.judgment_stats['total_judgments'] += 1
        
        if judgment_result == JudgmentResult.REASONABLE:
            self.judgment_stats['reasonable_count'] += 1
        elif judgment_result == JudgmentResult.UNREASONABLE:
            self.judgment_stats['unreasonable_count'] += 1
        elif judgment_result == JudgmentResult.UNCERTAIN:
            self.judgment_stats['uncertain_count'] += 1
    
    def get_judgment_stats(self) -> Dict[str, Any]:
        """获取判定统计信息"""
        total = self.judgment_stats['total_judgments']
        
        if total == 0:
            return self.judgment_stats.copy()
        
        stats = self.judgment_stats.copy()
        stats.update({
            'reasonable_rate': self.judgment_stats['reasonable_count'] / total * 100,
            'unreasonable_rate': self.judgment_stats['unreasonable_count'] / total * 100,
            'uncertain_rate': self.judgment_stats['uncertain_count'] / total * 100
        })
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.judgment_stats = {
            'total_judgments': 0,
            'reasonable_count': 0,
            'unreasonable_count': 0,
            'uncertain_count': 0
        }
    
    def generate_judgment_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成判定报告
        
        Args:
            results: 判定结果列表
            
        Returns:
            判定报告
        """
        logger.info("生成判定报告")
        
        # 统计各类判定结果
        reasonable_count = len([r for r in results if r.get('judgment') == 'reasonable'])
        unreasonable_count = len([r for r in results if r.get('judgment') == 'unreasonable'])
        uncertain_count = len([r for r in results if r.get('judgment') == 'uncertain'])
        error_count = len([r for r in results if r.get('judgment') == 'error'])
        total_count = len(results)
        
        # 计算误差统计
        valid_results = [r for r in results if r.get('judgment') != 'error']
        
        if valid_results:
            absolute_errors = [r['absolute_error'] for r in valid_results]
            relative_errors = [r['relative_error'] for r in valid_results]
            confidence_scores = [r['confidence_score'] for r in valid_results]
            
            error_stats = {
                'mean_absolute_error': np.mean(absolute_errors),
                'median_absolute_error': np.median(absolute_errors),
                'max_absolute_error': np.max(absolute_errors),
                'mean_relative_error': np.mean(relative_errors),
                'median_relative_error': np.median(relative_errors),
                'max_relative_error': np.max(relative_errors),
                'mean_confidence': np.mean(confidence_scores)
            }
        else:
            error_stats = {}
        
        # 构建报告
        report = {
            'summary': {
                'total_records': total_count,
                'reasonable_count': reasonable_count,
                'unreasonable_count': unreasonable_count,
                'uncertain_count': uncertain_count,
                'error_count': error_count,
                'reasonable_rate': reasonable_count / total_count * 100 if total_count > 0 else 0,
                'unreasonable_rate': unreasonable_count / total_count * 100 if total_count > 0 else 0,
                'uncertain_rate': uncertain_count / total_count * 100 if total_count > 0 else 0,
                'error_rate': error_count / total_count * 100 if total_count > 0 else 0
            },
            'error_statistics': error_stats,
            'threshold_config': self.judgment_thresholds,
            'model_info': self.predictor.get_model_info(),
            'report_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"判定报告生成完成 - 合理率: {report['summary']['reasonable_rate']:.2f}%")
        return report
    
    def save_judgment_report(self, results: List[Dict[str, Any]], save_path: str = None) -> str:
        """
        保存判定报告
        
        Args:
            results: 判定结果列表
            save_path: 保存路径
            
        Returns:
            实际保存路径
        """
        report = self.generate_judgment_report(results)
        
        if save_path is None:
            reports_dir = project_root / "outputs" / "reports"
            reports_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = reports_dir / f"{self.fee_type}_judgment_report_{timestamp}.json"
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"判定报告已保存: {save_path}")
        return str(save_path)
