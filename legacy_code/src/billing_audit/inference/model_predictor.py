#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型预测器
实现模型加载、特征预处理和预测功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
import joblib
import json
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger
from src.billing_audit.preprocessing import FeatureEngineer

logger = get_logger(__name__)


class ModelPredictor:
    """模型预测器"""
    
    def __init__(self, fee_type: str = 'fixed_fee', model_path: str = None):
        """
        初始化模型预测器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
            model_path: 模型路径，如果为None则使用最新模型
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.model_config = self.config.get_model_config('billing_audit', fee_type)
        
        # 模型和特征工程器
        self.model = None
        self.feature_engineer = None
        self.model_info = {}
        
        # 加载模型
        if model_path:
            self.load_model(model_path)
        else:
            self._load_latest_model()
        
        logger.info(f"初始化{fee_type}模型预测器")
    
    def _load_latest_model(self) -> None:
        """加载最新的模型"""
        models_dir = project_root / "models" / "billing_audit"
        
        if not models_dir.exists():
            logger.warning(f"模型目录不存在: {models_dir}")
            return
        
        # 查找最新的模型目录
        model_dirs = [d for d in models_dir.iterdir() if d.is_dir() and d.name.startswith(f"{self.fee_type}_model_")]
        
        if not model_dirs:
            logger.warning(f"未找到{self.fee_type}模型")
            return
        
        # 按时间戳排序，选择最新的
        latest_model_dir = sorted(model_dirs, key=lambda x: x.name)[-1]
        self.load_model(str(latest_model_dir))
    
    def load_model(self, model_path: str) -> None:
        """
        加载模型和相关组件
        
        Args:
            model_path: 模型路径
        """
        model_path = Path(model_path)
        
        if not model_path.exists():
            raise FileNotFoundError(f"模型路径不存在: {model_path}")
        
        logger.info(f"加载模型: {model_path}")
        
        try:
            # 加载模型
            model_file = model_path / "model.joblib"
            if model_file.exists():
                self.model = joblib.load(model_file)
                logger.info("模型加载成功")
            else:
                logger.warning(f"模型文件不存在: {model_file}")
            
            # 加载模型信息
            info_file = model_path / "model_info.json"
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    self.model_info = json.load(f)
                logger.info("模型信息加载成功")
            
            # 加载特征工程器
            self.feature_engineer = FeatureEngineer(self.fee_type)
            transformers_path = model_path.parent
            
            try:
                self.feature_engineer.load_transformers(str(transformers_path))
                logger.info("特征工程器加载成功")
            except FileNotFoundError:
                logger.warning("特征工程器文件不存在，将使用默认配置")
                self.feature_engineer = FeatureEngineer(self.fee_type)
        
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def preprocess_input(self, input_data: Union[pd.DataFrame, Dict[str, Any], List[Dict[str, Any]]]) -> pd.DataFrame:
        """
        预处理输入数据
        
        Args:
            input_data: 输入数据，可以是DataFrame、字典或字典列表
            
        Returns:
            预处理后的特征DataFrame
        """
        logger.info("预处理输入数据")
        
        # 转换输入数据为DataFrame
        if isinstance(input_data, dict):
            df = pd.DataFrame([input_data])
        elif isinstance(input_data, list):
            df = pd.DataFrame(input_data)
        elif isinstance(input_data, pd.DataFrame):
            df = input_data.copy()
        else:
            raise ValueError(f"不支持的输入数据类型: {type(input_data)}")
        
        # 验证必要字段
        required_fields = self.model_config.feature_columns
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            logger.warning(f"缺少必要字段: {missing_fields}")
            # 为缺少的字段填充默认值
            for field in missing_fields:
                if field in self.model_config.numerical_columns:
                    df[field] = 0.0
                elif field in self.model_config.categorical_columns:
                    df[field] = 'unknown'
                elif field in self.model_config.date_columns:
                    df[field] = '20240101'  # 默认日期
                else:
                    df[field] = None
        
        # 应用特征工程
        if self.feature_engineer:
            try:
                df_processed = self.feature_engineer.transform(df)
                logger.info(f"特征工程完成，特征数: {df_processed.shape[1]}")
                return df_processed
            except Exception as e:
                logger.error(f"特征工程失败: {e}")
                # 如果特征工程失败，返回原始数据的子集
                available_features = [col for col in required_fields if col in df.columns]
                return df[available_features]
        else:
            # 没有特征工程器，返回原始特征
            available_features = [col for col in required_fields if col in df.columns]
            return df[available_features]
    
    def predict(self, input_data: Union[pd.DataFrame, Dict[str, Any], List[Dict[str, Any]]]) -> np.ndarray:
        """
        进行预测
        
        Args:
            input_data: 输入数据
            
        Returns:
            预测结果数组
        """
        if self.model is None:
            raise ValueError("模型未加载，无法进行预测")
        
        logger.info("开始模型预测")
        
        # 预处理输入数据
        X_processed = self.preprocess_input(input_data)
        
        # 模型预测
        try:
            predictions = self.model.predict(X_processed)
            logger.info(f"预测完成，样本数: {len(predictions)}")
            return predictions
        except Exception as e:
            logger.error(f"模型预测失败: {e}")
            raise
    
    def predict_with_details(self, input_data: Union[pd.DataFrame, Dict[str, Any], List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        进行预测并返回详细信息
        
        Args:
            input_data: 输入数据
            
        Returns:
            包含预测结果和详细信息的字典
        """
        logger.info("开始详细预测")
        
        # 转换输入数据
        if isinstance(input_data, dict):
            original_df = pd.DataFrame([input_data])
        elif isinstance(input_data, list):
            original_df = pd.DataFrame(input_data)
        else:
            original_df = input_data.copy()
        
        # 预测
        predictions = self.predict(input_data)
        
        # 构建详细结果
        result = {
            'predictions': predictions.tolist(),
            'input_samples': len(original_df),
            'model_info': {
                'fee_type': self.fee_type,
                'model_type': self.model_info.get('model_type', 'unknown'),
                'training_samples': self.model_info.get('training_history', {}).get('training_samples', 0),
                'feature_count': self.model_info.get('training_history', {}).get('feature_count', 0)
            },
            'prediction_timestamp': datetime.now().isoformat()
        }
        
        # 添加特征重要性信息（如果可用）
        if hasattr(self.model, 'feature_importances_') and self.feature_engineer:
            feature_names = self.feature_engineer.feature_names
            if feature_names and len(feature_names) == len(self.model.feature_importances_):
                importance_dict = dict(zip(feature_names, self.model.feature_importances_))
                # 获取前10个重要特征
                top_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)[:10]
                result['top_features'] = dict(top_features)
        
        logger.info("详细预测完成")
        return result
    
    def batch_predict(self, input_file: str, output_file: str = None, 
                     batch_size: int = 1000) -> str:
        """
        批量预测
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            batch_size: 批处理大小
            
        Returns:
            输出文件路径
        """
        logger.info(f"开始批量预测: {input_file}")
        
        # 读取输入文件
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        if input_path.suffix.lower() == '.xlsx':
            df = pd.read_excel(input_file)
        elif input_path.suffix.lower() == '.csv':
            df = pd.read_csv(input_file)
        else:
            raise ValueError(f"不支持的文件格式: {input_path.suffix}")
        
        logger.info(f"读取数据完成，样本数: {len(df)}")
        
        # 设置输出文件路径
        if output_file is None:
            output_dir = project_root / "outputs" / "predictions"
            output_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = output_dir / f"{self.fee_type}_predictions_{timestamp}.csv"
        
        # 批量处理
        all_predictions = []
        total_batches = (len(df) + batch_size - 1) // batch_size
        
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_predictions = self.predict(batch_df)
            all_predictions.extend(batch_predictions)
            
            logger.info(f"处理批次 {i//batch_size + 1}/{total_batches}")
        
        # 构建结果DataFrame
        result_df = df.copy()
        result_df['predicted_amount'] = all_predictions
        result_df['prediction_timestamp'] = datetime.now().isoformat()
        
        # 保存结果
        result_df.to_csv(output_file, index=False, encoding='utf-8')
        
        logger.info(f"批量预测完成，结果保存到: {output_file}")
        return str(output_file)
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.model_info:
            return {
                'fee_type': self.fee_type,
                'model_loaded': self.model is not None,
                'feature_engineer_loaded': self.feature_engineer is not None
            }
        
        return self.model_info.copy()
    
    def is_ready(self) -> bool:
        """检查预测器是否就绪"""
        return self.model is not None
    
    def get_feature_requirements(self) -> Dict[str, List[str]]:
        """获取特征要求"""
        return {
            'required_features': self.model_config.feature_columns,
            'categorical_features': self.model_config.categorical_columns,
            'numerical_features': self.model_config.numerical_columns,
            'date_features': self.model_config.date_columns
        }
    
    def validate_input(self, input_data: Union[pd.DataFrame, Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            验证结果
        """
        if isinstance(input_data, dict):
            df = pd.DataFrame([input_data])
        else:
            df = input_data
        
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'missing_features': [],
            'invalid_values': {}
        }
        
        # 检查必要特征
        required_features = self.model_config.feature_columns
        missing_features = [f for f in required_features if f not in df.columns]
        
        if missing_features:
            validation_result['missing_features'] = missing_features
            validation_result['warnings'].append(f"缺少特征: {missing_features}")
        
        # 检查数值特征
        for col in self.model_config.numerical_columns:
            if col in df.columns:
                non_numeric = df[col].apply(lambda x: not isinstance(x, (int, float)) and not pd.isna(x))
                if non_numeric.any():
                    validation_result['invalid_values'][col] = 'contains_non_numeric_values'
        
        # 检查日期特征
        for col in self.model_config.date_columns:
            if col in df.columns:
                try:
                    if col in ['final_eff_date', 'final_exp_date']:
                        pd.to_datetime(df[col], format='%Y%m%d', errors='raise')
                    elif col in ['cur_year_month', 'run_time']:
                        pd.to_datetime(df[col], format='%Y%m', errors='raise')
                except:
                    validation_result['invalid_values'][col] = 'invalid_date_format'
        
        # 如果有错误或无效值，标记为无效
        if validation_result['invalid_values']:
            validation_result['is_valid'] = False
            validation_result['errors'].append("存在无效的数据值")
        
        return validation_result
