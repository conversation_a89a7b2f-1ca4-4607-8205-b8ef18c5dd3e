#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估器
提供全面的模型性能评估功能，包括各种回归指标和可视化
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger

logger = get_logger(__name__)


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, fee_type: str = 'fixed_fee'):
        """
        初始化模型评估器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.judgment_thresholds = self.config.get_judgment_thresholds()
        
        # 评估结果存储
        self.evaluation_results = {}
        self.detailed_predictions = None
        
        logger.info(f"初始化{fee_type}模型评估器")
    
    def calculate_regression_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算回归评估指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            评估指标字典
        """
        logger.info("计算回归评估指标")
        
        try:
            # 尝试使用sklearn
            from sklearn.metrics import (
                mean_absolute_error, mean_squared_error, r2_score,
                mean_absolute_percentage_error, explained_variance_score
            )
            
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_true, y_pred)
            
            try:
                mape = mean_absolute_percentage_error(y_true, y_pred) * 100
            except:
                # 手动计算MAPE
                mape = np.mean(np.abs((y_true - y_pred) / y_true.replace(0, np.nan))) * 100
            
            explained_var = explained_variance_score(y_true, y_pred)
            
        except ImportError:
            logger.info("sklearn不可用，使用手动计算")
            # 手动计算所有指标
            mae = np.mean(np.abs(y_true - y_pred))
            mse = np.mean((y_true - y_pred) ** 2)
            rmse = np.sqrt(mse)
            
            # R²计算
            ss_res = np.sum((y_true - y_pred) ** 2)
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
            
            # MAPE计算
            mape = np.mean(np.abs((y_true - y_pred) / y_true.replace(0, np.nan))) * 100
            
            # 解释方差
            var_y = np.var(y_true)
            var_residual = np.var(y_true - y_pred)
            explained_var = 1 - (var_residual / var_y) if var_y != 0 else 0
        
        # 计算额外指标
        median_ae = np.median(np.abs(y_true - y_pred))
        max_error = np.max(np.abs(y_true - y_pred))
        
        # 计算相对误差分布
        relative_errors = np.abs((y_true - y_pred) / y_true.replace(0, np.nan))
        relative_errors = relative_errors.dropna()
        
        metrics = {
            'mae': float(mae),
            'mse': float(mse),
            'rmse': float(rmse),
            'r2': float(r2),
            'mape': float(mape),
            'explained_variance': float(explained_var),
            'median_absolute_error': float(median_ae),
            'max_error': float(max_error),
            'mean_relative_error': float(np.mean(relative_errors)) if len(relative_errors) > 0 else 0,
            'std_relative_error': float(np.std(relative_errors)) if len(relative_errors) > 0 else 0
        }
        
        logger.info(f"回归指标计算完成 - MAE: {metrics['mae']:.4f}, R²: {metrics['r2']:.4f}")
        return metrics
    
    def calculate_business_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, Any]:
        """
        计算业务相关指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            业务指标字典
        """
        logger.info("计算业务相关指标")
        
        # 计算绝对误差和相对误差
        absolute_errors = np.abs(y_true - y_pred)
        relative_errors = np.abs((y_true - y_pred) / y_true.replace(0, np.nan))
        relative_errors = relative_errors.dropna()
        
        # 获取判定阈值
        abs_threshold = self.judgment_thresholds.get('absolute_threshold', 0.01)
        rel_threshold = self.judgment_thresholds.get('relative_threshold', 0.01)
        use_mixed = self.judgment_thresholds.get('use_mixed_threshold', True)
        
        # 计算准确性指标
        if use_mixed:
            # 混合阈值：绝对误差 <= abs_threshold OR 相对误差 <= rel_threshold
            accurate_mask = (absolute_errors <= abs_threshold) | (relative_errors <= rel_threshold)
        else:
            # 仅使用绝对阈值
            accurate_mask = absolute_errors <= abs_threshold
        
        accuracy_rate = np.mean(accurate_mask) * 100
        
        # 误差分布统计
        error_distribution = {
            'within_1_percent': np.mean(relative_errors <= 0.01) * 100 if len(relative_errors) > 0 else 0,
            'within_5_percent': np.mean(relative_errors <= 0.05) * 100 if len(relative_errors) > 0 else 0,
            'within_10_percent': np.mean(relative_errors <= 0.10) * 100 if len(relative_errors) > 0 else 0,
            'above_10_percent': np.mean(relative_errors > 0.10) * 100 if len(relative_errors) > 0 else 0
        }
        
        # 金额范围分析
        amount_ranges = {
            'small': (y_true <= 10),
            'medium': (y_true > 10) & (y_true <= 100),
            'large': (y_true > 100)
        }
        
        range_performance = {}
        for range_name, mask in amount_ranges.items():
            if mask.sum() > 0:
                range_mae = np.mean(absolute_errors[mask])
                range_accuracy = np.mean(accurate_mask[mask]) * 100
                range_performance[range_name] = {
                    'count': int(mask.sum()),
                    'mae': float(range_mae),
                    'accuracy_rate': float(range_accuracy)
                }
        
        business_metrics = {
            'accuracy_rate': float(accuracy_rate),
            'threshold_config': {
                'absolute_threshold': abs_threshold,
                'relative_threshold': rel_threshold,
                'use_mixed_threshold': use_mixed
            },
            'error_distribution': error_distribution,
            'amount_range_performance': range_performance,
            'total_samples': len(y_true),
            'accurate_samples': int(accurate_mask.sum()),
            'inaccurate_samples': int((~accurate_mask).sum())
        }
        
        logger.info(f"业务指标计算完成 - 准确率: {accuracy_rate:.2f}%")
        return business_metrics
    
    def analyze_prediction_errors(self, y_true: pd.Series, y_pred: np.ndarray, 
                                 passthrough_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        分析预测误差
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            passthrough_data: 透传数据（用于错误分析）
            
        Returns:
            误差分析结果
        """
        logger.info("分析预测误差")
        
        # 计算误差
        errors = y_pred - y_true
        absolute_errors = np.abs(errors)
        relative_errors = errors / y_true.replace(0, np.nan)
        
        # 创建详细预测结果DataFrame
        detailed_results = pd.DataFrame({
            'true_value': y_true,
            'predicted_value': y_pred,
            'error': errors,
            'absolute_error': absolute_errors,
            'relative_error': relative_errors
        })
        
        # 添加透传数据
        if passthrough_data is not None:
            detailed_results = pd.concat([detailed_results, passthrough_data], axis=1)
        
        # 保存详细结果
        self.detailed_predictions = detailed_results
        
        # 误差统计
        error_stats = {
            'mean_error': float(np.mean(errors)),
            'std_error': float(np.std(errors)),
            'min_error': float(np.min(errors)),
            'max_error': float(np.max(errors)),
            'median_error': float(np.median(errors)),
            'q25_error': float(np.percentile(errors, 25)),
            'q75_error': float(np.percentile(errors, 75))
        }
        
        # 识别异常预测
        error_threshold = np.percentile(absolute_errors, 95)  # 95分位数作为异常阈值
        anomaly_mask = absolute_errors > error_threshold
        
        anomaly_analysis = {
            'anomaly_threshold': float(error_threshold),
            'anomaly_count': int(anomaly_mask.sum()),
            'anomaly_rate': float(np.mean(anomaly_mask) * 100),
            'anomaly_samples': detailed_results[anomaly_mask].head(10).to_dict('records') if anomaly_mask.sum() > 0 else []
        }
        
        # 预测偏差分析
        overestimate_mask = errors > 0
        underestimate_mask = errors < 0
        
        bias_analysis = {
            'overestimate_count': int(overestimate_mask.sum()),
            'underestimate_count': int(underestimate_mask.sum()),
            'overestimate_rate': float(np.mean(overestimate_mask) * 100),
            'underestimate_rate': float(np.mean(underestimate_mask) * 100),
            'mean_overestimate': float(np.mean(errors[overestimate_mask])) if overestimate_mask.sum() > 0 else 0,
            'mean_underestimate': float(np.mean(errors[underestimate_mask])) if underestimate_mask.sum() > 0 else 0
        }
        
        error_analysis = {
            'error_statistics': error_stats,
            'anomaly_analysis': anomaly_analysis,
            'bias_analysis': bias_analysis,
            'sample_count': len(y_true)
        }
        
        logger.info(f"误差分析完成 - 异常样本: {anomaly_analysis['anomaly_count']}")
        return error_analysis
    
    def comprehensive_evaluation(self, model: Any, X_test: pd.DataFrame, y_test: pd.Series,
                               passthrough_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        综合模型评估
        
        Args:
            model: 训练好的模型
            X_test: 测试特征
            y_test: 测试目标
            passthrough_data: 透传数据
            
        Returns:
            综合评估结果
        """
        logger.info("开始综合模型评估")
        
        # 模型预测
        y_pred = model.predict(X_test)
        
        # 计算各类指标
        regression_metrics = self.calculate_regression_metrics(y_test, y_pred)
        business_metrics = self.calculate_business_metrics(y_test, y_pred)
        error_analysis = self.analyze_prediction_errors(y_test, y_pred, passthrough_data)
        
        # 综合评估结果
        evaluation_result = {
            'model_info': {
                'fee_type': self.fee_type,
                'test_samples': len(X_test),
                'feature_count': X_test.shape[1],
                'evaluation_timestamp': datetime.now().isoformat()
            },
            'regression_metrics': regression_metrics,
            'business_metrics': business_metrics,
            'error_analysis': error_analysis,
            'overall_score': self._calculate_overall_score(regression_metrics, business_metrics)
        }
        
        # 保存评估结果
        self.evaluation_results = evaluation_result
        
        logger.info(f"综合评估完成 - 总体得分: {evaluation_result['overall_score']:.2f}")
        return evaluation_result
    
    def _calculate_overall_score(self, regression_metrics: Dict[str, float], 
                               business_metrics: Dict[str, Any]) -> float:
        """
        计算综合评分
        
        Args:
            regression_metrics: 回归指标
            business_metrics: 业务指标
            
        Returns:
            综合评分 (0-100)
        """
        # 权重配置
        weights = {
            'r2': 0.3,           # R²得分权重
            'mae': 0.2,          # MAE得分权重  
            'accuracy': 0.4,     # 业务准确率权重
            'stability': 0.1     # 稳定性权重
        }
        
        # R²得分 (0-100)
        r2_score = max(0, min(100, regression_metrics['r2'] * 100))
        
        # MAE得分 (基于相对误差，越小越好)
        mae_relative = regression_metrics['mae'] / np.mean([1, 10, 100])  # 假设平均金额范围
        mae_score = max(0, min(100, (1 - mae_relative) * 100))
        
        # 业务准确率得分
        accuracy_score = business_metrics['accuracy_rate']
        
        # 稳定性得分 (基于误差标准差)
        stability_score = max(0, min(100, 100 - regression_metrics['std_relative_error'] * 100))
        
        # 综合得分
        overall_score = (
            weights['r2'] * r2_score +
            weights['mae'] * mae_score +
            weights['accuracy'] * accuracy_score +
            weights['stability'] * stability_score
        )
        
        return float(overall_score)
    
    def save_evaluation_results(self, save_path: str = None) -> str:
        """
        保存评估结果
        
        Args:
            save_path: 保存路径
            
        Returns:
            实际保存路径
        """
        if not self.evaluation_results:
            raise ValueError("没有评估结果可保存")
        
        if save_path is None:
            results_dir = project_root / "outputs" / "reports"
            results_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = results_dir / f"{self.fee_type}_evaluation_{timestamp}.json"
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(self.evaluation_results, f, ensure_ascii=False, indent=2)
        
        # 保存详细预测结果
        if self.detailed_predictions is not None:
            detail_path = Path(save_path).with_suffix('.csv')
            self.detailed_predictions.to_csv(detail_path, index=False, encoding='utf-8')
            logger.info(f"详细预测结果已保存: {detail_path}")
        
        logger.info(f"评估结果已保存: {save_path}")
        return str(save_path)
    
    def get_evaluation_summary(self) -> str:
        """获取评估结果摘要"""
        if not self.evaluation_results:
            return "暂无评估结果"
        
        metrics = self.evaluation_results['regression_metrics']
        business = self.evaluation_results['business_metrics']
        
        summary = f"""
模型评估摘要 ({self.fee_type})
{'='*50}
📊 回归指标:
  - MAE (平均绝对误差): {metrics['mae']:.4f}
  - RMSE (均方根误差): {metrics['rmse']:.4f}
  - R² (决定系数): {metrics['r2']:.4f}
  - MAPE (平均绝对百分比误差): {metrics['mape']:.2f}%

💼 业务指标:
  - 准确率: {business['accuracy_rate']:.2f}%
  - 测试样本数: {business['total_samples']}
  - 准确样本数: {business['accurate_samples']}

🎯 综合得分: {self.evaluation_results['overall_score']:.2f}/100
        """
        
        return summary.strip()
