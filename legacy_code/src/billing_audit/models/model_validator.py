#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型验证器
提供模型验证、交叉验证和稳定性测试功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
import json
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger

logger = get_logger(__name__)


class ModelValidator:
    """模型验证器"""
    
    def __init__(self, fee_type: str = 'fixed_fee'):
        """
        初始化模型验证器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.cv_config = self.config.get('billing_audit.model_params.cross_validation', {})
        
        # 验证结果存储
        self.validation_results = {}
        
        logger.info(f"初始化{fee_type}模型验证器")
    
    def cross_validate(self, model_class: Any, X: pd.DataFrame, y: pd.Series,
                      cv_folds: int = None, scoring: str = None,
                      model_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        交叉验证
        
        Args:
            model_class: 模型类
            X: 特征数据
            y: 目标变量
            cv_folds: 交叉验证折数
            scoring: 评分指标
            model_params: 模型参数
            
        Returns:
            交叉验证结果
        """
        if cv_folds is None:
            cv_folds = self.cv_config.get('cv_folds', 5)
        
        if scoring is None:
            scoring = self.cv_config.get('scoring', 'neg_mean_absolute_error')
        
        if model_params is None:
            model_params = {}
        
        logger.info(f"开始{cv_folds}折交叉验证")
        
        try:
            from sklearn.model_selection import cross_val_score, KFold
            
            # 创建交叉验证对象
            kfold = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
            
            # 创建模型实例
            model = model_class(**model_params)
            
            # 执行交叉验证
            cv_scores = cross_val_score(model, X, y, cv=kfold, scoring=scoring, n_jobs=-1)
            
            # 计算统计信息
            cv_results = {
                'method': 'sklearn_cross_validation',
                'cv_folds': cv_folds,
                'scoring': scoring,
                'scores': cv_scores.tolist(),
                'mean_score': float(np.mean(cv_scores)),
                'std_score': float(np.std(cv_scores)),
                'min_score': float(np.min(cv_scores)),
                'max_score': float(np.max(cv_scores)),
                'confidence_interval': self._calculate_confidence_interval(cv_scores)
            }
            
        except ImportError:
            logger.warning("sklearn不可用，使用手动交叉验证")
            cv_results = self._manual_cross_validate(model_class, X, y, cv_folds, model_params)
        
        logger.info(f"交叉验证完成 - 平均得分: {cv_results['mean_score']:.4f} ± {cv_results['std_score']:.4f}")
        return cv_results
    
    def _manual_cross_validate(self, model_class: Any, X: pd.DataFrame, y: pd.Series,
                              cv_folds: int, model_params: Dict[str, Any]) -> Dict[str, Any]:
        """手动实现交叉验证"""
        n_samples = len(X)
        fold_size = n_samples // cv_folds
        
        scores = []
        
        for fold in range(cv_folds):
            # 划分训练集和验证集
            start_idx = fold * fold_size
            end_idx = start_idx + fold_size if fold < cv_folds - 1 else n_samples
            
            val_indices = list(range(start_idx, end_idx))
            train_indices = [i for i in range(n_samples) if i not in val_indices]
            
            X_train = X.iloc[train_indices]
            X_val = X.iloc[val_indices]
            y_train = y.iloc[train_indices]
            y_val = y.iloc[val_indices]
            
            # 训练模型
            model = model_class(**model_params)
            model.fit(X_train, y_train)
            
            # 预测和评分
            y_pred = model.predict(X_val)
            score = -np.mean(np.abs(y_val - y_pred))  # 负MAE
            scores.append(score)
        
        scores = np.array(scores)
        
        return {
            'method': 'manual_cross_validation',
            'cv_folds': cv_folds,
            'scoring': 'neg_mean_absolute_error',
            'scores': scores.tolist(),
            'mean_score': float(np.mean(scores)),
            'std_score': float(np.std(scores)),
            'min_score': float(np.min(scores)),
            'max_score': float(np.max(scores)),
            'confidence_interval': self._calculate_confidence_interval(scores)
        }
    
    def _calculate_confidence_interval(self, scores: np.ndarray, confidence: float = 0.95) -> Dict[str, float]:
        """计算置信区间"""
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        n = len(scores)
        
        # 使用t分布计算置信区间
        try:
            from scipy import stats
            t_value = stats.t.ppf((1 + confidence) / 2, n - 1)
        except ImportError:
            # 如果scipy不可用，使用近似值
            t_value = 2.0  # 近似95%置信区间的t值
        
        margin_error = t_value * (std_score / np.sqrt(n))
        
        return {
            'confidence_level': confidence,
            'lower_bound': float(mean_score - margin_error),
            'upper_bound': float(mean_score + margin_error),
            'margin_error': float(margin_error)
        }
    
    def stability_test(self, model_class: Any, X: pd.DataFrame, y: pd.Series,
                      n_runs: int = 10, model_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        模型稳定性测试
        
        Args:
            model_class: 模型类
            X: 特征数据
            y: 目标变量
            n_runs: 运行次数
            model_params: 模型参数
            
        Returns:
            稳定性测试结果
        """
        if model_params is None:
            model_params = {}
        
        logger.info(f"开始模型稳定性测试，运行{n_runs}次")
        
        # 存储每次运行的结果
        run_results = []
        
        for run in range(n_runs):
            try:
                # 随机划分数据
                from sklearn.model_selection import train_test_split
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=run
                )
            except ImportError:
                # 手动划分
                n_test = int(len(X) * 0.2)
                np.random.seed(run)
                test_indices = np.random.choice(len(X), n_test, replace=False)
                train_indices = [i for i in range(len(X)) if i not in test_indices]
                
                X_train = X.iloc[train_indices]
                X_test = X.iloc[test_indices]
                y_train = y.iloc[train_indices]
                y_test = y.iloc[test_indices]
            
            # 训练模型
            model = model_class(**model_params)
            model.fit(X_train, y_train)
            
            # 评估模型
            y_pred = model.predict(X_test)
            
            # 计算指标
            mae = np.mean(np.abs(y_test - y_pred))
            mse = np.mean((y_test - y_pred) ** 2)
            rmse = np.sqrt(mse)
            
            # R²计算
            ss_res = np.sum((y_test - y_pred) ** 2)
            ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
            
            run_results.append({
                'run': run + 1,
                'mae': float(mae),
                'mse': float(mse),
                'rmse': float(rmse),
                'r2': float(r2),
                'train_samples': len(X_train),
                'test_samples': len(X_test)
            })
        
        # 计算稳定性统计
        metrics = ['mae', 'mse', 'rmse', 'r2']
        stability_stats = {}
        
        for metric in metrics:
            values = [result[metric] for result in run_results]
            stability_stats[metric] = {
                'mean': float(np.mean(values)),
                'std': float(np.std(values)),
                'min': float(np.min(values)),
                'max': float(np.max(values)),
                'coefficient_of_variation': float(np.std(values) / np.mean(values)) if np.mean(values) != 0 else 0
            }
        
        # 计算整体稳定性得分
        cv_scores = [stability_stats[metric]['coefficient_of_variation'] for metric in metrics]
        stability_score = max(0, 100 - np.mean(cv_scores) * 100)  # 变异系数越小，稳定性越好
        
        stability_result = {
            'n_runs': n_runs,
            'run_results': run_results,
            'stability_statistics': stability_stats,
            'stability_score': float(stability_score),
            'is_stable': stability_score > 80  # 稳定性阈值
        }
        
        logger.info(f"稳定性测试完成 - 稳定性得分: {stability_score:.2f}")
        return stability_result
    
    def data_drift_detection(self, X_train: pd.DataFrame, X_new: pd.DataFrame) -> Dict[str, Any]:
        """
        数据漂移检测
        
        Args:
            X_train: 训练数据特征
            X_new: 新数据特征
            
        Returns:
            数据漂移检测结果
        """
        logger.info("开始数据漂移检测")
        
        drift_results = {}
        
        # 检查特征分布变化
        for column in X_train.columns:
            if column in X_new.columns:
                train_values = X_train[column].dropna()
                new_values = X_new[column].dropna()
                
                if pd.api.types.is_numeric_dtype(train_values):
                    # 数值特征：比较统计量
                    drift_info = {
                        'feature_type': 'numerical',
                        'train_stats': {
                            'mean': float(train_values.mean()),
                            'std': float(train_values.std()),
                            'min': float(train_values.min()),
                            'max': float(train_values.max())
                        },
                        'new_stats': {
                            'mean': float(new_values.mean()),
                            'std': float(new_values.std()),
                            'min': float(new_values.min()),
                            'max': float(new_values.max())
                        }
                    }
                    
                    # 计算统计差异
                    mean_diff = abs(drift_info['new_stats']['mean'] - drift_info['train_stats']['mean'])
                    std_diff = abs(drift_info['new_stats']['std'] - drift_info['train_stats']['std'])
                    
                    # 简单的漂移判定（可以改进为更复杂的统计检验）
                    drift_info['drift_detected'] = (
                        mean_diff > drift_info['train_stats']['std'] * 0.5 or
                        std_diff > drift_info['train_stats']['std'] * 0.3
                    )
                    
                else:
                    # 类别特征：比较分布
                    train_dist = train_values.value_counts(normalize=True)
                    new_dist = new_values.value_counts(normalize=True)
                    
                    # 计算分布差异
                    all_categories = set(train_dist.index) | set(new_dist.index)
                    distribution_diff = 0
                    
                    for cat in all_categories:
                        train_prob = train_dist.get(cat, 0)
                        new_prob = new_dist.get(cat, 0)
                        distribution_diff += abs(train_prob - new_prob)
                    
                    drift_info = {
                        'feature_type': 'categorical',
                        'train_distribution': train_dist.to_dict(),
                        'new_distribution': new_dist.to_dict(),
                        'distribution_difference': float(distribution_diff),
                        'drift_detected': distribution_diff > 0.2  # 阈值可调整
                    }
                
                drift_results[column] = drift_info
        
        # 计算整体漂移得分
        drift_features = [col for col, info in drift_results.items() if info['drift_detected']]
        drift_score = len(drift_features) / len(drift_results) * 100 if drift_results else 0
        
        overall_result = {
            'feature_drift_results': drift_results,
            'drift_features': drift_features,
            'drift_score': float(drift_score),
            'overall_drift_detected': drift_score > 20,  # 20%特征漂移作为阈值
            'total_features': len(drift_results),
            'drifted_features': len(drift_features)
        }
        
        logger.info(f"数据漂移检测完成 - 漂移得分: {drift_score:.2f}%")
        return overall_result
    
    def comprehensive_validation(self, model_class: Any, X: pd.DataFrame, y: pd.Series,
                               model_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        综合模型验证
        
        Args:
            model_class: 模型类
            X: 特征数据
            y: 目标变量
            model_params: 模型参数
            
        Returns:
            综合验证结果
        """
        logger.info("开始综合模型验证")
        
        # 交叉验证
        cv_results = self.cross_validate(model_class, X, y, model_params=model_params)
        
        # 稳定性测试
        stability_results = self.stability_test(model_class, X, y, model_params=model_params)
        
        # 综合验证结果
        validation_result = {
            'validation_info': {
                'fee_type': self.fee_type,
                'samples': len(X),
                'features': X.shape[1],
                'validation_timestamp': datetime.now().isoformat()
            },
            'cross_validation': cv_results,
            'stability_test': stability_results,
            'overall_validation_score': self._calculate_validation_score(cv_results, stability_results)
        }
        
        # 保存验证结果
        self.validation_results = validation_result
        
        logger.info(f"综合验证完成 - 验证得分: {validation_result['overall_validation_score']:.2f}")
        return validation_result
    
    def _calculate_validation_score(self, cv_results: Dict[str, Any], 
                                  stability_results: Dict[str, Any]) -> float:
        """
        计算综合验证得分
        
        Args:
            cv_results: 交叉验证结果
            stability_results: 稳定性测试结果
            
        Returns:
            综合验证得分 (0-100)
        """
        # 交叉验证得分 (基于平均得分，转换为0-100)
        cv_score = max(0, min(100, (1 + cv_results['mean_score']) * 50))  # 假设得分范围-1到1
        
        # 稳定性得分
        stability_score = stability_results['stability_score']
        
        # 一致性得分 (基于交叉验证标准差，越小越好)
        consistency_score = max(0, min(100, 100 - cv_results['std_score'] * 100))
        
        # 综合得分
        overall_score = (cv_score * 0.5 + stability_score * 0.3 + consistency_score * 0.2)
        
        return float(overall_score)
    
    def save_validation_results(self, save_path: str = None) -> str:
        """
        保存验证结果
        
        Args:
            save_path: 保存路径
            
        Returns:
            实际保存路径
        """
        if not self.validation_results:
            raise ValueError("没有验证结果可保存")
        
        if save_path is None:
            results_dir = project_root / "outputs" / "reports"
            results_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = results_dir / f"{self.fee_type}_validation_{timestamp}.json"
        
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if hasattr(obj, 'item'):
                return obj.item()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            else:
                return obj

        serializable_results = convert_numpy_types(self.validation_results)

        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"验证结果已保存: {save_path}")
        return str(save_path)
