#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收费稽核数据预处理器
处理固费和优惠费数据的加载、清洗和基础预处理
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger, DataValidator, DataLoader, DataCleaner

logger = get_logger(__name__)


class BillingDataPreprocessor:
    """收费稽核数据预处理器"""
    
    def __init__(self, fee_type: str = 'fixed_fee'):
        """
        初始化预处理器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.model_config = self.config.get_model_config('billing_audit', fee_type)
        self.preprocessing_config = self.config.get_preprocessing_config()
        
        logger.info(f"初始化{fee_type}数据预处理器")
    
    def load_data(self, file_path: str = None) -> pd.DataFrame:
        """
        加载数据
        
        Args:
            file_path: 数据文件路径，如果为None则使用配置中的路径
            
        Returns:
            原始数据DataFrame
        """
        if file_path is None:
            if self.fee_type == 'fixed_fee':
                file_path = self.config.get_data_source('fixed_fee_sample')
            else:
                file_path = self.config.get_data_source('discount_sample')
        
        # 转换为绝对路径
        if not Path(file_path).is_absolute():
            file_path = project_root / file_path
        
        logger.info(f"加载{self.fee_type}数据: {file_path}")
        
        try:
            df = DataLoader.load_excel(str(file_path))
            logger.info(f"成功加载数据: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        验证数据质量
        
        Args:
            df: 待验证的DataFrame
            
        Returns:
            验证结果
        """
        logger.info("开始数据质量验证")
        
        # 基础验证
        required_columns = self.model_config.feature_columns + [self.model_config.target_column]
        validation_result = DataValidator.validate_dataframe(df, required_columns)
        
        # 验证数值列
        for col in self.model_config.numerical_columns:
            if col in df.columns:
                col_result = DataValidator.validate_numerical_column(df[col], col)
                validation_result[f'{col}_validation'] = col_result
        
        # 验证日期列
        for col in self.model_config.date_columns:
            if col in df.columns:
                validation_result[f'{col}_date_validation'] = self._validate_date_column(df[col], col)

        # 验证新的年月日字段
        year_fields = ['final_eff_year', 'final_exp_year']
        month_fields = ['final_eff_mon', 'final_exp_mon']
        day_fields = ['final_eff_day', 'final_exp_day']

        for field in year_fields:
            if field in df.columns:
                validation_result[f'{field}_validation'] = self._validate_year_field(df[field], field)

        for field in month_fields:
            if field in df.columns:
                validation_result[f'{field}_validation'] = self._validate_month_field(df[field], field)

        for field in day_fields:
            if field in df.columns:
                validation_result[f'{field}_validation'] = self._validate_day_field(df[field], field)
        
        # 验证目标变量
        if self.model_config.target_column in df.columns:
            target_validation = DataValidator.validate_numerical_column(
                df[self.model_config.target_column], 
                self.model_config.target_column
            )
            validation_result['target_validation'] = target_validation
        
        logger.info(f"数据验证完成: {'通过' if validation_result['is_valid'] else '失败'}")
        return validation_result
    
    def _validate_date_column(self, series: pd.Series, col_name: str) -> Dict[str, Any]:
        """验证日期列"""
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'stats': {}
        }
        
        try:
            # 尝试转换为日期
            if col_name in ['cur_year_month', 'run_time']:
                # yyyymm格式
                date_series = pd.to_datetime(series, format='%Y%m', errors='coerce')
            else:
                date_series = pd.to_datetime(series, errors='coerce')
            
            # 统计无效日期
            invalid_count = date_series.isnull().sum() - series.isnull().sum()
            if invalid_count > 0:
                result['warnings'].append(f"列 '{col_name}' 有 {invalid_count} 个无效日期")
            
            # 基本统计
            valid_dates = date_series.dropna()
            if len(valid_dates) > 0:
                result['stats'] = {
                    'min_date': valid_dates.min(),
                    'max_date': valid_dates.max(),
                    'date_range_days': (valid_dates.max() - valid_dates.min()).days
                }
        
        except Exception as e:
            result['errors'].append(f"日期列 '{col_name}' 验证失败: {e}")
            result['is_valid'] = False
        
        return result

    def _validate_year_field(self, series: pd.Series, col_name: str) -> Dict[str, Any]:
        """验证年份字段"""
        result = {'is_valid': True, 'errors': [], 'warnings': [], 'stats': {}}

        try:
            # 检查年份范围 (2020-2030)
            valid_years = series.dropna()
            if len(valid_years) > 0:
                invalid_years = valid_years[(valid_years < 2020) | (valid_years > 2030)]
                if len(invalid_years) > 0:
                    result['warnings'].append(f"列 '{col_name}' 有 {len(invalid_years)} 个年份超出合理范围(2020-2030)")

                result['stats'] = {
                    'min_year': int(valid_years.min()),
                    'max_year': int(valid_years.max()),
                    'unique_years': len(valid_years.unique())
                }
        except Exception as e:
            result['is_valid'] = False
            result['errors'].append(f"年份字段 '{col_name}' 验证失败: {e}")

        return result

    def _validate_month_field(self, series: pd.Series, col_name: str) -> Dict[str, Any]:
        """验证月份字段"""
        result = {'is_valid': True, 'errors': [], 'warnings': [], 'stats': {}}

        try:
            # 检查月份范围 (1-12)
            valid_months = series.dropna()
            if len(valid_months) > 0:
                invalid_months = valid_months[(valid_months < 1) | (valid_months > 12)]
                if len(invalid_months) > 0:
                    result['is_valid'] = False
                    result['errors'].append(f"列 '{col_name}' 有 {len(invalid_months)} 个无效月份")

                result['stats'] = {
                    'min_month': int(valid_months.min()),
                    'max_month': int(valid_months.max()),
                    'unique_months': len(valid_months.unique())
                }
        except Exception as e:
            result['is_valid'] = False
            result['errors'].append(f"月份字段 '{col_name}' 验证失败: {e}")

        return result

    def _validate_day_field(self, series: pd.Series, col_name: str) -> Dict[str, Any]:
        """验证日期字段"""
        result = {'is_valid': True, 'errors': [], 'warnings': [], 'stats': {}}

        try:
            # 检查日期范围 (1-31)
            valid_days = series.dropna()
            if len(valid_days) > 0:
                invalid_days = valid_days[(valid_days < 1) | (valid_days > 31)]
                if len(invalid_days) > 0:
                    result['is_valid'] = False
                    result['errors'].append(f"列 '{col_name}' 有 {len(invalid_days)} 个无效日期")

                result['stats'] = {
                    'min_day': int(valid_days.min()),
                    'max_day': int(valid_days.max()),
                    'unique_days': len(valid_days.unique())
                }
        except Exception as e:
            result['is_valid'] = False
            result['errors'].append(f"日期字段 '{col_name}' 验证失败: {e}")

        return result

    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗
        
        Args:
            df: 原始数据
            
        Returns:
            清洗后的数据
        """
        logger.info("开始数据清洗")
        df_cleaned = df.copy()
        
        # 处理缺失值
        missing_strategy = self.preprocessing_config.get('missing_value_strategy', {})
        
        # 为不同类型的列设置不同的缺失值处理策略
        column_strategies = {}
        
        # 数值列使用中位数填充
        for col in self.model_config.numerical_columns:
            if col in df_cleaned.columns:
                column_strategies[col] = missing_strategy.get('numerical', 'median')
        
        # 类别列使用众数填充
        for col in self.model_config.categorical_columns:
            if col in df_cleaned.columns:
                column_strategies[col] = missing_strategy.get('categorical', 'mode')
        
        # 日期列删除缺失值
        for col in self.model_config.date_columns:
            if col in df_cleaned.columns:
                column_strategies[col] = 'drop'
        
        # 目标变量删除缺失值
        if self.model_config.target_column in df_cleaned.columns:
            column_strategies[self.model_config.target_column] = 'drop'
        
        df_cleaned = DataCleaner.handle_missing_values(df_cleaned, column_strategies)
        
        # 移除异常值（仅对数值列）
        outlier_config = self.preprocessing_config.get('outlier_detection', {})
        if outlier_config.get('method') == 'iqr':
            numerical_cols = [col for col in self.model_config.numerical_columns if col in df_cleaned.columns]
            if self.model_config.target_column in df_cleaned.columns:
                numerical_cols.append(self.model_config.target_column)
            
            df_cleaned = DataCleaner.remove_outliers(
                df_cleaned,
                columns=numerical_cols,
                method='iqr',
                multiplier=outlier_config.get('iqr_multiplier', 1.5)
            )
        
        logger.info(f"数据清洗完成: {df_cleaned.shape}")
        return df_cleaned
    
    def prepare_features_and_target(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """
        准备特征和目标变量
        
        Args:
            df: 清洗后的数据
            
        Returns:
            (特征DataFrame, 目标Series)
        """
        logger.info("准备特征和目标变量")
        
        # 提取特征列
        feature_cols = [col for col in self.model_config.feature_columns if col in df.columns]
        X = df[feature_cols].copy()
        
        # 提取目标变量
        if self.model_config.target_column not in df.columns:
            raise ValueError(f"目标变量 '{self.model_config.target_column}' 不存在")
        
        y = df[self.model_config.target_column].copy()
        
        logger.info(f"特征维度: {X.shape}, 目标变量长度: {len(y)}")
        return X, y
    
    def get_passthrough_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        获取透传数据（用于结果追溯）
        
        Args:
            df: 原始数据
            
        Returns:
            透传数据DataFrame
        """
        passthrough_cols = [col for col in self.model_config.passthrough_columns if col in df.columns]
        if passthrough_cols:
            return df[passthrough_cols].copy()
        else:
            return pd.DataFrame(index=df.index)
    
    def process(self, file_path: str = None) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame]:
        """
        完整的数据预处理流程
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            (特征DataFrame, 目标Series, 透传数据DataFrame)
        """
        logger.info(f"开始{self.fee_type}数据预处理流程")
        
        # 1. 加载数据
        df = self.load_data(file_path)
        
        # 2. 验证数据
        validation_result = self.validate_data(df)
        if not validation_result['is_valid']:
            logger.warning("数据验证未通过，但继续处理")
            for error in validation_result['errors']:
                logger.error(error)
        
        # 3. 清洗数据
        df_cleaned = self.clean_data(df)
        
        # 4. 准备特征和目标变量
        X, y = self.prepare_features_and_target(df_cleaned)
        
        # 5. 获取透传数据
        passthrough_data = self.get_passthrough_data(df_cleaned)
        
        logger.info("数据预处理流程完成")
        return X, y, passthrough_data
