#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征工程模块
处理日期特征、类别编码、数值标准化等特征工程任务
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.compose import ColumnTransformer
import joblib
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger

logger = get_logger(__name__)


class FeatureEngineer:
    """特征工程器"""
    
    def __init__(self, fee_type: str = 'fixed_fee'):
        """
        初始化特征工程器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.model_config = self.config.get_model_config('billing_audit', fee_type)
        self.preprocessing_config = self.config.get_preprocessing_config()
        
        # 存储转换器
        self.transformers = {}
        self.feature_names = []
        
        logger.info(f"初始化{fee_type}特征工程器")
    
    def create_date_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建日期特征
        
        Args:
            df: 输入DataFrame
            
        Returns:
            包含日期特征的DataFrame
        """
        logger.info("创建日期特征")
        df_features = df.copy()
        
        # 处理yyyymm格式的日期字段
        for col in self.model_config.date_columns:
            if col not in df.columns:
                continue

            try:
                if col in ['cur_year_month', 'run_time']:
                    # yyyymm格式
                    date_series = pd.to_datetime(df[col], format='%Y%m', errors='coerce')

                    # 提取日期特征
                    df_features[f'{col}_year'] = date_series.dt.year
                    df_features[f'{col}_month'] = date_series.dt.month
                    df_features[f'{col}_quarter'] = date_series.dt.quarter

                    # 删除原始日期列
                    df_features.drop(col, axis=1, inplace=True)

            except Exception as e:
                logger.warning(f"处理日期列 {col} 失败: {e}")

        # 处理已拆分的年月日字段，创建组合特征
        if all(col in df.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']):
            try:
                # 组合生效日期
                eff_date = pd.to_datetime(
                    df[['final_eff_year', 'final_eff_mon', 'final_eff_day']].rename(
                        columns={'final_eff_year': 'year', 'final_eff_mon': 'month', 'final_eff_day': 'day'}
                    ), errors='coerce'
                )

                # 创建生效日期的额外特征
                df_features['final_eff_dayofweek'] = eff_date.dt.dayofweek
                df_features['final_eff_quarter'] = eff_date.dt.quarter
                df_features['final_eff_is_weekend'] = (eff_date.dt.dayofweek >= 5).astype(int)

            except Exception as e:
                logger.warning(f"处理生效日期组合特征失败: {e}")

        if all(col in df.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day']):
            try:
                # 组合失效日期
                exp_date = pd.to_datetime(
                    df[['final_exp_year', 'final_exp_mon', 'final_exp_day']].rename(
                        columns={'final_exp_year': 'year', 'final_exp_mon': 'month', 'final_exp_day': 'day'}
                    ), errors='coerce'
                )

                # 创建失效日期的额外特征
                df_features['final_exp_dayofweek'] = exp_date.dt.dayofweek
                df_features['final_exp_quarter'] = exp_date.dt.quarter
                df_features['final_exp_is_weekend'] = (exp_date.dt.dayofweek >= 5).astype(int)

            except Exception as e:
                logger.warning(f"处理失效日期组合特征失败: {e}")
        
        # 创建基于新字段结构的组合日期特征
        if (all(col in df.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']) and
            all(col in df.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day'])):
            try:
                # 组合生效和失效日期
                eff_date = pd.to_datetime(
                    df[['final_eff_year', 'final_eff_mon', 'final_eff_day']].rename(
                        columns={'final_eff_year': 'year', 'final_eff_mon': 'month', 'final_eff_day': 'day'}
                    ), errors='coerce'
                )
                exp_date = pd.to_datetime(
                    df[['final_exp_year', 'final_exp_mon', 'final_exp_day']].rename(
                        columns={'final_exp_year': 'year', 'final_exp_mon': 'month', 'final_exp_day': 'day'}
                    ), errors='coerce'
                )

                # 计算订阅时长（天数）
                df_features['subscription_duration_days'] = (exp_date - eff_date).dt.days

                # 计算当前月份与生效月份的差异
                if 'cur_year_month' in df.columns:
                    cur_date = pd.to_datetime(df['cur_year_month'], format='%Y%m', errors='coerce')
                    df_features['months_since_effective'] = (
                        (cur_date.dt.year - eff_date.dt.year) * 12 +
                        (cur_date.dt.month - eff_date.dt.month)
                    )

                    # 计算距离失效的月数
                    df_features['months_until_expiry'] = (
                        (exp_date.dt.year - cur_date.dt.year) * 12 +
                        (exp_date.dt.month - cur_date.dt.month)
                    )

                # 创建生效和失效的季度差异
                df_features['quarter_diff'] = (
                    exp_date.dt.quarter - eff_date.dt.quarter +
                    (exp_date.dt.year - eff_date.dt.year) * 4
                )

            except Exception as e:
                logger.warning(f"创建组合日期特征失败: {e}")
        
        # 创建计费相关特征
        if self.fee_type == 'fixed_fee':
            if 'charge_day_count' in df.columns and 'month_day_count' in df.columns:
                # 计费天数比例
                df_features['charge_day_ratio'] = df['charge_day_count'] / df['month_day_count']
                
            if 'should_fee' in df.columns and 'charge_day_count' in df.columns:
                # 日均应收费
                df_features['daily_should_fee'] = df['should_fee'] / df['charge_day_count'].replace(0, 1)
        
        elif self.fee_type == 'discount':
            if 'fav_cal_fee' in df.columns and 'fav_value' in df.columns:
                # 优惠比例
                df_features['discount_ratio'] = df['fav_value'] / df['fav_cal_fee'].replace(0, 1)
        
        logger.info(f"日期特征创建完成，新增特征数: {df_features.shape[1] - df.shape[1]}")
        return df_features
    
    def encode_categorical_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """
        编码类别特征
        
        Args:
            df: 输入DataFrame
            fit: 是否拟合编码器
            
        Returns:
            编码后的DataFrame
        """
        logger.info("编码类别特征")
        df_encoded = df.copy()
        
        encoding_method = self.preprocessing_config.get('encoding', {}).get('categorical_method', 'onehot')
        
        categorical_cols = [col for col in self.model_config.categorical_columns if col in df.columns]
        
        if not categorical_cols:
            logger.info("没有需要编码的类别特征")
            return df_encoded
        
        if encoding_method == 'onehot':
            if fit:
                # 创建OneHot编码器
                encoder = OneHotEncoder(
                    drop='first',  # 避免多重共线性
                    sparse_output=False,
                    handle_unknown='ignore'
                )
                
                # 拟合并转换
                encoded_array = encoder.fit_transform(df[categorical_cols])
                
                # 保存编码器
                self.transformers['categorical_encoder'] = encoder
                
                # 获取特征名称
                feature_names = encoder.get_feature_names_out(categorical_cols)
                
            else:
                # 使用已保存的编码器
                encoder = self.transformers.get('categorical_encoder')
                if encoder is None:
                    raise ValueError("编码器未拟合，请先调用fit=True")
                
                encoded_array = encoder.transform(df[categorical_cols])
                feature_names = encoder.get_feature_names_out(categorical_cols)
            
            # 创建编码后的DataFrame
            encoded_df = pd.DataFrame(
                encoded_array,
                columns=feature_names,
                index=df.index
            )
            
            # 删除原始类别列，添加编码列
            df_encoded = df_encoded.drop(categorical_cols, axis=1)
            df_encoded = pd.concat([df_encoded, encoded_df], axis=1)
        
        elif encoding_method == 'label':
            for col in categorical_cols:
                if fit:
                    encoder = LabelEncoder()
                    df_encoded[col] = encoder.fit_transform(df[col].astype(str))
                    self.transformers[f'{col}_label_encoder'] = encoder
                else:
                    encoder = self.transformers.get(f'{col}_label_encoder')
                    if encoder is None:
                        raise ValueError(f"列 {col} 的标签编码器未拟合")
                    
                    # 处理未见过的类别
                    unique_values = set(encoder.classes_)
                    df_encoded[col] = df[col].astype(str).apply(
                        lambda x: encoder.transform([x])[0] if x in unique_values else -1
                    )
        
        logger.info(f"类别特征编码完成，特征数: {df_encoded.shape[1]}")
        return df_encoded
    
    def scale_numerical_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """
        标准化数值特征
        
        Args:
            df: 输入DataFrame
            fit: 是否拟合标准化器
            
        Returns:
            标准化后的DataFrame
        """
        logger.info("标准化数值特征")
        df_scaled = df.copy()
        
        # 获取数值列（包括新创建的特征）
        numerical_cols = []
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                numerical_cols.append(col)
        
        if not numerical_cols:
            logger.info("没有需要标准化的数值特征")
            return df_scaled
        
        scaling_config = self.preprocessing_config.get('feature_scaling', {})
        method = scaling_config.get('method', 'standard')
        
        if method == 'standard':
            if fit:
                scaler = StandardScaler(
                    with_mean=scaling_config.get('with_mean', True),
                    with_std=scaling_config.get('with_std', True)
                )
                df_scaled[numerical_cols] = scaler.fit_transform(df[numerical_cols])
                self.transformers['numerical_scaler'] = scaler
            else:
                scaler = self.transformers.get('numerical_scaler')
                if scaler is None:
                    raise ValueError("数值标准化器未拟合，请先调用fit=True")
                df_scaled[numerical_cols] = scaler.transform(df[numerical_cols])
        
        logger.info(f"数值特征标准化完成，处理列数: {len(numerical_cols)}")
        return df_scaled
    
    def fit_transform(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        拟合并转换特征
        
        Args:
            df: 输入DataFrame
            
        Returns:
            转换后的DataFrame
        """
        logger.info("开始特征工程拟合和转换")
        
        # 1. 创建日期特征
        df_features = self.create_date_features(df)
        
        # 2. 编码类别特征
        df_encoded = self.encode_categorical_features(df_features, fit=True)
        
        # 3. 标准化数值特征
        df_final = self.scale_numerical_features(df_encoded, fit=True)
        
        # 保存最终特征名称
        self.feature_names = df_final.columns.tolist()
        
        logger.info(f"特征工程完成，最终特征数: {len(self.feature_names)}")
        return df_final
    
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        转换特征（使用已拟合的转换器）
        
        Args:
            df: 输入DataFrame
            
        Returns:
            转换后的DataFrame
        """
        logger.info("使用已拟合的转换器进行特征转换")
        
        # 1. 创建日期特征
        df_features = self.create_date_features(df)
        
        # 2. 编码类别特征
        df_encoded = self.encode_categorical_features(df_features, fit=False)
        
        # 3. 标准化数值特征
        df_final = self.scale_numerical_features(df_encoded, fit=False)
        
        # 确保特征顺序一致
        if self.feature_names:
            missing_cols = set(self.feature_names) - set(df_final.columns)
            if missing_cols:
                logger.warning(f"缺少特征列: {missing_cols}")
                # 添加缺失列（填充0）
                for col in missing_cols:
                    df_final[col] = 0
            
            # 按照训练时的顺序排列特征
            df_final = df_final[self.feature_names]
        
        logger.info(f"特征转换完成，特征数: {df_final.shape[1]}")
        return df_final
    
    def save_transformers(self, save_path: str) -> None:
        """保存转换器"""
        save_path = Path(save_path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # 保存所有转换器
        transformers_file = save_path / f"{self.fee_type}_feature_transformers.joblib"
        joblib.dump({
            'transformers': self.transformers,
            'feature_names': self.feature_names,
            'fee_type': self.fee_type
        }, transformers_file)
        
        logger.info(f"特征转换器已保存到: {transformers_file}")
    
    def load_transformers(self, load_path: str) -> None:
        """加载转换器"""
        load_path = Path(load_path)
        transformers_file = load_path / f"{self.fee_type}_feature_transformers.joblib"
        
        if not transformers_file.exists():
            raise FileNotFoundError(f"转换器文件不存在: {transformers_file}")
        
        data = joblib.load(transformers_file)
        self.transformers = data['transformers']
        self.feature_names = data['feature_names']
        
        logger.info(f"特征转换器已从 {transformers_file} 加载")
