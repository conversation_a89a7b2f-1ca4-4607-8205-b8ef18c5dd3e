#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超参数调优模块
实现网格搜索和随机搜索等超参数优化方法
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger

logger = get_logger(__name__)


class HyperparameterTuner:
    """超参数调优器"""
    
    def __init__(self, fee_type: str = 'fixed_fee'):
        """
        初始化超参数调优器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.cv_config = self.config.get('billing_audit.model_params.cross_validation', {})
        
        # 调优结果
        self.best_params = None
        self.best_score = None
        self.tuning_results = []
        
        logger.info(f"初始化{fee_type}超参数调优器")
    
    def get_param_grid(self) -> Dict[str, List[Any]]:
        """
        获取参数搜索网格
        
        Returns:
            参数网格字典
        """
        # 定义XGBoost参数搜索空间
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [3, 4, 5, 6],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0],
            'reg_alpha': [0, 0.1, 1],
            'reg_lambda': [1, 1.5, 2]
        }
        
        return param_grid
    
    def grid_search(self, X: pd.DataFrame, y: pd.Series, 
                   param_grid: Dict[str, List[Any]] = None,
                   cv_folds: int = None, scoring: str = None) -> Dict[str, Any]:
        """
        网格搜索超参数优化
        
        Args:
            X: 特征数据
            y: 目标变量
            param_grid: 参数网格
            cv_folds: 交叉验证折数
            scoring: 评分指标
            
        Returns:
            最佳参数和评分
        """
        if param_grid is None:
            param_grid = self.get_param_grid()
        
        if cv_folds is None:
            cv_folds = self.cv_config.get('cv_folds', 5)
        
        if scoring is None:
            scoring = self.cv_config.get('scoring', 'neg_mean_absolute_error')
        
        logger.info(f"开始网格搜索，参数组合数: {self._count_param_combinations(param_grid)}")
        
        try:
            from sklearn.model_selection import GridSearchCV
            import xgboost as xgb
            
            # 创建基础模型
            base_model = xgb.XGBRegressor(random_state=42, n_jobs=-1, verbosity=0)
            
            # 创建网格搜索对象
            grid_search = GridSearchCV(
                estimator=base_model,
                param_grid=param_grid,
                cv=cv_folds,
                scoring=scoring,
                n_jobs=-1,
                verbose=1
            )
            
            # 执行搜索
            start_time = datetime.now()
            grid_search.fit(X, y)
            search_time = (datetime.now() - start_time).total_seconds()
            
            # 保存结果
            self.best_params = grid_search.best_params_
            self.best_score = grid_search.best_score_
            
            # 保存详细结果
            self.tuning_results = [{
                'params': result['params'],
                'mean_score': result['mean_test_score'],
                'std_score': result['std_test_score']
            } for result in grid_search.cv_results_]
            
            result = {
                'method': 'grid_search',
                'best_params': self.best_params,
                'best_score': self.best_score,
                'search_time_seconds': search_time,
                'total_combinations': len(self.tuning_results),
                'cv_folds': cv_folds,
                'scoring': scoring
            }
            
            logger.info(f"网格搜索完成，最佳得分: {self.best_score:.4f}, 耗时: {search_time:.2f}秒")
            return result
            
        except ImportError:
            logger.warning("sklearn或xgboost未安装，使用模拟搜索")
            return self._mock_grid_search(param_grid, cv_folds, scoring)
    
    def random_search(self, X: pd.DataFrame, y: pd.Series,
                     param_distributions: Dict[str, Any] = None,
                     n_iter: int = 50, cv_folds: int = None, 
                     scoring: str = None) -> Dict[str, Any]:
        """
        随机搜索超参数优化
        
        Args:
            X: 特征数据
            y: 目标变量
            param_distributions: 参数分布
            n_iter: 搜索迭代次数
            cv_folds: 交叉验证折数
            scoring: 评分指标
            
        Returns:
            最佳参数和评分
        """
        if param_distributions is None:
            param_distributions = self._get_param_distributions()
        
        if cv_folds is None:
            cv_folds = self.cv_config.get('cv_folds', 5)
        
        if scoring is None:
            scoring = self.cv_config.get('scoring', 'neg_mean_absolute_error')
        
        logger.info(f"开始随机搜索，迭代次数: {n_iter}")
        
        try:
            from sklearn.model_selection import RandomizedSearchCV
            import xgboost as xgb
            
            # 创建基础模型
            base_model = xgb.XGBRegressor(random_state=42, n_jobs=-1, verbosity=0)
            
            # 创建随机搜索对象
            random_search = RandomizedSearchCV(
                estimator=base_model,
                param_distributions=param_distributions,
                n_iter=n_iter,
                cv=cv_folds,
                scoring=scoring,
                n_jobs=-1,
                random_state=42,
                verbose=1
            )
            
            # 执行搜索
            start_time = datetime.now()
            random_search.fit(X, y)
            search_time = (datetime.now() - start_time).total_seconds()
            
            # 保存结果
            self.best_params = random_search.best_params_
            self.best_score = random_search.best_score_
            
            result = {
                'method': 'random_search',
                'best_params': self.best_params,
                'best_score': self.best_score,
                'search_time_seconds': search_time,
                'n_iter': n_iter,
                'cv_folds': cv_folds,
                'scoring': scoring
            }
            
            logger.info(f"随机搜索完成，最佳得分: {self.best_score:.4f}, 耗时: {search_time:.2f}秒")
            return result
            
        except ImportError:
            logger.warning("sklearn或xgboost未安装，使用模拟搜索")
            return self._mock_random_search(n_iter, cv_folds, scoring)
    
    def _get_param_distributions(self) -> Dict[str, Any]:
        """获取参数分布（用于随机搜索）"""
        try:
            from scipy.stats import randint, uniform
            
            param_distributions = {
                'n_estimators': randint(50, 300),
                'max_depth': randint(3, 8),
                'learning_rate': uniform(0.01, 0.3),
                'subsample': uniform(0.6, 0.4),
                'colsample_bytree': uniform(0.6, 0.4),
                'reg_alpha': uniform(0, 2),
                'reg_lambda': uniform(0.5, 2)
            }
            
        except ImportError:
            # 如果scipy不可用，使用简单的列表
            param_distributions = {
                'n_estimators': [50, 100, 150, 200, 250, 300],
                'max_depth': [3, 4, 5, 6, 7, 8],
                'learning_rate': [0.01, 0.05, 0.1, 0.15, 0.2, 0.3],
                'subsample': [0.6, 0.7, 0.8, 0.9, 1.0],
                'colsample_bytree': [0.6, 0.7, 0.8, 0.9, 1.0],
                'reg_alpha': [0, 0.1, 0.5, 1, 1.5, 2],
                'reg_lambda': [0.5, 1, 1.5, 2, 2.5]
            }
        
        return param_distributions
    
    def _count_param_combinations(self, param_grid: Dict[str, List[Any]]) -> int:
        """计算参数组合总数"""
        count = 1
        for values in param_grid.values():
            count *= len(values)
        return count
    
    def _mock_grid_search(self, param_grid: Dict[str, List[Any]], 
                         cv_folds: int, scoring: str) -> Dict[str, Any]:
        """模拟网格搜索（用于测试）"""
        import random
        
        # 随机选择一组参数作为"最佳"参数
        best_params = {}
        for param, values in param_grid.items():
            best_params[param] = random.choice(values)
        
        self.best_params = best_params
        self.best_score = -random.uniform(0.1, 1.0)  # 负值因为使用neg_mean_absolute_error
        
        return {
            'method': 'mock_grid_search',
            'best_params': self.best_params,
            'best_score': self.best_score,
            'search_time_seconds': 10.0,
            'total_combinations': self._count_param_combinations(param_grid),
            'cv_folds': cv_folds,
            'scoring': scoring
        }
    
    def _mock_random_search(self, n_iter: int, cv_folds: int, scoring: str) -> Dict[str, Any]:
        """模拟随机搜索（用于测试）"""
        import random
        
        # 随机生成参数
        self.best_params = {
            'n_estimators': random.choice([50, 100, 200]),
            'max_depth': random.choice([3, 4, 5, 6]),
            'learning_rate': random.choice([0.01, 0.1, 0.2]),
            'subsample': random.choice([0.8, 0.9, 1.0]),
            'colsample_bytree': random.choice([0.8, 0.9, 1.0])
        }
        
        self.best_score = -random.uniform(0.1, 1.0)
        
        return {
            'method': 'mock_random_search',
            'best_params': self.best_params,
            'best_score': self.best_score,
            'search_time_seconds': 5.0,
            'n_iter': n_iter,
            'cv_folds': cv_folds,
            'scoring': scoring
        }
    
    def save_tuning_results(self, save_path: str = None) -> str:
        """
        保存调优结果
        
        Args:
            save_path: 保存路径
            
        Returns:
            实际保存路径
        """
        if self.best_params is None:
            raise ValueError("未进行超参数调优，无法保存结果")
        
        if save_path is None:
            results_dir = project_root / "models" / "billing_audit" / "tuning_results"
            results_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = results_dir / f"{self.fee_type}_tuning_{timestamp}.json"
        
        tuning_results = {
            'fee_type': self.fee_type,
            'best_params': self.best_params,
            'best_score': self.best_score,
            'detailed_results': self.tuning_results,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(tuning_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"调优结果已保存到: {save_path}")
        return str(save_path)
    
    def get_best_params(self) -> Dict[str, Any]:
        """获取最佳参数"""
        if self.best_params is None:
            logger.warning("未进行超参数调优")
            return {}
        
        return self.best_params.copy()
    
    def get_best_score(self) -> float:
        """获取最佳得分"""
        if self.best_score is None:
            logger.warning("未进行超参数调优")
            return 0.0
        
        return self.best_score
