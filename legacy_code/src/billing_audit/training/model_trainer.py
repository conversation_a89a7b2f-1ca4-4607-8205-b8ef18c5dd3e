#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收费稽核模型训练器
实现XGBoost模型的训练、验证和保存功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import joblib
import json
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils import get_config, get_logger

logger = get_logger(__name__)


class BillingModelTrainer:
    """收费稽核模型训练器"""
    
    def __init__(self, fee_type: str = 'fixed_fee'):
        """
        初始化模型训练器
        
        Args:
            fee_type: 费用类型 ('fixed_fee' 或 'discount')
        """
        self.fee_type = fee_type
        self.config = get_config()
        self.model_config = self.config.get_model_config('billing_audit', fee_type)
        self.model_params = self.config.get_model_params('xgboost')
        
        # 模型和评估结果
        self.model = None
        self.feature_importance = None
        self.training_history = {}
        self.evaluation_results = {}
        
        logger.info(f"初始化{fee_type}模型训练器")
    
    def split_data(self, X: pd.DataFrame, y: pd.Series, 
                   test_size: float = None, random_state: int = None) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """
        划分训练集和测试集
        
        Args:
            X: 特征数据
            y: 目标变量
            test_size: 测试集比例
            random_state: 随机种子
            
        Returns:
            (X_train, X_test, y_train, y_test)
        """
        try:
            from sklearn.model_selection import train_test_split
            
            split_config = self.config.get('billing_audit.model_params.train_test_split', {})
            
            if test_size is None:
                test_size = split_config.get('test_size', 0.2)
            if random_state is None:
                random_state = split_config.get('random_state', 42)
            
            logger.info(f"划分数据集，测试集比例: {test_size}")
            
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, 
                test_size=test_size,
                random_state=random_state,
                stratify=None  # 回归任务不需要分层
            )
            
            logger.info(f"数据划分完成 - 训练集: {X_train.shape}, 测试集: {X_test.shape}")
            return X_train, X_test, y_train, y_test
            
        except ImportError:
            logger.error("sklearn未安装，使用简单划分方法")
            # 简单的数据划分
            n_samples = len(X)
            n_test = int(n_samples * (test_size or 0.2))
            
            if random_state:
                np.random.seed(random_state)
            
            indices = np.random.permutation(n_samples)
            test_indices = indices[:n_test]
            train_indices = indices[n_test:]
            
            X_train = X.iloc[train_indices]
            X_test = X.iloc[test_indices]
            y_train = y.iloc[train_indices]
            y_test = y.iloc[test_indices]
            
            return X_train, X_test, y_train, y_test
    
    def create_model(self) -> Any:
        """
        创建机器学习模型

        Returns:
            模型实例
        """
        # 首先尝试XGBoost
        try:
            import xgboost as xgb

            logger.info("创建XGBoost模型")

            # 使用配置中的参数
            model = xgb.XGBRegressor(
                n_estimators=self.model_params.get('n_estimators', 100),
                max_depth=self.model_params.get('max_depth', 6),
                learning_rate=self.model_params.get('learning_rate', 0.1),
                subsample=self.model_params.get('subsample', 0.8),
                colsample_bytree=self.model_params.get('colsample_bytree', 0.8),
                random_state=self.model_params.get('random_state', 42),
                n_jobs=-1,
                verbosity=0
            )

            return model

        except Exception as e:
            logger.warning(f"XGBoost不可用: {e}")

            # 尝试LightGBM
            try:
                import lightgbm as lgb

                logger.info("创建LightGBM模型")

                model = lgb.LGBMRegressor(
                    n_estimators=self.model_params.get('n_estimators', 100),
                    max_depth=self.model_params.get('max_depth', 6),
                    learning_rate=self.model_params.get('learning_rate', 0.1),
                    subsample=self.model_params.get('subsample', 0.8),
                    colsample_bytree=self.model_params.get('colsample_bytree', 0.8),
                    random_state=self.model_params.get('random_state', 42),
                    n_jobs=-1,
                    verbosity=-1
                )

                return model

            except Exception as e2:
                logger.warning(f"LightGBM不可用: {e2}")

                # 尝试RandomForest
                try:
                    from sklearn.ensemble import RandomForestRegressor

                    logger.info("创建RandomForest模型")

                    model = RandomForestRegressor(
                        n_estimators=self.model_params.get('n_estimators', 100),
                        max_depth=self.model_params.get('max_depth', 6),
                        random_state=self.model_params.get('random_state', 42),
                        n_jobs=-1
                    )

                    return model

                except Exception as e3:
                    logger.warning(f"RandomForest不可用: {e3}")

                    # 最后使用模拟模型
                    logger.error("所有ML库都不可用，使用模拟模型")
                    return MockXGBModel(self.model_params)
    
    def train_model(self, X_train: pd.DataFrame, y_train: pd.Series,
                   X_val: pd.DataFrame = None, y_val: pd.Series = None) -> Any:
        """
        训练模型
        
        Args:
            X_train: 训练特征
            y_train: 训练目标
            X_val: 验证特征
            y_val: 验证目标
            
        Returns:
            训练好的模型
        """
        logger.info("开始模型训练")
        
        # 创建模型
        self.model = self.create_model()
        
        # 记录训练开始时间
        start_time = datetime.now()
        
        try:
            # 如果有验证集，使用早停
            if X_val is not None and y_val is not None:
                eval_set = [(X_train, y_train), (X_val, y_val)]
                self.model.fit(
                    X_train, y_train,
                    eval_set=eval_set,
                    eval_metric='rmse',
                    early_stopping_rounds=10,
                    verbose=False
                )
            else:
                self.model.fit(X_train, y_train)
            
            # 记录训练时间
            training_time = (datetime.now() - start_time).total_seconds()
            
            # 保存训练历史
            self.training_history = {
                'training_time_seconds': training_time,
                'training_samples': len(X_train),
                'feature_count': X_train.shape[1],
                'model_params': self.model_params,
                'timestamp': datetime.now().isoformat()
            }
            
            # 获取特征重要性
            if hasattr(self.model, 'feature_importances_'):
                self.feature_importance = dict(zip(
                    X_train.columns,
                    self.model.feature_importances_
                ))
            
            logger.info(f"模型训练完成，耗时: {training_time:.2f}秒")
            return self.model
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            raise
    
    def evaluate_model(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            X_test: 测试特征
            y_test: 测试目标
            
        Returns:
            评估指标字典
        """
        if self.model is None:
            raise ValueError("模型未训练，请先调用train_model")
        
        logger.info("开始模型评估")
        
        # 预测
        y_pred = self.model.predict(X_test)
        
        # 计算评估指标
        metrics = self._calculate_metrics(y_test, y_pred)
        
        # 保存评估结果
        self.evaluation_results = {
            'metrics': metrics,
            'test_samples': len(X_test),
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"模型评估完成 - MAE: {metrics['mae']:.4f}, RMSE: {metrics['rmse']:.4f}, R2: {metrics['r2']:.4f}")
        return metrics
    
    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        try:
            from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
            
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_true, y_pred)
            
        except ImportError:
            # 手动计算指标
            mae = np.mean(np.abs(y_true - y_pred))
            mse = np.mean((y_true - y_pred) ** 2)
            rmse = np.sqrt(mse)
            
            # R2计算
            ss_res = np.sum((y_true - y_pred) ** 2)
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # 计算相对误差
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        
        return {
            'mae': float(mae),
            'mse': float(mse),
            'rmse': float(rmse),
            'r2': float(r2),
            'mape': float(mape)
        }
    
    def save_model(self, save_path: str = None) -> str:
        """
        保存模型和相关信息
        
        Args:
            save_path: 保存路径
            
        Returns:
            实际保存路径
        """
        if self.model is None:
            raise ValueError("模型未训练，无法保存")
        
        if save_path is None:
            model_dir = project_root / "models" / "billing_audit"
            model_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = model_dir / f"{self.fee_type}_model_{timestamp}"
        else:
            save_path = Path(save_path)
        
        save_path.mkdir(parents=True, exist_ok=True)
        
        # 保存模型
        model_file = save_path / "model.joblib"
        joblib.dump(self.model, model_file)
        
        # 保存模型信息
        model_info = {
            'fee_type': self.fee_type,
            'model_type': 'xgboost',
            'training_history': self.training_history,
            'evaluation_results': self.evaluation_results,
            'feature_importance': self.feature_importance,
            'model_config': self.model_config.__dict__,
            'save_timestamp': datetime.now().isoformat()
        }
        
        info_file = save_path / "model_info.json"
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if hasattr(obj, 'item'):
                return obj.item()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            else:
                return obj

        serializable_info = convert_numpy_types(model_info)

        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_info, f, ensure_ascii=False, indent=2)
        
        logger.info(f"模型已保存到: {save_path}")
        return str(save_path)
    
    def load_model(self, load_path: str) -> Any:
        """
        加载模型
        
        Args:
            load_path: 模型路径
            
        Returns:
            加载的模型
        """
        load_path = Path(load_path)
        
        # 加载模型
        model_file = load_path / "model.joblib"
        if not model_file.exists():
            raise FileNotFoundError(f"模型文件不存在: {model_file}")
        
        self.model = joblib.load(model_file)
        
        # 加载模型信息
        info_file = load_path / "model_info.json"
        if info_file.exists():
            with open(info_file, 'r', encoding='utf-8') as f:
                model_info = json.load(f)
            
            self.training_history = model_info.get('training_history', {})
            self.evaluation_results = model_info.get('evaluation_results', {})
            self.feature_importance = model_info.get('feature_importance', {})
        
        logger.info(f"模型已从 {load_path} 加载")
        return self.model
    
    def get_feature_importance(self, top_n: int = 10) -> Dict[str, float]:
        """
        获取特征重要性
        
        Args:
            top_n: 返回前N个重要特征
            
        Returns:
            特征重要性字典
        """
        if self.feature_importance is None:
            logger.warning("特征重要性未计算")
            return {}
        
        # 按重要性排序
        sorted_features = sorted(
            self.feature_importance.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return dict(sorted_features[:top_n])


class MockXGBModel:
    """模拟XGBoost模型（用于没有安装XGBoost的环境）"""
    
    def __init__(self, params: Dict[str, Any]):
        self.params = params
        self.feature_importances_ = None
        self.is_fitted = False
    
    def fit(self, X, y, **kwargs):
        """模拟训练"""
        self.feature_importances_ = np.random.random(X.shape[1])
        self.is_fitted = True
        return self
    
    def predict(self, X):
        """模拟预测"""
        if not self.is_fitted:
            raise ValueError("模型未训练")
        
        # 返回随机预测值（仅用于测试）
        return np.random.random(len(X)) * 100
