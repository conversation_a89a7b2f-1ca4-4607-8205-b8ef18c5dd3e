#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收费稽核模型训练脚本
完整的模型训练流程，包括数据预处理、特征工程、模型训练和评估
"""

import sys
from pathlib import Path
import argparse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.billing_audit.preprocessing import BillingDataPreprocessor, FeatureEngineer
from src.billing_audit.training import BillingModelTrainer, HyperparameterTuner
from src.utils import get_logger

logger = get_logger(__name__)


def train_single_model(fee_type: str, tune_hyperparams: bool = False, save_model: bool = True) -> dict:
    """
    训练单个模型
    
    Args:
        fee_type: 费用类型 ('fixed_fee' 或 'discount')
        tune_hyperparams: 是否进行超参数调优
        save_model: 是否保存模型
        
    Returns:
        训练结果字典
    """
    print(f"\n{'='*60}")
    print(f"🚀 开始训练{fee_type}模型")
    print(f"{'='*60}")
    
    try:
        # 1. 数据预处理
        print("📊 步骤1: 数据预处理...")
        preprocessor = BillingDataPreprocessor(fee_type)
        X, y, passthrough = preprocessor.process()
        
        print(f"  ✅ 数据预处理完成:")
        print(f"    - 特征维度: {X.shape}")
        print(f"    - 目标变量长度: {len(y)}")
        print(f"    - 样本数量: {len(X)}")
        
        # 2. 特征工程
        print("🔧 步骤2: 特征工程...")
        feature_engineer = FeatureEngineer(fee_type)
        X_engineered = feature_engineer.fit_transform(X)
        
        print(f"  ✅ 特征工程完成:")
        print(f"    - 工程后特征维度: {X_engineered.shape}")
        print(f"    - 新增特征数: {X_engineered.shape[1] - X.shape[1]}")
        
        # 保存特征转换器
        models_dir = project_root / "models" / "billing_audit"
        feature_engineer.save_transformers(str(models_dir))
        
        # 3. 超参数调优（可选）
        best_params = None
        if tune_hyperparams:
            print("🎯 步骤3: 超参数调优...")
            tuner = HyperparameterTuner(fee_type)
            
            # 使用随机搜索（更快）
            tuning_result = tuner.random_search(X_engineered, y, n_iter=20)
            best_params = tuner.get_best_params()
            
            print(f"  ✅ 超参数调优完成:")
            print(f"    - 最佳得分: {tuning_result['best_score']:.4f}")
            print(f"    - 搜索时间: {tuning_result['search_time_seconds']:.2f}秒")
            print(f"    - 最佳参数: {best_params}")
            
            # 保存调优结果
            tuner.save_tuning_results()
        
        # 4. 模型训练
        print("🤖 步骤4: 模型训练...")
        trainer = BillingModelTrainer(fee_type)
        
        # 如果有调优结果，更新模型参数
        if best_params:
            trainer.model_params.update(best_params)
        
        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = trainer.split_data(X_engineered, y)
        
        # 训练模型
        model = trainer.train_model(X_train, y_train)
        
        print(f"  ✅ 模型训练完成:")
        print(f"    - 训练时间: {trainer.training_history.get('training_time_seconds', 0):.2f}秒")
        print(f"    - 训练样本数: {trainer.training_history.get('training_samples', 0)}")
        
        # 5. 模型评估
        print("📈 步骤5: 模型评估...")
        metrics = trainer.evaluate_model(X_test, y_test)
        
        print(f"  ✅ 模型评估完成:")
        print(f"    - MAE (平均绝对误差): {metrics['mae']:.4f}")
        print(f"    - RMSE (均方根误差): {metrics['rmse']:.4f}")
        print(f"    - R² (决定系数): {metrics['r2']:.4f}")
        print(f"    - MAPE (平均绝对百分比误差): {metrics['mape']:.2f}%")
        
        # 6. 特征重要性分析
        print("🔍 步骤6: 特征重要性分析...")
        feature_importance = trainer.get_feature_importance(top_n=10)
        
        if feature_importance:
            print("  ✅ 前10个重要特征:")
            for i, (feature, importance) in enumerate(feature_importance.items(), 1):
                print(f"    {i:2d}. {feature:<30} {importance:.4f}")
        
        # 7. 保存模型
        model_path = None
        if save_model:
            print("💾 步骤7: 保存模型...")
            model_path = trainer.save_model()
            print(f"  ✅ 模型已保存到: {model_path}")
        
        # 返回训练结果
        result = {
            'fee_type': fee_type,
            'success': True,
            'data_shape': X_engineered.shape,
            'metrics': metrics,
            'feature_importance': feature_importance,
            'model_path': model_path,
            'training_time': trainer.training_history.get('training_time_seconds', 0),
            'hyperparameter_tuning': tune_hyperparams,
            'best_params': best_params
        }
        
        print(f"🎉 {fee_type}模型训练成功完成!")
        return result
        
    except Exception as e:
        print(f"❌ {fee_type}模型训练失败: {e}")
        logger.exception(f"{fee_type}模型训练异常")
        
        return {
            'fee_type': fee_type,
            'success': False,
            'error': str(e)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='收费稽核模型训练')
    parser.add_argument('--fee-type', choices=['fixed_fee', 'discount', 'both'], 
                       default='both', help='费用类型')
    parser.add_argument('--tune-hyperparams', action='store_true', 
                       help='是否进行超参数调优')
    parser.add_argument('--no-save', action='store_true', 
                       help='不保存模型')
    
    args = parser.parse_args()
    
    print("🚀 开始收费稽核模型训练...")
    print(f"配置: 费用类型={args.fee_type}, 超参数调优={args.tune_hyperparams}, 保存模型={not args.no_save}")
    
    # 确定要训练的模型类型
    if args.fee_type == 'both':
        fee_types = ['fixed_fee', 'discount']
    else:
        fee_types = [args.fee_type]
    
    # 训练模型
    results = []
    for fee_type in fee_types:
        result = train_single_model(
            fee_type=fee_type,
            tune_hyperparams=args.tune_hyperparams,
            save_model=not args.no_save
        )
        results.append(result)
    
    # 输出总结
    print(f"\n{'='*80}")
    print("📊 训练总结")
    print(f"{'='*80}")
    
    successful_models = [r for r in results if r['success']]
    failed_models = [r for r in results if not r['success']]
    
    print(f"✅ 成功训练模型: {len(successful_models)}/{len(results)}")
    
    for result in successful_models:
        print(f"\n📈 {result['fee_type']} 模型:")
        print(f"  - 数据维度: {result['data_shape']}")
        print(f"  - MAE: {result['metrics']['mae']:.4f}")
        print(f"  - R²: {result['metrics']['r2']:.4f}")
        print(f"  - 训练时间: {result['training_time']:.2f}秒")
        if result['model_path']:
            print(f"  - 模型路径: {result['model_path']}")
    
    if failed_models:
        print(f"\n❌ 失败的模型:")
        for result in failed_models:
            print(f"  - {result['fee_type']}: {result['error']}")
    
    # 返回状态码
    return 0 if len(failed_models) == 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
