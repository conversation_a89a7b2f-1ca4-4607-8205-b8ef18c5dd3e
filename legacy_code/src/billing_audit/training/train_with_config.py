#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置化的大规模模型训练脚本
支持从配置文件读取所有参数，便于生产环境部署
"""

import sys
import argparse
from pathlib import Path
from datetime import datetime
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.config.production_config_manager import get_config_manager
from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer
from src.billing_audit.training.train_large_scale_model import LargeScaleModelTrainer


class ConfigurableModelTrainer:
    """配置化的模型训练器"""
    
    def __init__(self, config_path=None):
        # 加载配置
        if config_path:
            import os
            os.environ['BILLING_AUDIT_CONFIG'] = config_path
        
        self.config_manager = get_config_manager()
        self.config = self.config_manager.config
        
        # 设置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("配置化模型训练器初始化完成")
        self.logger.info(f"配置文件: {self.config_manager.config_path}")
        self.logger.info(f"项目版本: {self.config_manager.get('project.version')}")
    
    def setup_logging(self):
        """设置日志"""
        log_config = self.config_manager.get('logging', {})
        
        # 创建日志目录
        log_dir = Path(self.config_manager.get_data_path('logs_dir'))
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.FileHandler(log_dir / 'model_training.log'),
                logging.StreamHandler()
            ]
        )
    
    def get_training_data_path(self):
        """获取训练数据路径"""
        # 优先使用命令行参数，然后使用配置文件
        data_sources = self.config_manager.get('data_sources', {})
        return data_sources.get('training_data', '')
    
    def get_output_dir(self):
        """获取输出目录"""
        return self.config_manager.get_data_path('model_dir')
    
    def get_batch_size(self):
        """获取批处理大小"""
        return self.config_manager.get_batch_size()
    
    def get_algorithm_config(self):
        """获取算法配置"""
        training_config = self.config_manager.get('model_training', {})
        
        algorithms = training_config.get('algorithms', ['random_forest'])
        default_algo = training_config.get('default_algorithm', 'random_forest')
        hyperparams = training_config.get('hyperparameters', {})
        
        return {
            'algorithms': algorithms,
            'default_algorithm': default_algo,
            'hyperparameters': hyperparams
        }
    
    def get_feature_config(self):
        """获取特征工程配置"""
        return self.config_manager.get('feature_engineering', {})
    
    def train_model(self, input_file=None, output_dir=None, algorithm=None):
        """训练模型"""
        # 使用配置文件中的参数，命令行参数优先
        input_file = input_file or self.get_training_data_path()
        output_dir = output_dir or self.get_output_dir()
        
        if not input_file:
            raise ValueError("未指定训练数据文件")
        
        if not Path(input_file).exists():
            raise FileNotFoundError(f"训练数据文件不存在: {input_file}")
        
        self.logger.info(f"开始模型训练")
        self.logger.info(f"输入文件: {input_file}")
        self.logger.info(f"输出目录: {output_dir}")
        
        # 获取算法配置
        algo_config = self.get_algorithm_config()
        algorithm = algorithm or algo_config['default_algorithm']
        
        if algorithm not in algo_config['algorithms']:
            raise ValueError(f"不支持的算法: {algorithm}")
        
        hyperparams = algo_config['hyperparameters'].get(algorithm, {})
        
        self.logger.info(f"使用算法: {algorithm}")
        self.logger.info(f"超参数: {hyperparams}")
        
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 步骤1: 特征工程
        self.logger.info("步骤1: 执行特征工程")
        
        feature_config = self.get_feature_config()
        feature_engineer = LargeScaleFeatureEngineer(
            batch_size=self.get_batch_size(),
            enable_auto_features=feature_config.get('enable_auto_features', True)
        )
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 特征工程器输出路径
        feature_engineer_path = Path(output_dir) / f"large_scale_feature_engineer_{timestamp}.pkl"
        
        # 执行特征工程
        feature_engineer.fit_file(input_file)
        feature_engineer.save_preprocessor(str(feature_engineer_path))
        
        self.logger.info(f"特征工程完成，保存到: {feature_engineer_path}")
        
        # 步骤2: 模型训练
        self.logger.info("步骤2: 执行模型训练")
        
        trainer = LargeScaleModelTrainer(
            batch_size=self.get_batch_size(),
            algorithm=algorithm,
            **hyperparams
        )
        
        # 模型输出路径
        model_path = Path(output_dir) / f"large_scale_model_{timestamp}.pkl"
        
        # 执行训练
        result = trainer.train_from_file(
            input_file=input_file,
            feature_engineer=feature_engineer,
            output_path=str(model_path)
        )
        
        self.logger.info(f"模型训练完成，保存到: {model_path}")
        
        # 创建最新版本的软链接
        latest_model_path = Path(output_dir) / "large_scale_model_latest.pkl"
        latest_feature_path = Path(output_dir) / "large_scale_feature_engineer_latest.pkl"
        
        # 删除旧的软链接
        if latest_model_path.exists():
            latest_model_path.unlink()
        if latest_feature_path.exists():
            latest_feature_path.unlink()
        
        # 创建新的软链接
        latest_model_path.symlink_to(model_path.name)
        latest_feature_path.symlink_to(feature_engineer_path.name)
        
        self.logger.info("已创建最新版本软链接")
        
        # 返回训练结果
        training_result = {
            'model_path': str(model_path),
            'feature_engineer_path': str(feature_engineer_path),
            'latest_model_path': str(latest_model_path),
            'latest_feature_path': str(latest_feature_path),
            'algorithm': algorithm,
            'hyperparameters': hyperparams,
            'training_metrics': result,
            'timestamp': timestamp
        }
        
        # 保存训练报告
        report_path = Path(output_dir) / f"training_report_{timestamp}.json"
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(training_result, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"训练报告已保存: {report_path}")
        
        return training_result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='配置化的大规模模型训练')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--input', '-i', help='训练数据文件路径 (覆盖配置文件)')
    parser.add_argument('--output', '-o', help='输出目录 (覆盖配置文件)')
    parser.add_argument('--algorithm', '-a', help='算法名称 (覆盖配置文件)')
    parser.add_argument('--dry-run', action='store_true', help='仅显示配置，不执行训练')
    
    args = parser.parse_args()
    
    try:
        # 初始化训练器
        trainer = ConfigurableModelTrainer(config_path=args.config)
        
        if args.dry_run:
            # 仅显示配置
            print("🔧 配置信息:")
            print(f"  训练数据: {trainer.get_training_data_path()}")
            print(f"  输出目录: {trainer.get_output_dir()}")
            print(f"  批处理大小: {trainer.get_batch_size()}")
            
            algo_config = trainer.get_algorithm_config()
            print(f"  可用算法: {algo_config['algorithms']}")
            print(f"  默认算法: {algo_config['default_algorithm']}")
            
            feature_config = trainer.get_feature_config()
            print(f"  自动特征工程: {feature_config.get('enable_auto_features', True)}")
            
            print("\n✅ 配置检查完成，使用 --dry-run=false 开始训练")
            return
        
        # 执行训练
        result = trainer.train_model(
            input_file=args.input,
            output_dir=args.output,
            algorithm=args.algorithm
        )
        
        print("\n🎉 模型训练完成！")
        print(f"📁 模型文件: {result['model_path']}")
        print(f"📁 特征工程器: {result['feature_engineer_path']}")
        print(f"📊 训练指标: R²={result['training_metrics'].get('r2_score', 'N/A'):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
