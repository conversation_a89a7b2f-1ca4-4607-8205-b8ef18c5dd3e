2025-08-02 20:43:33 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:43:33 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-02 20:43:33 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:43:33 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-02 20:43:33 - billing_audit_main - INFO - 时间戳: 20250802_204333
2025-08-02 20:43:33 - billing_audit_main - INFO - 开始特征工程...
2025-08-02 20:43:33 - billing_audit_main - INFO - 开始执行: 特征工程
2025-08-02 20:43:33 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/large_scale_feature_engineer.py
2025-08-02 20:43:33 - billing_audit_main - INFO - 参数: --input ofrm_result.txt --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_204333.pkl --batch-size 1000
2025-08-02 20:43:33 - billing_audit_main - ERROR - 特征工程 执行失败，返回码: 1
2025-08-02 20:43:33 - billing_audit_main - ERROR - 错误输出: Traceback (most recent call last):
  File "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/src/billing_audit/preprocessing/large_scale_feature_engineer.py", line 30, in <module>
    from src.utils.logger import get_logger
  File "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/src/utils/__init__.py", line 7, in <module>
    from .config_manager import ConfigManager, get_config
  File "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/src/utils/config_manager.py", line 9, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'

2025-08-02 20:43:33 - billing_audit_main - ERROR - 特征工程失败
2025-08-02 20:43:33 - billing_audit_main - ERROR - ❌ 执行失败
