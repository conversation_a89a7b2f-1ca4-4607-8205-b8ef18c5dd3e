2025-08-02 20:44:52 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:44:52 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-02 20:44:52 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:44:52 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-02 20:44:52 - billing_audit_main - INFO - 时间戳: 20250802_204452
2025-08-02 20:44:52 - billing_audit_main - INFO - 开始模型训练...
2025-08-02 20:44:52 - billing_audit_main - INFO - 选择的训练算法: hierarchical
2025-08-02 20:44:52 - billing_audit_main - INFO - 使用分层模型训练
2025-08-02 20:44:52 - billing_audit_main - INFO - 开始分层模型训练...
2025-08-02 20:44:59 - billing_audit_main - INFO - 分层模型训练完成
2025-08-02 20:44:59 - billing_audit_main - INFO - 训练统计: {'model_type': 'HierarchicalBillingModel', 'algorithm': 'LightGBM', 'samples': 60354, 'features': 19, 'r2': 0.10647807288268929, 'mae': 72.74623608633074, 'rmse': np.float64(3607.052727323373), 'zero_accuracy': np.float64(0.9676906253106671), 'training_time': 2.2801401615142822, 'timestamp': '2025-08-02T20:44:59.728526'}
2025-08-02 20:44:59 - billing_audit_main - INFO - ✅ 执行成功完成
