2025-08-02 20:45:42 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:45:42 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-02 20:45:42 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:45:42 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-02 20:45:42 - billing_audit_main - INFO - 时间戳: 20250802_204542
2025-08-02 20:45:42 - billing_audit_main - INFO - 开始收费合理性判定...
2025-08-02 20:45:42 - billing_audit_main - INFO - 使用分层模型进行判定: hierarchical_model_20250802_204459.pkl
2025-08-02 20:45:42 - billing_audit_main - INFO - 开始执行: 收费合理性判定
2025-08-02 20:45:42 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/large_scale_billing_judge.py
2025-08-02 20:45:42 - billing_audit_main - INFO - 参数: --input ofrm_result.txt --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250802_204459.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_204437.pkl --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_204542.csv --target-column amount --batch-size 1000 --abs-threshold 50.0 --rel-threshold 0.1
2025-08-02 20:45:44 - billing_audit_main - INFO - 收费合理性判定 执行成功，耗时: 2.22秒
2025-08-02 20:45:44 - billing_audit_main - INFO - 收费合理性判定完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_204542.csv
2025-08-02 20:45:44 - billing_audit_main - INFO - ✅ 执行成功完成
