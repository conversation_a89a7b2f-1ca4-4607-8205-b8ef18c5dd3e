2025-08-02 20:51:41 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:51:41 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-02 20:51:41 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:51:41 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-02 20:51:41 - billing_audit_main - INFO - 时间戳: 20250802_205141
2025-08-02 20:51:41 - billing_audit_main - INFO - 开始模型预测...
2025-08-02 20:51:41 - billing_audit_main - INFO - 开始执行: 模型预测
2025-08-02 20:51:41 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/predict_large_scale.py
2025-08-02 20:51:41 - billing_audit_main - INFO - 参数: --input ofrm_result.txt --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250802_205130.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205130.pkl --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205141.csv --batch-size 1000 --include-features
2025-08-02 20:51:44 - billing_audit_main - INFO - 模型预测 执行成功，耗时: 2.94秒
2025-08-02 20:51:44 - billing_audit_main - INFO - 模型预测完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205141.csv
2025-08-02 20:51:44 - billing_audit_main - INFO - ✅ 执行成功完成
