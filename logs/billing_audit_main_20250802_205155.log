2025-08-02 20:51:55 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:51:55 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-02 20:51:55 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:51:55 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-02 20:51:55 - billing_audit_main - INFO - 时间戳: 20250802_205155
2025-08-02 20:51:55 - billing_audit_main - INFO - 开始模型评估...
2025-08-02 20:51:55 - billing_audit_main - INFO - 开始执行: 模型评估
2025-08-02 20:51:55 - billing_audit_main - INFO - 脚本路径: src/billing_audit/models/large_scale_model_evaluation.py
2025-08-02 20:51:55 - billing_audit_main - INFO - 参数: --test-data ofrm_result.txt --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250802_205130.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205130.pkl --target-column amount --batch-size 1000 --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/evaluation_report_20250802_205155.json
2025-08-02 20:51:58 - billing_audit_main - INFO - 模型评估 执行成功，耗时: 2.62秒
2025-08-02 20:51:58 - billing_audit_main - INFO - 模型评估完成，报告: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/evaluation_report_20250802_205155.json
2025-08-02 20:51:58 - billing_audit_main - INFO - ✅ 执行成功完成
