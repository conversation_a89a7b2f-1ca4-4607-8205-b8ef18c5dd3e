2025-08-02 20:52:29 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:52:29 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-02 20:52:29 - billing_audit_main - INFO - ================================================================================
2025-08-02 20:52:29 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-02 20:52:29 - billing_audit_main - INFO - 时间戳: 20250802_205229
2025-08-02 20:52:29 - billing_audit_main - INFO - 开始模型训练...
2025-08-02 20:52:29 - billing_audit_main - INFO - 选择的训练算法: lightgbm
2025-08-02 20:52:29 - billing_audit_main - INFO - 使用增强训练器，算法: lightgbm
2025-08-02 20:52:29 - billing_audit_main - WARNING - 增强训练器的命令行接口尚未实现，使用大规模训练脚本
2025-08-02 20:52:29 - billing_audit_main - INFO - 开始执行: 大规模模型训练
2025-08-02 20:52:29 - billing_audit_main - INFO - 脚本路径: src/billing_audit/training/train_large_scale_model.py
2025-08-02 20:52:29 - billing_audit_main - INFO - 参数: --input ofrm_result.txt --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models --batch-size 1000
2025-08-02 20:52:32 - billing_audit_main - INFO - 大规模模型训练 执行成功，耗时: 2.73秒
2025-08-02 20:52:32 - billing_audit_main - INFO - ✅ 执行成功完成
