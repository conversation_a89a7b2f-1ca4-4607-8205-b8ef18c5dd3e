2025-08-06 11:02:50 - billing_audit_main - INFO - ================================================================================
2025-08-06 11:02:50 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-06 11:02:50 - billing_audit_main - INFO - ================================================================================
2025-08-06 11:02:50 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-06 11:02:50 - billing_audit_main - INFO - 时间戳: 20250806_110250
2025-08-06 11:02:50 - billing_audit_main - INFO - 开始模型预测...
2025-08-06 11:02:50 - billing_audit_main - INFO - 使用分层预测脚本进行预测...
2025-08-06 11:02:50 - billing_audit_main - INFO - 开始执行: 分层模型预测
2025-08-06 11:02:50 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/predict_large_scale.py
2025-08-06 11:02:50 - billing_audit_main - INFO - 参数: --input data/raw/ofrm_result.csv --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_105254.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_105248.pkl --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_110250.csv --batch-size 1000 --include-features
2025-08-06 11:02:51 - billing_audit_main - INFO - 分层模型预测 执行成功，耗时: 1.54秒
2025-08-06 11:02:51 - billing_audit_main - INFO - 分层模型预测完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_110250.csv
2025-08-06 11:02:51 - billing_audit_main - INFO - ✅ 执行成功完成
