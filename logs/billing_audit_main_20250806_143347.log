2025-08-06 14:33:47 - billing_audit_main - INFO - ================================================================================
2025-08-06 14:33:47 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-06 14:33:47 - billing_audit_main - INFO - ================================================================================
2025-08-06 14:33:47 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-06 14:33:47 - billing_audit_main - INFO - 时间戳: 20250806_143347
2025-08-06 14:33:47 - billing_audit_main - INFO - 开始运行完整的出账稽核AI流程
2025-08-06 14:33:47 - billing_audit_main - INFO - 🔄 正确的流程: 原始数据 → 特征工程 → 数据拆分 → 训练 → 评估 → 预测 → 判定
2025-08-06 14:33:47 - billing_audit_main - INFO - ============================================================
2025-08-06 14:33:47 - billing_audit_main - INFO - 步骤1: 验证原始输入数据
2025-08-06 14:33:47 - billing_audit_main - INFO - 验证输入数据: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv
2025-08-06 14:33:47 - billing_audit_main - INFO - 数据规模: 7,379 行 × 26 列
2025-08-06 14:33:47 - billing_audit_main - INFO - 执行数据质量检查...
2025-08-06 14:33:47 - billing_audit_main - INFO - 目标列 amount 统计: 均值=1509.50, 标准差=3305.58
2025-08-06 14:33:47 - billing_audit_main - INFO - 零值比例: 43.64% (3220/7379)
2025-08-06 14:33:47 - billing_audit_main - INFO - 输入数据验证通过
2025-08-06 14:33:47 - billing_audit_main - INFO - 字段分类: 训练特征(14) + 目标字段(1) + 透传字段(11) = 26个字段
2025-08-06 14:33:47 - billing_audit_main - INFO - 步骤2: 对原始数据进行特征工程
2025-08-06 14:33:47 - billing_audit_main - INFO - 开始特征工程...
2025-08-06 14:33:47 - billing_audit_main - INFO - 开始执行: 特征工程
2025-08-06 14:33:47 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/large_scale_feature_engineer.py
2025-08-06 14:33:47 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143347.pkl --batch-size 1000
2025-08-06 14:33:48 - billing_audit_main - INFO - 特征工程 执行成功，耗时: 0.34秒
2025-08-06 14:33:48 - billing_audit_main - INFO - 特征工程完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143347.pkl
2025-08-06 14:33:48 - billing_audit_main - INFO - 步骤3: 拆分特征工程后的数据
2025-08-06 14:33:48 - billing_audit_main - INFO - 创建处理后数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/processed_data_20250806_143347.csv
2025-08-06 14:33:48 - billing_audit_main - INFO - 临时方案：复制原始文件到 /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/processed_data_20250806_143347.csv
2025-08-06 14:33:48 - billing_audit_main - INFO - 开始数据拆分...
2025-08-06 14:33:48 - billing_audit_main - INFO - 开始执行: 数据拆分
2025-08-06 14:33:48 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/data_splitter.py
2025-08-06 14:33:48 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/processed_data_20250806_143347.csv --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347 --test-size 0.2 --target-column amount
2025-08-06 14:33:49 - billing_audit_main - INFO - 数据拆分 执行成功，耗时: 1.75秒
2025-08-06 14:33:49 - billing_audit_main - INFO - 数据拆分完成，训练集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/train_data_20250806_143349.csv, 测试集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/test_data_20250806_143349.csv
2025-08-06 14:33:49 - billing_audit_main - INFO - 步骤4: 使用训练集进行模型训练
2025-08-06 14:33:49 - billing_audit_main - INFO - 开始模型训练...
2025-08-06 14:33:49 - billing_audit_main - INFO - 选择的训练算法: random_forest
2025-08-06 14:33:49 - billing_audit_main - INFO - 使用大规模训练脚本
2025-08-06 14:33:49 - billing_audit_main - INFO - 开始执行: 大规模模型训练
2025-08-06 14:33:49 - billing_audit_main - INFO - 脚本路径: src/billing_audit/training/train_large_scale_model.py
2025-08-06 14:33:49 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/train_data_20250806_143349.csv --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models --batch-size 1000
2025-08-06 14:33:51 - billing_audit_main - INFO - 大规模模型训练 执行成功，耗时: 1.79秒
2025-08-06 14:33:51 - billing_audit_main - INFO - 步骤5: 使用测试集进行模型评估
2025-08-06 14:33:51 - billing_audit_main - INFO - 开始模型评估...
2025-08-06 14:33:51 - billing_audit_main - INFO - 开始执行: 模型评估
2025-08-06 14:33:51 - billing_audit_main - INFO - 脚本路径: src/billing_audit/models/large_scale_model_evaluation.py
2025-08-06 14:33:51 - billing_audit_main - INFO - 参数: --test-data /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/test_data_20250806_143349.csv --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_143351.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143347.pkl --target-column amount --batch-size 1000 --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/evaluation_report_20250806_143347.json
2025-08-06 14:33:52 - billing_audit_main - ERROR - 模型评估 执行失败，返回码: 1
2025-08-06 14:33:52 - billing_audit_main - ERROR - 错误输出: 2025-08-06 14:33:52,500 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json
2025-08-06 14:33:52,501 - src.config.production_config_manager - INFO - 配置加载成功
2025-08-06 14:33:52,512 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143347.pkl
2025-08-06 14:33:52,512 - large_scale_model_evaluator - INFO - 开始大规模模型评估: 测试文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/test_data_20250806_143349.csv, 目标列=amount
2025-08-06 14:33:52,518 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 14:33:52,546 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
Traceback (most recent call last):
  File "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/src/billing_audit/models/large_scale_model_evaluation.py", line 459, in main
    json.dump(report_data, f, indent=2, ensure_ascii=False)
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/__init__.py", line 179, in dump
    for chunk in iterable:
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py", line 438, in _iterencode
    o = _default(o)
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int64 is not JSON serializable

2025-08-06 14:33:52 - billing_audit_main - ERROR - 模型评估失败
2025-08-06 14:33:52 - billing_audit_main - WARNING - 模型评估失败，但继续执行后续步骤
2025-08-06 14:33:52 - billing_audit_main - INFO - 步骤6: 进行模型预测
2025-08-06 14:33:52 - billing_audit_main - INFO - 开始模型预测...
2025-08-06 14:33:52 - billing_audit_main - INFO - 开始执行: 模型预测
2025-08-06 14:33:52 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/predict_large_scale.py
2025-08-06 14:33:52 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/test_data_20250806_143349.csv --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_143351.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143347.pkl --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143347.csv --batch-size 1000 --include-features
2025-08-06 14:33:53 - billing_audit_main - ERROR - 模型预测 执行失败，返回码: 1
2025-08-06 14:33:53 - billing_audit_main - ERROR - 错误输出: 2025-08-06 14:33:53,572 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json
2025-08-06 14:33:53,572 - src.config.production_config_manager - INFO - 配置加载成功
2025-08-06 14:33:53,573 - large_scale_prediction - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_143351.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143347.pkl
2025-08-06 14:33:53,584 - large_scale_prediction - INFO - 传统模型加载成功: RandomForestRegressor
2025-08-06 14:33:53,584 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143347.pkl
2025-08-06 14:33:53,585 - large_scale_prediction - INFO - 开始大规模数据预测: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143347/test_data_20250806_143349.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143347.csv

🔮 批量预测: 0批次 [00:00, ?批次/s]2025-08-06 14:33:53,602 - large_scale_prediction - INFO - 开始处理第 1 批数据，样本数: 1000
2025-08-06 14:33:53,609 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 14:33:53,610 - large_scale_prediction - INFO - 处理批次 1, 状态检查间隔: 5
2025-08-06 14:33:53,626 - large_scale_prediction - INFO - 第 1 批预测完成: 1,000 条，累计: 1,000 条

🔮 批量预测: 0批次 [00:00, ?批次/s, 批次=1, 当前=1,000, 累计=1,000, 预测范围=0.0-19408.4]2025-08-06 14:33:53,627 - large_scale_prediction - INFO - 开始处理第 2 批数据，样本数: 476
2025-08-06 14:33:53,632 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...
2025-08-06 14:33:53,632 - large_scale_prediction - INFO - 处理批次 2, 状态检查间隔: 5
2025-08-06 14:33:53,647 - large_scale_prediction - INFO - 第 2 批预测完成: 476 条，累计: 1,476 条

🔮 批量预测: 1批次 [00:00, 21.12批次/s, 批次=2, 当前=476, 累计=1,476, 预测范围=0.0-20140.9]
🔮 批量预测: 2批次 [00:00, 42.14批次/s, 批次=2, 当前=476, 累计=1,476, 预测范围=0.0-20140.9]
2025-08-06 14:33:53,656 - large_scale_prediction - INFO - 最终结果已保存(包含特征): /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143347.csv, 记录数: 1476
2025-08-06 14:33:53,656 - large_scale_prediction - INFO - 列顺序: 训练特征(14) + 透传字段(11) + 目标字段(1) + 预测字段(1)
2025-08-06 14:33:53,656 - large_scale_prediction - INFO - 预测完成: 总数=1,476, 耗时=0.07秒, 速度=20897条/秒
2025-08-06 14:33:53,656 - large_scale_prediction - INFO - 短期改进统计: 批次数=2, 状态恢复=0次, 成功率=73.8%
预测失败: 'predictions_per_second'
Traceback (most recent call last):
  File "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/src/billing_audit/inference/predict_large_scale.py", line 619, in main
    result = predict_with_statistics(
  File "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/src/billing_audit/inference/predict_large_scale.py", line 565, in predict_with_statistics
    print(f"  - 处理速度: {result['predictions_per_second']:.0f} 条/秒")
KeyError: 'predictions_per_second'

2025-08-06 14:33:53 - billing_audit_main - ERROR - 模型预测失败
2025-08-06 14:33:53 - billing_audit_main - ERROR - 模型预测失败，终止流程
2025-08-06 14:33:53 - billing_audit_main - ERROR - ❌ 执行失败
