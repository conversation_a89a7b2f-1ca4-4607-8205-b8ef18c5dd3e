2025-08-06 14:43:55 - billing_audit_main - INFO - ================================================================================
2025-08-06 14:43:55 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-06 14:43:55 - billing_audit_main - INFO - ================================================================================
2025-08-06 14:43:55 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI
2025-08-06 14:43:55 - billing_audit_main - INFO - 时间戳: 20250806_144355
2025-08-06 14:43:55 - billing_audit_main - INFO - 开始运行完整的出账稽核AI流程
2025-08-06 14:43:55 - billing_audit_main - INFO - 🔄 正确的流程: 原始数据 → 特征工程 → 数据拆分 → 训练 → 评估 → 预测 → 判定
2025-08-06 14:43:55 - billing_audit_main - INFO - ============================================================
2025-08-06 14:43:55 - billing_audit_main - INFO - 步骤1: 验证原始输入数据
2025-08-06 14:43:55 - billing_audit_main - INFO - 验证输入数据: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv
2025-08-06 14:43:55 - billing_audit_main - INFO - 数据规模: 7,379 行 × 26 列
2025-08-06 14:43:55 - billing_audit_main - INFO - 执行数据质量检查...
2025-08-06 14:43:55 - billing_audit_main - INFO - 目标列 amount 统计: 均值=1509.50, 标准差=3305.58
2025-08-06 14:43:55 - billing_audit_main - INFO - 零值比例: 43.64% (3220/7379)
2025-08-06 14:43:55 - billing_audit_main - INFO - 输入数据验证通过
2025-08-06 14:43:55 - billing_audit_main - INFO - 字段分类: 训练特征(14) + 目标字段(1) + 透传字段(11) = 26个字段
2025-08-06 14:43:55 - billing_audit_main - INFO - 步骤2: 对原始数据进行特征工程
2025-08-06 14:43:55 - billing_audit_main - INFO - 开始特征工程...
2025-08-06 14:43:55 - billing_audit_main - INFO - 开始执行: 特征工程
2025-08-06 14:43:55 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/large_scale_feature_engineer.py
2025-08-06 14:43:55 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144355.pkl --batch-size 1000
2025-08-06 14:43:55 - billing_audit_main - INFO - 特征工程 执行成功，耗时: 0.45秒
2025-08-06 14:43:55 - billing_audit_main - INFO - 特征工程完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144355.pkl
2025-08-06 14:43:55 - billing_audit_main - INFO - 步骤3: 拆分特征工程后的数据
2025-08-06 14:43:55 - billing_audit_main - INFO - 创建处理后数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/processed_data_20250806_144355.csv
2025-08-06 14:43:55 - billing_audit_main - INFO - 临时方案：复制原始文件到 /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/processed_data_20250806_144355.csv
2025-08-06 14:43:55 - billing_audit_main - INFO - 开始数据拆分...
2025-08-06 14:43:55 - billing_audit_main - INFO - 开始执行: 数据拆分
2025-08-06 14:43:55 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/data_splitter.py
2025-08-06 14:43:55 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/processed_data_20250806_144355.csv --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355 --test-size 0.2 --target-column amount
2025-08-06 14:43:56 - billing_audit_main - INFO - 数据拆分 执行成功，耗时: 0.93秒
2025-08-06 14:43:56 - billing_audit_main - INFO - 数据拆分完成，训练集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/train_data_20250806_144356.csv, 测试集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/test_data_20250806_144356.csv
2025-08-06 14:43:56 - billing_audit_main - INFO - 步骤4: 使用训练集进行模型训练
2025-08-06 14:43:56 - billing_audit_main - INFO - 开始模型训练...
2025-08-06 14:43:56 - billing_audit_main - INFO - 选择的训练算法: random_forest
2025-08-06 14:43:56 - billing_audit_main - INFO - 算法选择已保存，将影响后续评估、预测、判定步骤
2025-08-06 14:43:56 - billing_audit_main - INFO - 使用大规模训练脚本
2025-08-06 14:43:56 - billing_audit_main - INFO - 开始执行: 大规模模型训练
2025-08-06 14:43:56 - billing_audit_main - INFO - 脚本路径: src/billing_audit/training/train_large_scale_model.py
2025-08-06 14:43:56 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/train_data_20250806_144356.csv --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models --batch-size 1000
2025-08-06 14:43:58 - billing_audit_main - INFO - 大规模模型训练 执行成功，耗时: 1.40秒
2025-08-06 14:43:58 - billing_audit_main - INFO - 步骤5: 使用测试集进行模型评估
2025-08-06 14:43:58 - billing_audit_main - INFO - 开始模型评估...
2025-08-06 14:43:58 - billing_audit_main - INFO - 基于算法选择进行评估: random_forest
2025-08-06 14:43:58 - billing_audit_main - INFO - ✅ 使用传统模型评估（用户选择或回退）
2025-08-06 14:43:58 - billing_audit_main - INFO - 开始执行: 模型评估
2025-08-06 14:43:58 - billing_audit_main - INFO - 脚本路径: src/billing_audit/models/large_scale_model_evaluation.py
2025-08-06 14:43:58 - billing_audit_main - INFO - 参数: --test-data /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/test_data_20250806_144356.csv --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_144357.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144355.pkl --target-column amount --batch-size 1000 --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/evaluation_report_20250806_144355.json
2025-08-06 14:43:59 - billing_audit_main - INFO - 模型评估 执行成功，耗时: 1.01秒
2025-08-06 14:43:59 - billing_audit_main - INFO - 模型评估完成，报告: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/evaluation_report_20250806_144355.json
2025-08-06 14:43:59 - billing_audit_main - INFO - 步骤6: 进行模型预测
2025-08-06 14:43:59 - billing_audit_main - INFO - 开始模型预测...
2025-08-06 14:43:59 - billing_audit_main - INFO - 基于算法选择进行预测: random_forest
2025-08-06 14:43:59 - billing_audit_main - INFO - ✅ 使用传统模型预测（用户选择或回退）
2025-08-06 14:43:59 - billing_audit_main - INFO - 开始执行: 模型预测
2025-08-06 14:43:59 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/predict_large_scale.py
2025-08-06 14:43:59 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/test_data_20250806_144356.csv --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_144357.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144355.pkl --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_144355.csv --batch-size 1000 --include-features
2025-08-06 14:44:00 - billing_audit_main - INFO - 模型预测 执行成功，耗时: 1.05秒
2025-08-06 14:44:00 - billing_audit_main - INFO - 模型预测完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_144355.csv
2025-08-06 14:44:00 - billing_audit_main - INFO - 步骤7: 进行收费合理性判定
2025-08-06 14:44:00 - billing_audit_main - INFO - 开始收费合理性判定...
2025-08-06 14:44:00 - billing_audit_main - INFO - 基于算法选择进行判定: random_forest
2025-08-06 14:44:00 - billing_audit_main - INFO - ✅ 使用传统模型进行判定（用户选择）
2025-08-06 14:44:00 - billing_audit_main - INFO - 使用传统模型进行判定: large_scale_model_20250806_144357.pkl
2025-08-06 14:44:00 - billing_audit_main - INFO - 开始执行: 收费合理性判定
2025-08-06 14:44:00 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/large_scale_billing_judge.py
2025-08-06 14:44:00 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144355/test_data_20250806_144356.csv --model /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_144357.pkl --feature-engineer /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144355.pkl --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144355.csv --target-column amount --batch-size 1000 --abs-threshold 50.0 --rel-threshold 0.1
2025-08-06 14:44:01 - billing_audit_main - INFO - 收费合理性判定 执行成功，耗时: 1.03秒
2025-08-06 14:44:01 - billing_audit_main - INFO - 收费合理性判定完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144355.csv
2025-08-06 14:44:01 - billing_audit_main - INFO - ============================================================
2025-08-06 14:44:01 - billing_audit_main - INFO - ✅ 完整流程执行完成
2025-08-06 14:44:01 - billing_audit_main - INFO - 总耗时: 5.88秒
2025-08-06 14:44:01 - billing_audit_main - INFO - 🎯 流程总结: 原始数据 → 特征工程 → 数据拆分 → 训练 → 评估 → 预测 → 判定
2025-08-06 14:44:01 - billing_audit_main - INFO - 执行报告已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/execution_report_20250806_144355.json
2025-08-06 14:44:01 - billing_audit_main - INFO - 执行摘要: 7/7 步骤成功 (100.0%)
2025-08-06 14:44:01 - billing_audit_main - INFO - 通过专用脚本生成综合报告...
2025-08-06 14:44:01 - billing_audit_main - INFO - 开始执行: 综合报告生成
2025-08-06 14:44:01 - billing_audit_main - INFO - 脚本路径: src/billing_audit/reporting/comprehensive_report_generator.py
2025-08-06 14:44:01 - billing_audit_main - INFO - 参数: --timestamp 20250806_144355 --models-dir /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models --data-dir /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data --reports-dir /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports --total-duration 5.879188 --execution-results /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/temp_execution_results_20250806_144355.json
2025-08-06 14:44:01 - billing_audit_main - INFO - 综合报告生成 执行成功，耗时: 0.37秒
2025-08-06 14:44:01 - billing_audit_main - INFO - 综合报告生成成功
2025-08-06 14:44:01 - billing_audit_main - INFO - ✅ 执行成功完成
