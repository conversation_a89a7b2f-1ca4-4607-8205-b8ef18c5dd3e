{"execution_info": {"timestamp": "20250802_205331", "start_time": "2025-08-02T20:53:40.128817", "total_duration": 8.856716, "project_root": "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI"}, "results": {"data_validation": {"success": true, "data_quality": {"total_samples": 60354, "total_features": 14, "missing_values": [{"feature": "final_eff_mon", "null_count": 34330, "null_ratio": 0.5688106836332306}, {"feature": "final_eff_day", "null_count": 34330, "null_ratio": 0.5688106836332306}, {"feature": "final_exp_mon", "null_count": 34330, "null_ratio": 0.5688106836332306}, {"feature": "final_exp_day", "null_count": 34330, "null_ratio": 0.5688106836332306}], "zero_ratio": 0.9267322795506512, "has_missing_values": true}}, "feature_engineering": {"success": true, "duration": 0.835569, "stdout": "初始化大规模特征工程器\n  - 特征列数: 14\n  - 类别列数: 4\n  - 数值列数: 9\n开始拟合统计量...\n  - 数据文件: ofrm_result.txt\n  - 批次大小: 1,000\n  统计量拟合完成:\n    - 总批次: 61\n    - 总行数: 60,354\n\n数值列统计摘要:\n  final_eff_year: 均值=871.62, 标准差=1001.11, 范围=[0.00, 2025.00]\n  final_eff_mon: 均值=6.30, 标准差=3.36, 范围=[1.00, 12.00]\n  final_eff_day: 均值=11.96, 标准差=10.30, 范围=[1.00, 31.00]\n  final_exp_year: 均值=879.27, 标准差=1009.92, 范围=[0.00, 2050.00]\n  final_exp_mon: 均值=6.80, 标准差=3.67, 范围=[1.00, 12.00]\n  final_exp_day: 均值=25.96, 标准差=9.34, 范围=[1.00, 31.00]\n  charge_day_count: 均值=9.80, 标准差=14.42, 范围=[0.00, 31.00]\n  month_day_count: 均值=31.00, 标准差=0.00, 范围=[31.00, 31.00]\n  should_fee: 均值=763.05, 标准差=5153.87, 范围=[0.00, 346000.00]\n\n类别列统计摘要:\n  cal_type: 3 个唯一值\n  unit_type: 2 个唯一值\n  rate_unit: 4 个唯一值\n  busi_flag: 2 个唯一值\n预处理器已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n\n大规模特征工程器测试完成！\n", "stderr": "2025-08-02 20:53:31,545 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-02 20:53:31,545 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-02 20:53:31,545 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器\n2025-08-02 20:53:31,545 - large_scale_feature_engineer - INFO - 特征列数: 14\n2025-08-02 20:53:31,545 - large_scale_feature_engineer - INFO - 类别列数: 4\n2025-08-02 20:53:31,545 - large_scale_feature_engineer - INFO - 数值列数: 9\n2025-08-02 20:53:31,545 - large_scale_feature_engineer - INFO - 开始拟合统计量\n2025-08-02 20:53:31,545 - large_scale_feature_engineer - INFO - 数据文件: ofrm_result.txt\n2025-08-02 20:53:31,545 - large_scale_feature_engineer - INFO - 批次大小: 1,000\n\n统计拟合: 0批次 [00:00, ?批次/s]\n统计拟合: 0批次 [00:00, ?批次/s, 批次=1, 当前行数=1,000, 累计行数=1,000, 数值列=9, 类别列=4]\n统计拟合: 1批次 [00:00, 79.50批次/s, 批次=2, 当前行数=1,000, 累计行数=2,000, 数值列=9, 类别列=4]\n统计拟合: 2批次 [00:00, 110.99批次/s, 批次=3, 当前行数=1,000, 累计行数=3,000, 数值列=9, 类别列=4]\n统计拟合: 3批次 [00:00, 126.10批次/s, 批次=4, 当前行数=1,000, 累计行数=4,000, 数值列=9, 类别列=4]\n统计拟合: 4批次 [00:00, 135.38批次/s, 批次=5, 当前行数=1,000, 累计行数=5,000, 数值列=9, 类别列=4]\n统计拟合: 5批次 [00:00, 142.83批次/s, 批次=6, 当前行数=1,000, 累计行数=6,000, 数值列=9, 类别列=4]\n统计拟合: 6批次 [00:00, 147.20批次/s, 批次=7, 当前行数=1,000, 累计行数=7,000, 数值列=9, 类别列=4]\n统计拟合: 7批次 [00:00, 147.31批次/s, 批次=8, 当前行数=1,000, 累计行数=8,000, 数值列=9, 类别列=4]\n统计拟合: 8批次 [00:00, 146.22批次/s, 批次=9, 当前行数=1,000, 累计行数=9,000, 数值列=9, 类别列=4]\n统计拟合: 9批次 [00:00, 145.00批次/s, 批次=10, 当前行数=1,000, 累计行数=10,000, 数值列=9, 类别列=4]2025-08-02 20:53:31,618 - large_scale_feature_engineer - INFO - 处理进度: 已完成10批次, 累计10,000行\n\n统计拟合: 10批次 [00:00, 131.11批次/s, 批次=11, 当前行数=1,000, 累计行数=11,000, 数值列=9, 类别列=4]\n统计拟合: 11批次 [00:00, 131.29批次/s, 批次=12, 当前行数=1,000, 累计行数=12,000, 数值列=9, 类别列=4]\n统计拟合: 12批次 [00:00, 132.17批次/s, 批次=13, 当前行数=1,000, 累计行数=13,000, 数值列=9, 类别列=4]\n统计拟合: 13批次 [00:00, 132.27批次/s, 批次=14, 当前行数=1,000, 累计行数=14,000, 数值列=9, 类别列=4]\n统计拟合: 14批次 [00:00, 132.90批次/s, 批次=15, 当前行数=1,000, 累计行数=15,000, 数值列=9, 类别列=4]\n统计拟合: 15批次 [00:00, 142.36批次/s, 批次=15, 当前行数=1,000, 累计行数=15,000, 数值列=9, 类别列=4]\n统计拟合: 15批次 [00:00, 142.36批次/s, 批次=16, 当前行数=1,000, 累计行数=16,000, 数值列=9, 类别列=4]\n统计拟合: 16批次 [00:00, 142.36批次/s, 批次=17, 当前行数=1,000, 累计行数=17,000, 数值列=9, 类别列=4]\n统计拟合: 17批次 [00:00, 142.36批次/s, 批次=18, 当前行数=1,000, 累计行数=18,000, 数值列=9, 类别列=4]\n统计拟合: 18批次 [00:00, 142.36批次/s, 批次=19, 当前行数=1,000, 累计行数=19,000, 数值列=9, 类别列=4]\n统计拟合: 19批次 [00:00, 142.36批次/s, 批次=20, 当前行数=1,000, 累计行数=20,000, 数值列=9, 类别列=4]2025-08-02 20:53:31,695 - large_scale_feature_engineer - INFO - 处理进度: 已完成20批次, 累计20,000行\n\n统计拟合: 20批次 [00:00, 142.36批次/s, 批次=21, 当前行数=1,000, 累计行数=21,000, 数值列=9, 类别列=4]\n统计拟合: 21批次 [00:00, 142.36批次/s, 批次=22, 当前行数=1,000, 累计行数=22,000, 数值列=9, 类别列=4]\n统计拟合: 22批次 [00:00, 142.36批次/s, 批次=23, 当前行数=1,000, 累计行数=23,000, 数值列=9, 类别列=4]\n统计拟合: 23批次 [00:00, 142.36批次/s, 批次=24, 当前行数=1,000, 累计行数=24,000, 数值列=9, 类别列=4]\n统计拟合: 24批次 [00:00, 142.36批次/s, 批次=25, 当前行数=1,000, 累计行数=25,000, 数值列=9, 类别列=4]\n统计拟合: 25批次 [00:00, 142.36批次/s, 批次=26, 当前行数=1,000, 累计行数=26,000, 数值列=9, 类别列=4]\n统计拟合: 26批次 [00:00, 142.36批次/s, 批次=27, 当前行数=1,000, 累计行数=27,000, 数值列=9, 类别列=4]\n统计拟合: 27批次 [00:00, 142.36批次/s, 批次=28, 当前行数=1,000, 累计行数=28,000, 数值列=9, 类别列=4]\n统计拟合: 28批次 [00:00, 142.36批次/s, 批次=29, 当前行数=1,000, 累计行数=29,000, 数值列=9, 类别列=4]\n统计拟合: 29批次 [00:00, 142.36批次/s, 批次=30, 当前行数=1,000, 累计行数=30,000, 数值列=9, 类别列=4]\n统计拟合: 30批次 [00:00, 144.58批次/s, 批次=30, 当前行数=1,000, 累计行数=30,000, 数值列=9, 类别列=4]2025-08-02 20:53:31,764 - large_scale_feature_engineer - INFO - 处理进度: 已完成30批次, 累计30,000行\n\n统计拟合: 30批次 [00:00, 144.58批次/s, 批次=31, 当前行数=1,000, 累计行数=31,000, 数值列=9, 类别列=4]\n统计拟合: 31批次 [00:00, 144.58批次/s, 批次=32, 当前行数=1,000, 累计行数=32,000, 数值列=9, 类别列=4]\n统计拟合: 32批次 [00:00, 144.58批次/s, 批次=33, 当前行数=1,000, 累计行数=33,000, 数值列=9, 类别列=4]\n统计拟合: 33批次 [00:00, 144.58批次/s, 批次=34, 当前行数=1,000, 累计行数=34,000, 数值列=9, 类别列=4]\n统计拟合: 34批次 [00:00, 144.58批次/s, 批次=35, 当前行数=1,000, 累计行数=35,000, 数值列=9, 类别列=4]\n统计拟合: 35批次 [00:00, 144.58批次/s, 批次=36, 当前行数=1,000, 累计行数=36,000, 数值列=9, 类别列=4]\n统计拟合: 36批次 [00:00, 144.58批次/s, 批次=37, 当前行数=1,000, 累计行数=37,000, 数值列=9, 类别列=4]\n统计拟合: 37批次 [00:00, 144.58批次/s, 批次=38, 当前行数=1,000, 累计行数=38,000, 数值列=9, 类别列=4]\n统计拟合: 38批次 [00:00, 144.58批次/s, 批次=39, 当前行数=1,000, 累计行数=39,000, 数值列=9, 类别列=4]\n统计拟合: 39批次 [00:00, 144.58批次/s, 批次=40, 当前行数=1,000, 累计行数=40,000, 数值列=9, 类别列=4]2025-08-02 20:53:31,840 - large_scale_feature_engineer - INFO - 处理进度: 已完成40批次, 累计40,000行\n\n统计拟合: 40批次 [00:00, 144.58批次/s, 批次=41, 当前行数=1,000, 累计行数=41,000, 数值列=9, 类别列=4]\n统计拟合: 41批次 [00:00, 144.58批次/s, 批次=42, 当前行数=1,000, 累计行数=42,000, 数值列=9, 类别列=4]\n统计拟合: 42批次 [00:00, 144.58批次/s, 批次=43, 当前行数=1,000, 累计行数=43,000, 数值列=9, 类别列=4]\n统计拟合: 43批次 [00:00, 144.58批次/s, 批次=44, 当前行数=1,000, 累计行数=44,000, 数值列=9, 类别列=4]\n统计拟合: 44批次 [00:00, 144.58批次/s, 批次=45, 当前行数=1,000, 累计行数=45,000, 数值列=9, 类别列=4]\n统计拟合: 45批次 [00:00, 112.60批次/s, 批次=45, 当前行数=1,000, 累计行数=45,000, 数值列=9, 类别列=4]\n统计拟合: 45批次 [00:00, 112.60批次/s, 批次=46, 当前行数=1,000, 累计行数=46,000, 数值列=9, 类别列=4]\n统计拟合: 46批次 [00:00, 112.60批次/s, 批次=47, 当前行数=1,000, 累计行数=47,000, 数值列=9, 类别列=4]\n统计拟合: 47批次 [00:00, 112.60批次/s, 批次=48, 当前行数=1,000, 累计行数=48,000, 数值列=9, 类别列=4]\n统计拟合: 48批次 [00:00, 112.60批次/s, 批次=49, 当前行数=1,000, 累计行数=49,000, 数值列=9, 类别列=4]\n统计拟合: 49批次 [00:00, 112.60批次/s, 批次=50, 当前行数=1,000, 累计行数=50,000, 数值列=9, 类别列=4]2025-08-02 20:53:32,020 - large_scale_feature_engineer - INFO - 处理进度: 已完成50批次, 累计50,000行\n\n统计拟合: 50批次 [00:00, 112.60批次/s, 批次=51, 当前行数=1,000, 累计行数=51,000, 数值列=9, 类别列=4]\n统计拟合: 51批次 [00:00, 112.60批次/s, 批次=52, 当前行数=1,000, 累计行数=52,000, 数值列=9, 类别列=4]\n统计拟合: 52批次 [00:00, 112.60批次/s, 批次=53, 当前行数=1,000, 累计行数=53,000, 数值列=9, 类别列=4]\n统计拟合: 53批次 [00:00, 112.60批次/s, 批次=54, 当前行数=1,000, 累计行数=54,000, 数值列=9, 类别列=4]\n统计拟合: 54批次 [00:00, 112.60批次/s, 批次=55, 当前行数=1,000, 累计行数=55,000, 数值列=9, 类别列=4]\n统计拟合: 55批次 [00:00, 112.60批次/s, 批次=56, 当前行数=1,000, 累计行数=56,000, 数值列=9, 类别列=4]\n统计拟合: 56批次 [00:00, 112.60批次/s, 批次=57, 当前行数=1,000, 累计行数=57,000, 数值列=9, 类别列=4]\n统计拟合: 57批次 [00:00, 98.16批次/s, 批次=57, 当前行数=1,000, 累计行数=57,000, 数值列=9, 类别列=4] \n统计拟合: 57批次 [00:00, 98.16批次/s, 批次=58, 当前行数=1,000, 累计行数=58,000, 数值列=9, 类别列=4]\n统计拟合: 58批次 [00:00, 98.16批次/s, 批次=59, 当前行数=1,000, 累计行数=59,000, 数值列=9, 类别列=4]\n统计拟合: 59批次 [00:00, 98.16批次/s, 批次=60, 当前行数=1,000, 累计行数=60,000, 数值列=9, 类别列=4]2025-08-02 20:53:32,111 - large_scale_feature_engineer - INFO - 处理进度: 已完成60批次, 累计60,000行\n\n统计拟合: 60批次 [00:00, 98.16批次/s, 批次=61, 当前行数=354, 累计行数=60,354, 数值列=9, 类别列=4]  \n统计拟合: 61批次 [00:00, 108.19批次/s, 批次=61, 当前行数=354, 累计行数=60,354, 数值列=9, 类别列=4]\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=61, 总行数=60,354\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 统计摘要生成完成\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=871.62, 标准差=1001.11\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.30, 标准差=3.36\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=11.96, 标准差=10.30\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=879.27, 标准差=1009.92\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=6.80, 标准差=3.67\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=25.96, 标准差=9.34\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=9.80, 标准差=14.42\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=31.00, 标准差=0.00\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=763.05, 标准差=5153.87\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 4 个唯一值\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 保存预处理器到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n2025-08-02 20:53:32,120 - large_scale_feature_engineer - INFO - 预处理器保存成功: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n", "returncode": 0}, "data_splitting": {"success": true, "duration": 1.192649, "stdout": "🔀 开始数据拆分\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/processed_data_20250802_205331.csv\n  输出目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331\n  测试集比例: 0.2\n  目标列: amount\n\n读取数据...\n  数据形状: (60354, 26)\n  特征数: 25\n  样本数: 60354\n\n拆分数据...\n  训练集: 48,283 样本\n  测试集: 12,071 样本\n\n保存拆分后的数据...\n  训练集文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv\n  测试集文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n\n验证保存的文件:\n  训练集: (48283, 26)\n  测试集: (12071, 26)\n  总样本数: 60354 (原始: 60354)\n\n数据拆分统计:\n  训练集目标值范围: 0.00 - 729672.00\n  测试集目标值范围: 0.00 - 416666.00\n  训练集目标值均值: 223.73\n  测试集目标值均值: 224.93\n\n✅ 数据拆分完成\n\n🎯 数据拆分成功完成\n训练集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv\n测试集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n", "stderr": "2025-08-02 20:53:32,972 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-02 20:53:32,973 - src.config.production_config_manager - INFO - 配置加载成功\n", "returncode": 0}, "model_training": {"success": true, "duration": 2.342708, "stdout": "大规模数据模型训练\n============================================================\n拟合大规模特征工程器...\n初始化大规模特征工程器\n  - 特征列数: 14\n  - 类别列数: 4\n  - 数值列数: 9\n开始拟合统计量...\n  - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv\n  - 批次大小: 1,000\n  统计量拟合完成:\n    - 总批次: 49\n    - 总行数: 48,283\n\n数值列统计摘要:\n  final_eff_year: 均值=871.03, 标准差=1001.03, 范围=[0.00, 2025.00]\n  final_eff_mon: 均值=6.31, 标准差=3.36, 范围=[1.00, 12.00]\n  final_eff_day: 均值=11.95, 标准差=10.31, 范围=[1.00, 31.00]\n  final_exp_year: 均值=878.68, 标准差=1009.84, 范围=[0.00, 2050.00]\n  final_exp_mon: 均值=6.80, 标准差=3.67, 范围=[1.00, 12.00]\n  final_exp_day: 均值=25.91, 标准差=9.40, 范围=[1.00, 31.00]\n  charge_day_count: 均值=9.79, 标准差=14.41, 范围=[0.00, 31.00]\n  month_day_count: 均值=31.00, 标准差=0.00, 范围=[31.00, 31.00]\n  should_fee: 均值=774.35, 标准差=5241.57, 范围=[0.00, 346000.00]\n\n类别列统计摘要:\n  cal_type: 3 个唯一值\n  unit_type: 2 个唯一值\n  rate_unit: 4 个唯一值\n  busi_flag: 2 个唯一值\n  特征工程器拟合完成\n\n开始分批处理和训练...\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n  开始分批处理数据...\n  合并剩余的 10 批数据...\n    最终合并完成，数据形状: (48283, 19)\n\n合并所有数据...\n  最终数据形状: X(48283, 19), y(48283,)\n\n分割训练测试集...\n  训练集: 38,626 样本\n  测试集: 9,657 样本\n\n开始训练模型...\n  模型参数: n_estimators=100, max_depth=10\n  训练样本: 38,626 行 × 19 特征\n  训练完成，耗时: 0.22秒\n\n评估模型性能...\n  测试集性能:\n    - MAE: 118.25\n    - RMSE: 950.07\n    - R²: 0.5876\n    - 性能等级: 较差\n预处理器已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205335.pkl\n\n模型保存完成:\n  模型文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250802_205335.pkl\n  特征工程器: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205335.pkl\n\n大规模模型训练完成！\n处理样本: 48,283 条\n训练时间: 0.22秒\n模型性能: R²=0.5876\n", "stderr": "2025-08-02 20:53:34,520 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-02 20:53:34,520 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-02 20:53:34,520 - large_scale_model_trainer - INFO - 开始大规模数据模型训练: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv\n2025-08-02 20:53:34,520 - large_scale_model_trainer - INFO - 开始拟合大规模特征工程器: 文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv, 批次大小=1,000\n2025-08-02 20:53:34,521 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器\n2025-08-02 20:53:34,521 - large_scale_feature_engineer - INFO - 特征列数: 14\n2025-08-02 20:53:34,521 - large_scale_feature_engineer - INFO - 类别列数: 4\n2025-08-02 20:53:34,521 - large_scale_feature_engineer - INFO - 数值列数: 9\n2025-08-02 20:53:34,521 - large_scale_model_trainer - INFO - 特征工程器初始化完成\n2025-08-02 20:53:34,521 - large_scale_model_trainer - INFO - 特征配置加载完成: 特征列数=14, 目标列=amount\n2025-08-02 20:53:34,521 - large_scale_feature_engineer - INFO - 开始拟合统计量\n2025-08-02 20:53:34,521 - large_scale_feature_engineer - INFO - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv\n2025-08-02 20:53:34,521 - large_scale_feature_engineer - INFO - 批次大小: 1,000\n\n统计拟合: 0批次 [00:00, ?批次/s]\n统计拟合: 0批次 [00:00, ?批次/s, 批次=1, 当前行数=1,000, 累计行数=1,000, 数值列=9, 类别列=4]\n统计拟合: 1批次 [00:00, 61.61批次/s, 批次=2, 当前行数=1,000, 累计行数=2,000, 数值列=9, 类别列=4]\n统计拟合: 2批次 [00:00, 86.69批次/s, 批次=3, 当前行数=1,000, 累计行数=3,000, 数值列=9, 类别列=4]\n统计拟合: 3批次 [00:00, 98.98批次/s, 批次=4, 当前行数=1,000, 累计行数=4,000, 数值列=9, 类别列=4]\n统计拟合: 4批次 [00:00, 108.21批次/s, 批次=5, 当前行数=1,000, 累计行数=5,000, 数值列=9, 类别列=4]\n统计拟合: 5批次 [00:00, 114.17批次/s, 批次=6, 当前行数=1,000, 累计行数=6,000, 数值列=9, 类别列=4]\n统计拟合: 6批次 [00:00, 117.66批次/s, 批次=7, 当前行数=1,000, 累计行数=7,000, 数值列=9, 类别列=4]\n统计拟合: 7批次 [00:00, 121.13批次/s, 批次=8, 当前行数=1,000, 累计行数=8,000, 数值列=9, 类别列=4]\n统计拟合: 8批次 [00:00, 124.12批次/s, 批次=9, 当前行数=1,000, 累计行数=9,000, 数值列=9, 类别列=4]\n统计拟合: 9批次 [00:00, 125.45批次/s, 批次=10, 当前行数=1,000, 累计行数=10,000, 数值列=9, 类别列=4]2025-08-02 20:53:34,609 - large_scale_feature_engineer - INFO - 处理进度: 已完成10批次, 累计10,000行\n\n统计拟合: 10批次 [00:00, 95.94批次/s, 批次=11, 当前行数=1,000, 累计行数=11,000, 数值列=9, 类别列=4]\n统计拟合: 11批次 [00:00, 105.49批次/s, 批次=11, 当前行数=1,000, 累计行数=11,000, 数值列=9, 类别列=4]\n统计拟合: 11批次 [00:00, 105.49批次/s, 批次=12, 当前行数=1,000, 累计行数=12,000, 数值列=9, 类别列=4]\n统计拟合: 12批次 [00:00, 105.49批次/s, 批次=13, 当前行数=1,000, 累计行数=13,000, 数值列=9, 类别列=4]\n统计拟合: 13批次 [00:00, 105.49批次/s, 批次=14, 当前行数=1,000, 累计行数=14,000, 数值列=9, 类别列=4]\n统计拟合: 14批次 [00:00, 105.49批次/s, 批次=15, 当前行数=1,000, 累计行数=15,000, 数值列=9, 类别列=4]\n统计拟合: 15批次 [00:00, 105.49批次/s, 批次=16, 当前行数=1,000, 累计行数=16,000, 数值列=9, 类别列=4]\n统计拟合: 16批次 [00:00, 105.49批次/s, 批次=17, 当前行数=1,000, 累计行数=17,000, 数值列=9, 类别列=4]\n统计拟合: 17批次 [00:00, 105.49批次/s, 批次=18, 当前行数=1,000, 累计行数=18,000, 数值列=9, 类别列=4]\n统计拟合: 18批次 [00:00, 105.49批次/s, 批次=19, 当前行数=1,000, 累计行数=19,000, 数值列=9, 类别列=4]\n统计拟合: 19批次 [00:00, 105.49批次/s, 批次=20, 当前行数=1,000, 累计行数=20,000, 数值列=9, 类别列=4]2025-08-02 20:53:34,703 - large_scale_feature_engineer - INFO - 处理进度: 已完成20批次, 累计20,000行\n\n统计拟合: 20批次 [00:00, 105.49批次/s, 批次=21, 当前行数=1,000, 累计行数=21,000, 数值列=9, 类别列=4]\n统计拟合: 21批次 [00:00, 105.49批次/s, 批次=22, 当前行数=1,000, 累计行数=22,000, 数值列=9, 类别列=4]\n统计拟合: 22批次 [00:00, 105.49批次/s, 批次=23, 当前行数=1,000, 累计行数=23,000, 数值列=9, 类别列=4]\n统计拟合: 23批次 [00:00, 112.83批次/s, 批次=23, 当前行数=1,000, 累计行数=23,000, 数值列=9, 类别列=4]\n统计拟合: 23批次 [00:00, 112.83批次/s, 批次=24, 当前行数=1,000, 累计行数=24,000, 数值列=9, 类别列=4]\n统计拟合: 24批次 [00:00, 112.83批次/s, 批次=25, 当前行数=1,000, 累计行数=25,000, 数值列=9, 类别列=4]\n统计拟合: 25批次 [00:00, 112.83批次/s, 批次=26, 当前行数=1,000, 累计行数=26,000, 数值列=9, 类别列=4]\n统计拟合: 26批次 [00:00, 112.83批次/s, 批次=27, 当前行数=1,000, 累计行数=27,000, 数值列=9, 类别列=4]\n统计拟合: 27批次 [00:00, 112.83批次/s, 批次=28, 当前行数=1,000, 累计行数=28,000, 数值列=9, 类别列=4]\n统计拟合: 28批次 [00:00, 112.83批次/s, 批次=29, 当前行数=1,000, 累计行数=29,000, 数值列=9, 类别列=4]\n统计拟合: 29批次 [00:00, 112.83批次/s, 批次=30, 当前行数=1,000, 累计行数=30,000, 数值列=9, 类别列=4]2025-08-02 20:53:34,791 - large_scale_feature_engineer - INFO - 处理进度: 已完成30批次, 累计30,000行\n\n统计拟合: 30批次 [00:00, 112.83批次/s, 批次=31, 当前行数=1,000, 累计行数=31,000, 数值列=9, 类别列=4]\n统计拟合: 31批次 [00:00, 112.83批次/s, 批次=32, 当前行数=1,000, 累计行数=32,000, 数值列=9, 类别列=4]\n统计拟合: 32批次 [00:00, 112.83批次/s, 批次=33, 当前行数=1,000, 累计行数=33,000, 数值列=9, 类别列=4]\n统计拟合: 33批次 [00:00, 112.83批次/s, 批次=34, 当前行数=1,000, 累计行数=34,000, 数值列=9, 类别列=4]\n统计拟合: 34批次 [00:00, 112.83批次/s, 批次=35, 当前行数=1,000, 累计行数=35,000, 数值列=9, 类别列=4]\n统计拟合: 35批次 [00:00, 115.96批次/s, 批次=35, 当前行数=1,000, 累计行数=35,000, 数值列=9, 类别列=4]\n统计拟合: 35批次 [00:00, 115.96批次/s, 批次=36, 当前行数=1,000, 累计行数=36,000, 数值列=9, 类别列=4]\n统计拟合: 36批次 [00:00, 115.96批次/s, 批次=37, 当前行数=1,000, 累计行数=37,000, 数值列=9, 类别列=4]\n统计拟合: 37批次 [00:00, 115.96批次/s, 批次=38, 当前行数=1,000, 累计行数=38,000, 数值列=9, 类别列=4]\n统计拟合: 38批次 [00:00, 115.96批次/s, 批次=39, 当前行数=1,000, 累计行数=39,000, 数值列=9, 类别列=4]\n统计拟合: 39批次 [00:00, 115.96批次/s, 批次=40, 当前行数=1,000, 累计行数=40,000, 数值列=9, 类别列=4]2025-08-02 20:53:34,879 - large_scale_feature_engineer - INFO - 处理进度: 已完成40批次, 累计40,000行\n\n统计拟合: 40批次 [00:00, 115.96批次/s, 批次=41, 当前行数=1,000, 累计行数=41,000, 数值列=9, 类别列=4]\n统计拟合: 41批次 [00:00, 115.96批次/s, 批次=42, 当前行数=1,000, 累计行数=42,000, 数值列=9, 类别列=4]\n统计拟合: 42批次 [00:00, 115.96批次/s, 批次=43, 当前行数=1,000, 累计行数=43,000, 数值列=9, 类别列=4]\n统计拟合: 43批次 [00:00, 115.96批次/s, 批次=44, 当前行数=1,000, 累计行数=44,000, 数值列=9, 类别列=4]\n统计拟合: 44批次 [00:00, 115.96批次/s, 批次=45, 当前行数=1,000, 累计行数=45,000, 数值列=9, 类别列=4]\n统计拟合: 45批次 [00:00, 115.96批次/s, 批次=46, 当前行数=1,000, 累计行数=46,000, 数值列=9, 类别列=4]\n统计拟合: 46批次 [00:00, 115.96批次/s, 批次=47, 当前行数=1,000, 累计行数=47,000, 数值列=9, 类别列=4]\n统计拟合: 47批次 [00:00, 115.70批次/s, 批次=47, 当前行数=1,000, 累计行数=47,000, 数值列=9, 类别列=4]\n统计拟合: 47批次 [00:00, 115.70批次/s, 批次=48, 当前行数=1,000, 累计行数=48,000, 数值列=9, 类别列=4]\n统计拟合: 48批次 [00:00, 115.70批次/s, 批次=49, 当前行数=283, 累计行数=48,283, 数值列=9, 类别列=4]  \n统计拟合: 49批次 [00:00, 116.53批次/s, 批次=49, 当前行数=283, 累计行数=48,283, 数值列=9, 类别列=4]\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=49, 总行数=48,283\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 统计摘要生成完成\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=871.03, 标准差=1001.03\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.31, 标准差=3.36\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=11.95, 标准差=10.31\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=878.68, 标准差=1009.84\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=6.80, 标准差=3.67\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=25.91, 标准差=9.40\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=9.79, 标准差=14.41\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=31.00, 标准差=0.00\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=774.35, 标准差=5241.57\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 4 个唯一值\n2025-08-02 20:53:34,958 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值\n2025-08-02 20:53:34,958 - large_scale_model_trainer - INFO - 特征工程器统计量拟合完成\n2025-08-02 20:53:34,958 - large_scale_model_trainer - INFO - 开始分批处理和训练: 批次大小=1,000\n2025-08-02 20:53:34,959 - large_scale_model_trainer - INFO - 模型初始化完成: RandomForestRegressor, 参数={'n_estimators': 100, 'max_depth': 10, 'min_samples_split': 5, 'min_samples_leaf': 2, 'random_state': 42, 'n_jobs': -1}\n2025-08-02 20:53:34,959 - large_scale_model_trainer - INFO - 开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/train_data_20250802_205332.csv, 批次大小: 1,000\n2025-08-02 20:53:34,959 - large_scale_model_trainer - INFO - 检测到分隔符: ','\n2025-08-02 20:53:34,959 - large_scale_model_trainer - INFO - 开始分批处理数据，使用分隔符: ','\n\n数据处理: 0批次 [00:00, ?批次/s]2025-08-02 20:53:34,965 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 0批次 [00:00, ?批次/s, 当前批次=1/None, 样本数=1,000, 累计=1,000, 内存批次=1]2025-08-02 20:53:34,971 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 1批次 [00:00, 78.26批次/s, 当前批次=2/None, 样本数=1,000, 累计=2,000, 内存批次=2]2025-08-02 20:53:34,977 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 2批次 [00:00, 106.02批次/s, 当前批次=3/None, 样本数=1,000, 累计=3,000, 内存批次=3]2025-08-02 20:53:34,984 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 3批次 [00:00, 119.90批次/s, 当前批次=4/None, 样本数=1,000, 累计=4,000, 内存批次=4]2025-08-02 20:53:34,990 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 4批次 [00:00, 127.99批次/s, 当前批次=5/None, 样本数=1,000, 累计=5,000, 内存批次=5]2025-08-02 20:53:34,995 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 5批次 [00:00, 136.71批次/s, 当前批次=6/None, 样本数=1,000, 累计=6,000, 内存批次=6]2025-08-02 20:53:35,000 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 6批次 [00:00, 143.56批次/s, 当前批次=7/None, 样本数=1,000, 累计=7,000, 内存批次=7]2025-08-02 20:53:35,007 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 7批次 [00:00, 145.51批次/s, 当前批次=8/None, 样本数=1,000, 累计=8,000, 内存批次=8]2025-08-02 20:53:35,013 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 8批次 [00:00, 147.03批次/s, 当前批次=9/None, 样本数=1,000, 累计=9,000, 内存批次=9]2025-08-02 20:53:35,019 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 9批次 [00:00, 149.83批次/s, 当前批次=10/None, 样本数=1,000, 累计=10,000, 内存批次=10]\n合并数据: : 10批次 [00:00, 166.40批次/s, 当前批次=10/None, 样本数=1,000, 累计=10,000, 内存批次=10]\n数据处理: : 10批次 [00:00, 126.34批次/s, 当前批次=10/None, 样本数=1,000, 累计=10,000, 内存批次=10]2025-08-02 20:53:35,044 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 10批次 [00:00, 117.07批次/s, 当前批次=11/None, 样本数=1,000, 累计=11,000, 内存批次=2] 2025-08-02 20:53:35,050 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 11批次 [00:00, 120.83批次/s, 当前批次=12/None, 样本数=1,000, 累计=12,000, 内存批次=3]2025-08-02 20:53:35,055 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 12批次 [00:00, 124.37批次/s, 当前批次=13/None, 样本数=1,000, 累计=13,000, 内存批次=4]2025-08-02 20:53:35,061 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 13批次 [00:00, 127.33批次/s, 当前批次=14/None, 样本数=1,000, 累计=14,000, 内存批次=5]\n数据处理: : 14批次 [00:00, 137.09批次/s, 当前批次=14/None, 样本数=1,000, 累计=14,000, 内存批次=5]2025-08-02 20:53:35,067 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 14批次 [00:00, 137.09批次/s, 当前批次=15/None, 样本数=1,000, 累计=15,000, 内存批次=6]2025-08-02 20:53:35,072 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 15批次 [00:00, 137.09批次/s, 当前批次=16/None, 样本数=1,000, 累计=16,000, 内存批次=7]2025-08-02 20:53:35,078 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 16批次 [00:00, 137.09批次/s, 当前批次=17/None, 样本数=1,000, 累计=17,000, 内存批次=8]2025-08-02 20:53:35,083 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 17批次 [00:00, 137.09批次/s, 当前批次=18/None, 样本数=1,000, 累计=18,000, 内存批次=9]2025-08-02 20:53:35,089 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 18批次 [00:00, 137.09批次/s, 当前批次=19/None, 样本数=1,000, 累计=19,000, 内存批次=10]2025-08-02 20:53:35,094 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 19批次 [00:00, 137.09批次/s, 当前批次=20/None, 样本数=1,000, 累计=20,000, 内存批次=11]\n合并数据: : 20批次 [00:00, 137.09批次/s, 当前批次=20/None, 样本数=1,000, 累计=20,000, 内存批次=11]\n数据处理: : 20批次 [00:00, 137.09批次/s, 当前批次=20/None, 样本数=1,000, 累计=20,000, 内存批次=11]2025-08-02 20:53:35,119 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 20批次 [00:00, 137.09批次/s, 当前批次=21/None, 样本数=1,000, 累计=21,000, 内存批次=2] 2025-08-02 20:53:35,126 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 21批次 [00:00, 137.09批次/s, 当前批次=22/None, 样本数=1,000, 累计=22,000, 内存批次=3]2025-08-02 20:53:35,132 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 22批次 [00:00, 137.09批次/s, 当前批次=23/None, 样本数=1,000, 累计=23,000, 内存批次=4]2025-08-02 20:53:35,138 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 23批次 [00:00, 137.09批次/s, 当前批次=24/None, 样本数=1,000, 累计=24,000, 内存批次=5]2025-08-02 20:53:35,144 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 24批次 [00:00, 137.09批次/s, 当前批次=25/None, 样本数=1,000, 累计=25,000, 内存批次=6]2025-08-02 20:53:35,149 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 25批次 [00:00, 137.09批次/s, 当前批次=26/None, 样本数=1,000, 累计=26,000, 内存批次=7]2025-08-02 20:53:35,156 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 26批次 [00:00, 137.09批次/s, 当前批次=27/None, 样本数=1,000, 累计=27,000, 内存批次=8]2025-08-02 20:53:35,161 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 27批次 [00:00, 137.09批次/s, 当前批次=28/None, 样本数=1,000, 累计=28,000, 内存批次=9]\n数据处理: : 28批次 [00:00, 138.27批次/s, 当前批次=28/None, 样本数=1,000, 累计=28,000, 内存批次=9]2025-08-02 20:53:35,168 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 28批次 [00:00, 138.27批次/s, 当前批次=29/None, 样本数=1,000, 累计=29,000, 内存批次=10]2025-08-02 20:53:35,173 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 29批次 [00:00, 138.27批次/s, 当前批次=30/None, 样本数=1,000, 累计=30,000, 内存批次=11]\n合并数据: : 30批次 [00:00, 138.27批次/s, 当前批次=30/None, 样本数=1,000, 累计=30,000, 内存批次=11]\n数据处理: : 30批次 [00:00, 138.27批次/s, 当前批次=30/None, 样本数=1,000, 累计=30,000, 内存批次=11]2025-08-02 20:53:35,201 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 30批次 [00:00, 138.27批次/s, 当前批次=31/None, 样本数=1,000, 累计=31,000, 内存批次=2] 2025-08-02 20:53:35,207 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 31批次 [00:00, 138.27批次/s, 当前批次=32/None, 样本数=1,000, 累计=32,000, 内存批次=3]2025-08-02 20:53:35,213 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 32批次 [00:00, 138.27批次/s, 当前批次=33/None, 样本数=1,000, 累计=33,000, 内存批次=4]2025-08-02 20:53:35,220 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 33批次 [00:00, 138.27批次/s, 当前批次=34/None, 样本数=1,000, 累计=34,000, 内存批次=5]2025-08-02 20:53:35,226 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 34批次 [00:00, 138.27批次/s, 当前批次=35/None, 样本数=1,000, 累计=35,000, 内存批次=6]2025-08-02 20:53:35,231 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 35批次 [00:00, 138.27批次/s, 当前批次=36/None, 样本数=1,000, 累计=36,000, 内存批次=7]2025-08-02 20:53:35,237 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 36批次 [00:00, 138.27批次/s, 当前批次=37/None, 样本数=1,000, 累计=37,000, 内存批次=8]2025-08-02 20:53:35,243 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 37批次 [00:00, 138.27批次/s, 当前批次=38/None, 样本数=1,000, 累计=38,000, 内存批次=9]2025-08-02 20:53:35,249 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 38批次 [00:00, 138.27批次/s, 当前批次=39/None, 样本数=1,000, 累计=39,000, 内存批次=10]2025-08-02 20:53:35,254 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 39批次 [00:00, 138.27批次/s, 当前批次=40/None, 样本数=1,000, 累计=40,000, 内存批次=11]\n合并数据: : 40批次 [00:00, 138.27批次/s, 当前批次=40/None, 样本数=1,000, 累计=40,000, 内存批次=11]\n数据处理: : 40批次 [00:00, 138.27批次/s, 当前批次=40/None, 样本数=1,000, 累计=40,000, 内存批次=11]2025-08-02 20:53:35,282 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 40批次 [00:00, 138.27批次/s, 当前批次=41/None, 样本数=1,000, 累计=41,000, 内存批次=2] 2025-08-02 20:53:35,288 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 41批次 [00:00, 138.27批次/s, 当前批次=42/None, 样本数=1,000, 累计=42,000, 内存批次=3]\n数据处理: : 42批次 [00:00, 124.30批次/s, 当前批次=42/None, 样本数=1,000, 累计=42,000, 内存批次=3]2025-08-02 20:53:35,294 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 42批次 [00:00, 124.30批次/s, 当前批次=43/None, 样本数=1,000, 累计=43,000, 内存批次=4]2025-08-02 20:53:35,301 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 43批次 [00:00, 124.30批次/s, 当前批次=44/None, 样本数=1,000, 累计=44,000, 内存批次=5]2025-08-02 20:53:35,306 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 44批次 [00:00, 124.30批次/s, 当前批次=45/None, 样本数=1,000, 累计=45,000, 内存批次=6]2025-08-02 20:53:35,313 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 45批次 [00:00, 124.30批次/s, 当前批次=46/None, 样本数=1,000, 累计=46,000, 内存批次=7]2025-08-02 20:53:35,320 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 46批次 [00:00, 124.30批次/s, 当前批次=47/None, 样本数=1,000, 累计=47,000, 内存批次=8]2025-08-02 20:53:35,326 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 47批次 [00:00, 124.30批次/s, 当前批次=48/None, 样本数=1,000, 累计=48,000, 内存批次=9]2025-08-02 20:53:35,330 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: : 48批次 [00:00, 124.30批次/s, 当前批次=49/None, 样本数=283, 累计=48,283, 内存批次=10] \n数据处理: : 49批次 [00:00, 131.82批次/s, 当前批次=49/None, 样本数=283, 累计=48,283, 内存批次=10]\n2025-08-02 20:53:35,331 - large_scale_model_trainer - INFO - 合并剩余的 10 批数据\n2025-08-02 20:53:35,333 - large_scale_model_trainer - INFO - 剩余数据合并完成，数据形状: (48283, 19)\n2025-08-02 20:53:35,333 - large_scale_model_trainer - INFO - 开始最终数据合并，待合并批次数: 1\n2025-08-02 20:53:35,333 - large_scale_model_trainer - INFO - 最终数据合并完成: X(48283, 19), y(48283,)\n2025-08-02 20:53:35,333 - large_scale_model_trainer - INFO - 开始分割训练测试集，测试集比例: 0.2\n2025-08-02 20:53:35,337 - large_scale_model_trainer - INFO - 数据集分割完成: 训练集=38,626样本, 测试集=9,657样本\n2025-08-02 20:53:35,337 - large_scale_model_trainer - INFO - 开始模型训练: 算法=RandomForestRegressor, 特征数=19\n2025-08-02 20:53:35,337 - large_scale_model_trainer - INFO - 模型参数: n_estimators=100, max_depth=10\n\n模型训练:   0%|          | 0/100 [00:00<?, ?%/s]\n模型训练:   0%|          | 0/100 [00:00<?, ?%/s, 状态=初始化]\n模型训练:  10%|█         | 10/100 [00:00<00:00, 226719.14%/s, 状态=构建决策树]\n模型训练: 100%|██████████| 100/100 [00:00<00:00, 447.62%/s, 状态=构建决策树]  \n模型训练: 100%|██████████| 100/100 [00:00<00:00, 447.62%/s, 状态=完成]      \n模型训练: 100%|██████████| 100/100 [00:00<00:00, 447.36%/s, 状态=完成]\n2025-08-02 20:53:35,561 - large_scale_model_trainer - INFO - 模型训练完成，耗时: 0.22秒\n2025-08-02 20:53:35,561 - large_scale_model_trainer - INFO - 开始模型性能评估，测试样本数: 9,657\n\n模型评估:   0%|          | 0/100 [00:00<?, ?%/s]\n模型评估:   0%|          | 0/100 [00:00<?, ?%/s, 状态=预测中]\n模型评估:  50%|█████     | 50/100 [00:00<00:00, 3240.85%/s, 状态=计算指标]\n模型评估: 100%|██████████| 100/100 [00:00<00:00, 6098.68%/s, 状态=完成]   \n模型评估: 100%|██████████| 100/100 [00:00<00:00, 6078.26%/s, 状态=完成]\n2025-08-02 20:53:35,578 - large_scale_model_trainer - INFO - 模型性能评估: MAE=118.25, RMSE=950.07, R²=0.5876\n2025-08-02 20:53:35,578 - large_scale_model_trainer - INFO - 性能等级评估: 较差 (R²=0.5876)\n2025-08-02 20:53:35,578 - large_scale_model_trainer - INFO - 开始保存模型和特征工程器: 时间戳=20250802_205335\n2025-08-02 20:53:35,596 - large_scale_feature_engineer - INFO - 保存预处理器到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205335.pkl\n2025-08-02 20:53:35,596 - large_scale_feature_engineer - INFO - 预处理器保存成功: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205335.pkl\n2025-08-02 20:53:35,596 - large_scale_model_trainer - INFO - 模型保存完成: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250802_205335.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205335.pkl\n", "returncode": 0}, "model_evaluation": {"success": true, "duration": 1.414358, "stdout": "加载模型和特征工程器...\n  传统模型加载成功: RandomForestRegressor\n  模型特征数: 19\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n  特征工程器加载成功\n开始大规模模型评估\n  测试文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  评估第 1 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=539.97, RMSE=13182.94, R²=0.0039\n    累计样本: 1,000\n\n  评估第 2 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=112.55, RMSE=473.17, R²=0.8715\n    累计样本: 2,000\n\n  评估第 3 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=97.23, RMSE=254.38, R²=0.9442\n    累计样本: 3,000\n\n  评估第 4 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=118.14, RMSE=521.85, R²=0.8000\n    累计样本: 4,000\n\n  评估第 5 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=99.66, RMSE=384.72, R²=0.9194\n    累计样本: 5,000\n\n  评估第 6 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=95.19, RMSE=239.69, R²=0.9529\n    累计样本: 6,000\n\n  评估第 7 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=116.02, RMSE=631.48, R²=0.7455\n    累计样本: 7,000\n\n  评估第 8 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=109.69, RMSE=344.11, R²=0.9647\n    累计样本: 8,000\n\n  评估第 9 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=98.01, RMSE=354.03, R²=0.9068\n    累计样本: 9,000\n\n  评估第 10 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=106.47, RMSE=426.98, R²=0.7861\n    累计样本: 10,000\n\n  评估第 11 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=100.16, RMSE=318.45, R²=0.9413\n    累计样本: 11,000\n\n  评估第 12 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=92.83, RMSE=257.18, R²=0.9485\n    累计样本: 12,000\n\n  评估第 13 批数据...\n    评估完成: 71 样本\n    批次指标: MAE=85.32, RMSE=180.45, R²=0.9910\n    累计样本: 12,071\n\n分批评估完成！\n  - 总批次: 13\n  - 总样本: 12,071\n  - 总耗时: 0.29秒\n  - 评估速度: 41855 样本/秒\n\n计算整体评估指标...\n使用传统模型评估...\n\n================================================================================\n大规模模型评估报告\n================================================================================\n\n整体性能指标:\n  - 样本数量: 12,071\n  - MAE (平均绝对误差): 140.17\n  - RMSE (均方根误差): 3813.56\n  - R² (决定系数): 0.0874\n\n💼 业务准确率 (预测误差在阈值内的比例):\n  - ±10元内: 38.2%\n  - ±20元内: 38.8%\n  - ±50元内: 39.6%\n  - ±100元内: 91.3%\n\n预测值分布:\n  - 范围: 0.00 - 31753.63\n  - 均值: 216.58\n  - 中位数: 76.40\n  - 标准差: 1167.77\n  - 零值比例: 5.9%\n\n真实值分布:\n  - 范围: 0.00 - 416666.00\n  - 均值: 224.93\n  - 中位数: 0.00\n  - 标准差: 3991.89\n  - 零值比例: 92.6%\n\n批次性能稳定性:\n  - R²变化范围: 0.0039 - 0.9910\n  - R²标准差: 0.2491\n  - MAE变化范围: 85.32 - 539.97\n  - MAE标准差: 116.91\n\n🏆 模型质量评估:\n   模型质量: 较差 (R²=0.0874)\n  业务适用性: 需要改进 (±50元准确率: 39.6%)\n\n评估报告已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/evaluation_report_20250802_205331.json\n\n大规模模型评估完成！\n", "stderr": "2025-08-02 20:53:36,724 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-02 20:53:36,725 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-02 20:53:36,738 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n2025-08-02 20:53:36,738 - large_scale_model_evaluator - INFO - 开始大规模模型评估: 测试文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv, 目标列=amount\n2025-08-02 20:53:36,745 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,776 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,800 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,821 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,842 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,864 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,886 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,907 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,929 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,951 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,972 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:36,994 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:37,013 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n", "returncode": 0}, "prediction": {"success": true, "duration": 1.552377, "stdout": "加载模型和特征工程器...\n  传统模型加载成功: RandomForestRegressor\n  模型特征数: 19\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n  特征工程器加载成功\n🔮 开始大规模数据预测\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n  输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205331.csv\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n保存最终预测结果...\n  最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205331.csv\n\n预测完成！\n  总预测数: 12,071 条\n  总耗时: 0.37秒\n  预测速度: 32788 条/秒\n  结果文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205331.csv\n\n预测统计报告:\n  - 输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n  - 输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205331.csv\n  - 预测总数: 12,071 条\n  - 处理时间: 0.37 秒\n  - 处理速度: 32788 条/秒\n  - 批次大小: 1,000 条\n\n预测结果分析:\n  - 最小值: 0.00\n  - 最大值: 31753.63\n  - 平均值: 216.58\n  - 中位数: 76.40\n  - 标准差: 1167.82\n  - 零值数量: 717 (5.9%)\n\n大规模预测完成！\n", "stderr": "2025-08-02 20:53:38,175 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-02 20:53:38,175 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-02 20:53:38,175 - large_scale_prediction - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250802_205335.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n2025-08-02 20:53:38,192 - large_scale_prediction - INFO - 传统模型加载成功: RandomForestRegressor\n2025-08-02 20:53:38,192 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n2025-08-02 20:53:38,192 - large_scale_prediction - INFO - 开始大规模数据预测: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205331.csv\n\n🔮 批量预测: 0批次 [00:00, ?批次/s]2025-08-02 20:53:38,215 - large_scale_prediction - INFO - 开始处理第 1 批数据，样本数: 1000\n2025-08-02 20:53:38,220 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,239 - large_scale_prediction - INFO - 第 1 批预测完成: 1,000 条，累计: 1,000 条\n\n🔮 批量预测: 0批次 [00:00, ?批次/s, 批次=1, 当前=1,000, 累计=1,000, 预测范围=0.0-10931.5]2025-08-02 20:53:38,241 - large_scale_prediction - INFO - 开始处理第 2 批数据，样本数: 1000\n2025-08-02 20:53:38,246 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,261 - large_scale_prediction - INFO - 第 2 批预测完成: 1,000 条，累计: 2,000 条\n\n🔮 批量预测: 1批次 [00:00, 20.64批次/s, 批次=2, 当前=1,000, 累计=2,000, 预测范围=0.0-19824.2]2025-08-02 20:53:38,263 - large_scale_prediction - INFO - 开始处理第 3 批数据，样本数: 1000\n2025-08-02 20:53:38,268 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,283 - large_scale_prediction - INFO - 第 3 批预测完成: 1,000 条，累计: 3,000 条\n\n🔮 批量预测: 2批次 [00:00, 28.28批次/s, 批次=3, 当前=1,000, 累计=3,000, 预测范围=0.0-12863.5]2025-08-02 20:53:38,286 - large_scale_prediction - INFO - 开始处理第 4 批数据，样本数: 1000\n2025-08-02 20:53:38,290 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,305 - large_scale_prediction - INFO - 第 4 批预测完成: 1,000 条，累计: 4,000 条\n\n🔮 批量预测: 3批次 [00:00, 32.19批次/s, 批次=4, 当前=1,000, 累计=4,000, 预测范围=0.0-19805.9]2025-08-02 20:53:38,308 - large_scale_prediction - INFO - 开始处理第 5 批数据，样本数: 1000\n2025-08-02 20:53:38,312 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,327 - large_scale_prediction - INFO - 第 5 批预测完成: 1,000 条，累计: 5,000 条\n\n🔮 批量预测: 4批次 [00:00, 34.93批次/s, 批次=5, 当前=1,000, 累计=5,000, 预测范围=0.0-12894.3]\n🔮 批量预测: 5批次 [00:00, 43.64批次/s, 批次=5, 当前=1,000, 累计=5,000, 预测范围=0.0-12894.3]2025-08-02 20:53:38,328 - large_scale_prediction - INFO - 开始处理第 6 批数据，样本数: 1000\n2025-08-02 20:53:38,333 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,348 - large_scale_prediction - INFO - 第 6 批预测完成: 1,000 条，累计: 6,000 条\n\n🔮 批量预测: 5批次 [00:00, 43.64批次/s, 批次=6, 当前=1,000, 累计=6,000, 预测范围=0.0-18433.6]2025-08-02 20:53:38,350 - large_scale_prediction - INFO - 开始处理第 7 批数据，样本数: 1000\n2025-08-02 20:53:38,355 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,370 - large_scale_prediction - INFO - 第 7 批预测完成: 1,000 条，累计: 7,000 条\n\n🔮 批量预测: 6批次 [00:00, 43.64批次/s, 批次=7, 当前=1,000, 累计=7,000, 预测范围=0.0-17056.0]2025-08-02 20:53:38,371 - large_scale_prediction - INFO - 开始处理第 8 批数据，样本数: 1000\n2025-08-02 20:53:38,376 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,391 - large_scale_prediction - INFO - 第 8 批预测完成: 1,000 条，累计: 8,000 条\n\n🔮 批量预测: 7批次 [00:00, 43.64批次/s, 批次=8, 当前=1,000, 累计=8,000, 预测范围=0.0-31753.6]2025-08-02 20:53:38,393 - large_scale_prediction - INFO - 开始处理第 9 批数据，样本数: 1000\n2025-08-02 20:53:38,398 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,413 - large_scale_prediction - INFO - 第 9 批预测完成: 1,000 条，累计: 9,000 条\n\n🔮 批量预测: 8批次 [00:00, 43.64批次/s, 批次=9, 当前=1,000, 累计=9,000, 预测范围=0.0-12929.7]2025-08-02 20:53:38,415 - large_scale_prediction - INFO - 开始处理第 10 批数据，样本数: 1000\n2025-08-02 20:53:38,420 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,435 - large_scale_prediction - INFO - 第 10 批预测完成: 1,000 条，累计: 10,000 条\n\n🔮 批量预测: 9批次 [00:00, 43.64批次/s, 批次=10, 当前=1,000, 累计=10,000, 预测范围=0.0-12898.4]\n🔮 批量预测: 10批次 [00:00, 45.19批次/s, 批次=10, 当前=1,000, 累计=10,000, 预测范围=0.0-12898.4]2025-08-02 20:53:38,436 - large_scale_prediction - INFO - 开始处理第 11 批数据，样本数: 1000\n2025-08-02 20:53:38,441 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,456 - large_scale_prediction - INFO - 第 11 批预测完成: 1,000 条，累计: 11,000 条\n\n🔮 批量预测: 10批次 [00:00, 45.19批次/s, 批次=11, 当前=1,000, 累计=11,000, 预测范围=0.0-31362.0]2025-08-02 20:53:38,458 - large_scale_prediction - INFO - 开始处理第 12 批数据，样本数: 1000\n2025-08-02 20:53:38,462 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,476 - large_scale_prediction - INFO - 第 12 批预测完成: 1,000 条，累计: 12,000 条\n\n🔮 批量预测: 11批次 [00:00, 45.19批次/s, 批次=12, 当前=1,000, 累计=12,000, 预测范围=0.0-19823.0]2025-08-02 20:53:38,477 - large_scale_prediction - INFO - 开始处理第 13 批数据，样本数: 71\n2025-08-02 20:53:38,482 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:38,498 - large_scale_prediction - INFO - 第 13 批预测完成: 71 条，累计: 12,071 条\n\n🔮 批量预测: 12批次 [00:00, 45.19批次/s, 批次=13, 当前=71, 累计=12,071, 预测范围=0.0-12574.2]   \n🔮 批量预测: 13批次 [00:00, 45.50批次/s, 批次=13, 当前=71, 累计=12,071, 预测范围=0.0-12574.2]\n2025-08-02 20:53:38,560 - large_scale_prediction - INFO - 最终结果已保存(包含特征): /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250802_205331.csv, 记录数: 12071\n2025-08-02 20:53:38,561 - large_scale_prediction - INFO - 列顺序: 训练特征(14) + 透传字段(11) + 目标字段(1) + 预测字段(1)\n2025-08-02 20:53:38,561 - large_scale_prediction - INFO - 预测完成: 总数=12,071, 耗时=0.37秒, 速度=32788条/秒\n", "returncode": 0}, "billing_judgment": {"success": true, "duration": 1.467777, "stdout": "加载模型和特征工程器...\n  传统模型加载成功: RandomForestRegressor\n  模型特征数: 19\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n  特征工程器加载成功\n🏛️ 大规模收费合理性判定器初始化完成\n  - 批次大小: 1,000\n  - 绝对阈值: ±10.0元\n  - 相对阈值: ±10.0%\n⚖️  开始大规模收费合理性判定\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n  输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_205331.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  判定第 1 批数据...\n    判定完成: 1,000 条\n    累计判定: 1,000 条\n    批次统计: 合理39.4% | 不合理7.4% | 不确定53.2%\n\n  判定第 2 批数据...\n    判定完成: 1,000 条\n    累计判定: 2,000 条\n    批次统计: 合理39.4% | 不合理7.0% | 不确定53.6%\n\n  判定第 3 批数据...\n    判定完成: 1,000 条\n    累计判定: 3,000 条\n    批次统计: 合理38.7% | 不合理6.6% | 不确定54.7%\n\n  判定第 4 批数据...\n    判定完成: 1,000 条\n    累计判定: 4,000 条\n    批次统计: 合理41.7% | 不合理7.1% | 不确定51.2%\n\n  判定第 5 批数据...\n    判定完成: 1,000 条\n    累计判定: 5,000 条\n    批次统计: 合理42.8% | 不合理6.7% | 不确定50.5%\n\n  判定第 6 批数据...\n    判定完成: 1,000 条\n    累计判定: 6,000 条\n    批次统计: 合理41.6% | 不合理7.4% | 不确定51.0%\n\n  判定第 7 批数据...\n    判定完成: 1,000 条\n    累计判定: 7,000 条\n    批次统计: 合理40.6% | 不合理7.5% | 不确定51.9%\n\n  判定第 8 批数据...\n    判定完成: 1,000 条\n    累计判定: 8,000 条\n    批次统计: 合理40.4% | 不合理6.9% | 不确定52.7%\n\n  判定第 9 批数据...\n    判定完成: 1,000 条\n    累计判定: 9,000 条\n    批次统计: 合理42.4% | 不合理5.6% | 不确定52.0%\n\n  判定第 10 批数据...\n    判定完成: 1,000 条\n    累计判定: 10,000 条\n    批次统计: 合理42.0% | 不合理6.9% | 不确定51.1%\n\n  判定第 11 批数据...\n    判定完成: 1,000 条\n    累计判定: 11,000 条\n    批次统计: 合理40.4% | 不合理6.8% | 不确定52.8%\n\n  判定第 12 批数据...\n    判定完成: 1,000 条\n    累计判定: 12,000 条\n    批次统计: 合理39.8% | 不合理6.6% | 不确定53.6%\n\n  判定第 13 批数据...\n    判定完成: 71 条\n    累计判定: 12,071 条\n    批次统计: 合理40.8% | 不合理5.6% | 不确定53.5%\n\n保存最终判定结果...\n  最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_205331.csv\n\n⚖️  大规模收费合理性判定完成！\n  总判定数: 12,071 条\n  总耗时: 0.38秒\n  判定速度: 31473 条/秒\n  结果文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_205331.csv\n\n判定统计:\n  - 合理收费: 4,921 条 (40.8%)\n  - 不合理收费: 829 条 (6.9%)\n  - 不确定收费: 6,321 条 (52.4%)\n\n大规模收费合理性判定完成！\n", "stderr": "2025-08-02 20:53:39,650 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-02 20:53:39,650 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-02 20:53:39,650 - large_scale_billing_judge - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250802_205335.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n2025-08-02 20:53:39,661 - large_scale_billing_judge - INFO - 传统模型加载成功: RandomForestRegressor\n2025-08-02 20:53:39,661 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250802_205331.pkl\n2025-08-02 20:53:39,661 - large_scale_billing_judge - INFO - 大规模收费合理性判定器初始化完成\n2025-08-02 20:53:39,661 - large_scale_billing_judge - INFO - 批次大小: 1,000\n2025-08-02 20:53:39,661 - large_scale_billing_judge - INFO - 绝对阈值: ±10.0元\n2025-08-02 20:53:39,661 - large_scale_billing_judge - INFO - 相对阈值: ±10.0%\n2025-08-02 20:53:39,661 - large_scale_billing_judge - INFO - 开始大规模收费合理性判定: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_205331.csv, 目标列=amount\n2025-08-02 20:53:39,661 - large_scale_billing_judge - INFO - 开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250802_205331/test_data_20250802_205332.csv, 批次大小: 1,000\n2025-08-02 20:53:39,662 - large_scale_billing_judge - INFO - 检测到分隔符: ','\n2025-08-02 20:53:39,663 - large_scale_billing_judge - INFO - 开始判定第 1 批数据，样本数: 1000\n2025-08-02 20:53:39,668 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,694 - large_scale_billing_judge - INFO - 第 1 批判定完成: 1,000 条，累计: 1,000 条\n2025-08-02 20:53:39,695 - large_scale_billing_judge - INFO - 开始判定第 2 批数据，样本数: 1000\n2025-08-02 20:53:39,700 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,717 - large_scale_billing_judge - INFO - 第 2 批判定完成: 1,000 条，累计: 2,000 条\n2025-08-02 20:53:39,719 - large_scale_billing_judge - INFO - 开始判定第 3 批数据，样本数: 1000\n2025-08-02 20:53:39,723 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,741 - large_scale_billing_judge - INFO - 第 3 批判定完成: 1,000 条，累计: 3,000 条\n2025-08-02 20:53:39,742 - large_scale_billing_judge - INFO - 开始判定第 4 批数据，样本数: 1000\n2025-08-02 20:53:39,746 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,763 - large_scale_billing_judge - INFO - 第 4 批判定完成: 1,000 条，累计: 4,000 条\n2025-08-02 20:53:39,765 - large_scale_billing_judge - INFO - 开始判定第 5 批数据，样本数: 1000\n2025-08-02 20:53:39,769 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,786 - large_scale_billing_judge - INFO - 第 5 批判定完成: 1,000 条，累计: 5,000 条\n2025-08-02 20:53:39,788 - large_scale_billing_judge - INFO - 开始判定第 6 批数据，样本数: 1000\n2025-08-02 20:53:39,792 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,809 - large_scale_billing_judge - INFO - 第 6 批判定完成: 1,000 条，累计: 6,000 条\n2025-08-02 20:53:39,811 - large_scale_billing_judge - INFO - 开始判定第 7 批数据，样本数: 1000\n2025-08-02 20:53:39,815 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,832 - large_scale_billing_judge - INFO - 第 7 批判定完成: 1,000 条，累计: 7,000 条\n2025-08-02 20:53:39,833 - large_scale_billing_judge - INFO - 开始判定第 8 批数据，样本数: 1000\n2025-08-02 20:53:39,837 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,854 - large_scale_billing_judge - INFO - 第 8 批判定完成: 1,000 条，累计: 8,000 条\n2025-08-02 20:53:39,856 - large_scale_billing_judge - INFO - 开始判定第 9 批数据，样本数: 1000\n2025-08-02 20:53:39,860 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,875 - large_scale_billing_judge - INFO - 第 9 批判定完成: 1,000 条，累计: 9,000 条\n2025-08-02 20:53:39,877 - large_scale_billing_judge - INFO - 开始判定第 10 批数据，样本数: 1000\n2025-08-02 20:53:39,881 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,898 - large_scale_billing_judge - INFO - 第 10 批判定完成: 1,000 条，累计: 10,000 条\n2025-08-02 20:53:39,902 - large_scale_billing_judge - INFO - 开始判定第 11 批数据，样本数: 1000\n2025-08-02 20:53:39,909 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,924 - large_scale_billing_judge - INFO - 第 11 批判定完成: 1,000 条，累计: 11,000 条\n2025-08-02 20:53:39,925 - large_scale_billing_judge - INFO - 开始判定第 12 批数据，样本数: 1000\n2025-08-02 20:53:39,929 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,946 - large_scale_billing_judge - INFO - 第 12 批判定完成: 1,000 条，累计: 12,000 条\n2025-08-02 20:53:39,947 - large_scale_billing_judge - INFO - 开始判定第 13 批数据，样本数: 71\n2025-08-02 20:53:39,951 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-02 20:53:39,965 - large_scale_billing_judge - INFO - 第 13 批判定完成: 71 条，累计: 12,071 条\n2025-08-02 20:53:39,966 - large_scale_billing_judge - INFO - 开始保存最终判定结果到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_205331.csv\n2025-08-02 20:53:40,045 - large_scale_billing_judge - INFO - 最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250802_205331.csv, 总记录数: 12071\n2025-08-02 20:53:40,045 - large_scale_billing_judge - INFO - 大规模收费合理性判定完成: 总数=12,071, 耗时=0.38秒, 速度=31473条/秒\n2025-08-02 20:53:40,046 - large_scale_billing_judge - INFO - 判定统计: 合理=4,921(40.8%), 不合理=829(6.9%), 不确定=6,321(52.4%)\n", "returncode": 0}}, "summary": {"total_steps": 7, "successful_steps": 7, "failed_steps": 0, "success_rate": 100.0}}