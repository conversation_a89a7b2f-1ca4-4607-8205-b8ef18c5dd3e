{"execution_info": {"timestamp": "20250806_143611", "start_time": "2025-08-06T14:36:18.043273", "total_duration": 6.898659, "project_root": "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI"}, "results": {"data_validation": {"success": true, "data_quality": {"total_samples": 7379, "total_features": 14, "missing_values": [], "zero_ratio": 0.43637349234313594, "has_missing_values": false}}, "feature_engineering": {"success": true, "duration": 0.479738, "stdout": "初始化大规模特征工程器\n  - 特征列数: 14\n  - 类别列数: 4\n  - 数值列数: 9\n开始拟合统计量...\n  - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv\n  - 批次大小: 1,000\n  统计量拟合完成:\n    - 总批次: 8\n    - 总行数: 7,379\n\n数值列统计摘要:\n  final_eff_year: 均值=2021.66, 标准差=2.88, 范围=[2003.00, 2025.00]\n  final_eff_mon: 均值=6.29, 标准差=3.39, 范围=[1.00, 12.00]\n  final_eff_day: 均值=8.65, 标准差=9.93, 范围=[1.00, 31.00]\n  final_exp_year: 均值=2039.83, 标准差=9.83, 范围=[2022.00, 2051.00]\n  final_exp_mon: 均值=7.46, 标准差=3.83, 范围=[1.00, 12.00]\n  final_exp_day: 均值=23.40, 标准差=11.00, 范围=[1.00, 31.00]\n  charge_day_count: 均值=23.46, 标准差=12.55, 范围=[0.00, 37.00]\n  month_day_count: 均值=30.00, 标准差=0.00, 范围=[30.00, 30.00]\n  should_fee: 均值=6361.62, 标准差=13478.20, 范围=[100.00, 346000.00]\n\n类别列统计摘要:\n  cal_type: 3 个唯一值\n  unit_type: 2 个唯一值\n  rate_unit: 3 个唯一值\n  busi_flag: 2 个唯一值\n预处理器已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n\n大规模特征工程器测试完成！\n", "stderr": "2025-08-06 14:36:11,534 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:36:11,534 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:36:11,534 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器\n2025-08-06 14:36:11,535 - large_scale_feature_engineer - INFO - 特征列数: 14\n2025-08-06 14:36:11,535 - large_scale_feature_engineer - INFO - 类别列数: 4\n2025-08-06 14:36:11,535 - large_scale_feature_engineer - INFO - 数值列数: 9\n2025-08-06 14:36:11,535 - large_scale_feature_engineer - INFO - 开始拟合统计量\n2025-08-06 14:36:11,535 - large_scale_feature_engineer - INFO - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv\n2025-08-06 14:36:11,535 - large_scale_feature_engineer - INFO - 批次大小: 1,000\n\n统计拟合: 0批次 [00:00, ?批次/s]\n统计拟合: 0批次 [00:00, ?批次/s, 批次=1, 当前行数=1,000, 累计行数=1,000, 数值列=9, 类别列=4]\n统计拟合: 1批次 [00:00, 75.85批次/s, 批次=2, 当前行数=1,000, 累计行数=2,000, 数值列=9, 类别列=4]\n统计拟合: 2批次 [00:00, 105.30批次/s, 批次=3, 当前行数=1,000, 累计行数=3,000, 数值列=9, 类别列=4]\n统计拟合: 3批次 [00:00, 118.42批次/s, 批次=4, 当前行数=1,000, 累计行数=4,000, 数值列=9, 类别列=4]\n统计拟合: 4批次 [00:00, 128.60批次/s, 批次=5, 当前行数=1,000, 累计行数=5,000, 数值列=9, 类别列=4]\n统计拟合: 5批次 [00:00, 133.46批次/s, 批次=6, 当前行数=1,000, 累计行数=6,000, 数值列=9, 类别列=4]\n统计拟合: 6批次 [00:00, 138.77批次/s, 批次=7, 当前行数=1,000, 累计行数=7,000, 数值列=9, 类别列=4]\n统计拟合: 7批次 [00:00, 153.04批次/s, 批次=8, 当前行数=379, 累计行数=7,379, 数值列=9, 类别列=4]  \n统计拟合: 8批次 [00:00, 174.50批次/s, 批次=8, 当前行数=379, 累计行数=7,379, 数值列=9, 类别列=4]\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=8, 总行数=7,379\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 统计摘要生成完成\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=2021.66, 标准差=2.88\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.29, 标准差=3.39\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=8.65, 标准差=9.93\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=2039.83, 标准差=9.83\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=7.46, 标准差=3.83\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=23.40, 标准差=11.00\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=23.46, 标准差=12.55\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=30.00, 标准差=0.00\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=6361.62, 标准差=13478.20\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 3 个唯一值\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值\n2025-08-06 14:36:11,599 - large_scale_feature_engineer - INFO - 保存预处理器到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n2025-08-06 14:36:11,600 - large_scale_feature_engineer - INFO - 预处理器保存成功: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n", "returncode": 0}, "data_splitting": {"success": true, "duration": 1.59302, "stdout": "🔀 开始数据拆分\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/processed_data_20250806_143611.csv\n  输出目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611\n  测试集比例: 0.2\n  目标列: amount\n\n读取数据...\n  数据形状: (7379, 26)\n  特征数: 25\n  样本数: 7379\n\n拆分数据...\n  训练集: 5,903 样本\n  测试集: 1,476 样本\n\n保存拆分后的数据...\n  训练集文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv\n  测试集文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n\n验证保存的文件:\n  训练集: (5903, 26)\n  测试集: (1476, 26)\n  总样本数: 7379 (原始: 7379)\n\n数据拆分统计:\n  训练集目标值范围: 0.00 - 59900.00\n  测试集目标值范围: 0.00 - 19900.00\n  训练集目标值均值: 1520.37\n  测试集目标值均值: 1466.04\n\n✅ 数据拆分完成\n\n🎯 数据拆分成功完成\n训练集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv\n测试集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n", "stderr": "2025-08-06 14:36:13,134 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:36:13,135 - src.config.production_config_manager - INFO - 配置加载成功\n", "returncode": 0}, "model_training": {"success": true, "duration": 1.697452, "stdout": "大规模数据模型训练\n============================================================\n拟合大规模特征工程器...\n初始化大规模特征工程器\n  - 特征列数: 14\n  - 类别列数: 4\n  - 数值列数: 9\n开始拟合统计量...\n  - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv\n  - 批次大小: 1,000\n  统计量拟合完成:\n    - 总批次: 6\n    - 总行数: 5,903\n\n数值列统计摘要:\n  final_eff_year: 均值=2021.67, 标准差=2.89, 范围=[2003.00, 2025.00]\n  final_eff_mon: 均值=6.29, 标准差=3.40, 范围=[1.00, 12.00]\n  final_eff_day: 均值=8.71, 标准差=9.96, 范围=[1.00, 31.00]\n  final_exp_year: 均值=2039.88, 标准差=9.81, 范围=[2022.00, 2051.00]\n  final_exp_mon: 均值=7.45, 标准差=3.85, 范围=[1.00, 12.00]\n  final_exp_day: 均值=23.28, 标准差=11.11, 范围=[1.00, 31.00]\n  charge_day_count: 均值=23.66, 标准差=12.43, 范围=[0.00, 37.00]\n  month_day_count: 均值=30.00, 标准差=0.00, 范围=[30.00, 30.00]\n  should_fee: 均值=6339.86, 标准差=13565.48, 范围=[100.00, 346000.00]\n\n类别列统计摘要:\n  cal_type: 3 个唯一值\n  unit_type: 2 个唯一值\n  rate_unit: 3 个唯一值\n  busi_flag: 2 个唯一值\n  特征工程器拟合完成\n\n开始分批处理和训练...\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n  开始分批处理数据...\n  合并剩余的 6 批数据...\n    最终合并完成，数据形状: (5903, 19)\n\n合并所有数据...\n  最终数据形状: X(5903, 19), y(5903,)\n\n分割训练测试集...\n  训练集: 4,722 样本\n  测试集: 1,181 样本\n\n开始训练模型...\n  模型参数: n_estimators=100, max_depth=10\n  训练样本: 4,722 行 × 19 特征\n  训练完成，耗时: 0.07秒\n\n评估模型性能...\n  测试集性能:\n    - MAE: 328.28\n    - RMSE: 860.86\n    - R²: 0.9355\n    - 性能等级: 优秀\n预处理器已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143614.pkl\n\n模型保存完成:\n  模型文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_143614.pkl\n  特征工程器: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143614.pkl\n\n大规模模型训练完成！\n处理样本: 5,903 条\n训练时间: 0.07秒\n模型性能: R²=0.9355\n", "stderr": "2025-08-06 14:36:14,658 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:36:14,658 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:36:14,658 - large_scale_model_trainer - INFO - 开始大规模数据模型训练: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv\n2025-08-06 14:36:14,658 - large_scale_model_trainer - INFO - 开始拟合大规模特征工程器: 文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv, 批次大小=1,000\n2025-08-06 14:36:14,658 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器\n2025-08-06 14:36:14,658 - large_scale_feature_engineer - INFO - 特征列数: 14\n2025-08-06 14:36:14,658 - large_scale_feature_engineer - INFO - 类别列数: 4\n2025-08-06 14:36:14,658 - large_scale_feature_engineer - INFO - 数值列数: 9\n2025-08-06 14:36:14,658 - large_scale_model_trainer - INFO - 特征工程器初始化完成\n2025-08-06 14:36:14,658 - large_scale_model_trainer - INFO - 特征配置加载完成: 特征列数=14, 目标列=amount\n2025-08-06 14:36:14,658 - large_scale_feature_engineer - INFO - 开始拟合统计量\n2025-08-06 14:36:14,658 - large_scale_feature_engineer - INFO - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv\n2025-08-06 14:36:14,658 - large_scale_feature_engineer - INFO - 批次大小: 1,000\n\n统计拟合: 0批次 [00:00, ?批次/s]\n统计拟合: 0批次 [00:00, ?批次/s, 批次=1, 当前行数=1,000, 累计行数=1,000, 数值列=9, 类别列=4]\n统计拟合: 1批次 [00:00, 79.67批次/s, 批次=2, 当前行数=1,000, 累计行数=2,000, 数值列=9, 类别列=4]\n统计拟合: 2批次 [00:00, 108.55批次/s, 批次=3, 当前行数=1,000, 累计行数=3,000, 数值列=9, 类别列=4]\n统计拟合: 3批次 [00:00, 121.71批次/s, 批次=4, 当前行数=1,000, 累计行数=4,000, 数值列=9, 类别列=4]\n统计拟合: 4批次 [00:00, 131.50批次/s, 批次=5, 当前行数=1,000, 累计行数=5,000, 数值列=9, 类别列=4]\n统计拟合: 5批次 [00:00, 141.09批次/s, 批次=6, 当前行数=903, 累计行数=5,903, 数值列=9, 类别列=4]  \n统计拟合: 6批次 [00:00, 168.70批次/s, 批次=6, 当前行数=903, 累计行数=5,903, 数值列=9, 类别列=4]\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=6, 总行数=5,903\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 统计摘要生成完成\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=2021.67, 标准差=2.89\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.29, 标准差=3.40\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=8.71, 标准差=9.96\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=2039.88, 标准差=9.81\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=7.45, 标准差=3.85\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=23.28, 标准差=11.11\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=23.66, 标准差=12.43\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=30.00, 标准差=0.00\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=6339.86, 标准差=13565.48\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 3 个唯一值\n2025-08-06 14:36:14,704 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值\n2025-08-06 14:36:14,704 - large_scale_model_trainer - INFO - 特征工程器统计量拟合完成\n2025-08-06 14:36:14,704 - large_scale_model_trainer - INFO - 开始分批处理和训练: 批次大小=1,000\n2025-08-06 14:36:14,704 - large_scale_model_trainer - INFO - 模型初始化完成: RandomForestRegressor, 参数={'n_estimators': 100, 'max_depth': 10, 'min_samples_split': 5, 'min_samples_leaf': 2, 'random_state': 42, 'n_jobs': -1}\n2025-08-06 14:36:14,704 - large_scale_model_trainer - INFO - 开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/train_data_20250806_143613.csv, 批次大小: 1,000\n2025-08-06 14:36:14,704 - large_scale_model_trainer - INFO - 检测到分隔符: ','\n2025-08-06 14:36:14,705 - large_scale_model_trainer - INFO - 开始分批处理数据，使用分隔符: ','\n\n数据处理: 0批次 [00:00, ?批次/s]2025-08-06 14:36:14,711 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 0批次 [00:00, ?批次/s, 当前批次=1/None, 样本数=1,000, 累计=1,000, 内存批次=1]2025-08-06 14:36:14,715 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 1批次 [00:00, 93.11批次/s, 当前批次=2/None, 样本数=1,000, 累计=2,000, 内存批次=2]2025-08-06 14:36:14,721 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 2批次 [00:00, 123.27批次/s, 当前批次=3/None, 样本数=1,000, 累计=3,000, 内存批次=3]2025-08-06 14:36:14,726 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 3批次 [00:00, 142.23批次/s, 当前批次=4/None, 样本数=1,000, 累计=4,000, 内存批次=4]2025-08-06 14:36:14,730 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 4批次 [00:00, 156.65批次/s, 当前批次=5/None, 样本数=1,000, 累计=5,000, 内存批次=5]2025-08-06 14:36:14,734 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n\n数据处理: 5批次 [00:00, 168.45批次/s, 当前批次=6/None, 样本数=903, 累计=5,903, 内存批次=6]  \n数据处理: 6批次 [00:00, 201.67批次/s, 当前批次=6/None, 样本数=903, 累计=5,903, 内存批次=6]\n2025-08-06 14:36:14,735 - large_scale_model_trainer - INFO - 合并剩余的 6 批数据\n2025-08-06 14:36:14,735 - large_scale_model_trainer - INFO - 剩余数据合并完成，数据形状: (5903, 19)\n2025-08-06 14:36:14,735 - large_scale_model_trainer - INFO - 开始最终数据合并，待合并批次数: 1\n2025-08-06 14:36:14,735 - large_scale_model_trainer - INFO - 最终数据合并完成: X(5903, 19), y(5903,)\n2025-08-06 14:36:14,735 - large_scale_model_trainer - INFO - 开始分割训练测试集，测试集比例: 0.2\n2025-08-06 14:36:14,736 - large_scale_model_trainer - INFO - 数据集分割完成: 训练集=4,722样本, 测试集=1,181样本\n2025-08-06 14:36:14,736 - large_scale_model_trainer - INFO - 开始模型训练: 算法=RandomForestRegressor, 特征数=19\n2025-08-06 14:36:14,736 - large_scale_model_trainer - INFO - 模型参数: n_estimators=100, max_depth=10\n\n模型训练:   0%|          | 0/100 [00:00<?, ?%/s]\n模型训练:   0%|          | 0/100 [00:00<?, ?%/s, 状态=初始化]\n模型训练:  10%|█         | 10/100 [00:00<00:00, 262144.00%/s, 状态=构建决策树]\n模型训练: 100%|██████████| 100/100 [00:00<00:00, 1373.85%/s, 状态=完成]       \n模型训练: 100%|██████████| 100/100 [00:00<00:00, 1372.19%/s, 状态=完成]\n2025-08-06 14:36:14,810 - large_scale_model_trainer - INFO - 模型训练完成，耗时: 0.07秒\n2025-08-06 14:36:14,810 - large_scale_model_trainer - INFO - 开始模型性能评估，测试样本数: 1,181\n\n模型评估:   0%|          | 0/100 [00:00<?, ?%/s]\n模型评估:   0%|          | 0/100 [00:00<?, ?%/s, 状态=预测中]\n模型评估:  50%|█████     | 50/100 [00:00<00:00, 3506.53%/s, 状态=计算指标]\n模型评估: 100%|██████████| 100/100 [00:00<00:00, 6657.41%/s, 状态=完成]   \n模型评估: 100%|██████████| 100/100 [00:00<00:00, 6638.34%/s, 状态=完成]\n2025-08-06 14:36:14,825 - large_scale_model_trainer - INFO - 模型性能评估: MAE=328.28, RMSE=860.86, R²=0.9355\n2025-08-06 14:36:14,825 - large_scale_model_trainer - INFO - 性能等级评估: 优秀 (R²=0.9355)\n2025-08-06 14:36:14,825 - large_scale_model_trainer - INFO - 开始保存模型和特征工程器: 时间戳=20250806_143614\n2025-08-06 14:36:14,835 - large_scale_feature_engineer - INFO - 保存预处理器到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143614.pkl\n2025-08-06 14:36:14,835 - large_scale_feature_engineer - INFO - 预处理器保存成功: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143614.pkl\n2025-08-06 14:36:14,835 - large_scale_model_trainer - INFO - 模型保存完成: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_143614.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143614.pkl\n", "returncode": 0}, "model_evaluation": {"success": true, "duration": 0.986882, "stdout": "加载模型和特征工程器...\n  传统模型加载成功: RandomForestRegressor\n  模型特征数: 19\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n  特征工程器加载成功\n开始大规模模型评估\n  测试文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  评估第 1 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=337.01, RMSE=833.60, R²=0.9234\n    累计样本: 1,000\n\n  评估第 2 批数据...\n    评估完成: 476 样本\n    批次指标: MAE=318.14, RMSE=856.79, R²=0.9242\n    累计样本: 1,476\n\n分批评估完成！\n  - 总批次: 2\n  - 总样本: 1,476\n  - 总耗时: 0.05秒\n  - 评估速度: 31565 样本/秒\n\n计算整体评估指标...\n使用传统模型评估...\n\n================================================================================\n大规模模型评估报告\n================================================================================\n\n整体性能指标:\n  - 样本数量: 1,476\n  - MAE (平均绝对误差): 330.92\n  - RMSE (均方根误差): 841.15\n  - R² (决定系数): 0.9238\n\n💼 业务准确率 (预测误差在阈值内的比例):\n  - ±10元内: 34.1%\n  - ±20元内: 38.3%\n  - ±50元内: 45.4%\n  - ±100元内: 51.1%\n\n预测值分布:\n  - 范围: 0.00 - 20140.89\n  - 均值: 1479.91\n  - 中位数: 479.98\n  - 标准差: 2935.89\n  - 零值比例: 29.5%\n\n真实值分布:\n  - 范围: 0.00 - 19900.00\n  - 均值: 1466.04\n  - 中位数: 500.00\n  - 标准差: 3046.45\n  - 零值比例: 45.9%\n\n批次性能稳定性:\n  - R²变化范围: 0.9234 - 0.9242\n  - R²标准差: 0.0004\n  - MAE变化范围: 318.14 - 337.01\n  - MAE标准差: 9.44\n\n🏆 模型质量评估:\n   模型质量: 优秀 (R²=0.9238)\n  业务适用性: 需要改进 (±50元准确率: 45.4%)\n\n评估报告已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/evaluation_report_20250806_143611.json\n\n大规模模型评估完成！\n", "stderr": "2025-08-06 14:36:15,784 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:36:15,785 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:36:15,793 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n2025-08-06 14:36:15,793 - large_scale_model_evaluator - INFO - 开始大规模模型评估: 测试文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv, 目标列=amount\n2025-08-06 14:36:15,798 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:36:15,824 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n", "returncode": 0}, "prediction": {"success": true, "duration": 1.075094, "stdout": "加载模型和特征工程器...\n  传统模型加载成功: RandomForestRegressor\n  模型特征数: 19\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n  特征工程器加载成功\n🔮 开始大规模数据预测\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n  输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143611.csv\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n保存最终预测结果...\n  最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143611.csv\n\n🎉 预测完成！\n  总预测数: 1,476 条\n  总批次数: 2 批次\n  批次大小: 1000 行/批次\n  总耗时: 0.06秒\n  预测速度: 24548 条/秒\n  数据成功率: 73.8%\n  状态恢复次数: 0 次\n  结果文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143611.csv\n\n预测统计报告:\n  - 输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n  - 输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143611.csv\n  - 预测总数: 1,476 条\n  - 处理时间: 0.06 秒\n  - 处理速度: 24548 条/秒\n  - 批次大小: 1,000 条\n\n预测结果分析:\n  - 最小值: 0.00\n  - 最大值: 20140.89\n  - 平均值: 1479.91\n  - 中位数: 479.98\n  - 标准差: 2936.89\n  - 零值数量: 436 (29.5%)\n\n大规模预测完成！\n", "stderr": "2025-08-06 14:36:16,845 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:36:16,845 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:36:16,846 - large_scale_prediction - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_143614.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n2025-08-06 14:36:16,852 - large_scale_prediction - INFO - 传统模型加载成功: RandomForestRegressor\n2025-08-06 14:36:16,852 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n2025-08-06 14:36:16,852 - large_scale_prediction - INFO - 开始大规模数据预测: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143611.csv\n\n🔮 批量预测: 0批次 [00:00, ?批次/s]2025-08-06 14:36:16,862 - large_scale_prediction - INFO - 开始处理第 1 批数据，样本数: 1000\n2025-08-06 14:36:16,867 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:36:16,868 - large_scale_prediction - INFO - 处理批次 1, 状态检查间隔: 5\n2025-08-06 14:36:16,884 - large_scale_prediction - INFO - 第 1 批预测完成: 1,000 条，累计: 1,000 条\n\n🔮 批量预测: 0批次 [00:00, ?批次/s, 批次=1, 当前=1,000, 累计=1,000, 预测范围=0.0-19408.4]2025-08-06 14:36:16,885 - large_scale_prediction - INFO - 开始处理第 2 批数据，样本数: 476\n2025-08-06 14:36:16,888 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:36:16,889 - large_scale_prediction - INFO - 处理批次 2, 状态检查间隔: 5\n2025-08-06 14:36:16,901 - large_scale_prediction - INFO - 第 2 批预测完成: 476 条，累计: 1,476 条\n\n🔮 批量预测: 1批次 [00:00, 24.75批次/s, 批次=2, 当前=476, 累计=1,476, 预测范围=0.0-20140.9]\n🔮 批量预测: 2批次 [00:00, 49.31批次/s, 批次=2, 当前=476, 累计=1,476, 预测范围=0.0-20140.9]\n2025-08-06 14:36:16,913 - large_scale_prediction - INFO - 最终结果已保存(包含特征): /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/predictions_20250806_143611.csv, 记录数: 1476\n2025-08-06 14:36:16,913 - large_scale_prediction - INFO - 列顺序: 训练特征(14) + 透传字段(11) + 目标字段(1) + 预测字段(1)\n2025-08-06 14:36:16,913 - large_scale_prediction - INFO - 预测完成: 总数=1,476, 耗时=0.06秒, 速度=24548条/秒\n2025-08-06 14:36:16,913 - large_scale_prediction - INFO - 短期改进统计: 批次数=2, 状态恢复=0次, 成功率=73.8%\n", "returncode": 0}, "billing_judgment": {"success": true, "duration": 1.045228, "stdout": "加载模型和特征工程器...\n  传统模型加载成功: RandomForestRegressor\n  模型特征数: 19\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n  特征工程器加载成功\n🏛️ 大规模收费合理性判定器初始化完成\n  - 批次大小: 1,000\n  - 绝对阈值: ±10.0元\n  - 相对阈值: ±10.0%\n⚖️  开始大规模收费合理性判定\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n  输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_143611.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  判定第 1 批数据...\n    判定完成: 1,000 条\n    累计判定: 1,000 条\n    批次统计: 合理53.2% | 不合理31.2% | 不确定15.6%\n\n  判定第 2 批数据...\n    判定完成: 476 条\n    累计判定: 1,476 条\n    批次统计: 合理52.5% | 不合理30.9% | 不确定16.6%\n\n保存最终判定结果...\n  最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_143611.csv\n\n⚖️  大规模收费合理性判定完成！\n  总判定数: 1,476 条\n  总耗时: 0.06秒\n  判定速度: 23978 条/秒\n  结果文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_143611.csv\n\n判定统计:\n  - 合理收费: 782 条 (53.0%)\n  - 不合理收费: 459 条 (31.1%)\n  - 不确定收费: 235 条 (15.9%)\n\n大规模收费合理性判定完成！\n", "stderr": "2025-08-06 14:36:17,889 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:36:17,889 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:36:17,890 - large_scale_billing_judge - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_model_20250806_143614.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 传统模型加载成功: RandomForestRegressor\n2025-08-06 14:36:17,897 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_143611.pkl\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 大规模收费合理性判定器初始化完成\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 批次大小: 1,000\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 绝对阈值: ±10.0元\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 相对阈值: ±10.0%\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 开始大规模收费合理性判定: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_143611.csv, 目标列=amount\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_143611/test_data_20250806_143613.csv, 批次大小: 1,000\n2025-08-06 14:36:17,897 - large_scale_billing_judge - INFO - 检测到分隔符: ','\n2025-08-06 14:36:17,899 - large_scale_billing_judge - INFO - 开始判定第 1 批数据，样本数: 1000\n2025-08-06 14:36:17,903 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:36:17,926 - large_scale_billing_judge - INFO - 第 1 批判定完成: 1,000 条，累计: 1,000 条\n2025-08-06 14:36:17,927 - large_scale_billing_judge - INFO - 开始判定第 2 批数据，样本数: 476\n2025-08-06 14:36:17,930 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:36:17,945 - large_scale_billing_judge - INFO - 第 2 批判定完成: 476 条，累计: 1,476 条\n2025-08-06 14:36:17,946 - large_scale_billing_judge - INFO - 开始保存最终判定结果到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_143611.csv\n2025-08-06 14:36:17,959 - large_scale_billing_judge - INFO - 最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_143611.csv, 总记录数: 1476\n2025-08-06 14:36:17,959 - large_scale_billing_judge - INFO - 大规模收费合理性判定完成: 总数=1,476, 耗时=0.06秒, 速度=23978条/秒\n2025-08-06 14:36:17,959 - large_scale_billing_judge - INFO - 判定统计: 合理=782(53.0%), 不合理=459(31.1%), 不确定=235(15.9%)\n", "returncode": 0}}, "summary": {"total_steps": 7, "successful_steps": 7, "failed_steps": 0, "success_rate": 100.0}}