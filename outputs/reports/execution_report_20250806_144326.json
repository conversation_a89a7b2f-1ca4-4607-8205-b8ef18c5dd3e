{"execution_info": {"timestamp": "20250806_144326", "start_time": "2025-08-06T14:43:32.535638", "total_duration": 5.940801, "project_root": "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI"}, "results": {"data_validation": {"success": true, "data_quality": {"total_samples": 7379, "total_features": 14, "missing_values": [], "zero_ratio": 0.43637349234313594, "has_missing_values": false}}, "feature_engineering": {"success": true, "duration": 0.514716, "stdout": "初始化大规模特征工程器\n  - 特征列数: 14\n  - 类别列数: 4\n  - 数值列数: 9\n开始拟合统计量...\n  - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv\n  - 批次大小: 1,000\n  统计量拟合完成:\n    - 总批次: 8\n    - 总行数: 7,379\n\n数值列统计摘要:\n  final_eff_year: 均值=2021.66, 标准差=2.88, 范围=[2003.00, 2025.00]\n  final_eff_mon: 均值=6.29, 标准差=3.39, 范围=[1.00, 12.00]\n  final_eff_day: 均值=8.65, 标准差=9.93, 范围=[1.00, 31.00]\n  final_exp_year: 均值=2039.83, 标准差=9.83, 范围=[2022.00, 2051.00]\n  final_exp_mon: 均值=7.46, 标准差=3.83, 范围=[1.00, 12.00]\n  final_exp_day: 均值=23.40, 标准差=11.00, 范围=[1.00, 31.00]\n  charge_day_count: 均值=23.46, 标准差=12.55, 范围=[0.00, 37.00]\n  month_day_count: 均值=30.00, 标准差=0.00, 范围=[30.00, 30.00]\n  should_fee: 均值=6361.62, 标准差=13478.20, 范围=[100.00, 346000.00]\n\n类别列统计摘要:\n  cal_type: 3 个唯一值\n  unit_type: 2 个唯一值\n  rate_unit: 3 个唯一值\n  busi_flag: 2 个唯一值\n预处理器已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n\n大规模特征工程器测试完成！\n", "stderr": "2025-08-06 14:43:26,997 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:43:26,997 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:43:26,997 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器\n2025-08-06 14:43:26,997 - large_scale_feature_engineer - INFO - 特征列数: 14\n2025-08-06 14:43:26,997 - large_scale_feature_engineer - INFO - 类别列数: 4\n2025-08-06 14:43:26,997 - large_scale_feature_engineer - INFO - 数值列数: 9\n2025-08-06 14:43:26,997 - large_scale_feature_engineer - INFO - 开始拟合统计量\n2025-08-06 14:43:26,997 - large_scale_feature_engineer - INFO - 数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/data/raw/ofrm_result.csv\n2025-08-06 14:43:26,997 - large_scale_feature_engineer - INFO - 批次大小: 1,000\n\n统计拟合: 0批次 [00:00, ?批次/s]\n统计拟合: 0批次 [00:00, ?批次/s, 批次=1, 当前行数=1,000, 累计行数=1,000, 数值列=9, 类别列=4]\n统计拟合: 1批次 [00:00, 72.92批次/s, 批次=2, 当前行数=1,000, 累计行数=2,000, 数值列=9, 类别列=4]\n统计拟合: 2批次 [00:00, 103.48批次/s, 批次=3, 当前行数=1,000, 累计行数=3,000, 数值列=9, 类别列=4]\n统计拟合: 3批次 [00:00, 118.36批次/s, 批次=4, 当前行数=1,000, 累计行数=4,000, 数值列=9, 类别列=4]\n统计拟合: 4批次 [00:00, 128.04批次/s, 批次=5, 当前行数=1,000, 累计行数=5,000, 数值列=9, 类别列=4]\n统计拟合: 5批次 [00:00, 134.77批次/s, 批次=6, 当前行数=1,000, 累计行数=6,000, 数值列=9, 类别列=4]\n统计拟合: 6批次 [00:00, 140.85批次/s, 批次=7, 当前行数=1,000, 累计行数=7,000, 数值列=9, 类别列=4]\n统计拟合: 7批次 [00:00, 153.83批次/s, 批次=8, 当前行数=379, 累计行数=7,379, 数值列=9, 类别列=4]  \n统计拟合: 8批次 [00:00, 175.37批次/s, 批次=8, 当前行数=379, 累计行数=7,379, 数值列=9, 类别列=4]\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=8, 总行数=7,379\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 统计摘要生成完成\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=2021.66, 标准差=2.88\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.29, 标准差=3.39\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=8.65, 标准差=9.93\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=2039.83, 标准差=9.83\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=7.46, 标准差=3.83\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=23.40, 标准差=11.00\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=23.46, 标准差=12.55\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=30.00, 标准差=0.00\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=6361.62, 标准差=13478.20\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 3 个唯一值\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值\n2025-08-06 14:43:27,056 - large_scale_feature_engineer - INFO - 保存预处理器到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n2025-08-06 14:43:27,057 - large_scale_feature_engineer - INFO - 预处理器保存成功: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n", "returncode": 0}, "data_splitting": {"success": true, "duration": 1.068745, "stdout": "🔀 开始数据拆分\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/processed_data_20250806_144326.csv\n  输出目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326\n  测试集比例: 0.2\n  目标列: amount\n\n读取数据...\n  数据形状: (7379, 26)\n  特征数: 25\n  样本数: 7379\n\n拆分数据...\n  训练集: 5,903 样本\n  测试集: 1,476 样本\n\n保存拆分后的数据...\n  训练集文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/train_data_20250806_144328.csv\n  测试集文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n\n验证保存的文件:\n  训练集: (5903, 26)\n  测试集: (1476, 26)\n  总样本数: 7379 (原始: 7379)\n\n数据拆分统计:\n  训练集目标值范围: 0.00 - 59900.00\n  测试集目标值范围: 0.00 - 19900.00\n  训练集目标值均值: 1520.37\n  测试集目标值均值: 1466.04\n\n✅ 数据拆分完成\n\n🎯 数据拆分成功完成\n训练集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/train_data_20250806_144328.csv\n测试集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n", "stderr": "2025-08-06 14:43:28,097 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:43:28,097 - src.config.production_config_manager - INFO - 配置加载成功\n", "returncode": 0}, "model_training": {"success": true, "model_type": "hierarchical", "algorithm": "LightGBM", "samples": 5903, "features": 19, "r2": 0.934648216832924, "mae": 195.7227179340029, "zero_accuracy": 0.8971709300355751, "training_time": 0.31958913803100586}, "model_evaluation": {"success": true, "duration": 1.079059, "stdout": "加载模型和特征工程器...\n  分层模型加载成功: HierarchicalBillingModel\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n  特征工程器加载成功\n开始大规模模型评估\n  测试文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  评估第 1 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=227.66, RMSE=791.69, R²=0.9309\n    累计样本: 1,000\n\n  评估第 2 批数据...\n    评估完成: 476 样本\n    批次指标: MAE=243.46, RMSE=981.31, R²=0.9006\n    累计样本: 1,476\n\n分批评估完成！\n  - 总批次: 2\n  - 总样本: 1,476\n  - 总耗时: 0.07秒\n  - 评估速度: 20485 样本/秒\n\n计算整体评估指标...\n使用分层模型评估器...\n\n================================================================================\n分层模型评估报告\n================================================================================\n\n🎯 分层模型综合评估:\n  - 样本数量: 1,476\n  - 整体 R²: 0.9208\n  - 整体 MAE: 232.75元\n  - 整体 RMSE: 857.43元\n  - 性能等级: A\n\n🔍 零值分类性能:\n  - 分类准确率: 0.8814\n  - 分类 F1 分数: 0.8540\n\n📈 非零值回归性能:\n  - 回归 R²: 0.9876\n  - 回归 MAE: 88.15元\n\n💼 业务指标:\n  - ±50元准确率: 67.1%\n\n📊 详细业务准确率:\n  - ±1元内: 63.9%\n  - ±5元内: 65.0%\n  - ±10元内: 66.3%\n  - ±20元内: 66.4%\n  - ±50元内: 67.1%\n  - ±100元内: 76.8%\n\n评估报告已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/reports/hierarchical_evaluation_report_20250806_144326.json\n\n大规模模型评估完成！\n", "stderr": "2025-08-06 14:43:30,212 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:43:30,213 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:43:30,226 - hierarchical_billing_model - INFO - 分层模型初始化完成，使用算法: RandomForest\n2025-08-06 14:43:30,226 - hierarchical_billing_model - INFO - 分层模型已从 /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_144329.pkl 加载\n2025-08-06 14:43:30,227 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n2025-08-06 14:43:30,227 - large_scale_model_evaluator - INFO - 开始大规模模型评估: 测试文件=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv, 目标列=amount\n2025-08-06 14:43:30,232 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:43:30,271 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:43:30,300 - hierarchical_model_evaluator - INFO - 分层模型评估器初始化完成\n2025-08-06 14:43:30,300 - hierarchical_model_evaluator - INFO - 开始综合评估分层模型性能...\n2025-08-06 14:43:30,305 - hierarchical_model_evaluator - INFO - 零值分类评估: 准确率=0.8814, F1=0.8540\n2025-08-06 14:43:30,306 - hierarchical_model_evaluator - INFO - 非零值回归评估: R²=0.9876, MAE=88.15, MAPE=7.9%\n2025-08-06 14:43:30,306 - hierarchical_model_evaluator - INFO - 整体性能评估: R²=0.9208, MAE=232.75\n2025-08-06 14:43:30,306 - hierarchical_model_evaluator - INFO - 业务指标评估: ±50元准确率=67.1%\n2025-08-06 14:43:30,307 - hierarchical_model_evaluator - INFO - 综合评估完成\n", "returncode": 0}, "prediction": {"success": true, "duration": 1.060137, "stdout": "加载模型和特征工程器...\n  分层模型加载成功: HierarchicalBillingModel\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n  特征工程器加载成功\n🔮 开始大规模数据预测\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n  输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_144326.csv\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n保存最终预测结果...\n  最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_144326.csv\n\n🎉 预测完成！\n  总预测数: 1,476 条\n  总批次数: 2 批次\n  批次大小: 1000 行/批次\n  总耗时: 0.09秒\n  预测速度: 16898 条/秒\n  数据成功率: 73.8%\n  状态恢复次数: 0 次\n  结果文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_144326.csv\n\n🎉 分层模型预测完成！\n总样本数: 1,476\n零值预测: 522 (35.4%)\n非零值预测: 954\n非零值统计: 均值=2488.16元, 中位数=898.50元\n处理时间: 0.10秒\n处理速度: 14,120条/秒\n\n大规模预测完成！\n", "stderr": "2025-08-06 14:43:31,261 - hierarchical_prediction - INFO - ============================================================\n2025-08-06 14:43:31,261 - hierarchical_prediction - INFO - 开始分层模型预测\n2025-08-06 14:43:31,261 - hierarchical_prediction - INFO - ============================================================\n2025-08-06 14:43:31,261 - hierarchical_prediction - INFO - 创建预测器，批次大小参数: 1000\n2025-08-06 14:43:31,261 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:43:31,261 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:43:31,261 - large_scale_prediction - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_144329.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n2025-08-06 14:43:31,275 - hierarchical_billing_model - INFO - 分层模型初始化完成，使用算法: RandomForest\n2025-08-06 14:43:31,275 - hierarchical_billing_model - INFO - 分层模型已从 /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_144329.pkl 加载\n2025-08-06 14:43:31,275 - large_scale_prediction - INFO - 分层模型加载成功: HierarchicalBillingModel\n2025-08-06 14:43:31,275 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n2025-08-06 14:43:31,275 - large_scale_prediction - INFO - 回归器状态备份完成: {'n_features_in_': 19, '_n_classes': 1}\n2025-08-06 14:43:31,275 - hierarchical_prediction - INFO - 预测器实际批次大小: 1000\n2025-08-06 14:43:31,275 - hierarchical_prediction - INFO - 输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n2025-08-06 14:43:31,275 - hierarchical_prediction - INFO - 分层模型: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_144329.pkl\n2025-08-06 14:43:31,275 - hierarchical_prediction - INFO - 输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_144326.csv\n2025-08-06 14:43:31,275 - large_scale_prediction - INFO - 开始大规模数据预测: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_144326.csv\n\n🔮 批量预测: 0批次 [00:00, ?批次/s]2025-08-06 14:43:31,286 - large_scale_prediction - INFO - 开始处理第 1 批数据，样本数: 1000\n2025-08-06 14:43:31,292 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:43:31,292 - large_scale_prediction - INFO - 处理批次 1, 状态检查间隔: 5\n2025-08-06 14:43:31,320 - large_scale_prediction - INFO - 第 1 批预测完成: 1,000 条，累计: 1,000 条\n\n🔮 批量预测: 0批次 [00:00, ?批次/s, 批次=1, 当前=1,000, 累计=1,000, 预测范围=0.0-19966.0]2025-08-06 14:43:31,321 - large_scale_prediction - INFO - 开始处理第 2 批数据，样本数: 476\n2025-08-06 14:43:31,324 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:43:31,325 - large_scale_prediction - INFO - 处理批次 2, 状态检查间隔: 5\n2025-08-06 14:43:31,352 - large_scale_prediction - INFO - 第 2 批预测完成: 476 条，累计: 1,476 条\n\n🔮 批量预测: 1批次 [00:00, 14.75批次/s, 批次=2, 当前=476, 累计=1,476, 预测范围=0.0-19966.0]\n🔮 批量预测: 2批次 [00:00, 29.44批次/s, 批次=2, 当前=476, 累计=1,476, 预测范围=0.0-19966.0]\n2025-08-06 14:43:31,363 - large_scale_prediction - INFO - 最终结果已保存(包含特征): /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/hierarchical_predictions_20250806_144326.csv, 记录数: 1476\n2025-08-06 14:43:31,363 - large_scale_prediction - INFO - 列顺序: 训练特征(14) + 透传字段(11) + 目标字段(1) + 预测字段(1)\n2025-08-06 14:43:31,363 - large_scale_prediction - INFO - 预测完成: 总数=1,476, 耗时=0.09秒, 速度=16898条/秒\n2025-08-06 14:43:31,363 - large_scale_prediction - INFO - 短期改进统计: 批次数=2, 状态恢复=0次, 成功率=73.8%\n2025-08-06 14:43:31,366 - hierarchical_prediction - INFO - 分层预测完成:\n2025-08-06 14:43:31,366 - hierarchical_prediction - INFO -   总样本数: 1,476\n2025-08-06 14:43:31,366 - hierarchical_prediction - INFO -   零值预测: 522 (35.4%)\n2025-08-06 14:43:31,366 - hierarchical_prediction - INFO -   非零值预测: 954\n2025-08-06 14:43:31,366 - hierarchical_prediction - INFO -   非零值均值: 2488.16元\n2025-08-06 14:43:31,366 - hierarchical_prediction - INFO -   处理时间: 0.10秒\n2025-08-06 14:43:31,366 - hierarchical_prediction - INFO -   处理速度: 14,120条/秒\n", "returncode": 0}, "billing_judgment": {"success": true, "duration": 1.087342, "stdout": "加载模型和特征工程器...\n  分层模型加载成功: HierarchicalBillingModel\n预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n  特征工程器加载成功\n🏛️ 大规模收费合理性判定器初始化完成\n  - 批次大小: 1,000\n  - 绝对阈值: ±10.0元\n  - 相对阈值: ±10.0%\n⚖️  开始大规模收费合理性判定\n  输入文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n  输出文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144326.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  判定第 1 批数据...\n    判定完成: 1,000 条\n    累计判定: 1,000 条\n    批次统计: 合理72.1% | 不合理13.6% | 不确定14.3%\n\n  判定第 2 批数据...\n    判定完成: 476 条\n    累计判定: 1,476 条\n    批次统计: 合理71.6% | 不合理11.1% | 不确定17.2%\n\n保存最终判定结果...\n  最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144326.csv\n\n⚖️  大规模收费合理性判定完成！\n  总判定数: 1,476 条\n  总耗时: 0.09秒\n  判定速度: 16540 条/秒\n  结果文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144326.csv\n\n判定统计:\n  - 合理收费: 1,062 条 (72.0%)\n  - 不合理收费: 189 条 (12.8%)\n  - 不确定收费: 225 条 (15.2%)\n\n大规模收费合理性判定完成！\n", "stderr": "2025-08-06 14:43:32,353 - src.config.production_config_manager - INFO - 加载配置文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/config/production_config.json\n2025-08-06 14:43:32,353 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-06 14:43:32,353 - large_scale_billing_judge - INFO - 开始加载模型和特征工程器: 模型=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_144329.pkl, 特征工程器=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n2025-08-06 14:43:32,367 - hierarchical_billing_model - INFO - 分层模型初始化完成，使用算法: RandomForest\n2025-08-06 14:43:32,367 - hierarchical_billing_model - INFO - 分层模型已从 /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_144329.pkl 加载\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 分层模型加载成功: HierarchicalBillingModel\n2025-08-06 14:43:32,367 - large_scale_feature_engineer - INFO - 预处理器已加载: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_144326.pkl\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 大规模收费合理性判定器初始化完成\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 批次大小: 1,000\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 绝对阈值: ±10.0元\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 相对阈值: ±10.0%\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 开始大规模收费合理性判定: 输入=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv, 输出=/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144326.csv, 目标列=amount\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 开始分批读取数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_144326/test_data_20250806_144328.csv, 批次大小: 1,000\n2025-08-06 14:43:32,367 - large_scale_billing_judge - INFO - 检测到分隔符: ','\n2025-08-06 14:43:32,369 - large_scale_billing_judge - INFO - 开始判定第 1 批数据，样本数: 1000\n2025-08-06 14:43:32,373 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:43:32,411 - large_scale_billing_judge - INFO - 第 1 批判定完成: 1,000 条，累计: 1,000 条\n2025-08-06 14:43:32,413 - large_scale_billing_judge - INFO - 开始判定第 2 批数据，样本数: 476\n2025-08-06 14:43:32,416 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-06 14:43:32,444 - large_scale_billing_judge - INFO - 第 2 批判定完成: 476 条，累计: 1,476 条\n2025-08-06 14:43:32,444 - large_scale_billing_judge - INFO - 开始保存最终判定结果到: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144326.csv\n2025-08-06 14:43:32,457 - large_scale_billing_judge - INFO - 最终结果已保存: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/data/billing_judgments_20250806_144326.csv, 总记录数: 1476\n2025-08-06 14:43:32,457 - large_scale_billing_judge - INFO - 大规模收费合理性判定完成: 总数=1,476, 耗时=0.09秒, 速度=16540条/秒\n2025-08-06 14:43:32,457 - large_scale_billing_judge - INFO - 判定统计: 合理=1,062(72.0%), 不合理=189(12.8%), 不确定=225(15.2%)\n", "returncode": 0}}, "summary": {"total_steps": 7, "successful_steps": 7, "failed_steps": 0, "success_rate": 100.0}}