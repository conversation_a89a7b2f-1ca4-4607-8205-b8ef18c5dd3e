{"timestamp": "2025-08-06T11:15:34.644138", "test_file": "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/temp/run_20250806_111530/test_data_20250806_111532.csv", "model_file": "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/hierarchical_model_20250806_105254.pkl", "feature_engineer_file": "/Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/outputs/models/large_scale_feature_engineer_20250806_111530.pkl", "batch_size": 1000, "overall_metrics": {"model_type": "hierarchical", "total_samples": 1476, "overall_mae": 6211667481709.539, "overall_rmse": 7796541705251.122, "overall_r2": -1.7379568041000888, "zero_classification_accuracy": 0.0, "zero_classification_f1": 0.0, "nonzero_regression_r2": -1.7379568041000888, "nonzero_regression_mae": 6211667481709.539, "business_accuracy_50yuan": 0.0, "hierarchical_results": {"classification_metrics": {"overall_accuracy": 0.0, "overall_precision": 0.0, "overall_recall": 0.0, "overall_f1": 0.0, "zero_class": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0}, "nonzero_class": {"precision": 0.0, "recall": 0.0, "f1_score": 0.0}, "confusion_matrix": [[0, 1476], [0, 0]], "classification_report": {"非零值": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1476.0}, "零值": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 0.0}, "accuracy": 0.0, "macro avg": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1476.0}, "weighted avg": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 1476.0}}}, "regression_metrics": {"sample_count": 1476, "mae": 6211667481709.539, "mse": 6.078606256172007e+25, "rmse": 7796541705251.122, "r2": -1.7379568041000888, "mape": 100.0, "error_percentiles": {"p25": 100.0, "p50": 100.0, "p75": 100.0, "p90": 100.0, "p95": 100.0}, "prediction_stats": {"mean": 0.0, "median": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "true_value_stats": {"mean": 6211667481709.539, "median": 10000050486235.0, "std": 4711820206501.129, "min": ***********.0, "max": 10000192582142.0}}, "overall_metrics": {"total_samples": 1476, "overall_mae": 6211667481709.539, "overall_mse": 6.078606256172007e+25, "overall_rmse": 7796541705251.122, "overall_r2": -1.7379568041000888, "true_zero_count": 0, "pred_zero_count": 1476, "true_zero_ratio": 0.0, "pred_zero_ratio": 100.0, "prediction_bias": -6211667481709.539, "absolute_bias": 6211667481709.539}, "business_metrics": {"business_accuracy": {"within_1_yuan": 0.0, "within_5_yuan": 0.0, "within_10_yuan": 0.0, "within_20_yuan": 0.0, "within_50_yuan": 0.0, "within_100_yuan": 0.0}, "total_true_amount": 9168421203003280.0, "total_pred_amount": 0.0, "amount_bias": -9168421203003280.0, "amount_bias_ratio": -100.0, "false_zero_loss": 9168421203003280.0, "false_nonzero_cost": 0.0, "net_business_impact": 9168421203003280.0}, "distribution_analysis": {"true_value_distribution": {"zero_count": 0, "nonzero_count": 1476, "nonzero_mean": 6211667481709.539, "nonzero_std": 4711820206501.129, "nonzero_percentiles": {"p25": 414015657779.75, "p50": 10000050486235.0, "p75": 10000134792842.0, "p90": 10000173423831.0}}, "predicted_value_distribution": {"zero_count": 1476, "nonzero_count": 0, "nonzero_mean": 0, "nonzero_std": 0, "nonzero_percentiles": {"p25": 0, "p50": 0, "p75": 0, "p90": 0}}}, "summary": {"model_type": "HierarchicalBillingModel", "evaluation_timestamp": "2025-08-06T11:15:34.643758", "key_metrics": {"zero_classification_f1": 0.0, "nonzero_regression_r2": -1.7379568041000888, "overall_r2": -1.7379568041000888, "overall_mae": 6211667481709.539, "business_accuracy_50yuan": 0.0}, "performance_grade": "D"}}, "performance_grade": "D"}, "batch_count": 2}