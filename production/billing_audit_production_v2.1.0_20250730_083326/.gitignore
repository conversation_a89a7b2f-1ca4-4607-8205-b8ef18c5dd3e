# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Jupyter Notebook
.ipynb_checkpoints

# 重要文件不忽略：配置文件、日志、预测结果、模型文件
# 这些文件对大模型理解项目状态很重要

# 忽略大部分数据文件，但保留重要的配置和结果文件
*.csv
!config/*.csv
!outputs/data/*.csv
!outputs/reports/*.csv
!data/input/*.csv
!data/output/*.csv

*.xlsx
!数据/*.xlsx
!config/*.xlsx

*.xls
!config/*.xls

# 忽略大部分json文件，但保留配置和报告文件
*.json
!config/*.json
!deployment/config/*.json
!outputs/reports/*.json
!outputs/reports/markdown/*.json

# 忽略大部分pickle文件，但保留模型文件
*.pickle
*.pkl
!models/**/*.pkl
!outputs/models/*.pkl

# 保留legacy模型目录中的模型文件
!models/billing_audit/**/*.joblib
!models/billing_audit/**/*.pkl

# 允许日志文件被追踪（重要的调试和运行信息）
# logs/*.log  # 注释掉原来的忽略规则

# OS文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp

# 忽略大型数据文件
*.h5
# 但保留模型文件
!models/**/*.h5
!outputs/models/*.h5

# 忽略Python缓存但保留重要配置
*.pyc
