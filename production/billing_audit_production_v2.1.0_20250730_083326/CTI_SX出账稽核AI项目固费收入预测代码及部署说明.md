# CTI_SX出账稽核AI项目固费收入预测代码及部署运行说明

## 部署信息

- **版本**: v2.1.0 (完整生产版)
- **部署包名**: `billing_audit_production_v2.1.0_20250803_final.tar.gz`
- **压缩大小**: 274MB (包含Docker镜像)
- **镜像文件**: billing-audit-ai-v2.1.0-slim-fixed-v2.tar.gz (268MB)
- **包含内容**: 核心源代码 + Docker镜像 + 文档
- **主机IP：**************-ARM，账号：bas
- **体验路径：**/bas/work/lvpd/bill_audit/billing_audit_production_v2.1.0_20250730_083326

## 功能验证

### **生产脚本** 
- **deploy.sh**: 一键部署脚本，自动加载镜像、检测数据
- **run_with_mount.sh**: 一键运行脚本，支持全流程和单流程运行

### **功能验证** 
- **特征工程验证**: 14→19特征转换，6个衍生特征正确生成
- **全流程测试**: 7/7步骤100%成功，总耗时19.26秒
- **真实数据验证**: 60,354行生产数据完整处理
- **单环节测试**: 特征工程、训练、预测、评估、判定全部通过
- **预测格式**: 27列完整输出验证
- **分层建模**: 96.75%零值识别准确率
- **处理性能**: 154,286条/秒预测速度

## 快速部署

### 1. **解压部署包**
```bash
tar -xzf billing_audit_production_v2.1.0_20250803_final.tar.gz
cd billing_audit_production_v2.1.0_20250730_083326
```

### 2. **放置数据文件**
```bash
# 将您的数据文件放到 data/input 目录
cp /path/to/your/data.txt data/input/

# 支持任意文件名，如：
# cp ofrm_result.txt data/input/
# cp billing_data.csv data/input/
# cp test_data.txt data/input/
```

### 3. **一键部署** 
```bash
# 使用一键部署脚本（全自动化部署）
chmod +x deploy.sh
bash deploy.sh
```

### 4. **一键运行**
```bash
# 1. 使用智能挂载脚本
chmod +x run_with_mount.sh
```

#### **全流程运行** 
```bash
# 基本语法
./run_with_mount.sh full <数据文件名> [算法类型] [批处理大小]

# 示例: 完整参数
./run_with_mount.sh full ofrm_result.txt hierarchical 1000
```

#### **单流程运行** 
```bash
# 基本语法
./run_with_mount.sh <流程名> <数据文件名> [算法类型] [批处理大小]

# 1. 特征工程
./run_with_mount.sh feature-engineering ofrm_result.txt

# 2. 模型训练
./run_with_mount.sh training ofrm_result.txt hierarchical

# 3. 模型预测
./run_with_mount.sh prediction ofrm_result.txt hierarchical 1000

# 4. 模型评估
./run_with_mount.sh evaluation ofrm_result.txt hierarchical

# 5. 收费判定
./run_with_mount.sh judgment ofrm_result.txt hierarchical
```

### **参数说明**

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| **流程类型** | `full` 或单流程名称 | - | `full`, `training`, `prediction` |
| **数据文件名** | data/input目录下的文件名 | - | `ofrm_result.txt`, `billing_data.csv` |
| **算法类型** | 机器学习算法 | `hierarchical` | `hierarchical`, `lightgbm`, `randomforest` |
| **批处理大小** | 数据处理批次大小 | `1000` | `500`, `1000`, `2000` |

### **算法类型说明**

| 算法 | 说明 | 适用场景 | 性能特点 |
|------|------|----------|----------|
| **hierarchical** | 分层建模 (零值分类+非零值回归) | 高零值比例数据 | 96.75%零值识别准确率 |
| **lightgbm** | LightGBM梯度提升 | 大规模数据 | 训练速度快，内存占用低 |
| **randomforest** | 随机森林 | 通用场景 | 稳定性好，可解释性强 |
| **xgboost** | XGBoost梯度提升 | 高精度要求 | 预测精度高，特征重要性 |

### **执行流程说明**

#### **全流程执行顺序**
```
1. 特征工程     → 14→19特征转换，6个衍生特征
2. 模型训练     → 分层建模训练，零值分类+非零值回归
3. 模型预测     → 154,286条/秒预测速度
4. 模型评估     → 详细性能报告，JSON格式输出
5. 收费判定     → 合理性判定，96%合理率
```

#### **单流程独立执行**
- **feature-engineering**: 只执行特征工程，生成优化特征
- **training**: 只执行模型训练，生成模型文件
- **prediction**: 只执行预测，需要已有模型文件
- **evaluation**: 只执行评估，需要已有模型和预测结果
- **judgment**: 只执行判定，需要已有预测结果

### **输出文件说明**

#### **全流程输出**
```
outputs/
├── models/                       # 模型文件
│   ├── hierarchical_model_*.pkl  # 分层模型文件
│   └── feature_engineer_*.pkl    # 特征工程器
├── data/                         # 数据文件
│   ├── hierarchical_predictions_*.csv  # 预测结果 (27列)
│   └── billing_judgments_*.csv   # 判定结果
├── reports/                      # 评估报告
│   └── evaluation_report_*.json  # 详细评估报告
└── temp/                         # 临时文件

logs/
├── billing_audit_*.log           # 主程序日志
├── feature_engineering_*.log     # 特征工程日志
├── training_*.log                # 训练日志
├── prediction_*.log              # 预测日志
├── evaluation_*.log              # 评估日志
└── judgment_*.log                # 判定日志
```

## 技术特性

### **智能特征工程**
- **14→19特征转换**: 精确的特征工程流程
- **6个衍生特征**: 日均应收费用、计费效率、时间特征、订阅时长、交互特征、业务复杂度
- **内存优化**: 批处理架构，支持千万级数据

### **分层建模技术**
- **零值分类器**: 96.75%准确率识别零值数据
- **非零值回归器**: 针对非零值数据的精确预测
- **算法支持**: hierarchical、LightGBM、RandomForest、XGBoost多算法支持
- **自动选择**: 智能检测数据特征，自动选择最优算法
- **高零值适配**: 专门解决高零值比例问题
