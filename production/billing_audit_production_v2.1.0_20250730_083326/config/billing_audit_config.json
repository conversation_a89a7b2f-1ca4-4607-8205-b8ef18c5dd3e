{"project": {"name": "山西电信出账稽核AI系统", "version": "1.0.0", "description": "基于AI的计费稽核和用户行为分析系统"}, "data_sources": {"fixed_fee_sample": "数据/固费预测模拟数据_20250725_153411.xlsx", "discount_sample": "数据/优惠预测样例@20250707.xlsx", "call_detail_sample": "数据/话单增量样例.xlsx"}, "model_paths": {"fixed_fee_model": "models/billing_audit/fixed_fee_enhanced_model_20250725_155326.pkl", "discount_model": "models/billing_audit/discount_model_latest"}, "billing_audit": {"fixed_fee": {"feature_columns": ["cal_type", "unit_type", "rate_unit", "final_eff_year", "final_eff_mon", "final_eff_day", "final_exp_year", "final_exp_mon", "final_exp_day", "cur_year_month", "charge_day_count", "month_day_count", "run_code", "run_time", "should_fee", "busi_flag"], "target_column": "amount", "passthrough_columns": ["offer_inst_id", "prod_inst_id", "prod_id", "offer_id", "sub_prod_id", "event_pricing_strategy_id", "event_type_id", "calc_priority", "pricing_section_id", "calc_method_id", "role_id"], "categorical_columns": ["cal_type", "unit_type", "rate_unit", "run_code", "busi_flag"], "numerical_columns": ["final_eff_year", "final_eff_mon", "final_eff_day", "final_exp_year", "final_exp_mon", "final_exp_day", "charge_day_count", "month_day_count", "should_fee"], "date_columns": ["cur_year_month", "run_time"]}, "discount": {"feature_columns": ["fav_type", "final_eff_date", "final_exp_date", "cur_year_month", "run_code", "run_time", "fav_cal_fee", "fav_value", "busi_flag"], "target_column": "amount", "passthrough_columns": ["offer_inst_id", "prod_inst_id", "user_id", "phone_no", "prod_id", "offer_id", "sub_prod_id", "strategy_id", "life_cycle_id", "region_str"], "categorical_columns": ["fav_type", "run_code", "busi_flag"], "numerical_columns": ["fav_cal_fee", "fav_value"], "date_columns": ["final_eff_date", "final_exp_date", "cur_year_month", "run_time"]}, "model_params": {"xgboost": {"n_estimators": 100, "max_depth": 6, "learning_rate": 0.1, "subsample": 0.8, "colsample_bytree": 0.8, "random_state": 42}, "train_test_split": {"test_size": 0.2, "random_state": 42, "stratify": false}, "cross_validation": {"cv_folds": 5, "scoring": "neg_mean_absolute_error"}}, "judgment_thresholds": {"absolute_threshold": 0.01, "relative_threshold": 0.01, "use_mixed_threshold": true}}, "behavior_analysis": {"call_detail": {"feature_columns": ["system_type", "call_type", "cdr_type", "dial_type", "fee_type", "ism_type", "record_type", "roam_type", "sm_type", "struct_type", "call_duration", "down_flow", "up_flow", "item1", "item2", "item3", "fee1", "fee2", "fee3"], "passthrough_columns": ["msisdn", "start_time"], "categorical_columns": ["system_type", "call_type", "cdr_type", "dial_type", "fee_type", "ism_type", "record_type", "roam_type", "sm_type", "struct_type"], "numerical_columns": ["call_duration", "down_flow", "up_flow", "item1", "item2", "item3", "fee1", "fee2", "fee3"], "time_columns": ["start_time"]}, "clustering_params": {"kmeans": {"n_clusters": "auto", "max_clusters": 20, "random_state": 42, "n_init": 10}, "elbow_method": {"k_range": [2, 20], "metric": "inertia"}}, "change_detection": {"threshold_method": {"absolute_change": 50, "relative_change": 0.3}, "vector_distance_method": {"distance_metric": "cosine", "threshold": 0.3}, "default_method": "threshold_method"}}, "preprocessing": {"missing_value_strategy": {"numerical": "median", "categorical": "mode", "drop_threshold": 0.5}, "outlier_detection": {"method": "iqr", "iqr_multiplier": 1.5}, "feature_scaling": {"method": "standard", "with_mean": true, "with_std": true}, "encoding": {"categorical_method": "onehot", "handle_unknown": "ignore"}}, "model_management": {"model_save_path": "models/", "model_format": "joblib", "model_versioning": true, "backup_models": 3}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/billing_audit.log", "max_file_size": "10MB", "backup_count": 5}, "performance": {"batch_size": 1000, "n_jobs": -1, "memory_limit": "4GB"}}