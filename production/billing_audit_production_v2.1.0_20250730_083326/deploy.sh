#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - 一键部署运行脚本
# 支持任意目录部署，自动处理权限问题，完全自包含

set -e

# ============================================================================
# 配置和常量定义
# ============================================================================
VERSION="v2.1.0"
SYSTEM_NAME="山西电信出账稽核AI系统"
IMAGE_NAME="billing-audit-ai"
IMAGE_TAG="v2.1.0-slim-fixed-v2"
CONTAINER_NAME="billing-audit-ai-$(date +%s)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# ============================================================================
# 工具函数
# ============================================================================
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${PURPLE}[SUCCESS]${NC} $1"; }

# 获取脚本绝对路径（支持任意目录部署）
get_script_dir() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    echo "$script_dir"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "============================================================================"
    echo "                    $SYSTEM_NAME $VERSION"
    echo "                        一键部署运行脚本"
    echo "============================================================================"
    echo -e "${NC}"
    echo "📍 部署目录: $(get_script_dir)"
    echo "🕐 部署时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "👤 执行用户: $(whoami)"
    echo ""
}

# ============================================================================
# 环境检测
# ============================================================================
check_environment() {
    log_step "检测运行环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        echo "请安装Docker后重试"
        exit 1
    fi
    
    # 检查Docker服务
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        echo "请启动Docker服务: sudo systemctl start docker"
        exit 1
    fi
    
    log_info "✅ Docker环境正常"
    
    # 检查部署包完整性
    local script_dir=$(get_script_dir)
    local missing_files=()
    
    [ ! -f "$script_dir/production_config.json" ] && missing_files+=("production_config.json")
    [ ! -d "$script_dir/images" ] && missing_files+=("images/")
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "部署包不完整，缺少文件："
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    log_info "✅ 部署包完整性验证通过"
}

# ============================================================================
# 镜像管理
# ============================================================================
load_docker_images() {
    log_step "加载Docker镜像..."
    
    local script_dir=$(get_script_dir)
    local images_dir="$script_dir/images"
    
    # 检查镜像是否已存在
    if docker images | grep -q "$IMAGE_NAME.*$IMAGE_TAG"; then
        log_info "镜像已存在，跳过加载"
        return 0
    fi
    
    # 加载主应用镜像（优先使用最新修复版）
    local slim_fixed_v2_image="$images_dir/billing-audit-ai-$IMAGE_TAG.tar.gz"

    if [ -f "$slim_fixed_v2_image" ]; then
        log_info "加载最新修复版精简镜像 (推荐) - 包含中国时区、常用工具、后台常驻..."
        docker load < "$slim_fixed_v2_image"
    else
        log_error "主应用镜像文件不存在: $main_image"
        exit 1
    fi
    
    # 注意: 精简版部署包不包含基础镜像，主应用镜像已包含所有依赖
    log_info "精简版部署包，跳过基础镜像加载"
    
    log_success "镜像加载完成"
}

# ============================================================================
# 目录和权限管理
# ============================================================================
setup_directories() {
    log_step "设置目录结构和权限..."
    
    local script_dir=$(get_script_dir)
    
    # 创建必要目录
    local directories=(
        "$script_dir/data/input"
        "$script_dir/data/output"
        "$script_dir/data/output/models"
        "$script_dir/data/output/data"
        "$script_dir/data/output/reports"
        "$script_dir/data/output/reports/markdown"
        "$script_dir/data/output/temp"
        "$script_dir/logs"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
    done
    
    # 智能权限处理
    handle_permissions "$script_dir"
    
    log_success "目录结构设置完成"
}

handle_permissions() {
    local script_dir="$1"

    log_info "智能权限处理..."

    # 获取当前用户信息
    local current_user=$(whoami)
    local current_uid=$(id -u)
    local current_gid=$(id -g)

    log_info "当前用户: $current_user ($current_uid:$current_gid)"

    # 设置权限 - 使用755权限，避免过度开放
    if chmod -R 755 "$script_dir/data" "$script_dir/logs" 2>/dev/null; then
        log_info "✅ 权限设置成功 (755)"
    else
        log_warn "⚠️  权限设置失败，但继续执行"
    fi

    # 设置所有者 (仅在当前用户拥有文件时有效)
    if chown -R $(whoami):$(whoami) "$script_dir/data" "$script_dir/logs" 2>/dev/null; then
        log_info "✅ 所有者设置成功"
    else
        log_warn "⚠️  所有者设置失败，但继续执行"
    fi

    # 使用root用户运行容器，在容器内修复权限
    export USE_ROOT_USER=true
    log_success "智能权限处理完成"
}

# ============================================================================
# 数据文件管理
# ============================================================================
check_input_data() {
    log_step "检查输入数据..."
    
    local script_dir=$(get_script_dir)
    # 检查输入数据文件（支持任意文件名）
    local input_files=(data/input/*.txt data/input/*.csv)
    local input_file=""
    
    # 查找第一个有效的数据文件
    for file in "${input_files[@]}"; do
        if [ -f "$file" ]; then
            input_file="$file"
            break
        fi
    done
    
    if [ ! -f "$input_file" ]; then
        log_warn "输入数据文件不存在: $input_file"
        echo ""
        echo "📋 请按以下步骤准备数据："
        echo "  1. 将您的数据文件复制到: $script_dir/data/input/"
        echo "  2. 重命名为: ofrm_result.txt"
        echo "  3. 重新运行此脚本"
        echo ""
        echo "💡 示例命令:"
        echo "  cp /path/to/your/data.txt $script_dir/data/input/ofrm_result.txt"
        echo "  bash $script_dir/deploy.sh"
        echo ""
        read -p "是否现在准备数据文件？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "请在另一个终端准备数据文件，然后按回车继续..."
            read -p "数据文件已准备好？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "部署暂停，请准备数据文件后重新运行"
                exit 0
            fi
        else
            log_info "部署暂停，请准备数据文件后重新运行"
            exit 0
        fi
    fi
    
    # 再次检查文件
    if [ ! -f "$input_file" ]; then
        log_error "输入数据文件仍不存在，部署终止"
        exit 1
    fi
    
    # 显示文件信息
    local file_size=$(du -h "$input_file" | cut -f1)
    local line_count=$(wc -l < "$input_file")
    
    log_info "✅ 输入数据文件检查通过"
    echo "  📄 文件: ofrm_result.txt"
    echo "  📊 大小: $file_size"
    echo "  📈 行数: $line_count"
}

# ============================================================================
# 系统运行
# ============================================================================
run_billing_audit_system() {
    log_step "运行山西电信出账稽核AI系统..."
    
    local script_dir=$(get_script_dir)
    
    # 构建Docker运行参数
    local docker_args=(
        "--rm"
        "--name" "$CONTAINER_NAME"
        "-v" "$script_dir/data/input:/data/input:ro"
        "-v" "$script_dir/data/output:/app/outputs"
        "-v" "$script_dir/logs:/logs"
        "-v" "$script_dir/production_config.json:/app/config/production_config.json:ro"
    )
    
    # 获取当前用户的UID和GID用于容器内权限修复
    local host_uid=$(id -u)
    local host_gid=$(id -g)

    # 根据权限情况决定是否使用root用户
    if [ "$USE_ROOT_USER" = "true" ]; then
        docker_args+=("--user" "root")
        log_info "使用root用户运行容器（避免权限问题）"
        log_info "注意: 输出文件将在运行后修复为用户权限"
    fi

    # 运行系统
    echo ""
    log_info "🚀 开始执行完整流程..."
    log_info "⏱️  预计耗时: 10-30秒（取决于数据量）"
    echo ""

    if docker run "${docker_args[@]}" \
        "$IMAGE_NAME:$IMAGE_TAG" \
        /bin/bash -c "
            # 确保tmp目录存在并可写
            mkdir -p /tmp/billing_audit
            chmod 777 /tmp/billing_audit 2>/dev/null || true

            # 运行主程序
            python scripts/production/billing_audit_main.py full \
                --input /data/input/test_data.txt \
                --algorithm hierarchical \
                --batch-size 1000

            # 程序运行完成后，在容器内修复权限
            echo '🔧 在容器内修复输出文件权限...'
            echo '目标用户: $host_uid:$host_gid'

            # 修复data/output目录权限
            if [ -d '/data/output' ]; then
                chown -R $host_uid:$host_gid /data/output
                chmod -R 755 /data/output
                find /data/output -type f -exec chmod 644 {} \;
                echo '✅ data/output目录权限修复完成'
            fi

            # 修复logs目录权限
            if [ -d '/logs' ]; then
                chown -R $host_uid:$host_gid /logs
                chmod -R 755 /logs
                find /logs -type f -exec chmod 644 {} \;
                echo '✅ logs目录权限修复完成'
            fi

            echo '🎉 容器内权限修复完成！'
        "; then

        log_success "系统执行完成！"
        return 0
    else
        log_error "系统执行失败"
        return 1
    fi
}

# 权限修复已在容器内完成，无需额外处理

# ============================================================================
# 结果验证
# ============================================================================
verify_results() {
    log_step "验证执行结果..."
    
    local script_dir=$(get_script_dir)
    local success_count=0
    local total_checks=4
    
    echo "📊 输出文件检查:"
    echo "----------------------------------------"
    
    # 检查模型文件
    if [ -d "$script_dir/data/output/models" ] && [ "$(ls -A "$script_dir/data/output/models" 2>/dev/null)" ]; then
        local model_count=$(ls "$script_dir/data/output/models" | wc -l)
        echo "✅ 模型文件: $model_count 个"
        ls "$script_dir/data/output/models" | head -3 | sed 's/^/    /'
        ((success_count++))
    else
        echo "❌ 模型文件: 未找到"
    fi
    
    # 检查预测结果
    if [ -d "$script_dir/data/output/data" ] && [ "$(ls -A "$script_dir/data/output/data" 2>/dev/null)" ]; then
        local data_count=$(ls "$script_dir/data/output/data" | wc -l)
        echo "✅ 预测结果: $data_count 个"
        ls "$script_dir/data/output/data" | head -3 | sed 's/^/    /'
        ((success_count++))
    else
        echo "❌ 预测结果: 未找到"
    fi
    
    # 检查执行报告
    if [ -d "$script_dir/data/output/reports" ] && [ "$(ls -A "$script_dir/data/output/reports" 2>/dev/null)" ]; then
        local report_count=$(find "$script_dir/data/output/reports" -type f | wc -l)
        echo "✅ 执行报告: $report_count 个"
        find "$script_dir/data/output/reports" -name "*.json" -o -name "*.md" | head -3 | sed 's/^/    /'
        ((success_count++))
    else
        echo "❌ 执行报告: 未找到"
    fi
    
    # 检查日志文件
    if [ -d "$script_dir/logs" ] && [ "$(ls -A "$script_dir/logs" 2>/dev/null)" ]; then
        local log_count=$(ls "$script_dir/logs" | wc -l)
        echo "✅ 日志文件: $log_count 个"
        ls "$script_dir/logs" | head -3 | sed 's/^/    /'
        ((success_count++))
    else
        echo "❌ 日志文件: 未找到"
    fi
    
    echo "----------------------------------------"
    echo "📈 成功率: $success_count/$total_checks ($(( success_count * 100 / total_checks ))%)"
    
    if [ $success_count -eq $total_checks ]; then
        log_success "所有输出文件验证通过！"
        return 0
    elif [ $success_count -gt 0 ]; then
        log_warn "部分输出文件验证通过"
        return 0
    else
        log_error "输出文件验证失败"
        return 1
    fi
}

# ============================================================================
# 交互询问
# ============================================================================
ask_continue_execution() {
    echo ""
    log_step "准备执行山西电信出账稽核AI系统..."
    echo ""
    echo "📋 当前准备状态："
    echo "  ✅ 环境检测通过"
    echo "  ✅ Docker镜像已加载"
    echo "  ✅ 目录结构已设置"
    echo "  ✅ 输入数据已验证"
    echo ""
    echo "🚀 接下来将执行："
    echo "  🔹 运行AI分析系统（预计10-30秒）"
    echo "  🔹 生成模型文件和预测结果"
    echo "  🔹 创建执行报告"
    echo "  🔹 验证输出文件"
    echo ""
    
    while true; do
        read -p "是否现在运行山西电信出账稽核AI系统？(y/N): " -n 1 -r
        echo
        case $REPLY in
            [Yy]* ) 
                log_info "用户确认，开始运行系统..."
                return 0
                ;;
            [Nn]* | "" ) 
                log_info "用户取消，退出部署流程"
                echo ""
                echo "💡 提示："
                echo "  - 您可以随时重新运行此脚本"
                echo "  - 已完成的准备工作会被保留"
                echo "  - 下次运行会跳过已完成的步骤"
                echo ""
                log_success "部署流程已暂停，环境准备完成"
                return 1
                ;;
            * ) 
                echo "请输入 y (是) 或 n (否)"
                ;;
        esac
    done
}

# ============================================================================
# 使用指导
# ============================================================================
show_usage_guide() {
    local script_dir=$(get_script_dir)
    
    echo ""
    echo -e "${CYAN}============================================================================${NC}"
    echo -e "${CYAN}                           部署完成！使用指导${NC}"
    echo -e "${CYAN}============================================================================${NC}"
    echo ""
    echo "📁 输出文件位置:"
    echo "  🔹 模型文件: $script_dir/data/output/models/"
    echo "  🔹 预测结果: $script_dir/data/output/data/"
    echo "  🔹 执行报告: $script_dir/data/output/reports/"
    echo "  🔹 Markdown报告: $script_dir/data/output/reports/markdown/"
    echo "  🔹 日志文件: $script_dir/logs/"
    echo ""
    echo "🔍 查看结果:"
    echo "  ls -la $script_dir/data/output/models/"
    echo "  ls -la $script_dir/data/output/data/"
    echo "  ls -la $script_dir/data/output/reports/"
    echo ""
    echo "🔄 重新运行:"
    echo "  bash $script_dir/deploy.sh"
    echo ""
    echo "💡 更换数据:"
    echo "  cp /new/data/file.txt $script_dir/data/input/ofrm_result.txt"
    echo "  bash $script_dir/deploy.sh"
    echo ""
}

# ============================================================================
# 主函数
# ============================================================================
main() {
    # 显示横幅
    show_banner
    
    # 执行部署流程前半部分（环境准备）
    check_environment
    load_docker_images
    setup_directories
    check_input_data
    
    # 第五步结束后询问是否继续执行
    if ! ask_continue_execution; then
        # 用户选择不继续，正常退出
        exit 0
    fi
    
    # 用户确认继续，执行后续步骤
    if run_billing_audit_system; then
        verify_results
        show_usage_guide
        log_success "🎉 山西电信出账稽核AI系统部署运行完成！"
    else
        log_error "系统运行失败，请检查错误信息"
        exit 1
    fi
}

# ============================================================================
# 脚本入口
# ============================================================================
# 检查是否直接执行（非source）
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
