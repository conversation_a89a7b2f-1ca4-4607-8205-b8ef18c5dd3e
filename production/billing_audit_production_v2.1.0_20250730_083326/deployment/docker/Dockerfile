# 山西电信出账稽核AI系统 v2.1.0 - 生产环境Docker镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV BILLING_AUDIT_ENV=production
ENV DEBIAN_FRONTEND=noninteractive
ENV BILLING_AUDIT_VERSION=v2.1.0

# 安装系统依赖和常用工具
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libomp-dev \
    libgomp1 \
    curl \
    wget \
    procps \
    iputils-ping \
    telnet \
    net-tools \
    vim \
    htop \
    tree \
    less \
    && rm -rf /var/lib/apt/lists/*

# 设置时区为中国时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建必要的目录
RUN mkdir -p /data/input \
    /data/output \
    /data/output/reports \
    /data/output/reports/markdown \
    /data/output/models \
    /data/output/data \
    /data/output/visualizations \
    /data/output/temp \
    /models \
    /logs \
    /tmp/billing_audit \
    /data/backup \
    /app/outputs \
    /app/outputs/models \
    /app/outputs/data \
    /app/outputs/reports \
    /app/outputs/reports/markdown

# 复制requirements文件并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY config/ ./config/
COPY README.md .

# 设置权限
RUN chmod +x scripts/production/*.py
RUN chmod +x scripts/production/*.sh

# 创建非root用户
RUN useradd -m -u 1000 billing_user && \
    chown -R billing_user:billing_user /app /data /models /logs /tmp/billing_audit

# 切换到非root用户
USER billing_user

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "from src.config.production_config_manager import get_config_manager; get_config_manager()" || exit 1

# 暴露端口
EXPOSE 8000

# 默认命令
CMD ["python", "-c", "print('🚀 山西电信出账稽核AI系统 v2.1.0 容器已启动'); print(''); print('⭐ 推荐使用生产级主脚本:'); print('  python scripts/production/billing_audit_main.py full --input /data/input/data.csv'); print(''); print('🔧 或使用单独功能:'); print('  - 模型训练: python src/billing_audit/training/train_large_scale_model.py'); print('  - 批量预测: python src/billing_audit/inference/predict_large_scale.py'); print('  - 收费判定: python src/billing_audit/inference/large_scale_billing_judge.py'); print(''); print('📊 查看帮助: python scripts/production/billing_audit_main.py --help')"]
