# 山西电信出账稽核AI系统 v2.1.0 - 精简版Docker镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV BILLING_AUDIT_ENV=production
ENV DEBIAN_FRONTEND=noninteractive
ENV BILLING_AUDIT_VERSION=v2.1.0-slim

# 设置时区为中国时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装系统依赖和常用工具（精简版）
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libomp-dev \
    procps \
    iputils-ping \
    telnet \
    net-tools \
    curl \
    wget \
    vim \
    htop \
    tree \
    less \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建必要的目录（精简版）
RUN mkdir -p \
    /app/outputs/models \
    /app/outputs/data \
    /app/outputs/reports \
    /app/outputs/temp \
    /app/outputs/visualizations \
    /app/logs \
    /tmp/billing_audit

# 复制requirements文件并安装Python依赖（精简版）
COPY deployment/docker/requirements.slim.txt ./requirements.txt
RUN pip install --no-cache-dir --no-compile -r requirements.txt \
    && pip cache purge \
    && find /usr/local/lib/python3.9 -name "*.pyc" -delete \
    && find /usr/local/lib/python3.9 -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 复制核心项目文件（只复制必需文件）
COPY src/ ./src/
COPY scripts/production/ ./scripts/production/
COPY config/ ./config/

# 设置权限（精简版）
RUN chmod +x scripts/production/*.py

# 创建非root用户（精简版）
RUN useradd -m -u 1000 billing_user \
    && chown -R billing_user:billing_user /app /tmp/billing_audit

# 切换到非root用户
USER billing_user

# 健康检查（精简版）
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=2 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 暴露端口
EXPOSE 8000

# 默认命令（后台常驻运行）
CMD ["python", "scripts/production/keep_alive.py"]
