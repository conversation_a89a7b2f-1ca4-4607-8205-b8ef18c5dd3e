#!/bin/bash
# 山西电信出账稽核AI系统 v2.1.0 - Linux生产环境部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 版本信息
VERSION="v2.1.0"
BUILD_DATE=$(date +"%Y%m%d_%H%M%S")
IMAGE_NAME="billing-audit-ai"
IMAGE_TAG="${VERSION}-${BUILD_DATE}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🚀 山西电信出账稽核AI系统 ${VERSION}"
    echo "   Linux生产环境部署工具"
    echo "=================================================="
    echo -e "${NC}"
    echo "📅 构建时间: $(date)"
    echo "🏷️  镜像标签: ${IMAGE_TAG}"
    echo "🐳 Docker版本: $(docker --version 2>/dev/null || echo '未安装')"
    echo ""
}

# 检查系统环境
check_system() {
    log_step "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "操作系统: Linux ✅"
    else
        log_warn "当前系统: $OSTYPE (推荐使用Linux)"
    fi
    
    # 检查内存
    total_mem=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$total_mem" -ge 8 ]; then
        log_info "系统内存: ${total_mem}GB ✅"
    else
        log_warn "系统内存: ${total_mem}GB (推荐8GB+)"
    fi
    
    # 检查磁盘空间
    available_space=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$available_space" -ge 20 ]; then
        log_info "可用磁盘: ${available_space}GB ✅"
    else
        log_warn "可用磁盘: ${available_space}GB (推荐20GB+)"
    fi
}

# 检查Docker环境
check_docker() {
    log_step "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，正在安装..."
        install_docker
    else
        log_info "Docker已安装: $(docker --version)"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，正在安装..."
        install_docker_compose
    else
        log_info "Docker Compose已安装: $(docker-compose --version)"
    fi
    
    # 检查Docker服务状态
    if systemctl is-active --quiet docker; then
        log_info "Docker服务运行中 ✅"
    else
        log_warn "启动Docker服务..."
        sudo systemctl start docker
        sudo systemctl enable docker
    fi
    
    # 检查Docker权限
    if docker ps &> /dev/null; then
        log_info "Docker权限正常 ✅"
    else
        log_warn "当前用户需要Docker权限，请运行: sudo usermod -aG docker $USER"
        log_warn "然后重新登录或运行: newgrp docker"
    fi
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    # 更新包索引
    sudo apt-get update
    
    # 安装必要的包
    sudo apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 设置稳定版仓库
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
      $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker Engine
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log_success "Docker安装完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_step "安装Docker Compose..."
    
    # 下载Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 设置执行权限
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建符号链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose安装完成"
}

# 创建项目目录结构
create_project_structure() {
    log_step "创建项目目录结构..."
    
    # 创建主目录
    PROJECT_DIR="/opt/billing-audit-ai"
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown -R $USER:$USER "$PROJECT_DIR"
    cd "$PROJECT_DIR"
    
    # 创建数据目录
    directories=(
        "data/input"
        "data/output"
        "data/output/reports"
        "data/output/reports/markdown"
        "data/output/models"
        "data/output/data"
        "data/output/visualizations"
        "data/backup"
        "models"
        "logs"
        "config"
        "scripts"
        "monitoring"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
    
    # 设置权限
    chmod 755 data/input
    chmod 777 data/output
    chmod 777 logs
    chmod 777 models
    
    log_success "项目目录结构创建完成: $PROJECT_DIR"
}

# 复制项目文件
copy_project_files() {
    log_step "复制项目文件..."
    
    # 获取脚本所在目录的项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    SOURCE_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    log_info "源目录: $SOURCE_DIR"
    log_info "目标目录: $PROJECT_DIR"
    
    # 复制核心文件
    cp -r "$SOURCE_DIR/src" "$PROJECT_DIR/"
    cp -r "$SOURCE_DIR/scripts" "$PROJECT_DIR/"
    cp -r "$SOURCE_DIR/deployment" "$PROJECT_DIR/"
    cp "$SOURCE_DIR/README.md" "$PROJECT_DIR/"
    
    # 复制配置文件
    cp "$SOURCE_DIR/config/production_config.json" "$PROJECT_DIR/config/"
    
    # 复制requirements
    if [ -f "$SOURCE_DIR/requirements.txt" ]; then
        cp "$SOURCE_DIR/requirements.txt" "$PROJECT_DIR/"
    else
        cp "$SOURCE_DIR/deployment/requirements.txt" "$PROJECT_DIR/"
    fi
    
    # 设置脚本权限
    find "$PROJECT_DIR/scripts" -name "*.py" -exec chmod +x {} \;
    find "$PROJECT_DIR/scripts" -name "*.sh" -exec chmod +x {} \;
    find "$PROJECT_DIR/deployment/scripts" -name "*.sh" -exec chmod +x {} \;
    
    log_success "项目文件复制完成"
}

# 构建Docker镜像
build_docker_image() {
    log_step "构建Docker镜像..."
    
    cd "$PROJECT_DIR"
    
    # 构建镜像
    docker build -f deployment/docker/Dockerfile -t "${IMAGE_NAME}:${IMAGE_TAG}" -t "${IMAGE_NAME}:latest" .
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建成功: ${IMAGE_NAME}:${IMAGE_TAG}"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
    
    # 显示镜像信息
    docker images | grep "$IMAGE_NAME"
}

# 创建环境配置
create_environment_config() {
    log_step "创建环境配置..."
    
    # 创建.env文件
    cat > "$PROJECT_DIR/.env" << EOF
# 山西电信出账稽核AI系统 ${VERSION} 环境变量
BILLING_AUDIT_ENV=production
BILLING_AUDIT_VERSION=${VERSION}
DATA_INPUT_DIR=/data/input
DATA_OUTPUT_DIR=/data/output
MODEL_DIR=/models
LOGS_DIR=/logs
DB_PASSWORD=billing_password_$(date +%s)
PYTHONPATH=/app
OMP_NUM_THREADS=4
BUILD_DATE=${BUILD_DATE}
IMAGE_TAG=${IMAGE_TAG}
EOF
    
    log_info "环境配置文件已创建: $PROJECT_DIR/.env"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 使用docker-compose启动
    docker-compose -f deployment/docker/docker-compose.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    # 检查服务状态
    if docker ps | grep -q "billing-audit-ai.*Up"; then
        log_success "服务启动成功 ✅"
    else
        log_error "服务启动失败"
        docker-compose -f deployment/docker/docker-compose.yml logs
        exit 1
    fi
}

# 运行健康检查
run_health_check() {
    log_step "运行健康检查..."
    
    # 检查容器状态
    container_status=$(docker inspect --format='{{.State.Health.Status}}' billing-audit-ai 2>/dev/null || echo "no-health-check")
    log_info "容器健康状态: $container_status"
    
    # 测试配置加载
    docker exec billing-audit-ai python -c "
from src.config.production_config_manager import get_config_manager
config = get_config_manager()
print('✅ 配置加载成功')
print(f'项目: {config.get(\"project.name\")}')
print(f'版本: {config.get(\"project.version\")}')
print(f'批处理大小: {config.get_batch_size()}')
"
    
    if [ $? -eq 0 ]; then
        log_success "配置测试通过 ✅"
    else
        log_error "配置测试失败"
        exit 1
    fi
    
    # 测试主脚本
    log_info "测试主脚本..."
    docker exec billing-audit-ai python scripts/production/billing_audit_main.py --help > /dev/null
    
    if [ $? -eq 0 ]; then
        log_success "主脚本测试通过 ✅"
    else
        log_error "主脚本测试失败"
        exit 1
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."

    # 创建启动脚本
    cat > "$PROJECT_DIR/start.sh" << 'EOF'
#!/bin/bash
echo "🚀 启动山西电信出账稽核AI系统..."
cd /opt/billing-audit-ai
docker-compose -f deployment/docker/docker-compose.yml up -d
echo "✅ 系统已启动"
docker ps | grep billing-audit
EOF

    # 创建停止脚本
    cat > "$PROJECT_DIR/stop.sh" << 'EOF'
#!/bin/bash
echo "🛑 停止山西电信出账稽核AI系统..."
cd /opt/billing-audit-ai
docker-compose -f deployment/docker/docker-compose.yml down
echo "✅ 系统已停止"
EOF

    # 创建重启脚本
    cat > "$PROJECT_DIR/restart.sh" << 'EOF'
#!/bin/bash
echo "🔄 重启山西电信出账稽核AI系统..."
cd /opt/billing-audit-ai
docker-compose -f deployment/docker/docker-compose.yml restart
echo "✅ 系统已重启"
docker ps | grep billing-audit
EOF

    # 创建日志查看脚本
    cat > "$PROJECT_DIR/logs.sh" << 'EOF'
#!/bin/bash
echo "📋 查看系统日志..."
cd /opt/billing-audit-ai
docker-compose -f deployment/docker/docker-compose.yml logs -f billing-audit-ai
EOF

    # 创建进入容器脚本
    cat > "$PROJECT_DIR/shell.sh" << 'EOF'
#!/bin/bash
echo "🐚 进入系统容器..."
docker exec -it billing-audit-ai bash
EOF

    # 创建主脚本快捷方式
    cat > "$PROJECT_DIR/run_main.sh" << 'EOF'
#!/bin/bash
echo "🚀 运行生产级主脚本..."
echo "用法: ./run_main.sh <命令> [参数]"
echo "示例: ./run_main.sh full --input /data/input/data.csv"
echo ""
if [ $# -eq 0 ]; then
    docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py --help
else
    docker exec -it billing-audit-ai python scripts/production/billing_audit_main.py "$@"
fi
EOF

    # 设置执行权限
    chmod +x "$PROJECT_DIR"/*.sh

    log_success "管理脚本创建完成"
}

# 创建系统服务
create_systemd_service() {
    log_step "创建系统服务..."

    # 创建systemd服务文件
    sudo tee /etc/systemd/system/billing-audit-ai.service > /dev/null << EOF
[Unit]
Description=山西电信出账稽核AI系统 ${VERSION}
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=${PROJECT_DIR}
ExecStart=/usr/local/bin/docker-compose -f deployment/docker/docker-compose.yml up -d
ExecStop=/usr/local/bin/docker-compose -f deployment/docker/docker-compose.yml down
TimeoutStartSec=0
User=${USER}
Group=${USER}

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    sudo systemctl daemon-reload

    # 启用服务
    sudo systemctl enable billing-audit-ai.service

    log_success "系统服务创建完成"
    log_info "使用以下命令管理服务:"
    log_info "  启动: sudo systemctl start billing-audit-ai"
    log_info "  停止: sudo systemctl stop billing-audit-ai"
    log_info "  状态: sudo systemctl status billing-audit-ai"
}

# 显示部署信息
show_deployment_info() {
    log_step "部署信息"

    echo ""
    echo -e "${GREEN}=================================================="
    echo "🎉 山西电信出账稽核AI系统 ${VERSION} 部署完成"
    echo "==================================================${NC}"
    echo ""
    echo -e "${BLUE}📊 系统信息:${NC}"
    echo "  版本: ${VERSION}"
    echo "  构建时间: ${BUILD_DATE}"
    echo "  镜像: ${IMAGE_NAME}:${IMAGE_TAG}"
    echo "  项目目录: ${PROJECT_DIR}"
    echo ""
    echo -e "${BLUE}📊 服务状态:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(NAMES|billing-audit)"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo "  启动系统: cd ${PROJECT_DIR} && ./start.sh"
    echo "  停止系统: cd ${PROJECT_DIR} && ./stop.sh"
    echo "  重启系统: cd ${PROJECT_DIR} && ./restart.sh"
    echo "  查看日志: cd ${PROJECT_DIR} && ./logs.sh"
    echo "  进入容器: cd ${PROJECT_DIR} && ./shell.sh"
    echo "  运行主脚本: cd ${PROJECT_DIR} && ./run_main.sh"
    echo ""
    echo -e "${BLUE}📁 重要目录:${NC}"
    echo "  输入数据: ${PROJECT_DIR}/data/input/"
    echo "  输出结果: ${PROJECT_DIR}/data/output/"
    echo "  执行报告: ${PROJECT_DIR}/data/output/reports/markdown/"
    echo "  模型文件: ${PROJECT_DIR}/models/"
    echo "  日志文件: ${PROJECT_DIR}/logs/"
    echo ""
    echo -e "${BLUE}🚀 使用示例 (v2.1.0):${NC}"
    echo ""
    echo -e "${PURPLE}  ⭐ 推荐: 生产级主脚本 (完整流程)${NC}"
    echo "  cd ${PROJECT_DIR}"
    echo "  ./run_main.sh full --input /data/input/data.csv --batch-size 1000"
    echo "  # 输出: Markdown报告 + 模型 + 预测 + 判定结果"
    echo ""
    echo -e "${PURPLE}  🔧 模块化执行:${NC}"
    echo "  ./run_main.sh feature-engineering --input /data/input/raw_data.csv"
    echo "  ./run_main.sh training --input /data/input/processed_data.csv"
    echo "  ./run_main.sh prediction --input /data/input/test_data.csv"
    echo "  ./run_main.sh judgment --input /data/input/prediction_results.csv"
    echo ""
    echo -e "${BLUE}🔗 系统服务:${NC}"
    echo "  启动服务: sudo systemctl start billing-audit-ai"
    echo "  停止服务: sudo systemctl stop billing-audit-ai"
    echo "  查看状态: sudo systemctl status billing-audit-ai"
    echo ""
    echo -e "${GREEN}✅ 部署完成！系统已就绪，可以开始使用。${NC}"
    echo ""
}

# 清理函数
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "部署过程中发生错误，正在清理..."
        if [ -n "$PROJECT_DIR" ] && [ -d "$PROJECT_DIR" ]; then
            cd "$PROJECT_DIR" 2>/dev/null && docker-compose -f deployment/docker/docker-compose.yml down 2>/dev/null || true
        fi
    fi
}

# 主函数
main() {
    show_banner

    # 设置错误处理
    trap cleanup EXIT

    # 执行部署步骤
    check_system
    check_docker
    create_project_structure
    copy_project_files
    create_environment_config
    build_docker_image
    start_services
    run_health_check
    create_management_scripts
    create_systemd_service
    show_deployment_info

    log_success "🎉 部署完成！"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --project-dir)
            PROJECT_DIR="$2"
            shift 2
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --skip-docker-install)
            SKIP_DOCKER_INSTALL=true
            shift
            ;;
        --help)
            echo "山西电信出账稽核AI系统 ${VERSION} - Linux生产环境部署脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --project-dir DIR      指定项目目录 (默认: /opt/billing-audit-ai)"
            echo "  --image-tag TAG        指定镜像标签 (默认: ${VERSION}-时间戳)"
            echo "  --skip-docker-install  跳过Docker安装"
            echo "  --help                 显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                                    # 默认部署"
            echo "  $0 --project-dir /home/<USER>/billing   # 自定义目录"
            echo "  $0 --skip-docker-install             # 跳过Docker安装"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 设置默认项目目录
PROJECT_DIR="${PROJECT_DIR:-/opt/billing-audit-ai}"

# 运行主函数
main
