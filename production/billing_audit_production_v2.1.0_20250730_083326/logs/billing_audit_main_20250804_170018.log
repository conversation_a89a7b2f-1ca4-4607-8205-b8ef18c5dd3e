2025-08-04 17:00:18 - billing_audit_main - INFO - ================================================================================
2025-08-04 17:00:18 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-04 17:00:18 - billing_audit_main - INFO - ================================================================================
2025-08-04 17:00:18 - billing_audit_main - INFO - 项目根目录: /app
2025-08-04 17:00:18 - billing_audit_main - INFO - 时间戳: 20250804_170018
2025-08-04 17:00:18 - billing_audit_main - INFO - 开始运行完整的出账稽核AI流程
2025-08-04 17:00:18 - billing_audit_main - INFO - 🔄 正确的流程: 原始数据 → 特征工程 → 数据拆分 → 训练 → 评估 → 预测 → 判定
2025-08-04 17:00:18 - billing_audit_main - INFO - ============================================================
2025-08-04 17:00:18 - billing_audit_main - INFO - 步骤1: 验证原始输入数据
2025-08-04 17:00:18 - billing_audit_main - INFO - 验证输入数据: data/input/ofrm_result.txt
2025-08-04 17:00:18 - billing_audit_main - INFO - 数据规模: 60,354 行 × 26 列
2025-08-04 17:00:18 - billing_audit_main - INFO - 执行数据质量检查...
2025-08-04 17:00:18 - billing_audit_main - INFO - 目标列 amount 统计: 均值=223.97, 标准差=3815.96
2025-08-04 17:00:18 - billing_audit_main - INFO - 零值比例: 92.67% (55932/60354)
2025-08-04 17:00:18 - billing_audit_main - WARNING - 特征 final_eff_mon 存在 34330 个空值
2025-08-04 17:00:18 - billing_audit_main - WARNING - 特征 final_eff_day 存在 34330 个空值
2025-08-04 17:00:18 - billing_audit_main - WARNING - 特征 final_exp_mon 存在 34330 个空值
2025-08-04 17:00:18 - billing_audit_main - WARNING - 特征 final_exp_day 存在 34330 个空值
2025-08-04 17:00:18 - billing_audit_main - INFO - 输入数据验证通过
2025-08-04 17:00:18 - billing_audit_main - INFO - 字段分类: 训练特征(14) + 目标字段(1) + 透传字段(11) = 26个字段
2025-08-04 17:00:18 - billing_audit_main - INFO - 步骤2: 对原始数据进行特征工程
2025-08-04 17:00:18 - billing_audit_main - INFO - 开始特征工程...
2025-08-04 17:00:18 - billing_audit_main - INFO - 开始执行: 特征工程
2025-08-04 17:00:18 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/large_scale_feature_engineer.py
2025-08-04 17:00:18 - billing_audit_main - INFO - 参数: --input data/input/ofrm_result.txt --output /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl --batch-size 1000
2025-08-04 17:00:19 - billing_audit_main - INFO - 特征工程 执行成功，耗时: 0.70秒
2025-08-04 17:00:19 - billing_audit_main - INFO - 特征工程完成，输出: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl
2025-08-04 17:00:19 - billing_audit_main - INFO - 步骤3: 拆分特征工程后的数据
2025-08-04 17:00:19 - billing_audit_main - INFO - 创建处理后数据文件: /app/outputs/temp/run_20250804_170018/processed_data_20250804_170018.csv
2025-08-04 17:00:19 - billing_audit_main - INFO - 临时方案：复制原始文件到 /app/outputs/temp/run_20250804_170018/processed_data_20250804_170018.csv
2025-08-04 17:00:19 - billing_audit_main - INFO - 开始数据拆分...
2025-08-04 17:00:19 - billing_audit_main - INFO - 开始执行: 数据拆分
2025-08-04 17:00:19 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/data_splitter.py
2025-08-04 17:00:19 - billing_audit_main - INFO - 参数: --input /app/outputs/temp/run_20250804_170018/processed_data_20250804_170018.csv --output /app/outputs/temp/run_20250804_170018 --test-size 0.2 --target-column amount
2025-08-04 17:00:21 - billing_audit_main - INFO - 数据拆分 执行成功，耗时: 1.61秒
2025-08-04 17:00:21 - billing_audit_main - INFO - 数据拆分完成，训练集: /app/outputs/temp/run_20250804_170018/train_data_20250804_170020.csv, 测试集: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv
2025-08-04 17:00:21 - billing_audit_main - INFO - 步骤4: 使用训练集进行模型训练
2025-08-04 17:00:21 - billing_audit_main - INFO - 开始模型训练...
2025-08-04 17:00:21 - billing_audit_main - INFO - 选择的训练算法: hierarchical
2025-08-04 17:00:21 - billing_audit_main - INFO - 使用分层模型训练
2025-08-04 17:00:21 - billing_audit_main - INFO - 开始分层模型训练...
2025-08-04 17:00:27 - billing_audit_main - INFO - 分层模型训练完成
2025-08-04 17:00:27 - billing_audit_main - INFO - 训练统计: {'model_type': 'HierarchicalBillingModel', 'algorithm': 'LightGBM', 'samples': 48283, 'features': 19, 'r2': 0.11041416764487644, 'mae': 71.82717454938813, 'rmse': 3556.3960927574085, 'zero_accuracy': 0.9675248016900359, 'training_time': 4.75504469871521, 'timestamp': '2025-08-04T17:00:27.121707'}
2025-08-04 17:00:27 - billing_audit_main - INFO - 步骤5: 使用测试集进行模型评估
2025-08-04 17:00:27 - billing_audit_main - INFO - 开始模型评估...
2025-08-04 17:00:27 - billing_audit_main - INFO - 使用分层评估脚本进行评估...
2025-08-04 17:00:27 - billing_audit_main - INFO - 开始执行: 分层模型评估
2025-08-04 17:00:27 - billing_audit_main - INFO - 脚本路径: src/billing_audit/models/large_scale_model_evaluation.py
2025-08-04 17:00:27 - billing_audit_main - INFO - 参数: --test-data /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv --model /app/outputs/models/hierarchical_model_20250804_170027.pkl --feature-engineer /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl --target-column amount --batch-size 1000 --output /app/outputs/reports/hierarchical_evaluation_report_20250804_170018.json
2025-08-04 17:00:27 - billing_audit_main - INFO - 分层模型评估 执行成功，耗时: 0.68秒
2025-08-04 17:00:27 - billing_audit_main - INFO - 分层模型评估完成，报告: /app/outputs/reports/hierarchical_evaluation_report_20250804_170018.json
2025-08-04 17:00:27 - billing_audit_main - INFO - 步骤6: 进行模型预测
2025-08-04 17:00:27 - billing_audit_main - INFO - 开始模型预测...
2025-08-04 17:00:27 - billing_audit_main - INFO - 使用分层预测脚本进行预测...
2025-08-04 17:00:27 - billing_audit_main - INFO - 开始执行: 分层模型预测
2025-08-04 17:00:27 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/predict_large_scale.py
2025-08-04 17:00:27 - billing_audit_main - INFO - 参数: --input /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv --model /app/outputs/models/hierarchical_model_20250804_170027.pkl --feature-engineer /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl --output /app/outputs/data/hierarchical_predictions_20250804_170018.csv --batch-size 1000 --include-features
2025-08-04 17:00:28 - billing_audit_main - INFO - 分层模型预测 执行成功，耗时: 0.71秒
2025-08-04 17:00:28 - billing_audit_main - INFO - 分层模型预测完成，输出: /app/outputs/data/hierarchical_predictions_20250804_170018.csv
2025-08-04 17:00:28 - billing_audit_main - INFO - 步骤7: 进行收费合理性判定
2025-08-04 17:00:28 - billing_audit_main - INFO - 开始收费合理性判定...
2025-08-04 17:00:28 - billing_audit_main - INFO - 使用分层模型进行判定: hierarchical_model_20250804_170027.pkl
2025-08-04 17:00:28 - billing_audit_main - INFO - 开始执行: 收费合理性判定
2025-08-04 17:00:28 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/large_scale_billing_judge.py
2025-08-04 17:00:28 - billing_audit_main - INFO - 参数: --input /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv --model /app/outputs/models/hierarchical_model_20250804_170027.pkl --feature-engineer /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl --output /app/outputs/data/billing_judgments_20250804_170018.csv --target-column amount --batch-size 1000 --abs-threshold 50.0 --rel-threshold 0.1
2025-08-04 17:00:29 - billing_audit_main - INFO - 收费合理性判定 执行成功，耗时: 0.75秒
2025-08-04 17:00:29 - billing_audit_main - INFO - 收费合理性判定完成，输出: /app/outputs/data/billing_judgments_20250804_170018.csv
2025-08-04 17:00:29 - billing_audit_main - INFO - ============================================================
2025-08-04 17:00:29 - billing_audit_main - INFO - ✅ 完整流程执行完成
2025-08-04 17:00:29 - billing_audit_main - INFO - 总耗时: 10.39秒
2025-08-04 17:00:29 - billing_audit_main - INFO - 🎯 流程总结: 原始数据 → 特征工程 → 数据拆分 → 训练 → 评估 → 预测 → 判定
2025-08-04 17:00:29 - billing_audit_main - INFO - 执行报告已保存: /app/outputs/reports/execution_report_20250804_170018.json
2025-08-04 17:00:29 - billing_audit_main - INFO - 执行摘要: 7/7 步骤成功 (100.0%)
2025-08-04 17:00:29 - billing_audit_main - INFO - 通过专用脚本生成综合报告...
2025-08-04 17:00:29 - billing_audit_main - INFO - 开始执行: 综合报告生成
2025-08-04 17:00:29 - billing_audit_main - INFO - 脚本路径: src/billing_audit/reporting/comprehensive_report_generator.py
2025-08-04 17:00:29 - billing_audit_main - INFO - 参数: --timestamp 20250804_170018 --models-dir /app/outputs/models --data-dir /app/outputs/data --reports-dir /app/outputs/reports --total-duration 10.393064 --execution-results /app/outputs/reports/temp_execution_results_20250804_170018.json
2025-08-04 17:00:29 - billing_audit_main - INFO - 综合报告生成 执行成功，耗时: 0.19秒
2025-08-04 17:00:29 - billing_audit_main - INFO - 综合报告生成成功
2025-08-04 17:00:29 - billing_audit_main - INFO - ✅ 执行成功完成
