2025-08-05 16:32:47 - billing_audit_main - INFO - ================================================================================
2025-08-05 16:32:47 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-05 16:32:47 - billing_audit_main - INFO - ================================================================================
2025-08-05 16:32:47 - billing_audit_main - INFO - 项目根目录: /app
2025-08-05 16:32:47 - billing_audit_main - INFO - 时间戳: 20250805_163247
2025-08-05 16:32:47 - billing_audit_main - INFO - 开始模型预测...
2025-08-05 16:32:47 - billing_audit_main - INFO - 使用分层预测脚本进行预测...
2025-08-05 16:32:47 - billing_audit_main - INFO - 开始执行: 分层模型预测
2025-08-05 16:32:47 - billing_audit_main - INFO - 脚本路径: src/billing_audit/inference/predict_large_scale.py
2025-08-05 16:32:47 - billing_audit_main - INFO - 参数: --input data/input/ofrm_result.txt --model /app/outputs/models/hierarchical_model_20250805_160217.pkl --feature-engineer /app/outputs/models/large_scale_feature_engineer_20250805_160209.pkl --output /app/outputs/data/hierarchical_predictions_20250805_163247.csv --batch-size 1000 --include-features
2025-08-05 16:32:50 - billing_audit_main - INFO - 分层模型预测 执行成功，耗时: 2.26秒
2025-08-05 16:32:50 - billing_audit_main - INFO - 分层模型预测完成，输出: /app/outputs/data/hierarchical_predictions_20250805_163247.csv
2025-08-05 16:32:50 - billing_audit_main - INFO - ✅ 执行成功完成
