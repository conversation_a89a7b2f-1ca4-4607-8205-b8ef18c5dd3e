2025-08-05 21:00:01 - billing_audit_main - INFO - ================================================================================
2025-08-05 21:00:01 - billing_audit_main - INFO - 山西电信出账稽核AI系统主脚本启动
2025-08-05 21:00:01 - billing_audit_main - INFO - ================================================================================
2025-08-05 21:00:01 - billing_audit_main - INFO - 项目根目录: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326
2025-08-05 21:00:01 - billing_audit_main - INFO - 时间戳: 20250805_210001
2025-08-05 21:00:01 - billing_audit_main - INFO - 开始运行完整的出账稽核AI流程
2025-08-05 21:00:01 - billing_audit_main - INFO - 🔄 正确的流程: 原始数据 → 特征工程 → 数据拆分 → 训练 → 评估 → 预测 → 判定
2025-08-05 21:00:01 - billing_audit_main - INFO - ============================================================
2025-08-05 21:00:01 - billing_audit_main - INFO - 步骤1: 验证原始输入数据
2025-08-05 21:00:01 - billing_audit_main - INFO - 验证输入数据: data/input/ofrm_result.txt
2025-08-05 21:00:01 - billing_audit_main - INFO - 数据规模: 60,354 行 × 26 列
2025-08-05 21:00:01 - billing_audit_main - INFO - 执行数据质量检查...
2025-08-05 21:00:01 - billing_audit_main - INFO - 目标列 amount 统计: 均值=223.97, 标准差=3815.96
2025-08-05 21:00:01 - billing_audit_main - INFO - 零值比例: 92.67% (55932/60354)
2025-08-05 21:00:01 - billing_audit_main - WARNING - 特征 final_eff_mon 存在 34330 个空值
2025-08-05 21:00:01 - billing_audit_main - WARNING - 特征 final_eff_day 存在 34330 个空值
2025-08-05 21:00:01 - billing_audit_main - WARNING - 特征 final_exp_mon 存在 34330 个空值
2025-08-05 21:00:01 - billing_audit_main - WARNING - 特征 final_exp_day 存在 34330 个空值
2025-08-05 21:00:01 - billing_audit_main - INFO - 输入数据验证通过
2025-08-05 21:00:01 - billing_audit_main - INFO - 字段分类: 训练特征(14) + 目标字段(1) + 透传字段(11) = 26个字段
2025-08-05 21:00:01 - billing_audit_main - INFO - 步骤2: 对原始数据进行特征工程
2025-08-05 21:00:01 - billing_audit_main - INFO - 开始特征工程...
2025-08-05 21:00:01 - billing_audit_main - INFO - 开始执行: 特征工程
2025-08-05 21:00:01 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/large_scale_feature_engineer.py
2025-08-05 21:00:01 - billing_audit_main - INFO - 参数: --input data/input/ofrm_result.txt --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/models/large_scale_feature_engineer_20250805_210001.pkl --batch-size 50000
2025-08-05 21:00:01 - billing_audit_main - INFO - 特征工程 执行成功，耗时: 0.62秒
2025-08-05 21:00:01 - billing_audit_main - INFO - 特征工程完成，输出: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/models/large_scale_feature_engineer_20250805_210001.pkl
2025-08-05 21:00:01 - billing_audit_main - INFO - 步骤3: 拆分特征工程后的数据
2025-08-05 21:00:01 - billing_audit_main - INFO - 创建处理后数据文件: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/temp/run_20250805_210001/processed_data_20250805_210001.csv
2025-08-05 21:00:01 - billing_audit_main - INFO - 临时方案：复制原始文件到 /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/temp/run_20250805_210001/processed_data_20250805_210001.csv
2025-08-05 21:00:01 - billing_audit_main - INFO - 开始数据拆分...
2025-08-05 21:00:01 - billing_audit_main - INFO - 开始执行: 数据拆分
2025-08-05 21:00:01 - billing_audit_main - INFO - 脚本路径: src/billing_audit/preprocessing/data_splitter.py
2025-08-05 21:00:01 - billing_audit_main - INFO - 参数: --input /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/temp/run_20250805_210001/processed_data_20250805_210001.csv --output /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/temp/run_20250805_210001 --test-size 0.2 --target-column amount
2025-08-05 21:00:03 - billing_audit_main - INFO - 数据拆分 执行成功，耗时: 1.30秒
2025-08-05 21:00:03 - billing_audit_main - INFO - 数据拆分完成，训练集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/temp/run_20250805_210001/train_data_20250805_210002.csv, 测试集: /Users/<USER>/work/01-产品/九思计费专家/省份试点/山西电信/出账稽核AI/production/billing_audit_production_v2.1.0_20250730_083326/outputs/temp/run_20250805_210001/test_data_20250805_210002.csv
2025-08-05 21:00:03 - billing_audit_main - INFO - 步骤4: 使用训练集进行模型训练
2025-08-05 21:00:03 - billing_audit_main - INFO - 开始模型训练...
