{"execution_info": {"timestamp": "20250804_170018", "start_time": "2025-08-04T17:00:29.275095", "total_duration": 10.393064, "project_root": "/app"}, "results": {"data_validation": {"success": true, "data_quality": {"total_samples": 60354, "total_features": 14, "missing_values": [{"feature": "final_eff_mon", "null_count": 34330, "null_ratio": 0.5688106836332306}, {"feature": "final_eff_day", "null_count": 34330, "null_ratio": 0.5688106836332306}, {"feature": "final_exp_mon", "null_count": 34330, "null_ratio": 0.5688106836332306}, {"feature": "final_exp_day", "null_count": 34330, "null_ratio": 0.5688106836332306}], "zero_ratio": 0.9267322795506512, "has_missing_values": true}}, "feature_engineering": {"success": true, "duration": 0.700686, "stdout": "初始化大规模特征工程器\n  - 特征列数: 14\n  - 类别列数: 4\n  - 数值列数: 9\n开始拟合统计量...\n  - 数据文件: data/input/ofrm_result.txt\n  - 批次大小: 1,000\n  统计量拟合完成:\n    - 总批次: 61\n    - 总行数: 60,354\n\n数值列统计摘要:\n  final_eff_year: 均值=871.62, 标准差=1001.11, 范围=[0.00, 2025.00]\n  final_eff_mon: 均值=6.30, 标准差=3.36, 范围=[1.00, 12.00]\n  final_eff_day: 均值=11.96, 标准差=10.30, 范围=[1.00, 31.00]\n  final_exp_year: 均值=879.27, 标准差=1009.92, 范围=[0.00, 2050.00]\n  final_exp_mon: 均值=6.80, 标准差=3.67, 范围=[1.00, 12.00]\n  final_exp_day: 均值=25.96, 标准差=9.34, 范围=[1.00, 31.00]\n  charge_day_count: 均值=9.80, 标准差=14.42, 范围=[0.00, 31.00]\n  month_day_count: 均值=31.00, 标准差=0.00, 范围=[31.00, 31.00]\n  should_fee: 均值=763.05, 标准差=5153.87, 范围=[0.00, 346000.00]\n\n类别列统计摘要:\n  cal_type: 3 个唯一值\n  unit_type: 2 个唯一值\n  rate_unit: 4 个唯一值\n  busi_flag: 2 个唯一值\n预处理器已保存: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n\n大规模特征工程器测试完成！\n", "stderr": "2025-08-04 17:00:19,180 - src.config.production_config_manager - INFO - 加载配置文件: /app/config/production_config.json\n2025-08-04 17:00:19,181 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-04 17:00:19,181 - large_scale_feature_engineer - INFO - 初始化大规模特征工程器\n2025-08-04 17:00:19,181 - large_scale_feature_engineer - INFO - 特征列数: 14\n2025-08-04 17:00:19,181 - large_scale_feature_engineer - INFO - 类别列数: 4\n2025-08-04 17:00:19,182 - large_scale_feature_engineer - INFO - 数值列数: 9\n2025-08-04 17:00:19,182 - large_scale_feature_engineer - INFO - 开始拟合统计量\n2025-08-04 17:00:19,182 - large_scale_feature_engineer - INFO - 数据文件: data/input/ofrm_result.txt\n2025-08-04 17:00:19,182 - large_scale_feature_engineer - INFO - 批次大小: 1,000\n\n统计拟合: 0批次 [00:00, ?批次/s]\n统计拟合: 0批次 [00:00, ?批次/s, 批次=1, 当前行数=1,000, 累计行数=1,000, 数值列=9, 类别列=4]\n统计拟合: 1批次 [00:00, 84.45批次/s, 批次=2, 当前行数=1,000, 累计行数=2,000, 数值列=9, 类别列=4]\n统计拟合: 2批次 [00:00, 118.00批次/s, 批次=3, 当前行数=1,000, 累计行数=3,000, 数值列=9, 类别列=4]\n统计拟合: 3批次 [00:00, 134.17批次/s, 批次=4, 当前行数=1,000, 累计行数=4,000, 数值列=9, 类别列=4]\n统计拟合: 4批次 [00:00, 145.47批次/s, 批次=5, 当前行数=1,000, 累计行数=5,000, 数值列=9, 类别列=4]\n统计拟合: 5批次 [00:00, 153.77批次/s, 批次=6, 当前行数=1,000, 累计行数=6,000, 数值列=9, 类别列=4]\n统计拟合: 6批次 [00:00, 157.63批次/s, 批次=7, 当前行数=1,000, 累计行数=7,000, 数值列=9, 类别列=4]\n统计拟合: 7批次 [00:00, 158.04批次/s, 批次=8, 当前行数=1,000, 累计行数=8,000, 数值列=9, 类别列=4]\n统计拟合: 8批次 [00:00, 156.41批次/s, 批次=9, 当前行数=1,000, 累计行数=9,000, 数值列=9, 类别列=4]\n统计拟合: 9批次 [00:00, 153.89批次/s, 批次=10, 当前行数=1,000, 累计行数=10,000, 数值列=9, 类别列=4]2025-08-04 17:00:19,251 - large_scale_feature_engineer - INFO - 处理进度: 已完成10批次, 累计10,000行\n\n统计拟合: 10批次 [00:00, 137.81批次/s, 批次=11, 当前行数=1,000, 累计行数=11,000, 数值列=9, 类别列=4]\n统计拟合: 11批次 [00:00, 137.73批次/s, 批次=12, 当前行数=1,000, 累计行数=12,000, 数值列=9, 类别列=4]\n统计拟合: 12批次 [00:00, 137.68批次/s, 批次=13, 当前行数=1,000, 累计行数=13,000, 数值列=9, 类别列=4]\n统计拟合: 13批次 [00:00, 136.79批次/s, 批次=14, 当前行数=1,000, 累计行数=14,000, 数值列=9, 类别列=4]\n统计拟合: 14批次 [00:00, 136.76批次/s, 批次=15, 当前行数=1,000, 累计行数=15,000, 数值列=9, 类别列=4]\n统计拟合: 15批次 [00:00, 146.46批次/s, 批次=15, 当前行数=1,000, 累计行数=15,000, 数值列=9, 类别列=4]\n统计拟合: 15批次 [00:00, 146.46批次/s, 批次=16, 当前行数=1,000, 累计行数=16,000, 数值列=9, 类别列=4]\n统计拟合: 16批次 [00:00, 146.46批次/s, 批次=17, 当前行数=1,000, 累计行数=17,000, 数值列=9, 类别列=4]\n统计拟合: 17批次 [00:00, 146.46批次/s, 批次=18, 当前行数=1,000, 累计行数=18,000, 数值列=9, 类别列=4]\n统计拟合: 18批次 [00:00, 146.46批次/s, 批次=19, 当前行数=1,000, 累计行数=19,000, 数值列=9, 类别列=4]\n统计拟合: 19批次 [00:00, 146.46批次/s, 批次=20, 当前行数=1,000, 累计行数=20,000, 数值列=9, 类别列=4]2025-08-04 17:00:19,329 - large_scale_feature_engineer - INFO - 处理进度: 已完成20批次, 累计20,000行\n\n统计拟合: 20批次 [00:00, 146.46批次/s, 批次=21, 当前行数=1,000, 累计行数=21,000, 数值列=9, 类别列=4]\n统计拟合: 21批次 [00:00, 146.46批次/s, 批次=22, 当前行数=1,000, 累计行数=22,000, 数值列=9, 类别列=4]\n统计拟合: 22批次 [00:00, 146.46批次/s, 批次=23, 当前行数=1,000, 累计行数=23,000, 数值列=9, 类别列=4]\n统计拟合: 23批次 [00:00, 146.46批次/s, 批次=24, 当前行数=1,000, 累计行数=24,000, 数值列=9, 类别列=4]\n统计拟合: 24批次 [00:00, 146.46批次/s, 批次=25, 当前行数=1,000, 累计行数=25,000, 数值列=9, 类别列=4]\n统计拟合: 25批次 [00:00, 146.46批次/s, 批次=26, 当前行数=1,000, 累计行数=26,000, 数值列=9, 类别列=4]\n统计拟合: 26批次 [00:00, 146.46批次/s, 批次=27, 当前行数=1,000, 累计行数=27,000, 数值列=9, 类别列=4]\n统计拟合: 27批次 [00:00, 146.46批次/s, 批次=28, 当前行数=1,000, 累计行数=28,000, 数值列=9, 类别列=4]\n统计拟合: 28批次 [00:00, 146.46批次/s, 批次=29, 当前行数=1,000, 累计行数=29,000, 数值列=9, 类别列=4]\n统计拟合: 29批次 [00:00, 146.46批次/s, 批次=30, 当前行数=1,000, 累计行数=30,000, 数值列=9, 类别列=4]2025-08-04 17:00:19,394 - large_scale_feature_engineer - INFO - 处理进度: 已完成30批次, 累计30,000行\n\n统计拟合: 30批次 [00:00, 146.46批次/s, 批次=31, 当前行数=1,000, 累计行数=31,000, 数值列=9, 类别列=4]\n统计拟合: 31批次 [00:00, 144.94批次/s, 批次=31, 当前行数=1,000, 累计行数=31,000, 数值列=9, 类别列=4]\n统计拟合: 31批次 [00:00, 144.94批次/s, 批次=32, 当前行数=1,000, 累计行数=32,000, 数值列=9, 类别列=4]\n统计拟合: 32批次 [00:00, 144.94批次/s, 批次=33, 当前行数=1,000, 累计行数=33,000, 数值列=9, 类别列=4]\n统计拟合: 33批次 [00:00, 144.94批次/s, 批次=34, 当前行数=1,000, 累计行数=34,000, 数值列=9, 类别列=4]\n统计拟合: 34批次 [00:00, 144.94批次/s, 批次=35, 当前行数=1,000, 累计行数=35,000, 数值列=9, 类别列=4]\n统计拟合: 35批次 [00:00, 144.94批次/s, 批次=36, 当前行数=1,000, 累计行数=36,000, 数值列=9, 类别列=4]\n统计拟合: 36批次 [00:00, 144.94批次/s, 批次=37, 当前行数=1,000, 累计行数=37,000, 数值列=9, 类别列=4]\n统计拟合: 37批次 [00:00, 144.94批次/s, 批次=38, 当前行数=1,000, 累计行数=38,000, 数值列=9, 类别列=4]\n统计拟合: 38批次 [00:00, 144.94批次/s, 批次=39, 当前行数=1,000, 累计行数=39,000, 数值列=9, 类别列=4]\n统计拟合: 39批次 [00:00, 144.94批次/s, 批次=40, 当前行数=1,000, 累计行数=40,000, 数值列=9, 类别列=4]2025-08-04 17:00:19,463 - large_scale_feature_engineer - INFO - 处理进度: 已完成40批次, 累计40,000行\n\n统计拟合: 40批次 [00:00, 144.94批次/s, 批次=41, 当前行数=1,000, 累计行数=41,000, 数值列=9, 类别列=4]\n统计拟合: 41批次 [00:00, 144.94批次/s, 批次=42, 当前行数=1,000, 累计行数=42,000, 数值列=9, 类别列=4]\n统计拟合: 42批次 [00:00, 144.94批次/s, 批次=43, 当前行数=1,000, 累计行数=43,000, 数值列=9, 类别列=4]\n统计拟合: 43批次 [00:00, 144.94批次/s, 批次=44, 当前行数=1,000, 累计行数=44,000, 数值列=9, 类别列=4]\n统计拟合: 44批次 [00:00, 144.94批次/s, 批次=45, 当前行数=1,000, 累计行数=45,000, 数值列=9, 类别列=4]\n统计拟合: 45批次 [00:00, 144.94批次/s, 批次=46, 当前行数=1,000, 累计行数=46,000, 数值列=9, 类别列=4]\n统计拟合: 46批次 [00:00, 144.94批次/s, 批次=47, 当前行数=1,000, 累计行数=47,000, 数值列=9, 类别列=4]\n统计拟合: 47批次 [00:00, 150.24批次/s, 批次=47, 当前行数=1,000, 累计行数=47,000, 数值列=9, 类别列=4]\n统计拟合: 47批次 [00:00, 150.24批次/s, 批次=48, 当前行数=1,000, 累计行数=48,000, 数值列=9, 类别列=4]\n统计拟合: 48批次 [00:00, 150.24批次/s, 批次=49, 当前行数=1,000, 累计行数=49,000, 数值列=9, 类别列=4]\n统计拟合: 49批次 [00:00, 150.24批次/s, 批次=50, 当前行数=1,000, 累计行数=50,000, 数值列=9, 类别列=4]2025-08-04 17:00:19,530 - large_scale_feature_engineer - INFO - 处理进度: 已完成50批次, 累计50,000行\n\n统计拟合: 50批次 [00:00, 150.24批次/s, 批次=51, 当前行数=1,000, 累计行数=51,000, 数值列=9, 类别列=4]\n统计拟合: 51批次 [00:00, 150.24批次/s, 批次=52, 当前行数=1,000, 累计行数=52,000, 数值列=9, 类别列=4]\n统计拟合: 52批次 [00:00, 150.24批次/s, 批次=53, 当前行数=1,000, 累计行数=53,000, 数值列=9, 类别列=4]\n统计拟合: 53批次 [00:00, 150.24批次/s, 批次=54, 当前行数=1,000, 累计行数=54,000, 数值列=9, 类别列=4]\n统计拟合: 54批次 [00:00, 150.24批次/s, 批次=55, 当前行数=1,000, 累计行数=55,000, 数值列=9, 类别列=4]\n统计拟合: 55批次 [00:00, 150.24批次/s, 批次=56, 当前行数=1,000, 累计行数=56,000, 数值列=9, 类别列=4]\n统计拟合: 56批次 [00:00, 150.24批次/s, 批次=57, 当前行数=1,000, 累计行数=57,000, 数值列=9, 类别列=4]\n统计拟合: 57批次 [00:00, 150.24批次/s, 批次=58, 当前行数=1,000, 累计行数=58,000, 数值列=9, 类别列=4]\n统计拟合: 58批次 [00:00, 150.24批次/s, 批次=59, 当前行数=1,000, 累计行数=59,000, 数值列=9, 类别列=4]\n统计拟合: 59批次 [00:00, 150.24批次/s, 批次=60, 当前行数=1,000, 累计行数=60,000, 数值列=9, 类别列=4]2025-08-04 17:00:19,609 - large_scale_feature_engineer - INFO - 处理进度: 已完成60批次, 累计60,000行\n\n统计拟合: 60批次 [00:00, 150.24批次/s, 批次=61, 当前行数=354, 累计行数=60,354, 数值列=9, 类别列=4]  \n统计拟合: 61批次 [00:00, 143.18批次/s, 批次=61, 当前行数=354, 累计行数=60,354, 数值列=9, 类别列=4]\n2025-08-04 17:00:19,619 - large_scale_feature_engineer - INFO - 统计量拟合完成: 总批次=61, 总行数=60,354\n2025-08-04 17:00:19,619 - large_scale_feature_engineer - INFO - 统计摘要生成完成\n2025-08-04 17:00:19,619 - large_scale_feature_engineer - INFO - 数值列 final_eff_year: 均值=871.62, 标准差=1001.11\n2025-08-04 17:00:19,619 - large_scale_feature_engineer - INFO - 数值列 final_eff_mon: 均值=6.30, 标准差=3.36\n2025-08-04 17:00:19,619 - large_scale_feature_engineer - INFO - 数值列 final_eff_day: 均值=11.96, 标准差=10.30\n2025-08-04 17:00:19,620 - large_scale_feature_engineer - INFO - 数值列 final_exp_year: 均值=879.27, 标准差=1009.92\n2025-08-04 17:00:19,620 - large_scale_feature_engineer - INFO - 数值列 final_exp_mon: 均值=6.80, 标准差=3.67\n2025-08-04 17:00:19,620 - large_scale_feature_engineer - INFO - 数值列 final_exp_day: 均值=25.96, 标准差=9.34\n2025-08-04 17:00:19,620 - large_scale_feature_engineer - INFO - 数值列 charge_day_count: 均值=9.80, 标准差=14.42\n2025-08-04 17:00:19,620 - large_scale_feature_engineer - INFO - 数值列 month_day_count: 均值=31.00, 标准差=0.00\n2025-08-04 17:00:19,620 - large_scale_feature_engineer - INFO - 数值列 should_fee: 均值=763.05, 标准差=5153.87\n2025-08-04 17:00:19,620 - large_scale_feature_engineer - INFO - 类别列 cal_type: 3 个唯一值\n2025-08-04 17:00:19,621 - large_scale_feature_engineer - INFO - 类别列 unit_type: 2 个唯一值\n2025-08-04 17:00:19,621 - large_scale_feature_engineer - INFO - 类别列 rate_unit: 4 个唯一值\n2025-08-04 17:00:19,621 - large_scale_feature_engineer - INFO - 类别列 busi_flag: 2 个唯一值\n2025-08-04 17:00:19,621 - large_scale_feature_engineer - INFO - 保存预处理器到: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n2025-08-04 17:00:19,622 - large_scale_feature_engineer - INFO - 预处理器保存成功: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n", "returncode": 0}, "data_splitting": {"success": true, "duration": 1.611937, "stdout": "🔀 开始数据拆分\n  输入文件: /app/outputs/temp/run_20250804_170018/processed_data_20250804_170018.csv\n  输出目录: /app/outputs/temp/run_20250804_170018\n  测试集比例: 0.2\n  目标列: amount\n\n读取数据...\n  数据形状: (60354, 26)\n  特征数: 25\n  样本数: 60354\n\n拆分数据...\n  训练集: 48,283 样本\n  测试集: 12,071 样本\n\n保存拆分后的数据...\n  训练集文件: /app/outputs/temp/run_20250804_170018/train_data_20250804_170020.csv\n  测试集文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n\n验证保存的文件:\n  训练集: (48283, 26)\n  测试集: (12071, 26)\n  总样本数: 60354 (原始: 60354)\n\n数据拆分统计:\n  训练集目标值范围: 0.00 - 729672.00\n  测试集目标值范围: 0.00 - 416666.00\n  训练集目标值均值: 223.73\n  测试集目标值均值: 224.93\n\n✅ 数据拆分完成\n\n🎯 数据拆分成功完成\n训练集: /app/outputs/temp/run_20250804_170018/train_data_20250804_170020.csv\n测试集: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n", "stderr": "2025-08-04 17:00:20,794 - src.config.production_config_manager - INFO - 加载配置文件: /app/config/production_config.json\n2025-08-04 17:00:20,795 - src.config.production_config_manager - INFO - 配置加载成功\n", "returncode": 0}, "model_training": {"success": true, "model_type": "hierarchical", "algorithm": "LightGBM", "samples": 48283, "features": 19, "r2": 0.11041416764487644, "mae": 71.82717454938813, "zero_accuracy": 0.9675248016900359, "training_time": 4.75504469871521}, "model_evaluation": {"success": true, "duration": 0.678442, "stdout": "加载模型和特征工程器...\n  分层模型加载成功: HierarchicalBillingModel\n预处理器已加载: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n  特征工程器加载成功\n开始大规模模型评估\n  测试文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  评估第 1 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=495.93, RMSE=13187.91, R²=0.0031\n    累计样本: 1,000\n\n  评估第 2 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=79.41, RMSE=557.06, R²=0.8219\n    累计样本: 2,000\n\n  评估第 3 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=66.95, RMSE=407.33, R²=0.8568\n    累计样本: 3,000\n\n  评估第 4 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=85.35, RMSE=623.77, R²=0.7142\n    累计样本: 4,000\n\n  评估第 5 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=68.67, RMSE=448.71, R²=0.8903\n    累计样本: 5,000\n\n  评估第 6 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=70.21, RMSE=521.57, R²=0.7770\n    累计样本: 6,000\n\n  评估第 7 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=58.30, RMSE=464.88, R²=0.8621\n    累计样本: 7,000\n\n  评估第 8 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=75.60, RMSE=529.91, R²=0.9164\n    累计样本: 8,000\n\n  评估第 9 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=43.88, RMSE=295.69, R²=0.9350\n    累计样本: 9,000\n\n  评估第 10 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=77.44, RMSE=565.95, R²=0.6242\n    累计样本: 10,000\n\n  评估第 11 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=70.99, RMSE=428.36, R²=0.8938\n    累计样本: 11,000\n\n  评估第 12 批数据...\n    评估完成: 1,000 样本\n    批次指标: MAE=49.30, RMSE=324.09, R²=0.9183\n    累计样本: 12,000\n\n  评估第 13 批数据...\n    评估完成: 71 样本\n    批次指标: MAE=33.94, RMSE=162.39, R²=0.9927\n    累计样本: 12,071\n\n分批评估完成！\n  - 总批次: 13\n  - 总样本: 12,071\n  - 总耗时: 0.12秒\n  - 评估速度: 97434 样本/秒\n\n计算整体评估指标...\n使用分层模型评估器...\n\n================================================================================\n分层模型评估报告\n================================================================================\n\n🎯 分层模型综合评估:\n  - 样本数量: 12,071\n  - 整体 R²: 0.0827\n  - 整体 MAE: 103.09元\n  - 整体 RMSE: 3823.36元\n  - 性能等级: C\n\n🔍 零值分类性能:\n  - 分类准确率: 0.9622\n  - 分类 F1 分数: 0.9798\n\n📈 非零值回归性能:\n  - 回归 R²: 0.0505\n  - 回归 MAE: 1133.68元\n\n💼 业务指标:\n  - ±50元准确率: 93.0%\n\n📊 详细业务准确率:\n  - ±1元内: 91.8%\n  - ±5元内: 91.9%\n  - ±10元内: 92.1%\n  - ±20元内: 92.3%\n  - ±50元内: 93.0%\n  - ±100元内: 93.6%\n\n评估报告已保存: /app/outputs/reports/hierarchical_evaluation_report_20250804_170018.json\n\n大规模模型评估完成！\n", "stderr": "2025-08-04 17:00:27,581 - src.config.production_config_manager - INFO - 加载配置文件: /app/config/production_config.json\n2025-08-04 17:00:27,582 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-04 17:00:27,586 - hierarchical_billing_model - INFO - 分层模型初始化完成，使用算法: LightGBM\n2025-08-04 17:00:27,587 - hierarchical_billing_model - INFO - 分层模型已从 /app/outputs/models/hierarchical_model_20250804_170027.pkl 加载\n2025-08-04 17:00:27,587 - large_scale_feature_engineer - INFO - 预处理器已加载: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n2025-08-04 17:00:27,587 - large_scale_model_evaluator - INFO - 开始大规模模型评估: 测试文件=/app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv, 目标列=amount\n2025-08-04 17:00:27,598 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,607 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,617 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,626 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,637 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,645 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,654 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,665 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,674 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,684 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,694 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,703 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,712 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:27,716 - hierarchical_model_evaluator - INFO - 分层模型评估器初始化完成\n2025-08-04 17:00:27,716 - hierarchical_model_evaluator - INFO - 开始综合评估分层模型性能...\n2025-08-04 17:00:27,723 - hierarchical_model_evaluator - INFO - 零值分类评估: 准确率=0.9622, F1=0.9798\n2025-08-04 17:00:27,725 - hierarchical_model_evaluator - INFO - 非零值回归评估: R²=0.0505, MAE=1133.68, MAPE=100.1%\n2025-08-04 17:00:27,725 - hierarchical_model_evaluator - INFO - 整体性能评估: R²=0.0827, MAE=103.09\n2025-08-04 17:00:27,726 - hierarchical_model_evaluator - INFO - 业务指标评估: ±50元准确率=93.0%\n2025-08-04 17:00:27,726 - hierarchical_model_evaluator - INFO - 综合评估完成\n", "returncode": 0}, "prediction": {"success": true, "duration": 0.714835, "stdout": "加载模型和特征工程器...\n  分层模型加载成功: HierarchicalBillingModel\n预处理器已加载: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n  特征工程器加载成功\n🔮 开始大规模数据预测\n  输入文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n  输出文件: /app/outputs/data/hierarchical_predictions_20250804_170018.csv\n============================================================\n开始分批读取数据文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n保存最终预测结果...\n  最终结果已保存: /app/outputs/data/hierarchical_predictions_20250804_170018.csv\n\n预测完成！\n  总预测数: 12,071 条\n  总耗时: 0.19秒\n  预测速度: 64363 条/秒\n  结果文件: /app/outputs/data/hierarchical_predictions_20250804_170018.csv\n\n🎉 分层模型预测完成！\n总样本数: 12,071\n零值预测: 11,436 (94.7%)\n非零值预测: 635\n非零值统计: 均值=3583.77元, 中位数=1810.57元\n处理时间: 0.21秒\n处理速度: 57,769条/秒\n\n大规模预测完成！\n", "stderr": "2025-08-04 17:00:28,238 - hierarchical_prediction - INFO - ============================================================\n2025-08-04 17:00:28,238 - hierarchical_prediction - INFO - 开始分层模型预测\n2025-08-04 17:00:28,239 - hierarchical_prediction - INFO - ============================================================\n2025-08-04 17:00:28,239 - src.config.production_config_manager - INFO - 加载配置文件: /app/config/production_config.json\n2025-08-04 17:00:28,240 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-04 17:00:28,240 - large_scale_prediction - INFO - 开始加载模型和特征工程器: 模型=/app/outputs/models/hierarchical_model_20250804_170027.pkl, 特征工程器=/app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n2025-08-04 17:00:28,244 - hierarchical_billing_model - INFO - 分层模型初始化完成，使用算法: LightGBM\n2025-08-04 17:00:28,244 - hierarchical_billing_model - INFO - 分层模型已从 /app/outputs/models/hierarchical_model_20250804_170027.pkl 加载\n2025-08-04 17:00:28,244 - large_scale_prediction - INFO - 分层模型加载成功: HierarchicalBillingModel\n2025-08-04 17:00:28,245 - large_scale_feature_engineer - INFO - 预处理器已加载: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n2025-08-04 17:00:28,245 - hierarchical_prediction - INFO - 输入文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n2025-08-04 17:00:28,245 - hierarchical_prediction - INFO - 分层模型: /app/outputs/models/hierarchical_model_20250804_170027.pkl\n2025-08-04 17:00:28,245 - hierarchical_prediction - INFO - 输出文件: /app/outputs/data/hierarchical_predictions_20250804_170018.csv\n2025-08-04 17:00:28,246 - large_scale_prediction - INFO - 开始大规模数据预测: 输入=/app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv, 输出=/app/outputs/data/hierarchical_predictions_20250804_170018.csv\n\n🔮 批量预测: 0批次 [00:00, ?批次/s]2025-08-04 17:00:28,250 - large_scale_prediction - INFO - 开始处理第 1 批数据，样本数: 1000\n2025-08-04 17:00:28,256 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,258 - large_scale_prediction - INFO - 第 1 批预测完成: 1,000 条，累计: 1,000 条\n\n🔮 批量预测: 0批次 [00:00, ?批次/s, 批次=1, 当前=1,000, 累计=1,000, 预测范围=-930.3-12201.7]2025-08-04 17:00:28,260 - large_scale_prediction - INFO - 开始处理第 2 批数据，样本数: 1000\n2025-08-04 17:00:28,266 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,268 - large_scale_prediction - INFO - 第 2 批预测完成: 1,000 条，累计: 2,000 条\n\n🔮 批量预测: 1批次 [00:00, 50.31批次/s, 批次=2, 当前=1,000, 累计=2,000, 预测范围=0.0-21339.3]2025-08-04 17:00:28,270 - large_scale_prediction - INFO - 开始处理第 3 批数据，样本数: 1000\n2025-08-04 17:00:28,275 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,278 - large_scale_prediction - INFO - 第 3 批预测完成: 1,000 条，累计: 3,000 条\n\n🔮 批量预测: 2批次 [00:00, 68.07批次/s, 批次=3, 当前=1,000, 累计=3,000, 预测范围=0.0-14256.8]2025-08-04 17:00:28,280 - large_scale_prediction - INFO - 开始处理第 4 批数据，样本数: 1000\n2025-08-04 17:00:28,285 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,287 - large_scale_prediction - INFO - 第 4 批预测完成: 1,000 条，累计: 4,000 条\n\n🔮 批量预测: 3批次 [00:00, 76.77批次/s, 批次=4, 当前=1,000, 累计=4,000, 预测范围=0.0-20520.5]2025-08-04 17:00:28,290 - large_scale_prediction - INFO - 开始处理第 5 批数据，样本数: 1000\n2025-08-04 17:00:28,295 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,297 - large_scale_prediction - INFO - 第 5 批预测完成: 1,000 条，累计: 5,000 条\n\n🔮 批量预测: 4批次 [00:00, 82.34批次/s, 批次=5, 当前=1,000, 累计=5,000, 预测范围=-122.0-12890.2]2025-08-04 17:00:28,299 - large_scale_prediction - INFO - 开始处理第 6 批数据，样本数: 1000\n2025-08-04 17:00:28,305 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,308 - large_scale_prediction - INFO - 第 6 批预测完成: 1,000 条，累计: 6,000 条\n\n🔮 批量预测: 5批次 [00:00, 84.43批次/s, 批次=6, 当前=1,000, 累计=6,000, 预测范围=0.0-21387.7]   2025-08-04 17:00:28,309 - large_scale_prediction - INFO - 开始处理第 7 批数据，样本数: 1000\n2025-08-04 17:00:28,315 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,317 - large_scale_prediction - INFO - 第 7 批预测完成: 1,000 条，累计: 7,000 条\n\n🔮 批量预测: 6批次 [00:00, 87.00批次/s, 批次=7, 当前=1,000, 累计=7,000, 预测范围=0.0-16704.9]2025-08-04 17:00:28,319 - large_scale_prediction - INFO - 开始处理第 8 批数据，样本数: 1000\n2025-08-04 17:00:28,325 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,327 - large_scale_prediction - INFO - 第 8 批预测完成: 1,000 条，累计: 8,000 条\n\n🔮 批量预测: 7批次 [00:00, 88.55批次/s, 批次=8, 当前=1,000, 累计=8,000, 预测范围=-469.7-24743.1]2025-08-04 17:00:28,329 - large_scale_prediction - INFO - 开始处理第 9 批数据，样本数: 1000\n2025-08-04 17:00:28,335 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,338 - large_scale_prediction - INFO - 第 9 批预测完成: 1,000 条，累计: 9,000 条\n\n🔮 批量预测: 8批次 [00:00, 89.51批次/s, 批次=9, 当前=1,000, 累计=9,000, 预测范围=0.0-13495.3]   2025-08-04 17:00:28,340 - large_scale_prediction - INFO - 开始处理第 10 批数据，样本数: 1000\n2025-08-04 17:00:28,345 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,347 - large_scale_prediction - INFO - 第 10 批预测完成: 1,000 条，累计: 10,000 条\n\n🔮 批量预测: 9批次 [00:00, 91.03批次/s, 批次=10, 当前=1,000, 累计=10,000, 预测范围=0.0-12496.7]2025-08-04 17:00:28,349 - large_scale_prediction - INFO - 开始处理第 11 批数据，样本数: 1000\n2025-08-04 17:00:28,354 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,357 - large_scale_prediction - INFO - 第 11 批预测完成: 1,000 条，累计: 11,000 条\n\n🔮 批量预测: 10批次 [00:00, 92.31批次/s, 批次=11, 当前=1,000, 累计=11,000, 预测范围=0.0-24743.1]\n🔮 批量预测: 11批次 [00:00, 101.50批次/s, 批次=11, 当前=1,000, 累计=11,000, 预测范围=0.0-24743.1]2025-08-04 17:00:28,358 - large_scale_prediction - INFO - 开始处理第 12 批数据，样本数: 1000\n2025-08-04 17:00:28,363 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,366 - large_scale_prediction - INFO - 第 12 批预测完成: 1,000 条，累计: 12,000 条\n\n🔮 批量预测: 11批次 [00:00, 101.50批次/s, 批次=12, 当前=1,000, 累计=12,000, 预测范围=0.0-19913.1]2025-08-04 17:00:28,367 - large_scale_prediction - INFO - 开始处理第 13 批数据，样本数: 71\n2025-08-04 17:00:28,371 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,373 - large_scale_prediction - INFO - 第 13 批预测完成: 71 条，累计: 12,071 条\n\n🔮 批量预测: 12批次 [00:00, 101.50批次/s, 批次=13, 当前=71, 累计=12,071, 预测范围=0.0-13082.1]   \n🔮 批量预测: 13批次 [00:00, 104.26批次/s, 批次=13, 当前=71, 累计=12,071, 预测范围=0.0-13082.1]\n2025-08-04 17:00:28,435 - large_scale_prediction - INFO - 最终结果已保存(包含特征): /app/outputs/data/hierarchical_predictions_20250804_170018.csv, 记录数: 12071\n2025-08-04 17:00:28,435 - large_scale_prediction - INFO - 列顺序: 训练特征(14) + 透传字段(11) + 目标字段(1) + 预测字段(1)\n2025-08-04 17:00:28,435 - large_scale_prediction - INFO - 预测完成: 总数=12,071, 耗时=0.19秒, 速度=64363条/秒\n2025-08-04 17:00:28,448 - hierarchical_prediction - INFO - 分层预测完成:\n2025-08-04 17:00:28,448 - hierarchical_prediction - INFO -   总样本数: 12,071\n2025-08-04 17:00:28,448 - hierarchical_prediction - INFO -   零值预测: 11,436 (94.7%)\n2025-08-04 17:00:28,448 - hierarchical_prediction - INFO -   非零值预测: 635\n2025-08-04 17:00:28,448 - hierarchical_prediction - INFO -   非零值均值: 3583.77元\n2025-08-04 17:00:28,449 - hierarchical_prediction - INFO -   处理时间: 0.21秒\n2025-08-04 17:00:28,449 - hierarchical_prediction - INFO -   处理速度: 57,769条/秒\n", "returncode": 0}, "billing_judgment": {"success": true, "duration": 0.749026, "stdout": "加载模型和特征工程器...\n  分层模型加载成功: HierarchicalBillingModel\n预处理器已加载: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n  特征工程器加载成功\n🏛️ 大规模收费合理性判定器初始化完成\n  - 批次大小: 1,000\n  - 绝对阈值: ±10.0元\n  - 相对阈值: ±10.0%\n⚖️  开始大规模收费合理性判定\n  输入文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n  输出文件: /app/outputs/data/billing_judgments_20250804_170018.csv\n  目标列: amount\n============================================================\n开始分批读取数据文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv\n  - 批次大小: 1,000 行\n  - 检测到分隔符: ','\n\n  判定第 1 批数据...\n    判定完成: 1,000 条\n    累计判定: 1,000 条\n    批次统计: 合理94.2% | 不合理5.2% | 不确定0.6%\n\n  判定第 2 批数据...\n    判定完成: 1,000 条\n    累计判定: 2,000 条\n    批次统计: 合理93.9% | 不合理5.6% | 不确定0.5%\n\n  判定第 3 批数据...\n    判定完成: 1,000 条\n    累计判定: 3,000 条\n    批次统计: 合理93.8% | 不合理5.5% | 不确定0.7%\n\n  判定第 4 批数据...\n    判定完成: 1,000 条\n    累计判定: 4,000 条\n    批次统计: 合理94.4% | 不合理5.1% | 不确定0.5%\n\n  判定第 5 批数据...\n    判定完成: 1,000 条\n    累计判定: 5,000 条\n    批次统计: 合理94.3% | 不合理4.8% | 不确定0.9%\n\n  判定第 6 批数据...\n    判定完成: 1,000 条\n    累计判定: 6,000 条\n    批次统计: 合理93.4% | 不合理6.0% | 不确定0.6%\n\n  判定第 7 批数据...\n    判定完成: 1,000 条\n    累计判定: 7,000 条\n    批次统计: 合理95.0% | 不合理4.1% | 不确定0.9%\n\n  判定第 8 批数据...\n    判定完成: 1,000 条\n    累计判定: 8,000 条\n    批次统计: 合理94.8% | 不合理4.3% | 不确定0.9%\n\n  判定第 9 批数据...\n    判定完成: 1,000 条\n    累计判定: 9,000 条\n    批次统计: 合理95.3% | 不合理4.0% | 不确定0.7%\n\n  判定第 10 批数据...\n    判定完成: 1,000 条\n    累计判定: 10,000 条\n    批次统计: 合理94.6% | 不合理4.4% | 不确定1.0%\n\n  判定第 11 批数据...\n    判定完成: 1,000 条\n    累计判定: 11,000 条\n    批次统计: 合理93.7% | 不合理5.3% | 不确定1.0%\n\n  判定第 12 批数据...\n    判定完成: 1,000 条\n    累计判定: 12,000 条\n    批次统计: 合理94.8% | 不合理4.6% | 不确定0.6%\n\n  判定第 13 批数据...\n    判定完成: 71 条\n    累计判定: 12,071 条\n    批次统计: 合理95.8% | 不合理2.8% | 不确定1.4%\n\n保存最终判定结果...\n  最终结果已保存: /app/outputs/data/billing_judgments_20250804_170018.csv\n\n⚖️  大规模收费合理性判定完成！\n  总判定数: 12,071 条\n  总耗时: 0.24秒\n  判定速度: 49897 条/秒\n  结果文件: /app/outputs/data/billing_judgments_20250804_170018.csv\n\n判定统计:\n  - 合理收费: 11,390 条 (94.4%)\n  - 不合理收费: 591 条 (4.9%)\n  - 不确定收费: 90 条 (0.7%)\n\n大规模收费合理性判定完成！\n", "stderr": "2025-08-04 17:00:28,949 - src.config.production_config_manager - INFO - 加载配置文件: /app/config/production_config.json\n2025-08-04 17:00:28,950 - src.config.production_config_manager - INFO - 配置加载成功\n2025-08-04 17:00:28,950 - large_scale_billing_judge - INFO - 开始加载模型和特征工程器: 模型=/app/outputs/models/hierarchical_model_20250804_170027.pkl, 特征工程器=/app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n2025-08-04 17:00:28,954 - hierarchical_billing_model - INFO - 分层模型初始化完成，使用算法: LightGBM\n2025-08-04 17:00:28,954 - hierarchical_billing_model - INFO - 分层模型已从 /app/outputs/models/hierarchical_model_20250804_170027.pkl 加载\n2025-08-04 17:00:28,954 - large_scale_billing_judge - INFO - 分层模型加载成功: HierarchicalBillingModel\n2025-08-04 17:00:28,955 - large_scale_feature_engineer - INFO - 预处理器已加载: /app/outputs/models/large_scale_feature_engineer_20250804_170018.pkl\n2025-08-04 17:00:28,955 - large_scale_billing_judge - INFO - 大规模收费合理性判定器初始化完成\n2025-08-04 17:00:28,955 - large_scale_billing_judge - INFO - 批次大小: 1,000\n2025-08-04 17:00:28,955 - large_scale_billing_judge - INFO - 绝对阈值: ±10.0元\n2025-08-04 17:00:28,955 - large_scale_billing_judge - INFO - 相对阈值: ±10.0%\n2025-08-04 17:00:28,956 - large_scale_billing_judge - INFO - 开始大规模收费合理性判定: 输入=/app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv, 输出=/app/outputs/data/billing_judgments_20250804_170018.csv, 目标列=amount\n2025-08-04 17:00:28,956 - large_scale_billing_judge - INFO - 开始分批读取数据文件: /app/outputs/temp/run_20250804_170018/test_data_20250804_170020.csv, 批次大小: 1,000\n2025-08-04 17:00:28,956 - large_scale_billing_judge - INFO - 检测到分隔符: ','\n2025-08-04 17:00:28,959 - large_scale_billing_judge - INFO - 开始判定第 1 批数据，样本数: 1000\n2025-08-04 17:00:28,965 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,969 - large_scale_billing_judge - INFO - 第 1 批判定完成: 1,000 条，累计: 1,000 条\n2025-08-04 17:00:28,971 - large_scale_billing_judge - INFO - 开始判定第 2 批数据，样本数: 1000\n2025-08-04 17:00:28,976 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,980 - large_scale_billing_judge - INFO - 第 2 批判定完成: 1,000 条，累计: 2,000 条\n2025-08-04 17:00:28,982 - large_scale_billing_judge - INFO - 开始判定第 3 批数据，样本数: 1000\n2025-08-04 17:00:28,988 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:28,992 - large_scale_billing_judge - INFO - 第 3 批判定完成: 1,000 条，累计: 3,000 条\n2025-08-04 17:00:28,994 - large_scale_billing_judge - INFO - 开始判定第 4 批数据，样本数: 1000\n2025-08-04 17:00:29,003 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,010 - large_scale_billing_judge - INFO - 第 4 批判定完成: 1,000 条，累计: 4,000 条\n2025-08-04 17:00:29,012 - large_scale_billing_judge - INFO - 开始判定第 5 批数据，样本数: 1000\n2025-08-04 17:00:29,018 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,022 - large_scale_billing_judge - INFO - 第 5 批判定完成: 1,000 条，累计: 5,000 条\n2025-08-04 17:00:29,024 - large_scale_billing_judge - INFO - 开始判定第 6 批数据，样本数: 1000\n2025-08-04 17:00:29,029 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,033 - large_scale_billing_judge - INFO - 第 6 批判定完成: 1,000 条，累计: 6,000 条\n2025-08-04 17:00:29,034 - large_scale_billing_judge - INFO - 开始判定第 7 批数据，样本数: 1000\n2025-08-04 17:00:29,040 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,044 - large_scale_billing_judge - INFO - 第 7 批判定完成: 1,000 条，累计: 7,000 条\n2025-08-04 17:00:29,046 - large_scale_billing_judge - INFO - 开始判定第 8 批数据，样本数: 1000\n2025-08-04 17:00:29,051 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,055 - large_scale_billing_judge - INFO - 第 8 批判定完成: 1,000 条，累计: 8,000 条\n2025-08-04 17:00:29,056 - large_scale_billing_judge - INFO - 开始判定第 9 批数据，样本数: 1000\n2025-08-04 17:00:29,061 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,065 - large_scale_billing_judge - INFO - 第 9 批判定完成: 1,000 条，累计: 9,000 条\n2025-08-04 17:00:29,067 - large_scale_billing_judge - INFO - 开始判定第 10 批数据，样本数: 1000\n2025-08-04 17:00:29,072 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,076 - large_scale_billing_judge - INFO - 第 10 批判定完成: 1,000 条，累计: 10,000 条\n2025-08-04 17:00:29,077 - large_scale_billing_judge - INFO - 开始判定第 11 批数据，样本数: 1000\n2025-08-04 17:00:29,083 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,088 - large_scale_billing_judge - INFO - 第 11 批判定完成: 1,000 条，累计: 11,000 条\n2025-08-04 17:00:29,090 - large_scale_billing_judge - INFO - 开始判定第 12 批数据，样本数: 1000\n2025-08-04 17:00:29,095 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,099 - large_scale_billing_judge - INFO - 第 12 批判定完成: 1,000 条，累计: 12,000 条\n2025-08-04 17:00:29,100 - large_scale_billing_judge - INFO - 开始判定第 13 批数据，样本数: 71\n2025-08-04 17:00:29,105 - large_scale_feature_engineer - INFO - 选择的特征: ['final_eff_year', 'final_eff_mon', 'final_eff_day', 'final_exp_year', 'final_exp_mon', 'final_exp_day', 'charge_day_count', 'month_day_count', 'should_fee', 'cal_type']...\n2025-08-04 17:00:29,108 - large_scale_billing_judge - INFO - 第 13 批判定完成: 71 条，累计: 12,071 条\n2025-08-04 17:00:29,108 - large_scale_billing_judge - INFO - 开始保存最终判定结果到: /app/outputs/data/billing_judgments_20250804_170018.csv\n2025-08-04 17:00:29,199 - large_scale_billing_judge - INFO - 最终结果已保存: /app/outputs/data/billing_judgments_20250804_170018.csv, 总记录数: 12071\n2025-08-04 17:00:29,199 - large_scale_billing_judge - INFO - 大规模收费合理性判定完成: 总数=12,071, 耗时=0.24秒, 速度=49897条/秒\n2025-08-04 17:00:29,200 - large_scale_billing_judge - INFO - 判定统计: 合理=11,390(94.4%), 不合理=591(4.9%), 不确定=90(0.7%)\n", "returncode": 0}}, "summary": {"total_steps": 7, "successful_steps": 7, "failed_steps": 0, "success_rate": 100.0}}