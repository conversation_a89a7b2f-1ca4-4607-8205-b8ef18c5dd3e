#!/bin/bash

# 山西电信出账稽核AI系统 - 容器挂载运行脚本
# 版本: v2.1.0-container-fix
# 用途: root用户运行容器 + 容器内权限修复，适合生产环境
# 特性: 容器内自动修复权限、无sudo依赖、完全自动化权限处理

set -euo pipefail

# ============================================================================
# 配置变量
# ============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
IMAGE_NAME="billing-audit-ai:v2.1.0-slim-fixed-v2"
CONTAINER_NAME="billing-audit-ai-runner-$(date +%s)"

# 权限配置
USE_ROOT_USER="true"  # 使用root用户运行容器，避免权限问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ============================================================================
# 日志函数
# ============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# ============================================================================
# 环境检查
# ============================================================================

check_environment() {
    log_info "检查运行环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    # 检查镜像
    if ! docker image inspect "$IMAGE_NAME" >/dev/null 2>&1; then
        log_warn "镜像 $IMAGE_NAME 不存在，尝试加载..."
        if [ -f "images/billing-audit-ai-v2.1.0-slim-fixed-v2.tar.gz" ]; then
            docker load < images/billing-audit-ai-v2.1.0-slim-fixed-v2.tar.gz
            log_success "镜像加载完成"
        elif [ -f "images/billing-audit-ai-v2.1.0-slim-fixed.tar.gz" ]; then
            docker load < images/billing-audit-ai-v2.1.0-slim-fixed.tar.gz
            log_success "镜像加载完成"
        else
            log_error "镜像文件不存在: images/billing-audit-ai-v2.1.0-slim-fixed-v2.tar.gz"
            exit 1
        fi
    fi
    
    # 检查目录
    mkdir -p data/input data/output outputs logs
    
    # 处理权限问题
    handle_permissions
    
    log_success "环境检查完成"
}

# ============================================================================
# 权限处理
# ============================================================================

handle_permissions() {
    log_info "智能权限处理..."

    # 确保目录存在
    mkdir -p "$SCRIPT_DIR/data/input" "$SCRIPT_DIR/data/output" "$SCRIPT_DIR/outputs" "$SCRIPT_DIR/logs" "$SCRIPT_DIR/tmp/billing_audit"

    # 获取当前用户信息
    local current_user=$(whoami)
    local current_uid=$(id -u)
    local current_gid=$(id -g)

    log_info "当前用户: $current_user ($current_uid:$current_gid)"

    # 设置权限 - 使用755权限，避免过度开放
    if chmod -R 755 "$SCRIPT_DIR/data" "$SCRIPT_DIR/outputs" "$SCRIPT_DIR/logs" "$SCRIPT_DIR/tmp" 2>/dev/null; then
        log_info "✅ 权限设置成功 (755)"
    else
        log_warn "⚠️  权限设置失败，但继续执行"
    fi

    # 设置所有者 (仅在当前用户拥有文件时有效)
    if chown -R $(whoami):$(whoami) "$SCRIPT_DIR/data" "$SCRIPT_DIR/outputs" "$SCRIPT_DIR/logs" "$SCRIPT_DIR/tmp" 2>/dev/null; then
        log_info "✅ 所有者设置成功"
    else
        log_warn "⚠️  所有者设置失败，但继续执行"
    fi

    log_success "智能权限处理完成"
}

# ============================================================================
# 数据检查
# ============================================================================

check_data() {
    log_info "检查输入数据..."

    local data_files=($(find "$SCRIPT_DIR/data" -name "*.csv" -o -name "*.txt" 2>/dev/null))

    if [ ${#data_files[@]} -eq 0 ]; then
        log_warn "data目录中未找到数据文件"
        log_info "请确保在运行时指定正确的输入文件路径"
    else
        log_success "找到 ${#data_files[@]} 个数据文件"
        for file in "${data_files[@]}"; do
            log_info "  - $(basename "$file") ($(du -h "$file" | cut -f1))"
        done
    fi
}

# ============================================================================
# 运行函数
# ============================================================================

run_full_process() {
    local input_file="$1"
    local algorithm="${2:-hierarchical}"
    local batch_size="${3:-1000}"

    log_info "开始全流程处理..."
    log_info "输入文件: $input_file"
    log_info "算法: $algorithm"
    log_info "批处理大小: $batch_size"
    
    # 构建Docker运行参数
    local docker_args=(
        "--rm"
        "--name" "$CONTAINER_NAME"
        "--init"
        "-v" "$SCRIPT_DIR/data:/app/data"
        "-v" "$SCRIPT_DIR/outputs:/app/outputs"
        "-v" "$SCRIPT_DIR/logs:/app/logs"
        "-v" "$SCRIPT_DIR/tmp:/tmp"
        "-v" "$SCRIPT_DIR/production_config.json:/app/config/production_config.json:ro"
        "-e" "TMPDIR=/tmp"
        "-e" "TMP=/tmp"
        "-e" "TEMP=/tmp"
        "-w" "/app"
    )
    
    # 根据权限情况决定是否使用root用户
    if [ "$USE_ROOT_USER" = "true" ]; then
        docker_args+=("--user" "root")
        log_info "使用root用户运行容器（避免权限问题）"
        log_info "注意: 输出文件将在运行后修复为用户权限"
    else
        # 使用当前用户的UID:GID
        local user_id=$(id -u)
        local group_id=$(id -g)
        docker_args+=("--user" "${user_id}:${group_id}")
        log_info "使用当前用户运行容器 (${user_id}:${group_id})"
    fi
    
    # 获取当前用户的UID和GID
    local host_uid=$(id -u)
    local host_gid=$(id -g)

    local exit_code=0
    docker run "${docker_args[@]}" \
        "$IMAGE_NAME" \
        /bin/bash -c "
            # 确保tmp目录存在并可写
            mkdir -p /tmp/billing_audit
            chmod 777 /tmp/billing_audit 2>/dev/null || true

            # 运行主程序
            python scripts/production/billing_audit_main.py full \
                --input 'data/input/$(basename "$input_file")' \
                --algorithm '$algorithm' \
                --batch-size '$batch_size'

            # 程序运行完成后，在容器内修复权限
            echo '🔧 在容器内修复输出文件权限...'
            echo '目标用户: $host_uid:$host_gid'

            # 修复outputs目录权限
            if [ -d '/app/outputs' ]; then
                chown -R $host_uid:$host_gid /app/outputs
                chmod -R 755 /app/outputs
                find /app/outputs -type f -exec chmod 644 {} \;
                echo '✅ outputs目录权限修复完成'
            fi

            # 修复logs目录权限
            if [ -d '/app/logs' ]; then
                chown -R $host_uid:$host_gid /app/logs
                chmod -R 755 /app/logs
                find /app/logs -type f -exec chmod 644 {} \;
                echo '✅ logs目录权限修复完成'
            fi

            # 修复data目录权限
            if [ -d '/app/data' ]; then
                chown -R $host_uid:$host_gid /app/data
                chmod -R 755 /app/data
                find /app/data -type f -exec chmod 644 {} \;
                echo '✅ data目录权限修复完成'
            fi

            echo '🎉 容器内权限修复完成！'
        " || exit_code=$?

    # 检查执行结果
    if [ $exit_code -eq 0 ]; then
        log_success "全流程执行成功"
    else
        log_error "全流程执行失败 (退出码: $exit_code)"
        return $exit_code
    fi
}

run_single_process() {
    local process="$1"
    local input_file="$2"
    local algorithm="${3:-hierarchical}"
    local batch_size="${4:-1000}"

    log_info "开始单流程处理: $process"
    log_info "输入文件: $input_file"
    
    local cmd_args=("python" "scripts/production/billing_audit_main.py" "$process" "--input" "data/input/$(basename "$input_file")" "--batch-size" "$batch_size")
    
    # 根据流程类型添加特定参数
    case "$process" in
        "training")
            cmd_args+=("--algorithm" "$algorithm")
            ;;
        "judgment")
            cmd_args+=("--abs-threshold" "50.0" "--rel-threshold" "0.1")
            ;;
    esac
    
    # 构建Docker运行参数
    local docker_args=(
        "--rm"
        "--name" "$CONTAINER_NAME"
        "--init"
        "-v" "$SCRIPT_DIR/data:/app/data"
        "-v" "$SCRIPT_DIR/outputs:/app/outputs"
        "-v" "$SCRIPT_DIR/logs:/app/logs"
        "-v" "$SCRIPT_DIR/tmp:/tmp"
        "-v" "$SCRIPT_DIR/production_config.json:/app/config/production_config.json:ro"
        "-e" "TMPDIR=/tmp"
        "-e" "TMP=/tmp"
        "-e" "TEMP=/tmp"
        "-w" "/app"
    )
    
    # 根据权限情况决定是否使用root用户
    if [ "$USE_ROOT_USER" = "true" ]; then
        docker_args+=("--user" "root")
        log_info "使用root用户运行容器（避免权限问题）"
        log_info "注意: 输出文件将在运行后修复为用户权限"
    else
        # 使用当前用户的UID:GID
        local user_id=$(id -u)
        local group_id=$(id -g)
        docker_args+=("--user" "${user_id}:${group_id}")
        log_info "使用当前用户运行容器 (${user_id}:${group_id})"
    fi
    
    # 构建命令字符串
    local cmd_string=""
    for arg in "${cmd_args[@]}"; do
        cmd_string="$cmd_string '$arg'"
    done

    # 获取当前用户的UID和GID
    local host_uid=$(id -u)
    local host_gid=$(id -g)

    local exit_code=0
    docker run "${docker_args[@]}" \
        "$IMAGE_NAME" \
        /bin/bash -c "
            # 确保tmp目录存在并可写
            mkdir -p /tmp/billing_audit
            chmod 777 /tmp/billing_audit 2>/dev/null || true

            # 运行命令
            $cmd_string

            # 程序运行完成后，在容器内修复权限
            echo '🔧 在容器内修复输出文件权限...'
            echo '目标用户: $host_uid:$host_gid'

            # 修复outputs目录权限
            if [ -d '/app/outputs' ]; then
                chown -R $host_uid:$host_gid /app/outputs
                chmod -R 755 /app/outputs
                find /app/outputs -type f -exec chmod 644 {} \;
                echo '✅ outputs目录权限修复完成'
            fi

            # 修复logs目录权限
            if [ -d '/app/logs' ]; then
                chown -R $host_uid:$host_gid /app/logs
                chmod -R 755 /app/logs
                find /app/logs -type f -exec chmod 644 {} \;
                echo '✅ logs目录权限修复完成'
            fi

            # 修复data目录权限
            if [ -d '/app/data' ]; then
                chown -R $host_uid:$host_gid /app/data
                chmod -R 755 /app/data
                find /app/data -type f -exec chmod 644 {} \;
                echo '✅ data目录权限修复完成'
            fi

            echo '🎉 容器内权限修复完成！'
        " || exit_code=$?

    # 检查执行结果
    if [ $exit_code -eq 0 ]; then
        log_success "单流程 $process 执行成功"
    else
        log_error "单流程 $process 执行失败 (退出码: $exit_code)"
        return $exit_code
    fi
}

# ============================================================================
# 权限修复
# ============================================================================

fix_output_permissions() {
    log_info "验证容器内权限修复结果..."

    # 获取当前用户信息
    local current_user=$(whoami)

    # 验证修复结果
    local remaining_root_files=$(find "$SCRIPT_DIR/outputs" "$SCRIPT_DIR/logs" -user root 2>/dev/null | wc -l)
    if [ "$remaining_root_files" -eq 0 ]; then
        log_success "✅ 容器内权限修复成功，所有文件现在属于用户 $current_user"
    else
        log_warn "⚠️  仍有 $remaining_root_files 个文件为root权限，容器内修复可能失败"

        # 如果容器内修复失败，尝试宿主机修复（不使用sudo）
        log_info "尝试宿主机权限修复（无sudo）..."
        chown -R "$current_user:$current_user" "$SCRIPT_DIR/outputs" "$SCRIPT_DIR/logs" "$SCRIPT_DIR/data" 2>/dev/null || {
            log_warn "宿主机权限修复也失败，建议使用文件复制方案"
        }
    fi

    # 显示最近生成的文件及其权限
    log_info "最近生成的文件权限:"
    find "$SCRIPT_DIR/outputs" "$SCRIPT_DIR/logs" -type f -mmin -10 2>/dev/null | head -5 | while read file; do
        local size=$(du -h "$file" 2>/dev/null | cut -f1)
        local perms=$(ls -la "$file" 2>/dev/null | awk '{print $1, $3, $4}')
        log_info "  - $(basename "$file") ($size) [$perms]"
    done
}

# ============================================================================
# 主函数
# ============================================================================

show_usage() {
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  full <数据文件> [算法] [批大小]     - 运行完整流程"
    echo "  feature-engineering <数据文件>     - 特征工程"
    echo "  training <数据文件> [算法]          - 模型训练"
    echo "  prediction <数据文件>              - 模型预测"
    echo "  evaluation <数据文件>              - 模型评估"
    echo "  judgment <数据文件>                - 收费判定"
    echo ""
    echo "示例:"
    echo "  $0 full data.csv hierarchical 1000"
    echo "  $0 training data.csv hierarchical"
    echo "  $0 prediction data.csv"
    echo ""
    echo "支持的算法: hierarchical, random_forest, xgboost, lightgbm"
}

main() {
    if [ $# -lt 1 ]; then
        show_usage
        exit 1
    fi
    
    local command="$1"
    
    # 环境检查
    check_environment
    check_data
    
    case "$command" in
        "full")
            if [ $# -lt 2 ]; then
                log_error "全流程命令需要指定数据文件"
                show_usage
                exit 1
            fi
            run_full_process "$2" "${3:-hierarchical}" "${4:-1000}"
            # 运行后修复权限
            fix_output_permissions
            ;;
        "feature-engineering"|"training"|"prediction"|"evaluation"|"judgment")
            if [ $# -lt 2 ]; then
                log_error "单流程命令需要指定数据文件"
                show_usage
                exit 1
            fi
            run_single_process "$command" "$2" "${3:-hierarchical}" "${4:-1000}"
            # 运行后修复权限
            fix_output_permissions
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "未知命令: $command"
            show_usage
            exit 1
            ;;
    esac
    
    log_success "处理完成！"
    log_info "输出文件位置:"
    log_info "  - 模型文件: outputs/models/"
    log_info "  - 预测结果: outputs/data/"
    log_info "  - 评估报告: outputs/reports/"
    log_info "  - 日志文件: logs/"
}

# 执行主函数
main "$@"
