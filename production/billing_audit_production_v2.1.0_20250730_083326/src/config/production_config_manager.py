#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境配置管理器
支持环境变量替换、配置验证、动态加载等功能
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging
from dataclasses import dataclass


@dataclass
class ConfigValidationError(Exception):
    """配置验证错误"""
    message: str
    config_path: str = ""
    

class ProductionConfigManager:
    """生产环境配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._get_default_config_path()
        self.config = {}
        self.logger = self._setup_logger()
        
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        # 优先级：环境变量 > 生产配置 > 开发配置
        if os.getenv('BILLING_AUDIT_CONFIG'):
            return os.getenv('BILLING_AUDIT_CONFIG')
        
        project_root = Path(__file__).parent.parent.parent
        
        # 生产环境配置
        prod_config = project_root / "config" / "production_config.json"
        if prod_config.exists():
            return str(prod_config)
            
        # 开发环境配置
        dev_config = project_root / "config" / "billing_audit_config.json"
        if dev_config.exists():
            return str(dev_config)
            
        raise FileNotFoundError("未找到配置文件")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            self.logger.info(f"加载配置文件: {self.config_path}")

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()

            # 先解析JSON获取默认环境变量
            temp_config = json.loads(config_content)

            # 替换环境变量（现在可以访问默认值）
            config_content = self._replace_environment_variables(config_content, temp_config)

            # 重新解析JSON
            self.config = json.loads(config_content)

            # 验证配置
            self._validate_config()

            # 创建必要的目录
            self._create_directories()

            self.logger.info("配置加载成功")
            return self.config

        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            raise ConfigValidationError(f"配置加载失败: {e}", self.config_path)
    
    def _replace_environment_variables(self, content: str, temp_config: Dict[str, Any] = None) -> str:
        """替换配置中的环境变量"""
        # 匹配 ${VAR_NAME} 格式的环境变量
        pattern = r'\$\{([^}]+)\}'

        def replace_var(match):
            var_name = match.group(1)

            # 1. 首先检查系统环境变量（最高优先级）
            env_value = os.getenv(var_name)
            if env_value is not None:
                self.logger.debug(f"使用系统环境变量 {var_name}={env_value}")
                return env_value

            # 2. 然后检查配置文件中的默认环境变量
            if temp_config and 'environment_variables' in temp_config:
                if var_name in temp_config['environment_variables']:
                    default_value = str(temp_config['environment_variables'][var_name])
                    self.logger.debug(f"使用配置默认值 {var_name}={default_value}")
                    return default_value

            # 3. 如果都没有找到，保持原样并发出警告
            self.logger.warning(f"环境变量 {var_name} 未找到，保持原值")
            return match.group(0)

        return re.sub(pattern, replace_var, content)
    
    def _validate_config(self):
        """验证配置文件"""
        required_sections = [
            'project', 'data_paths', 'large_scale_processing', 
            'model_training', 'billing_judgment'
        ]
        
        for section in required_sections:
            if section not in self.config:
                raise ConfigValidationError(f"缺少必需的配置节: {section}")
        
        # 验证数据路径
        self._validate_data_paths()
        
        # 验证模型训练参数
        self._validate_model_training_params()
        
        # 验证判定阈值
        self._validate_judgment_thresholds()
    
    def _validate_data_paths(self):
        """验证数据路径配置"""
        data_paths = self.config.get('data_paths', {})
        required_paths = ['input_data_dir', 'output_data_dir', 'model_dir', 'logs_dir']
        
        for path_key in required_paths:
            if path_key not in data_paths:
                raise ConfigValidationError(f"缺少数据路径配置: {path_key}")
    
    def _validate_model_training_params(self):
        """验证模型训练参数"""
        training_config = self.config.get('model_training', {})
        
        if 'algorithms' not in training_config:
            raise ConfigValidationError("缺少算法配置")
        
        algorithms = training_config['algorithms']
        hyperparams = training_config.get('hyperparameters', {})
        
        for algo in algorithms:
            if algo not in hyperparams:
                self.logger.warning(f"算法 {algo} 缺少超参数配置")
    
    def _validate_judgment_thresholds(self):
        """验证判定阈值配置"""
        judgment_config = self.config.get('billing_judgment', {})
        thresholds = judgment_config.get('thresholds', {})
        
        required_thresholds = ['absolute_threshold', 'relative_threshold']
        for threshold in required_thresholds:
            if threshold not in thresholds:
                raise ConfigValidationError(f"缺少判定阈值配置: {threshold}")
            
            value = thresholds[threshold]
            if not isinstance(value, (int, float)) or value <= 0:
                raise ConfigValidationError(f"判定阈值 {threshold} 必须为正数")
    
    def _create_directories(self):
        """创建必要的目录"""
        data_paths = self.config.get('data_paths', {})
        
        directories_to_create = [
            data_paths.get('output_data_dir'),
            data_paths.get('logs_dir'),
            data_paths.get('temp_dir'),
            data_paths.get('backup_dir')
        ]
        
        for dir_path in directories_to_create:
            if dir_path:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"创建目录: {dir_path}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_data_path(self, path_type: str) -> str:
        """获取数据路径"""
        return self.get(f'data_paths.{path_type}', '')
    
    def get_model_path(self, model_type: str) -> str:
        """获取模型路径"""
        return self.get(f'model_paths.{model_type}', '')
    
    def get_batch_size(self) -> int:
        """获取批处理大小"""
        return self.get('large_scale_processing.batch_size', 50000)
    
    def get_judgment_thresholds(self) -> Dict[str, float]:
        """获取判定阈值"""
        return self.get('billing_judgment.thresholds', {})
    
    def get_model_hyperparameters(self, algorithm: str) -> Dict[str, Any]:
        """获取模型超参数"""
        return self.get(f'model_training.hyperparameters.{algorithm}', {})
    
    def get_feature_columns(self, data_type: str = 'fixed_fee') -> list:
        """获取特征列 (兼容旧接口，返回训练特征)"""
        return self.get_training_features(data_type)

    def get_training_features(self, data_type: str = 'fixed_fee') -> list:
        """获取训练特征列"""
        return self.get(f'data_schema.{data_type}.training_features', [])

    def get_target_column(self, data_type: str = 'fixed_fee') -> str:
        """获取目标列"""
        return self.get(f'data_schema.{data_type}.target_column', 'amount')

    def get_passthrough_columns(self, data_type: str = 'fixed_fee') -> list:
        """获取透传列"""
        return self.get(f'data_schema.{data_type}.passthrough_columns', [])

    def get_all_fields(self, data_type: str = 'fixed_fee') -> list:
        """获取所有字段列表"""
        training_features = self.get_training_features(data_type)
        target_column = self.get_target_column(data_type)
        passthrough_columns = self.get_passthrough_columns(data_type)

        all_fields = training_features + passthrough_columns
        if target_column:
            all_fields.append(target_column)

        return all_fields

    def get_field_by_category(self, category: str, data_type: str = 'fixed_fee') -> list:
        """根据类别获取字段列表"""
        if category == 'training_features':
            return self.get_training_features(data_type)
        elif category == 'target_column':
            return [self.get_target_column(data_type)]
        elif category == 'passthrough_columns':
            return self.get_passthrough_columns(data_type)
        elif category in ['categorical_columns', 'numerical_columns', 'date_columns', 'required_columns']:
            return self.get(f'data_schema.{data_type}.{category}', [])
        else:
            return []

    def validate_field_configuration(self, data_type: str = 'fixed_fee') -> dict:
        """验证字段配置的完整性"""
        training_features = self.get_training_features(data_type)
        target_column = self.get_target_column(data_type)
        passthrough_columns = self.get_passthrough_columns(data_type)
        required_columns = self.get(f'data_schema.{data_type}.required_columns', [])

        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'field_counts': {
                'training_features': len(training_features),
                'passthrough_columns': len(passthrough_columns),
                'total_fields': len(training_features) + len(passthrough_columns) + (1 if target_column else 0)
            }
        }

        # 验证必需字段
        all_expected_fields = training_features + passthrough_columns
        if target_column:
            all_expected_fields.append(target_column)

        missing_required = set(all_expected_fields) - set(required_columns)
        if missing_required:
            validation_result['errors'].append(f"必需字段列表缺少: {missing_required}")
            validation_result['valid'] = False

        # 验证目标字段
        if not target_column:
            validation_result['errors'].append("缺少目标字段定义")
            validation_result['valid'] = False

        return validation_result
    
    def update_config(self, key_path: str, value: Any):
        """更新配置值"""
        keys = key_path.split('.')
        config = self.config
        
        # 导航到最后一级的父节点
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        self.logger.info(f"更新配置: {key_path} = {value}")
    
    def save_config(self, output_path: Optional[str] = None):
        """保存配置到文件"""
        output_path = output_path or self.config_path
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"配置保存失败: {e}")
            raise
    
    def get_environment_setup(self) -> Dict[str, str]:
        """获取环境变量设置"""
        env_vars = self.get('environment_variables', {})
        
        # 添加一些默认的环境变量
        default_env = {
            'PYTHONPATH': str(Path(__file__).parent.parent.parent),
            'BILLING_AUDIT_CONFIG': self.config_path,
            'BILLING_AUDIT_ENV': 'production'
        }
        
        return {**default_env, **env_vars}
    
    def export_environment_script(self, output_path: str):
        """导出环境变量设置脚本"""
        env_vars = self.get_environment_setup()
        
        script_content = "#!/bin/bash\n"
        script_content += "# 山西电信出账稽核AI系统环境变量设置\n"
        script_content += f"# 生成时间: {os.popen('date').read().strip()}\n\n"
        
        for key, value in env_vars.items():
            script_content += f'export {key}="{value}"\n'
        
        script_content += '\necho "环境变量设置完成"\n'
        script_content += 'echo "配置文件: $BILLING_AUDIT_CONFIG"\n'
        script_content += 'echo "Python路径: $PYTHONPATH"\n'
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(output_path, 0o755)
        
        self.logger.info(f"环境变量脚本已导出到: {output_path}")


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ProductionConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ProductionConfigManager()
        _config_manager.load_config()
    return _config_manager

def get_config(key_path: str, default: Any = None) -> Any:
    """快捷方式：获取配置值"""
    return get_config_manager().get(key_path, default)


if __name__ == "__main__":
    # 测试配置管理器
    try:
        config_manager = ProductionConfigManager()
        config = config_manager.load_config()
        
        print("配置加载成功")
        print(f"项目名称: {config_manager.get('project.name')}")
        print(f"版本: {config_manager.get('project.version')}")
        print(f"批处理大小: {config_manager.get_batch_size()}")
        print(f"判定阈值: {config_manager.get_judgment_thresholds()}")
        
        # 导出环境变量脚本
        script_path = "scripts/production/setup_production_env.sh"
        config_manager.export_environment_script(script_path)
        print(f"环境变量脚本已导出: {script_path}")
        
    except Exception as e:
        print(f"配置测试失败: {e}")
