#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
提供统一的配置文件管理和参数访问接口
"""

import json
import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """模型配置数据类"""
    feature_columns: List[str]
    target_column: str
    passthrough_columns: List[str]
    categorical_columns: List[str]
    numerical_columns: List[str]
    date_columns: List[str]


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为项目根目录下的config/billing_audit_config.json
        """
        if config_path is None:
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "billing_audit_config.json"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.suffix.lower() == '.json':
                    return json.load(f)
                elif self.config_path.suffix.lower() in ['.yml', '.yaml']:
                    return yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {self.config_path.suffix}")
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，使用点号分隔，如 'billing_audit.fixed_fee.feature_columns'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_model_config(self, model_type: str, fee_type: str) -> ModelConfig:
        """
        获取模型配置
        
        Args:
            model_type: 模型类型 ('billing_audit' 或 'behavior_analysis')
            fee_type: 费用类型 ('fixed_fee', 'discount', 'call_detail')
            
        Returns:
            ModelConfig对象
        """
        config_path = f"{model_type}.{fee_type}"
        config_data = self.get(config_path, {})
        
        return ModelConfig(
            feature_columns=config_data.get('feature_columns', []),
            target_column=config_data.get('target_column', ''),
            passthrough_columns=config_data.get('passthrough_columns', []),
            categorical_columns=config_data.get('categorical_columns', []),
            numerical_columns=config_data.get('numerical_columns', []),
            date_columns=config_data.get('date_columns', [])
        )
    
    def get_data_source(self, source_name: str) -> str:
        """获取数据源路径"""
        return self.get(f"data_sources.{source_name}", "")
    
    def get_model_params(self, model_name: str) -> Dict[str, Any]:
        """获取模型参数"""
        return self.get(f"billing_audit.model_params.{model_name}", {})
    
    def get_judgment_thresholds(self) -> Dict[str, Any]:
        """获取判定阈值配置"""
        return self.get("billing_audit.judgment_thresholds", {})
    
    def get_clustering_params(self) -> Dict[str, Any]:
        """获取聚类参数"""
        return self.get("behavior_analysis.clustering_params", {})
    
    def get_preprocessing_config(self) -> Dict[str, Any]:
        """获取预处理配置"""
        return self.get("preprocessing", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get("logging", {})
    
    def update_config(self, key_path: str, value: Any) -> None:
        """
        更新配置值
        
        Args:
            key_path: 配置键路径
            value: 新值
        """
        keys = key_path.split('.')
        config = self.config
        
        # 导航到目标位置
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
    
    def save_config(self, backup: bool = True) -> None:
        """
        保存配置文件
        
        Args:
            backup: 是否创建备份
        """
        if backup and self.config_path.exists():
            backup_path = self.config_path.with_suffix(f"{self.config_path.suffix}.backup")
            import shutil
            shutil.copy2(self.config_path, backup_path)
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            if self.config_path.suffix.lower() == '.json':
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            elif self.config_path.suffix.lower() in ['.yml', '.yaml']:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
    
    def validate_config(self) -> List[str]:
        """
        验证配置文件的完整性
        
        Returns:
            错误信息列表，空列表表示验证通过
        """
        errors = []
        
        # 检查必需的配置项
        required_keys = [
            "data_sources",
            "billing_audit.fixed_fee.feature_columns",
            "billing_audit.discount.feature_columns",
            "behavior_analysis.call_detail.feature_columns"
        ]
        
        for key in required_keys:
            if self.get(key) is None:
                errors.append(f"缺少必需的配置项: {key}")
        
        # 检查数据源文件是否存在
        project_root = Path(__file__).parent.parent.parent
        for source_name, file_path in self.get("data_sources", {}).items():
            full_path = project_root / file_path
            if not full_path.exists():
                errors.append(f"数据源文件不存在: {file_path}")
        
        return errors
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return json.dumps(self.config, ensure_ascii=False, indent=2)


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> ConfigManager:
    """获取全局配置管理器实例"""
    return config_manager
