# 调试脚本目录

本目录包含山西电信出账稽核AI系统v2.1.0的调试和测试脚本。

## 📁 脚本清单

### 1. debug_data_reading.py
**功能**: 调试数据读取问题的脚本
- **用途**: 分析输入数据文件的基本信息和None值分布
- **检查内容**:
  - 文件大小和总行数统计
  - 分隔符自动检测
  - 分批读取测试
  - None值分布分析
- **使用场景**: 当数据读取出现问题时使用
- **运行方式**: `python scripts/debug/debug_data_reading.py`

### 2. debug_prediction.py
**功能**: 调试预测处理问题的脚本
- **用途**: 逐批次测试预测过程，定位预测失败原因
- **检查内容**:
  - 预测器创建和模型加载
  - 逐批次预处理和预测测试
  - 详细错误分析和数据特征统计
  - 预测成功率统计
- **使用场景**: 当预测过程出现错误或数据处理不完整时使用
- **运行方式**: `python scripts/debug/debug_prediction.py`

### 3. test_chunk_reader.py
**功能**: 测试pandas chunk_reader的完整性
- **用途**: 验证pandas分批读取是否能完整处理所有数据
- **检查内容**:
  - 文件总行数统计
  - pandas一次性读取vs分批读取对比
  - 数据完整性验证
  - chunk_reader迭代完整性测试
- **使用场景**: 当怀疑数据读取不完整时使用
- **运行方式**: `python scripts/debug/test_chunk_reader.py`

## 🔧 使用指南

### 运行环境要求
- Python 3.9+
- 已安装项目依赖包
- 在项目根目录下运行

### 典型使用流程
1. **数据问题排查**: 先运行 `debug_data_reading.py` 检查数据基本情况
2. **读取完整性验证**: 运行 `test_chunk_reader.py` 验证pandas读取是否完整
3. **预测问题诊断**: 运行 `debug_prediction.py` 定位预测过程中的具体问题

### 输出说明
- 所有脚本都会输出详细的诊断信息到控制台
- 包含统计数据、错误详情、建议修复方案
- 支持进度显示和实时状态更新

## 📊 问题诊断记录

### 已解决的问题
1. **LightGBM状态异常** (2025-08-04)
   - 问题: `'>' not supported between instances of 'NoneType' and 'int'`
   - 原因: 分层模型中非零值回归器内部状态异常
   - 解决: 添加容错机制和零值占位符

2. **None值处理不完整** (2025-08-04)
   - 问题: 特征工程中None值导致数值计算失败
   - 原因: 日期字段大量None值未正确填充
   - 解决: 完善create_efficient_features方法的None值处理

3. **数据处理不完整** (2025-08-04)
   - 问题: 预测只处理部分数据就停止
   - 原因: 批次预测失败后错误处理不当
   - 解决: 改进错误处理逻辑，使用零值占位符确保数据完整性

## 🚀 维护说明

### 添加新调试脚本
1. 在本目录下创建新的.py文件
2. 遵循现有脚本的命名规范: `debug_*.py` 或 `test_*.py`
3. 更新本README文档，添加脚本说明

### 脚本规范
- 包含详细的docstring说明
- 提供清晰的输出格式
- 包含错误处理和异常捕获
- 支持命令行参数（如需要）

### 归档原则
- 调试脚本统一放在 `scripts/debug/` 目录
- 不要在项目根目录随意创建临时脚本
- 定期清理过期或无用的调试脚本

---
**最后更新**: 2025-08-05
**维护者**: AI Assistant
