#!/usr/bin/env python3
"""
数据对比分析脚本 - 对比原始数据 vs 测试集数据的质量差异
"""
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import os

def analyze_data_quality(file_path, file_name):
    """分析数据质量"""
    print(f"\n📊 分析文件: {file_name}")
    print("=" * 60)
    
    try:
        # 读取数据
        df = pd.read_csv(file_path)
        print(f"✅ 文件读取成功")
        print(f"📏 数据形状: {df.shape}")
        
        # 基本统计
        print(f"\n📈 基本统计:")
        print(f"  - 总行数: {len(df):,}")
        print(f"  - 总列数: {len(df.columns)}")
        
        # None值分析
        print(f"\n🔍 None值分析:")
        total_nulls = df.isnull().sum().sum()
        print(f"  - 总None值数量: {total_nulls:,}")
        print(f"  - None值比例: {total_nulls/(len(df)*len(df.columns))*100:.2f}%")
        
        # 各列None值统计
        null_cols = df.columns[df.isnull().any()].tolist()
        if null_cols:
            print(f"  - 有None值的列数: {len(null_cols)}")
            print(f"  - 主要None值列:")
            for col in null_cols[:5]:  # 只显示前5个
                null_count = df[col].isnull().sum()
                null_pct = null_count / len(df) * 100
                print(f"    * {col}: {null_count:,} ({null_pct:.1f}%)")
        
        # 数据类型分析
        print(f"\n📋 数据类型分析:")
        dtype_counts = df.dtypes.value_counts()
        for dtype, count in dtype_counts.items():
            print(f"  - {dtype}: {count} 列")
        
        # 特殊字段分析
        special_fields = ['final_eff_mon', 'final_eff_day', 'final_exp_mon', 'final_exp_day', 'amount']
        print(f"\n🎯 关键字段分析:")
        for field in special_fields:
            if field in df.columns:
                null_count = df[field].isnull().sum()
                null_pct = null_count / len(df) * 100
                if field == 'amount':
                    # amount字段特殊分析
                    zero_count = (df[field] == 0).sum()
                    zero_pct = zero_count / len(df) * 100
                    print(f"  - {field}: None={null_count:,}({null_pct:.1f}%), 零值={zero_count:,}({zero_pct:.1f}%)")
                else:
                    print(f"  - {field}: None={null_count:,} ({null_pct:.1f}%)")
        
        # 数据分布分析
        if 'amount' in df.columns:
            print(f"\n💰 amount字段详细分析:")
            amount_series = df['amount'].dropna()
            if len(amount_series) > 0:
                print(f"  - 有效值数量: {len(amount_series):,}")
                print(f"  - 均值: {amount_series.mean():.2f}元")
                print(f"  - 中位数: {amount_series.median():.2f}元")
                print(f"  - 标准差: {amount_series.std():.2f}元")
                print(f"  - 最小值: {amount_series.min():.2f}元")
                print(f"  - 最大值: {amount_series.max():.2f}元")
                
                # 零值和非零值分析
                zero_mask = amount_series == 0
                zero_count = zero_mask.sum()
                nonzero_count = (~zero_mask).sum()
                print(f"  - 零值数量: {zero_count:,} ({zero_count/len(amount_series)*100:.1f}%)")
                print(f"  - 非零值数量: {nonzero_count:,} ({nonzero_count/len(amount_series)*100:.1f}%)")
                
                if nonzero_count > 0:
                    nonzero_values = amount_series[~zero_mask]
                    print(f"  - 非零值均值: {nonzero_values.mean():.2f}元")
                    print(f"  - 非零值中位数: {nonzero_values.median():.2f}元")
        
        return {
            'shape': df.shape,
            'total_nulls': total_nulls,
            'null_percentage': total_nulls/(len(df)*len(df.columns))*100,
            'null_columns': len(null_cols),
            'has_amount': 'amount' in df.columns
        }
        
    except Exception as e:
        print(f"❌ 文件分析失败: {e}")
        return None

def find_test_files():
    """查找测试集文件"""
    # 查找最新的测试集文件
    production_dir = Path("production/billing_audit_production_v2.1.0_20250730_083326")
    if production_dir.exists():
        temp_dir = production_dir / "outputs" / "temp"
        if temp_dir.exists():
            # 查找测试集文件
            test_files = list(temp_dir.glob("**/test_data_*.csv"))
            if test_files:
                return max(test_files, key=lambda x: x.stat().st_mtime)
    
    # 在当前目录查找
    temp_dirs = list(Path(".").glob("**/temp"))
    for temp_dir in temp_dirs:
        test_files = list(temp_dir.glob("test_data_*.csv"))
        if test_files:
            return max(test_files, key=lambda x: x.stat().st_mtime)
    
    return None

def main():
    """主函数"""
    print("🔍 山西电信出账稽核AI系统 - 数据质量对比分析")
    print("=" * 80)
    
    # 1. 分析原始数据文件
    original_file = "production/billing_audit_production_v2.1.0_20250730_083326/data/input/ofrm_result.txt"
    if not Path(original_file).exists():
        original_file = "data/input/ofrm_result.txt"
    
    if Path(original_file).exists():
        original_stats = analyze_data_quality(original_file, "原始数据文件 (ofrm_result.txt)")
    else:
        print("❌ 找不到原始数据文件")
        original_stats = None
    
    # 2. 查找并分析测试集文件
    test_file = find_test_files()
    if test_file:
        test_stats = analyze_data_quality(test_file, f"测试集文件 ({test_file.name})")
    else:
        print("❌ 找不到测试集文件")
        test_stats = None
    
    # 3. 对比分析
    if original_stats and test_stats:
        print(f"\n🔄 数据质量对比分析")
        print("=" * 60)
        
        print(f"📊 数据规模对比:")
        print(f"  - 原始数据: {original_stats['shape'][0]:,} 行 × {original_stats['shape'][1]} 列")
        print(f"  - 测试集: {test_stats['shape'][0]:,} 行 × {test_stats['shape'][1]} 列")
        print(f"  - 测试集占比: {test_stats['shape'][0]/original_stats['shape'][0]*100:.1f}%")
        
        print(f"\n🔍 数据质量对比:")
        print(f"  - 原始数据None值比例: {original_stats['null_percentage']:.2f}%")
        print(f"  - 测试集None值比例: {test_stats['null_percentage']:.2f}%")
        print(f"  - 质量改善: {original_stats['null_percentage'] - test_stats['null_percentage']:.2f}个百分点")
        
        print(f"\n🎯 关键发现:")
        if test_stats['null_percentage'] < original_stats['null_percentage']:
            print(f"  ✅ 测试集数据质量更好，None值比例更低")
        elif test_stats['null_percentage'] > original_stats['null_percentage']:
            print(f"  ⚠️ 测试集数据质量较差，None值比例更高")
        else:
            print(f"  ➡️ 测试集和原始数据质量相近")
        
        print(f"\n💡 预测处理差异分析:")
        print(f"  - 原始数据规模大，None值多，容易触发LightGBM状态异常")
        print(f"  - 测试集规模小，经过数据拆分处理，数据质量可能更稳定")
        print(f"  - 这可能是单独预测失败而完整流程成功的关键原因")
    
    print(f"\n🏁 分析完成")

if __name__ == "__main__":
    main()
