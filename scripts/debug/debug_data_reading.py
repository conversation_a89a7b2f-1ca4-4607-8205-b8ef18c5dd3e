#!/usr/bin/env python3
"""
调试数据读取问题的脚本
"""
import pandas as pd
from pathlib import Path

def debug_data_reading():
    """调试数据读取问题"""
    input_file = "data/input/ofrm_result.txt"
    batch_size = 1000
    
    print(f"🔍 调试数据读取问题")
    print(f"输入文件: {input_file}")
    print(f"批次大小: {batch_size}")
    print("=" * 60)
    
    # 1. 检查文件基本信息
    file_path = Path(input_file)
    if not file_path.exists():
        print(f"❌ 文件不存在: {input_file}")
        return
    
    file_size = file_path.stat().st_size / (1024 * 1024)  # MB
    print(f"📁 文件大小: {file_size:.2f} MB")
    
    # 2. 检查总行数
    with open(input_file, 'r', encoding='utf-8') as f:
        total_lines = sum(1 for _ in f)
    print(f"📊 总行数: {total_lines:,} 行")
    
    # 3. 检测分隔符
    with open(input_file, 'r', encoding='utf-8') as f:
        first_line = f.readline()
        if ',' in first_line:
            sep = ','
        elif '\t' in first_line:
            sep = '\t'
        else:
            sep = ','
    print(f"🔧 分隔符: '{sep}'")
    
    # 4. 测试分批读取
    print(f"\n🔄 开始测试分批读取...")
    try:
        chunk_reader = pd.read_csv(
            input_file, 
            sep=sep,
            chunksize=batch_size,
            encoding='utf-8',
            low_memory=False
        )
        
        chunk_count = 0
        total_rows = 0
        error_count = 0
        
        for chunk in chunk_reader:
            chunk_count += 1
            current_rows = len(chunk)
            total_rows += current_rows
            
            print(f"  批次 {chunk_count}: {current_rows:,} 行, 累计: {total_rows:,} 行")
            
            # 检查这个批次是否有问题
            try:
                # 检查None值
                null_counts = chunk.isnull().sum()
                if null_counts.sum() > 0:
                    print(f"    ⚠️  发现 {null_counts.sum()} 个None值")
                
                # 检查数据类型
                problematic_cols = []
                for col in chunk.columns:
                    if chunk[col].dtype == 'object':
                        # 检查是否有混合类型
                        try:
                            pd.to_numeric(chunk[col], errors='raise')
                        except:
                            problematic_cols.append(col)
                
                if problematic_cols:
                    print(f"    ⚠️  可能有问题的列: {problematic_cols}")
                    
            except Exception as e:
                error_count += 1
                print(f"    ❌ 批次 {chunk_count} 检查失败: {e}")
                
                # 如果连续多个批次失败，可能是系统性问题
                if error_count >= 3:
                    print(f"    🛑 连续 {error_count} 个批次失败，可能存在系统性问题")
                    break
            
            # 限制测试批次数，避免运行太久
            if chunk_count >= 70:  # 测试前70个批次
                print(f"    ℹ️  测试前 {chunk_count} 个批次，停止测试")
                break
        
        print(f"\n📈 测试结果:")
        print(f"  处理批次数: {chunk_count}")
        print(f"  处理行数: {total_rows:,}")
        print(f"  错误批次数: {error_count}")
        print(f"  预期总批次数: {(total_lines - 1) // batch_size + 1}")  # 减1是因为表头
        print(f"  数据完整性: {total_rows / (total_lines - 1) * 100:.1f}%")
        
    except Exception as e:
        print(f"❌ 分批读取失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_reading()
