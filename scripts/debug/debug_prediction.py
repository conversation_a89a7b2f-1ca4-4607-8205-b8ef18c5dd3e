#!/usr/bin/env python3
"""
调试预测处理问题的脚本
"""
import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
import pickle
from pathlib import Path
from src.billing_audit.inference.predict_large_scale import LargeScalePrediction

def debug_prediction():
    """调试预测处理问题"""
    input_file = "data/input/ofrm_result.txt"
    model_file = "outputs/models/hierarchical_model_20250804_170027.pkl"
    feature_engineer_file = "outputs/models/large_scale_feature_engineer_20250804_170018.pkl"
    batch_size = 1000
    
    print(f"🔍 调试预测处理问题")
    print(f"输入文件: {input_file}")
    print(f"模型文件: {model_file}")
    print(f"特征工程器: {feature_engineer_file}")
    print(f"批次大小: {batch_size}")
    print("=" * 60)
    
    # 创建预测器
    try:
        predictor = LargeScalePrediction(
            model_path=model_file,
            feature_engineer_path=feature_engineer_file,
            batch_size=batch_size
        )
        print("✅ 预测器创建成功")
    except Exception as e:
        print(f"❌ 预测器创建失败: {e}")
        return
    
    # 读取数据
    try:
        chunk_reader, _ = predictor.read_large_csv(input_file, batch_size)
        print("✅ 数据读取器创建成功")
    except Exception as e:
        print(f"❌ 数据读取器创建失败: {e}")
        return
    
    # 逐批次测试预测
    chunk_count = 0
    success_count = 0
    error_count = 0
    total_predictions = 0
    
    print(f"\n🔄 开始逐批次测试预测...")
    
    for chunk in chunk_reader:
        chunk_count += 1
        print(f"\n📦 批次 {chunk_count}: {len(chunk)} 行")
        
        try:
            # 1. 测试预处理
            print(f"  🔧 开始预处理...")
            X_chunk = predictor.preprocess_chunk(chunk)
            print(f"  ✅ 预处理成功: {X_chunk.shape}")
            
            # 检查预处理后的数据
            if X_chunk.isnull().sum().sum() > 0:
                print(f"  ⚠️  预处理后仍有None值: {X_chunk.isnull().sum().sum()}")
            
            # 2. 测试预测
            print(f"  🔮 开始预测...")
            predictions = predictor.predict_chunk(X_chunk)
            print(f"  ✅ 预测成功: {len(predictions)} 个预测值")
            
            # 检查预测结果
            if len(predictions) != len(chunk):
                print(f"  ⚠️  预测数量不匹配: 输入{len(chunk)}, 输出{len(predictions)}")
            
            if np.isnan(predictions).sum() > 0:
                print(f"  ⚠️  预测结果包含NaN: {np.isnan(predictions).sum()}")
            
            success_count += 1
            total_predictions += len(predictions)
            
            print(f"  📊 预测范围: {predictions.min():.2f} - {predictions.max():.2f}")
            
        except Exception as e:
            error_count += 1
            print(f"  ❌ 批次 {chunk_count} 处理失败: {e}")
            
            # 详细错误分析
            print(f"    🔍 错误详情:")
            import traceback
            traceback.print_exc()
            
            # 分析数据特征
            print(f"    📊 数据特征:")
            print(f"      - 行数: {len(chunk)}")
            print(f"      - 列数: {len(chunk.columns)}")
            print(f"      - None值总数: {chunk.isnull().sum().sum()}")
            
            # 显示有None值的列
            null_cols = chunk.columns[chunk.isnull().any()].tolist()
            if null_cols:
                print(f"      - 有None值的列: {null_cols}")
                for col in null_cols[:5]:  # 只显示前5个
                    null_count = chunk[col].isnull().sum()
                    print(f"        * {col}: {null_count} 个None值")
            
            # 如果连续多个批次失败，停止测试
            if error_count >= 3:
                print(f"    🛑 连续 {error_count} 个批次失败，停止测试")
                break
        
        # 限制测试批次数
        if chunk_count >= 10:
            print(f"  ℹ️  测试前 {chunk_count} 个批次，停止测试")
            break
    
    print(f"\n📈 测试结果:")
    print(f"  测试批次数: {chunk_count}")
    print(f"  成功批次数: {success_count}")
    print(f"  失败批次数: {error_count}")
    print(f"  成功率: {success_count/chunk_count*100:.1f}%")
    print(f"  总预测数: {total_predictions}")
    
    if error_count > 0:
        print(f"\n🔧 建议修复方案:")
        print(f"  1. 加强None值处理")
        print(f"  2. 改进错误处理机制")
        print(f"  3. 添加数据验证步骤")

if __name__ == "__main__":
    debug_prediction()
