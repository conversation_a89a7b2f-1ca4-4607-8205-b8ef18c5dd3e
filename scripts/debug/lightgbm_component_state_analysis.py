#!/usr/bin/env python3
"""
LightGBM组件状态分析脚本
专门分析零值分类器和非零值回归器的状态异常情况
"""
import pandas as pd
import numpy as np
import joblib
import gc
import time
from pathlib import Path

def analyze_lightgbm_component_states():
    """分析LightGBM组件状态"""
    print("🔍 LightGBM组件状态深度分析")
    print("=" * 60)
    
    # 加载模型
    model_path = "outputs/models/hierarchical_model_20250805_111931.pkl"
    model_dict = joblib.load(model_path)
    
    zero_classifier = model_dict['zero_classifier']
    nonzero_regressor = model_dict['nonzero_regressor']
    
    print(f"✅ 模型组件加载完成")
    print(f"  - 零值分类器: {type(zero_classifier).__name__}")
    print(f"  - 非零值回归器: {type(nonzero_regressor).__name__}")
    
    # 分析初始状态
    print(f"\n🔍 初始状态分析:")
    print(f"零值分类器状态:")
    print(f"  - _n_classes: {getattr(zero_classifier, '_n_classes', 'NOT_SET')}")
    print(f"  - _objective: {getattr(zero_classifier, '_objective', 'NOT_SET')}")
    
    print(f"非零值回归器状态:")
    print(f"  - _n_classes: {getattr(nonzero_regressor, '_n_classes', 'NOT_SET')}")
    print(f"  - _objective: {getattr(nonzero_regressor, '_objective', 'NOT_SET')}")
    
    # 加载测试数据
    feature_engineer = joblib.load("outputs/models/large_scale_feature_engineer_20250805_111919.pkl")
    df = pd.read_csv("data/input/ofrm_result.txt")
    
    print(f"\n📊 测试数据: {len(df):,} 行")
    
    # 分批测试组件稳定性
    batch_size = 1000
    max_batches = 20  # 测试前20个批次
    
    classifier_failures = 0
    regressor_failures = 0
    
    print(f"\n🧪 开始组件稳定性测试 (前{max_batches}批次):")
    
    for batch_idx in range(min(max_batches, len(df) // batch_size + 1)):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(df))
        
        if start_idx >= len(df):
            break
        
        batch_data = df.iloc[start_idx:end_idx]
        
        try:
            # 特征工程
            X_features = feature_engineer['feature_engineer'].transform(batch_data)
            
            # 测试零值分类器
            try:
                zero_pred = zero_classifier.predict(X_features)
                print(f"  批次 {batch_idx + 1}: 零值分类器 ✅ ({len(zero_pred)} 预测)")
            except Exception as e:
                classifier_failures += 1
                print(f"  批次 {batch_idx + 1}: 零值分类器 ❌ - {e}")
                continue
            
            # 测试非零值回归器（只对被分类为非零的样本）
            nonzero_mask = zero_pred == 1
            if np.any(nonzero_mask):
                X_nonzero = X_features[nonzero_mask]
                try:
                    nonzero_pred = nonzero_regressor.predict(X_nonzero)
                    print(f"  批次 {batch_idx + 1}: 非零值回归器 ✅ ({len(nonzero_pred)} 预测)")
                except Exception as e:
                    regressor_failures += 1
                    print(f"  批次 {batch_idx + 1}: 非零值回归器 ❌ - {e}")
                    
                    # 检查回归器状态
                    print(f"    回归器状态: _n_classes={getattr(nonzero_regressor, '_n_classes', 'NOT_SET')}")
            else:
                print(f"  批次 {batch_idx + 1}: 非零值回归器 ⏭️ (无非零样本)")
        
        except Exception as e:
            print(f"  批次 {batch_idx + 1}: 特征工程失败 - {e}")
            continue
    
    # 统计结果
    print(f"\n📊 组件稳定性测试结果:")
    print(f"  - 测试批次数: {min(max_batches, len(df) // batch_size + 1)}")
    print(f"  - 零值分类器失败: {classifier_failures} 次")
    print(f"  - 非零值回归器失败: {regressor_failures} 次")
    
    if regressor_failures > classifier_failures:
        print(f"  - 🎯 结论: 非零值回归器更容易出现状态异常")
    elif classifier_failures > regressor_failures:
        print(f"  - 🎯 结论: 零值分类器更容易出现状态异常")
    else:
        print(f"  - 🎯 结论: 两个组件稳定性相近")
    
    return {
        'classifier_failures': classifier_failures,
        'regressor_failures': regressor_failures,
        'total_batches': min(max_batches, len(df) // batch_size + 1)
    }

def test_hierarchical_prediction_flow():
    """测试分层预测流程"""
    print(f"\n🔄 分层预测流程测试:")
    print("=" * 60)
    
    # 加载组件
    model_dict = joblib.load("outputs/models/hierarchical_model_20250805_111931.pkl")
    feature_engineer = joblib.load("outputs/models/large_scale_feature_engineer_20250805_111919.pkl")
    df = pd.read_csv("data/input/ofrm_result.txt")
    
    zero_classifier = model_dict['zero_classifier']
    nonzero_regressor = model_dict['nonzero_regressor']
    
    # 测试单个批次的完整流程
    test_batch = df.iloc[7000:8000]  # 第8批次，历史上容易出现异常的批次
    
    print(f"📊 测试批次: 第8批次 (7000-8000行)")
    
    try:
        # 1. 特征工程
        print("  1️⃣ 特征工程...")
        X_features = feature_engineer['feature_engineer'].transform(test_batch)
        print(f"     ✅ 特征工程成功: {X_features.shape}")
        
        # 2. 零值分类
        print("  2️⃣ 零值分类...")
        zero_predictions = zero_classifier.predict(X_features)
        zero_count = (zero_predictions == 0).sum()
        nonzero_count = (zero_predictions == 1).sum()
        print(f"     ✅ 零值分类成功: 零值={zero_count}, 非零值={nonzero_count}")
        
        # 3. 非零值回归
        print("  3️⃣ 非零值回归...")
        final_predictions = np.zeros(len(test_batch))
        
        nonzero_mask = zero_predictions == 1
        if np.any(nonzero_mask):
            X_nonzero = X_features[nonzero_mask]
            print(f"     📊 非零值样本数: {len(X_nonzero)}")
            
            try:
                nonzero_pred = nonzero_regressor.predict(X_nonzero)
                final_predictions[nonzero_mask] = nonzero_pred
                print(f"     ✅ 非零值回归成功: 预测范围 {nonzero_pred.min():.2f} - {nonzero_pred.max():.2f}")
            except Exception as e:
                print(f"     ❌ 非零值回归失败: {e}")
                print(f"     🔍 回归器状态: _n_classes={getattr(nonzero_regressor, '_n_classes', 'NOT_SET')}")
                # 使用零值作为安全预测
                final_predictions[nonzero_mask] = 0.0
                print(f"     🛡️ 使用零值安全预测")
        else:
            print(f"     ⏭️ 无非零值样本，跳过回归")
        
        # 4. 结果统计
        print("  4️⃣ 结果统计...")
        final_zero_count = (final_predictions == 0).sum()
        final_nonzero_count = (final_predictions != 0).sum()
        print(f"     📊 最终预测: 零值={final_zero_count}, 非零值={final_nonzero_count}")
        
        if final_nonzero_count > 0:
            nonzero_values = final_predictions[final_predictions != 0]
            print(f"     📈 非零值统计: 均值={nonzero_values.mean():.2f}, 中位数={np.median(nonzero_values):.2f}")
        
        print(f"  ✅ 分层预测流程测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 分层预测流程测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 山西电信出账稽核AI系统v2.1.0")
    print("🔍 LightGBM组件状态深度分析")
    print("🎯 目标：确定状态异常主要发生在哪个组件")
    print("=" * 80)
    
    # 1. 组件状态分析
    component_result = analyze_lightgbm_component_states()
    
    # 2. 分层预测流程测试
    flow_result = test_hierarchical_prediction_flow()
    
    # 3. 综合分析
    print(f"\n🎯 综合分析结论:")
    print("=" * 60)
    
    if component_result['regressor_failures'] > component_result['classifier_failures']:
        print("✅ 确认：LightGBM状态异常主要发生在 **非零值回归器**")
        print("📊 证据：")
        print(f"  - 零值分类器失败: {component_result['classifier_failures']} 次")
        print(f"  - 非零值回归器失败: {component_result['regressor_failures']} 次")
        print("💡 技术洞察：")
        print("  - 零值分类器处理二分类任务，相对稳定")
        print("  - 非零值回归器处理回归任务，对数据变化更敏感")
        print("  - 回归器的_n_classes状态更容易在批处理中异常")
    else:
        print("⚠️ 需要进一步分析：两个组件的失败率相近")
    
    print(f"\n🔧 优化建议：")
    print("  1. 重点加强非零值回归器的状态管理")
    print("  2. 为回归器实施专门的状态备份和恢复机制")
    print("  3. 考虑在回归器异常时使用零值安全预测")
    
    return 0

if __name__ == "__main__":
    exit(main())
