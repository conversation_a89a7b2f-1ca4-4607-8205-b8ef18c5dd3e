#!/usr/bin/env python3
"""
LightGBM状态异常验证和诊断脚本
用于深度分析和验证LightGBM状态管理问题
"""
import pandas as pd
import numpy as np
import joblib
import gc
import psutil
import logging
from pathlib import Path
import time
from typing import Dict, Any, List, Optional

class LightGBMStateMonitor:
    """LightGBM状态监控器"""
    
    def __init__(self, model_path: str):
        self.model_path = Path(model_path)
        self.logger = self._setup_logger()
        self.model = None
        self.state_history = []
        self.load_model()
    
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger("LightGBMStateMonitor")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def load_model(self):
        """加载模型"""
        try:
            self.logger.info(f"加载模型: {self.model_path}")
            self.model = joblib.load(self.model_path)
            self._record_initial_state()
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def _record_initial_state(self):
        """记录初始状态"""
        initial_state = self._capture_model_state()
        initial_state['event'] = 'model_loaded'
        initial_state['timestamp'] = time.time()
        self.state_history.append(initial_state)
        self.logger.info("初始状态记录完成")
    
    def _capture_model_state(self) -> Dict[str, Any]:
        """捕获模型当前状态"""
        state = {
            'memory_mb': psutil.Process().memory_info().rss / 1024 / 1024,
            'model_type': type(self.model).__name__
        }
        
        # 检查分层模型
        if hasattr(self.model, 'zero_classifier'):
            state['zero_classifier'] = self._capture_component_state(self.model.zero_classifier)
        
        if hasattr(self.model, 'nonzero_regressor'):
            state['nonzero_regressor'] = self._capture_component_state(self.model.nonzero_regressor)
        
        # 检查单一模型
        if hasattr(self.model, '_n_classes'):
            state['_n_classes'] = getattr(self.model, '_n_classes')
        
        return state
    
    def _capture_component_state(self, component) -> Dict[str, Any]:
        """捕获组件状态"""
        component_state = {
            'type': type(component).__name__
        }
        
        critical_attrs = ['_n_classes', '_objective', '_n_features', '_classes']
        for attr in critical_attrs:
            if hasattr(component, attr):
                value = getattr(component, attr)
                component_state[attr] = value
        
        return component_state
    
    def test_batch_prediction_stability(self, test_data_path: str, max_batches: int = 100) -> Dict[str, Any]:
        """测试批量预测的状态稳定性"""
        self.logger.info(f"开始批量预测稳定性测试，最大批次: {max_batches}")
        
        # 读取测试数据
        df = pd.read_csv(test_data_path)
        self.logger.info(f"测试数据: {len(df)} 行")
        
        batch_size = 1000
        successful_batches = 0
        failed_batches = 0
        state_changes = 0
        
        for batch_idx in range(min(max_batches, len(df) // batch_size + 1)):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(df))
            
            if start_idx >= len(df):
                break
            
            batch_data = df.iloc[start_idx:end_idx]
            
            # 记录批次前状态
            state_before = self._capture_model_state()
            
            try:
                # 执行预测
                predictions = self.model.predict(batch_data)
                successful_batches += 1
                
                self.logger.info(f"批次 {batch_idx + 1}: 成功预测 {len(predictions)} 个样本")
                
            except Exception as e:
                failed_batches += 1
                self.logger.error(f"批次 {batch_idx + 1}: 预测失败 - {e}")
                
                # 记录失败状态
                failure_state = self._capture_model_state()
                failure_state['event'] = 'prediction_failed'
                failure_state['batch_idx'] = batch_idx + 1
                failure_state['error'] = str(e)
                failure_state['timestamp'] = time.time()
                self.state_history.append(failure_state)
            
            # 记录批次后状态
            state_after = self._capture_model_state()
            
            # 检查状态变化
            if self._states_differ(state_before, state_after):
                state_changes += 1
                self.logger.warning(f"批次 {batch_idx + 1}: 检测到状态变化")
                
                change_record = {
                    'event': 'state_changed',
                    'batch_idx': batch_idx + 1,
                    'state_before': state_before,
                    'state_after': state_after,
                    'timestamp': time.time()
                }
                self.state_history.append(change_record)
            
            # 每10个批次记录状态
            if (batch_idx + 1) % 10 == 0:
                checkpoint_state = self._capture_model_state()
                checkpoint_state['event'] = 'checkpoint'
                checkpoint_state['batch_idx'] = batch_idx + 1
                checkpoint_state['timestamp'] = time.time()
                self.state_history.append(checkpoint_state)
        
        # 测试结果
        result = {
            'total_batches_attempted': batch_idx + 1,
            'successful_batches': successful_batches,
            'failed_batches': failed_batches,
            'state_changes': state_changes,
            'success_rate': successful_batches / (successful_batches + failed_batches) * 100 if (successful_batches + failed_batches) > 0 else 0,
            'first_failure_batch': None
        }
        
        # 找到第一个失败的批次
        for record in self.state_history:
            if record.get('event') == 'prediction_failed':
                result['first_failure_batch'] = record.get('batch_idx')
                break
        
        self.logger.info(f"稳定性测试完成: 成功率 {result['success_rate']:.1f}%")
        return result
    
    def _states_differ(self, state1: Dict[str, Any], state2: Dict[str, Any]) -> bool:
        """检查两个状态是否不同"""
        # 忽略内存和时间戳等动态字段
        ignore_keys = {'memory_mb', 'timestamp', 'event', 'batch_idx', 'error'}
        
        for key in state1:
            if key in ignore_keys:
                continue
            
            if key not in state2:
                return True
            
            if state1[key] != state2[key]:
                return True
        
        return False
    
    def analyze_none_value_patterns(self, data_path: str) -> Dict[str, Any]:
        """分析None值模式"""
        self.logger.info("开始分析None值模式")
        
        df = pd.read_csv(data_path)
        batch_size = 1000
        
        patterns = []
        
        for batch_idx in range(0, len(df), batch_size):
            batch_end = min(batch_idx + batch_size, len(df))
            batch = df.iloc[batch_idx:batch_end]
            
            # 分析这个批次的None值模式
            pattern = {
                'batch_idx': batch_idx // batch_size + 1,
                'batch_size': len(batch),
                'total_nulls': batch.isnull().sum().sum(),
                'null_percentage': batch.isnull().sum().sum() / (len(batch) * len(batch.columns)) * 100
            }
            
            # 分析关键字段的None值
            key_fields = ['final_eff_mon', 'final_eff_day', 'final_exp_mon', 'final_exp_day']
            for field in key_fields:
                if field in batch.columns:
                    null_count = batch[field].isnull().sum()
                    pattern[f'{field}_nulls'] = null_count
                    pattern[f'{field}_null_pct'] = null_count / len(batch) * 100
            
            patterns.append(pattern)
            
            if len(patterns) >= 20:  # 只分析前20个批次
                break
        
        # 分析模式变化
        analysis = {
            'total_batches_analyzed': len(patterns),
            'patterns': patterns,
            'pattern_changes': []
        }
        
        # 检测模式变化点
        for i in range(1, len(patterns)):
            prev_pattern = patterns[i-1]
            curr_pattern = patterns[i]
            
            # 检查None值百分比变化
            null_pct_change = abs(curr_pattern['null_percentage'] - prev_pattern['null_percentage'])
            
            if null_pct_change > 5:  # 5%以上的变化
                change = {
                    'from_batch': prev_pattern['batch_idx'],
                    'to_batch': curr_pattern['batch_idx'],
                    'null_pct_change': null_pct_change,
                    'change_type': 'significant_null_percentage_change'
                }
                analysis['pattern_changes'].append(change)
        
        self.logger.info(f"None值模式分析完成，发现 {len(analysis['pattern_changes'])} 个显著变化")
        return analysis
    
    def generate_diagnostic_report(self) -> str:
        """生成诊断报告"""
        report = []
        report.append("# LightGBM状态诊断报告")
        report.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"模型路径: {self.model_path}")
        report.append("")
        
        # 状态历史摘要
        report.append("## 状态历史摘要")
        report.append(f"总记录数: {len(self.state_history)}")
        
        event_counts = {}
        for record in self.state_history:
            event = record.get('event', 'unknown')
            event_counts[event] = event_counts.get(event, 0) + 1
        
        for event, count in event_counts.items():
            report.append(f"- {event}: {count} 次")
        
        report.append("")
        
        # 失败分析
        failures = [r for r in self.state_history if r.get('event') == 'prediction_failed']
        if failures:
            report.append("## 预测失败分析")
            report.append(f"失败次数: {len(failures)}")
            
            first_failure = failures[0]
            report.append(f"首次失败批次: {first_failure.get('batch_idx')}")
            report.append(f"首次失败错误: {first_failure.get('error')}")
        
        report.append("")
        
        # 状态变化分析
        state_changes = [r for r in self.state_history if r.get('event') == 'state_changed']
        if state_changes:
            report.append("## 状态变化分析")
            report.append(f"状态变化次数: {len(state_changes)}")
            
            for change in state_changes[:3]:  # 只显示前3个变化
                report.append(f"- 批次 {change.get('batch_idx')}: 状态发生变化")
        
        return "\n".join(report)
    
    def save_diagnostic_data(self, output_dir: str):
        """保存诊断数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存状态历史
        import json
        
        # 处理不可序列化的对象
        serializable_history = []
        for record in self.state_history:
            serializable_record = {}
            for key, value in record.items():
                try:
                    json.dumps(value)  # 测试是否可序列化
                    serializable_record[key] = value
                except (TypeError, ValueError):
                    serializable_record[key] = str(value)
            serializable_history.append(serializable_record)
        
        with open(output_path / "state_history.json", "w", encoding="utf-8") as f:
            json.dump(serializable_history, f, indent=2, ensure_ascii=False)
        
        # 保存诊断报告
        with open(output_path / "diagnostic_report.md", "w", encoding="utf-8") as f:
            f.write(self.generate_diagnostic_report())
        
        self.logger.info(f"诊断数据已保存到: {output_path}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="LightGBM状态验证工具")
    parser.add_argument("--model", required=True, help="模型文件路径")
    parser.add_argument("--test-data", required=True, help="测试数据文件路径")
    parser.add_argument("--max-batches", type=int, default=100, help="最大测试批次数")
    parser.add_argument("--output-dir", default="./diagnostic_output", help="输出目录")
    
    args = parser.parse_args()
    
    # 创建状态监控器
    monitor = LightGBMStateMonitor(args.model)
    
    # 执行稳定性测试
    print("🔍 执行批量预测稳定性测试...")
    stability_result = monitor.test_batch_prediction_stability(args.test_data, args.max_batches)
    
    print(f"📊 测试结果:")
    print(f"  - 总批次: {stability_result['total_batches_attempted']}")
    print(f"  - 成功批次: {stability_result['successful_batches']}")
    print(f"  - 失败批次: {stability_result['failed_batches']}")
    print(f"  - 成功率: {stability_result['success_rate']:.1f}%")
    print(f"  - 状态变化: {stability_result['state_changes']} 次")
    
    if stability_result['first_failure_batch']:
        print(f"  - 首次失败批次: {stability_result['first_failure_batch']}")
    
    # 分析None值模式
    print("\n🔍 分析None值模式...")
    pattern_analysis = monitor.analyze_none_value_patterns(args.test_data)
    
    print(f"📊 None值模式分析:")
    print(f"  - 分析批次数: {pattern_analysis['total_batches_analyzed']}")
    print(f"  - 模式变化点: {len(pattern_analysis['pattern_changes'])} 个")
    
    # 保存诊断数据
    monitor.save_diagnostic_data(args.output_dir)
    print(f"\n💾 诊断数据已保存到: {args.output_dir}")
    
    return 0

if __name__ == "__main__":
    exit(main())
