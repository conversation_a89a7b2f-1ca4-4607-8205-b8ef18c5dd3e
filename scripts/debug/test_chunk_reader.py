#!/usr/bin/env python3
"""
测试pandas chunk_reader的完整性
"""
import pandas as pd

def test_chunk_reader():
    """测试chunk_reader是否能完整读取所有数据"""
    input_file = "data/input/ofrm_result.txt"
    batch_size = 1000
    
    print(f"🔍 测试pandas chunk_reader完整性")
    print(f"输入文件: {input_file}")
    print(f"批次大小: {batch_size}")
    print("=" * 60)
    
    # 1. 直接读取文件统计总行数
    with open(input_file, 'r', encoding='utf-8') as f:
        total_lines = sum(1 for _ in f)
    print(f"📊 文件总行数: {total_lines:,} 行")
    
    # 2. 使用pandas一次性读取
    try:
        df_full = pd.read_csv(input_file)
        print(f"📊 pandas一次性读取: {len(df_full):,} 行")
    except Exception as e:
        print(f"❌ pandas一次性读取失败: {e}")
        return
    
    # 3. 使用chunk_reader分批读取
    try:
        chunk_reader = pd.read_csv(
            input_file, 
            chunksize=batch_size,
            encoding='utf-8',
            low_memory=False
        )
        
        chunk_count = 0
        total_rows = 0
        
        print(f"\n🔄 开始chunk_reader测试...")
        
        for chunk in chunk_reader:
            chunk_count += 1
            current_rows = len(chunk)
            total_rows += current_rows
            
            print(f"  批次 {chunk_count}: {current_rows:,} 行, 累计: {total_rows:,} 行")
            
            # 检查是否有异常数据
            if current_rows == 0:
                print(f"    ⚠️  空批次！")
                break
            
            # 限制测试批次数，避免运行太久
            if chunk_count >= 65:  # 应该能覆盖所有数据
                print(f"    ℹ️  达到测试上限，停止")
                break
        
        print(f"\n📈 chunk_reader测试结果:")
        print(f"  处理批次数: {chunk_count}")
        print(f"  处理行数: {total_rows:,}")
        print(f"  预期行数: {len(df_full):,}")
        print(f"  数据完整性: {total_rows / len(df_full) * 100:.1f}%")
        
        if total_rows < len(df_full):
            print(f"  ❌ 数据不完整！缺少 {len(df_full) - total_rows:,} 行")
            
            # 尝试继续读取看是否还有数据
            print(f"  🔍 尝试继续读取...")
            try:
                for chunk in chunk_reader:
                    chunk_count += 1
                    current_rows = len(chunk)
                    total_rows += current_rows
                    print(f"    额外批次 {chunk_count}: {current_rows:,} 行, 累计: {total_rows:,} 行")
                    
                    if chunk_count >= 70:  # 防止无限循环
                        break
                        
                print(f"  📊 最终统计: {total_rows:,} 行 ({total_rows / len(df_full) * 100:.1f}%)")
            except Exception as e:
                print(f"  ❌ 继续读取失败: {e}")
        else:
            print(f"  ✅ 数据完整！")
            
    except Exception as e:
        print(f"❌ chunk_reader测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chunk_reader()
