#!/usr/bin/env python3
"""
生产级LightGBM状态管理包装器 - 根本性解决方案
完整的状态备份、恢复和监控机制
"""
import pandas as pd
import numpy as np
import joblib
import gc
import time
import logging
import pickle
import json
from pathlib import Path
from typing import Optional, Union, List, Dict, Any
from datetime import datetime

class ProductionGradeLightGBMWrapper:
    """生产级LightGBM包装器 - 根本性解决方案"""
    
    def __init__(self, model_path: str, feature_engineer_path: str):
        self.model_path = Path(model_path)
        self.feature_engineer_path = Path(feature_engineer_path)
        self.logger = self._setup_logger()
        
        # 状态管理
        self.state_snapshots = []
        self.state_backup_interval = 10
        self.max_snapshots = 5
        
        # 性能监控
        self.performance_metrics = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'classifier_failures': 0,
            'regressor_failures': 0,
            'state_restorations': 0,
            'processing_times': []
        }
        
        # 加载和初始化
        self._load_components()
        self._initialize_state_management()
        
        self.logger.info("生产级LightGBM包装器初始化完成")
    
    def _setup_logger(self):
        """设置生产级日志器"""
        logger = logging.getLogger("ProductionGradeLightGBMWrapper")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件处理器
            log_file = Path("logs/production_lightgbm.log")
            log_file.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.FileHandler(log_file)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _load_components(self):
        """加载模型组件"""
        try:
            # 加载模型
            self.model_dict = joblib.load(self.model_path)
            self.zero_classifier = self.model_dict['zero_classifier']
            self.nonzero_regressor = self.model_dict['nonzero_regressor']
            
            # 加载特征工程器
            self.feature_engineer_dict = joblib.load(self.feature_engineer_path)
            self.feature_engineer = self.feature_engineer_dict['feature_engineer']
            
            self.logger.info("模型组件加载成功")
            
        except Exception as e:
            self.logger.error(f"模型组件加载失败: {e}")
            raise
    
    def _initialize_state_management(self):
        """初始化状态管理"""
        # 创建初始状态快照
        initial_snapshot = self._create_state_snapshot("initial")
        self.state_snapshots.append(initial_snapshot)
        
        self.logger.info("状态管理初始化完成")
    
    def _create_state_snapshot(self, snapshot_type: str = "checkpoint") -> Dict[str, Any]:
        """创建状态快照"""
        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'type': snapshot_type,
            'classifier_state': self._extract_component_state(self.zero_classifier),
            'regressor_state': self._extract_component_state(self.nonzero_regressor),
            'performance_metrics': self.performance_metrics.copy()
        }
        
        return snapshot
    
    def _extract_component_state(self, component) -> Dict[str, Any]:
        """提取组件状态"""
        state = {}
        
        # 关键属性
        critical_attrs = [
            '_n_classes', '_objective', '_n_features', 'n_features_in_',
            '_classes', '_n_features_in', 'feature_importances_'
        ]
        
        for attr in critical_attrs:
            if hasattr(component, attr):
                value = getattr(component, attr)
                # 处理numpy数组
                if isinstance(value, np.ndarray):
                    state[attr] = value.tolist()
                else:
                    state[attr] = value
        
        # 模型特定状态
        if hasattr(component, 'booster_'):
            try:
                # LightGBM booster状态
                state['booster_model'] = component.booster_.model_to_string()
            except:
                pass
        
        return state
    
    def _restore_component_state(self, component, target_state: Dict[str, Any]) -> bool:
        """恢复组件状态"""
        restored_attrs = 0
        
        for attr, value in target_state.items():
            if attr == 'booster_model':
                continue  # 跳过booster模型字符串
            
            try:
                # 检查当前状态
                current_value = getattr(component, attr, None)
                
                if current_value is None or (attr == '_n_classes' and current_value is None):
                    # 恢复numpy数组
                    if isinstance(value, list) and attr in ['feature_importances_', '_classes']:
                        setattr(component, attr, np.array(value))
                    else:
                        setattr(component, attr, value)
                    
                    restored_attrs += 1
                    
            except Exception as e:
                self.logger.warning(f"恢复属性 {attr} 失败: {e}")
        
        return restored_attrs > 0
    
    def _restore_from_snapshot(self, snapshot_index: int = -1) -> bool:
        """从快照恢复状态"""
        if not self.state_snapshots:
            self.logger.error("没有可用的状态快照")
            return False
        
        try:
            snapshot = self.state_snapshots[snapshot_index]
            
            # 恢复分类器状态
            classifier_restored = self._restore_component_state(
                self.zero_classifier, snapshot['classifier_state']
            )
            
            # 恢复回归器状态
            regressor_restored = self._restore_component_state(
                self.nonzero_regressor, snapshot['regressor_state']
            )
            
            if classifier_restored or regressor_restored:
                self.performance_metrics['state_restorations'] += 1
                self.logger.info(f"从快照恢复状态: {snapshot['type']} ({snapshot['timestamp']})")
                return True
            
        except Exception as e:
            self.logger.error(f"状态恢复失败: {e}")
        
        return False
    
    def _health_check(self) -> Dict[str, bool]:
        """健康检查"""
        health = {
            'classifier_healthy': True,
            'regressor_healthy': True,
            'overall_healthy': True
        }
        
        # 检查分类器
        try:
            if not hasattr(self.zero_classifier, '_n_classes') or self.zero_classifier._n_classes is None:
                health['classifier_healthy'] = False
        except:
            health['classifier_healthy'] = False
        
        # 检查回归器
        try:
            if not hasattr(self.nonzero_regressor, '_n_classes'):
                # 回归器可能没有_n_classes，这是正常的
                pass
            elif self.nonzero_regressor._n_classes is None:
                health['regressor_healthy'] = False
        except:
            health['regressor_healthy'] = False
        
        health['overall_healthy'] = health['classifier_healthy'] and health['regressor_healthy']
        
        return health
    
    def predict_with_state_management(self, X: np.ndarray, batch_id: int = 0) -> np.ndarray:
        """带状态管理的预测"""
        start_time = time.time()
        
        try:
            # 健康检查
            health = self._health_check()
            if not health['overall_healthy']:
                self.logger.warning(f"检测到状态异常，尝试恢复")
                self._restore_from_snapshot()
            
            # 第一阶段：零值分类
            try:
                zero_predictions = self.zero_classifier.predict(X)
                self.performance_metrics['successful_predictions'] += len(zero_predictions)
            except Exception as e:
                self.performance_metrics['classifier_failures'] += 1
                self.logger.error(f"分类器预测失败: {e}")
                # 尝试状态恢复
                if self._restore_from_snapshot():
                    zero_predictions = self.zero_classifier.predict(X)
                else:
                    # 安全预测
                    zero_predictions = np.zeros(len(X), dtype=int)
            
            # 初始化结果
            final_predictions = np.zeros(len(X))
            
            # 第二阶段：非零值回归
            nonzero_mask = zero_predictions == 1
            if np.any(nonzero_mask):
                X_nonzero = X[nonzero_mask]
                
                try:
                    nonzero_predictions = self.nonzero_regressor.predict(X_nonzero)
                    final_predictions[nonzero_mask] = nonzero_predictions
                except Exception as e:
                    self.performance_metrics['regressor_failures'] += 1
                    self.logger.error(f"回归器预测失败: {e}")
                    
                    # 尝试状态恢复
                    if self._restore_from_snapshot():
                        try:
                            nonzero_predictions = self.nonzero_regressor.predict(X_nonzero)
                            final_predictions[nonzero_mask] = nonzero_predictions
                        except:
                            # 最终安全策略
                            final_predictions[nonzero_mask] = 0.0
                    else:
                        final_predictions[nonzero_mask] = 0.0
            
            # 创建状态快照
            if batch_id % self.state_backup_interval == 0:
                snapshot = self._create_state_snapshot("checkpoint")
                self.state_snapshots.append(snapshot)
                
                # 限制快照数量
                if len(self.state_snapshots) > self.max_snapshots:
                    self.state_snapshots.pop(0)
            
            # 记录性能
            processing_time = time.time() - start_time
            self.performance_metrics['processing_times'].append(processing_time)
            self.performance_metrics['total_predictions'] += len(final_predictions)
            
            return final_predictions
            
        except Exception as e:
            self.logger.error(f"预测过程失败: {e}")
            # 最终安全策略
            return np.zeros(len(X))
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        processing_times = self.performance_metrics['processing_times']
        
        report = {
            'total_predictions': self.performance_metrics['total_predictions'],
            'successful_predictions': self.performance_metrics['successful_predictions'],
            'success_rate': (
                self.performance_metrics['successful_predictions'] / 
                max(self.performance_metrics['total_predictions'], 1) * 100
            ),
            'classifier_failures': self.performance_metrics['classifier_failures'],
            'regressor_failures': self.performance_metrics['regressor_failures'],
            'state_restorations': self.performance_metrics['state_restorations'],
            'avg_processing_time': np.mean(processing_times) if processing_times else 0,
            'total_snapshots': len(self.state_snapshots),
            'health_status': self._health_check()
        }
        
        return report
    
    def save_state_snapshots(self, output_dir: str):
        """保存状态快照"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        snapshot_file = output_path / f"state_snapshots_{int(time.time())}.json"
        
        with open(snapshot_file, 'w', encoding='utf-8') as f:
            json.dump(self.state_snapshots, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"状态快照已保存: {snapshot_file}")

# 使用示例
def main():
    """主函数"""
    wrapper = ProductionGradeLightGBMWrapper(
        "outputs/models/hierarchical_model.pkl",
        "outputs/models/feature_engineer.pkl"
    )
    
    # 模拟预测
    test_data = np.random.randn(1000, 19)  # 假设19个特征
    
    for i in range(10):
        predictions = wrapper.predict_with_state_management(test_data, batch_id=i)
        print(f"批次 {i+1}: 预测 {len(predictions)} 个样本")
    
    # 获取性能报告
    report = wrapper.get_performance_report()
    print(f"性能报告: {report}")
    
    # 保存状态快照
    wrapper.save_state_snapshots("./state_backups")

if __name__ == "__main__":
    main()
