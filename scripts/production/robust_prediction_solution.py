#!/usr/bin/env python3
"""
生产级健壮预测解决方案
解决LightGBM状态异常和大规模数据处理问题
"""
import pandas as pd
import numpy as np
import joblib
import gc
import logging
import psutil
from pathlib import Path
from typing import Optional, Union, List
import time
from datetime import datetime

class ProductionLightGBMWrapper:
    """生产级LightGBM包装器 - 解决状态异常问题"""
    
    def __init__(self, model_path: str, logger: Optional[logging.Logger] = None):
        self.model_path = Path(model_path)
        self.logger = logger or self._setup_logger()
        self.model = None
        self.state_backup = {}
        self.prediction_count = 0
        self.load_model()
    
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger(f"RobustPredictor_{id(self)}")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def load_model(self):
        """加载模型并备份关键状态"""
        try:
            self.logger.info(f"加载模型: {self.model_path}")
            self.model = joblib.load(self.model_path)
            self._backup_critical_state()
            self._verify_model_integrity()
            self.logger.info("模型加载完成，状态备份成功")
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def _backup_critical_state(self):
        """备份模型关键状态"""
        self.state_backup.clear()
        
        # 如果是分层模型
        if hasattr(self.model, 'zero_classifier'):
            self._backup_component_state(self.model.zero_classifier, 'zero_classifier')
        
        if hasattr(self.model, 'nonzero_regressor'):
            self._backup_component_state(self.model.nonzero_regressor, 'nonzero_regressor')
        
        # 如果是单一模型
        if hasattr(self.model, '_n_classes'):
            self.state_backup['_n_classes'] = getattr(self.model, '_n_classes')
        
        self.logger.info(f"状态备份完成: {list(self.state_backup.keys())}")
    
    def _backup_component_state(self, component, component_name: str):
        """备份组件状态"""
        critical_attrs = ['_n_classes', '_objective', '_n_features', '_classes']
        component_backup = {}
        
        for attr in critical_attrs:
            if hasattr(component, attr):
                component_backup[attr] = getattr(component, attr)
        
        if component_backup:
            self.state_backup[component_name] = component_backup
    
    def _verify_model_integrity(self):
        """验证模型完整性"""
        if self.model is None:
            raise ValueError("模型未加载")
        
        # 检查分层模型组件
        if hasattr(self.model, 'zero_classifier') and hasattr(self.model, 'nonzero_regressor'):
            self.logger.info("检测到分层模型结构")
            return True
        
        # 检查单一模型
        if hasattr(self.model, 'predict'):
            self.logger.info("检测到单一模型结构")
            return True
        
        raise ValueError("模型结构异常")
    
    def _ensure_model_state(self):
        """确保模型状态正确"""
        try:
            # 恢复分层模型状态
            if hasattr(self.model, 'zero_classifier'):
                self._restore_component_state(self.model.zero_classifier, 'zero_classifier', default_n_classes=2)
            
            if hasattr(self.model, 'nonzero_regressor'):
                self._restore_component_state(self.model.nonzero_regressor, 'nonzero_regressor', default_n_classes=1)
            
            # 恢复单一模型状态
            if hasattr(self.model, '_n_classes') and self.model._n_classes is None:
                if '_n_classes' in self.state_backup:
                    self.model._n_classes = self.state_backup['_n_classes']
                    self.logger.info("已恢复模型_n_classes状态")
        
        except Exception as e:
            self.logger.warning(f"状态恢复失败: {e}")
    
    def _restore_component_state(self, component, component_name: str, default_n_classes: int = 2):
        """恢复组件状态"""
        if component_name in self.state_backup:
            backup_state = self.state_backup[component_name]
            
            for attr, value in backup_state.items():
                if not hasattr(component, attr) or getattr(component, attr) is None:
                    setattr(component, attr, value)
                    self.logger.debug(f"恢复 {component_name}.{attr} = {value}")
        
        # 确保_n_classes不为None
        if hasattr(component, '_n_classes') and component._n_classes is None:
            component._n_classes = default_n_classes
            self.logger.info(f"设置 {component_name}._n_classes = {default_n_classes}")
    
    def predict_robust(self, X: Union[pd.DataFrame, np.ndarray], batch_size: int = 1000) -> np.ndarray:
        """健壮的批量预测"""
        if isinstance(X, pd.DataFrame):
            X_array = X.values
        else:
            X_array = X
        
        total_samples = len(X_array)
        results = []
        
        self.logger.info(f"开始健壮预测: {total_samples} 样本, 批次大小: {batch_size}")
        
        for i in range(0, total_samples, batch_size):
            batch_idx = i // batch_size + 1
            batch_end = min(i + batch_size, total_samples)
            batch = X_array[i:batch_end]
            
            try:
                # 每个批次前检查状态
                self._ensure_model_state()
                
                # 执行预测
                batch_pred = self._predict_batch(batch)
                results.extend(batch_pred)
                
                self.prediction_count += len(batch)
                
                if batch_idx % 10 == 0:
                    self.logger.info(f"已完成 {batch_idx} 批次, 累计预测 {self.prediction_count} 样本")
                
                # 每20个批次进行维护
                if batch_idx % 20 == 0:
                    self._maintenance_check()
                
            except Exception as e:
                self.logger.warning(f"批次 {batch_idx} 预测失败: {e}")
                # 使用安全预测
                batch_pred = self._safe_predict(batch)
                results.extend(batch_pred)
        
        self.logger.info(f"健壮预测完成: {len(results)} 个结果")
        return np.array(results)
    
    def _predict_batch(self, batch: np.ndarray) -> List[float]:
        """预测单个批次"""
        try:
            predictions = self.model.predict(batch)
            return predictions.tolist() if hasattr(predictions, 'tolist') else list(predictions)
        except Exception as e:
            self.logger.error(f"批次预测异常: {e}")
            raise
    
    def _safe_predict(self, batch: np.ndarray) -> List[float]:
        """安全预测（容错策略）"""
        self.logger.warning(f"使用安全预测策略，批次大小: {len(batch)}")
        # 返回零值预测作为安全策略
        return [0.0] * len(batch)
    
    def _maintenance_check(self):
        """维护检查"""
        # 内存清理
        gc.collect()
        
        # 获取内存使用情况
        memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 状态检查
        self._ensure_model_state()
        
        self.logger.info(f"维护检查完成 - 内存使用: {memory_mb:.1f}MB, 累计预测: {self.prediction_count}")
    
    def get_status(self) -> dict:
        """获取预测器状态"""
        return {
            'model_path': str(self.model_path),
            'prediction_count': self.prediction_count,
            'memory_mb': psutil.Process().memory_info().rss / 1024 / 1024,
            'state_backup_keys': list(self.state_backup.keys()),
            'model_loaded': self.model is not None
        }

class RobustPredictionService:
    """健壮预测服务 - 生产环境使用"""
    
    def __init__(self, model_path: str, feature_engineer_path: str):
        self.model_path = model_path
        self.feature_engineer_path = feature_engineer_path
        self.logger = self._setup_logger()
        
        # 初始化组件
        self.predictor = ProductionLightGBMWrapper(model_path, self.logger)
        self.feature_engineer = joblib.load(feature_engineer_path)
        
        self.logger.info("健壮预测服务初始化完成")
    
    def _setup_logger(self):
        """设置服务日志器"""
        logger = logging.getLogger("RobustPredictionService")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def predict_file(self, input_file: str, output_file: str, batch_size: int = 1000) -> dict:
        """预测文件数据"""
        start_time = time.time()
        
        self.logger.info(f"开始文件预测: {input_file} -> {output_file}")
        
        try:
            # 读取数据
            df = pd.read_csv(input_file)
            self.logger.info(f"读取数据: {len(df)} 行 × {len(df.columns)} 列")
            
            # 特征工程
            X_features = self.feature_engineer.transform(df)
            self.logger.info(f"特征工程完成: {X_features.shape}")
            
            # 健壮预测
            predictions = self.predictor.predict_robust(X_features, batch_size)
            
            # 保存结果
            result_df = df.copy()
            result_df['predicted_amount'] = predictions
            result_df.to_csv(output_file, index=False)
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'success': True,
                'input_file': input_file,
                'output_file': output_file,
                'total_samples': len(df),
                'predictions_count': len(predictions),
                'duration_seconds': duration,
                'processing_speed': len(df) / duration,
                'predictor_status': self.predictor.get_status()
            }
            
            self.logger.info(f"文件预测完成: {len(predictions)} 个预测结果, 耗时 {duration:.2f} 秒")
            return result
            
        except Exception as e:
            self.logger.error(f"文件预测失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'input_file': input_file,
                'output_file': output_file
            }

def main():
    """主函数 - 演示使用"""
    import argparse
    
    parser = argparse.ArgumentParser(description="健壮预测服务")
    parser.add_argument("--input", required=True, help="输入文件路径")
    parser.add_argument("--output", required=True, help="输出文件路径")
    parser.add_argument("--model", required=True, help="模型文件路径")
    parser.add_argument("--feature-engineer", required=True, help="特征工程器路径")
    parser.add_argument("--batch-size", type=int, default=1000, help="批次大小")
    
    args = parser.parse_args()
    
    # 创建健壮预测服务
    service = RobustPredictionService(args.model, args.feature_engineer)
    
    # 执行预测
    result = service.predict_file(args.input, args.output, args.batch_size)
    
    # 输出结果
    if result['success']:
        print(f"✅ 预测成功完成")
        print(f"📊 处理样本: {result['total_samples']:,}")
        print(f"⏱️  处理时间: {result['duration_seconds']:.2f} 秒")
        print(f"🚀 处理速度: {result['processing_speed']:.0f} 样本/秒")
        print(f"📁 输出文件: {result['output_file']}")
    else:
        print(f"❌ 预测失败: {result['error']}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
