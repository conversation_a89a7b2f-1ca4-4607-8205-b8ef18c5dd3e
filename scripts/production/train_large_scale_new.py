#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模数据模型训练脚本 (重构版)
调用 src.billing_audit.large_scale 模块实现功能
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 导入大规模处理模块
from src.billing_audit.large_scale import LargeScaleFeatureEngineer
from src.config.production_config_manager import get_config_manager


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='大规模数据模型训练')
    parser.add_argument('--input', '-i', required=True, help='输入数据文件路径')
    parser.add_argument('--output', '-o', help='模型输出目录')
    parser.add_argument('--batch-size', '-b', type=int, default=50000, help='批处理大小')
    parser.add_argument('--config', '-c', help='配置文件路径')
    
    args = parser.parse_args()
    
    try:
        print("大规模数据模型训练 (重构版)")
        print("=" * 60)
        
        # 加载配置
        if args.config:
            config_manager = get_config_manager(args.config)
        else:
            config_manager = get_config_manager()
        
        # 初始化特征工程器
        feature_engineer = LargeScaleFeatureEngineer()
        
        # 拟合特征工程器
        print(f"拟合特征工程器...")
        feature_engineer.fit_statistics(args.input, args.batch_size)
        
        # 保存特征工程器
        output_dir = Path(args.output) if args.output else project_root / "outputs" / "models"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        feature_engineer_path = output_dir / f"large_scale_feature_engineer_{timestamp}.pkl"
        feature_engineer.save_preprocessor(feature_engineer_path)
        
        print(f"特征工程器已保存: {feature_engineer_path}")
        print(f"大规模数据模型训练完成！")
        
        return True
        
    except Exception as e:
        print(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
