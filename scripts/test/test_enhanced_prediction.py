#!/usr/bin/env python3
"""
短期改进方案验证测试脚本
测试增强的predict_large_scale.py的效果
"""
import sys
import os
import time
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def test_enhanced_prediction():
    """测试增强预测功能"""
    print("🚀 短期改进方案验证测试")
    print("=" * 50)
    
    # 测试参数
    input_file = "data/input/ofrm_result.txt"
    model_path = "outputs/models/hierarchical_model_20250805_111931.pkl"
    feature_engineer_path = "outputs/models/large_scale_feature_engineer_20250805_111919.pkl"
    output_file = f"outputs/data/enhanced_test_predictions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    print(f"📊 测试参数:")
    print(f"  - 输入文件: {input_file}")
    print(f"  - 模型文件: {model_path}")
    print(f"  - 输出文件: {output_file}")
    print(f"  - 批次大小: 250行 (改进前: 500行)")
    print(f"  - 状态检查: 每5批次")
    
    try:
        # 导入增强的预测类
        from src.billing_audit.inference.predict_large_scale import LargeScalePrediction
        
        print(f"\n🔧 初始化增强预测器...")
        start_time = time.time()
        
        # 创建预测器实例（批次大小250）
        predictor = LargeScalePrediction(
            model_path=model_path,
            feature_engineer_path=feature_engineer_path,
            batch_size=250
        )
        
        init_time = time.time() - start_time
        print(f"✅ 预测器初始化完成，耗时: {init_time:.2f}秒")
        
        # 检查模型类型
        model_type = type(predictor.model).__name__
        print(f"📋 模型类型: {model_type}")
        
        # 检查状态管理属性
        print(f"🔍 状态管理配置:")
        print(f"  - 批次大小: {predictor.batch_size}")
        print(f"  - 状态检查间隔: {predictor.state_check_interval}")
        print(f"  - 回归器状态备份: {len(predictor.regressor_state_backup)} 个属性")
        
        print(f"\n🎯 开始增强预测...")
        prediction_start = time.time()
        
        # 执行预测
        result = predictor.predict_large_file(
            input_file=input_file,
            output_file=output_file,
            include_features=True
        )
        
        prediction_time = time.time() - prediction_start
        
        print(f"\n📊 预测结果分析:")
        if result:
            print(f"  ✅ 预测成功完成")
            print(f"  📈 总预测数: {result.get('total_predictions', 0):,}")
            print(f"  📦 总批次数: {result.get('total_batches', 0)}")
            print(f"  ⏱️  总耗时: {result.get('total_time', 0):.2f}秒")
            print(f"  🚀 处理速度: {result.get('processing_speed', 0):.0f} 样本/秒")
            print(f"  📊 数据成功率: {result.get('success_rate', 0):.1f}%")
            print(f"  🔧 状态恢复次数: {result.get('state_restorations', 0)}")
        else:
            print(f"  ⚠️ 预测结果为空")
        
        # 验证输出文件
        if Path(output_file).exists():
            output_df = pd.read_csv(output_file)
            input_df = pd.read_csv(input_file)
            
            print(f"\n📋 文件验证:")
            print(f"  - 输入行数: {len(input_df):,}")
            print(f"  - 输出行数: {len(output_df):,}")
            print(f"  - 数据完整性: {len(output_df)/len(input_df)*100:.1f}%")
            print(f"  - 输出列数: {len(output_df.columns)}")
            
            # 检查预测结果分布
            if 'predicted_amount' in output_df.columns:
                pred_col = 'predicted_amount'
            elif 'prediction' in output_df.columns:
                pred_col = 'prediction'
            else:
                pred_col = None
            
            if pred_col:
                zero_count = (output_df[pred_col] == 0).sum()
                nonzero_count = len(output_df) - zero_count
                
                print(f"  📊 预测分布:")
                print(f"    - 零值预测: {zero_count:,} ({zero_count/len(output_df)*100:.1f}%)")
                print(f"    - 非零值预测: {nonzero_count:,} ({nonzero_count/len(output_df)*100:.1f}%)")
                
                if nonzero_count > 0:
                    nonzero_values = output_df[output_df[pred_col] > 0][pred_col]
                    print(f"    - 非零值范围: {nonzero_values.min():.2f} - {nonzero_values.max():.2f}")
                    print(f"    - 非零值均值: {nonzero_values.mean():.2f}")
        
        # 性能对比分析
        print(f"\n📈 性能对比分析:")
        
        # 与之前的基准对比
        baseline_success_rate = 37.3  # 之前的成功率
        baseline_speed = 22271  # 之前的处理速度
        
        if result:
            current_success_rate = result.get('success_rate', 0)
            current_speed = result.get('processing_speed', 0)
            
            success_improvement = current_success_rate - baseline_success_rate
            speed_change = (current_speed - baseline_speed) / baseline_speed * 100
            
            print(f"  📊 成功率对比:")
            print(f"    - 基准成功率: {baseline_success_rate:.1f}%")
            print(f"    - 当前成功率: {current_success_rate:.1f}%")
            print(f"    - 改进幅度: {success_improvement:+.1f}个百分点")
            
            print(f"  🚀 速度对比:")
            print(f"    - 基准速度: {baseline_speed:,} 样本/秒")
            print(f"    - 当前速度: {current_speed:.0f} 样本/秒")
            print(f"    - 速度变化: {speed_change:+.1f}%")
            
            # 评估结果
            print(f"\n🎯 改进效果评估:")
            if current_success_rate >= 65:
                print(f"  ✅ 成功率目标达成: {current_success_rate:.1f}% >= 65%")
            else:
                print(f"  ⚠️ 成功率未达目标: {current_success_rate:.1f}% < 65%")
            
            if current_speed >= 15000:
                print(f"  ✅ 速度目标达成: {current_speed:.0f} >= 15,000 样本/秒")
            else:
                print(f"  ⚠️ 速度未达目标: {current_speed:.0f} < 15,000 样本/秒")
            
            if result.get('state_restorations', 0) > 0:
                print(f"  ✅ 状态恢复机制生效: {result.get('state_restorations', 0)} 次")
            else:
                print(f"  ℹ️ 状态恢复机制未触发")
        
        print(f"\n🏆 测试总结:")
        print(f"  - 总测试时间: {time.time() - start_time:.2f}秒")
        print(f"  - 输出文件: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 山西电信出账稽核AI系统v2.1.0")
    print("🚀 短期改进方案验证测试")
    print("🎯 目标: 验证批次优化、状态检查、激进重置的效果")
    print("=" * 80)
    
    success = test_enhanced_prediction()
    
    if success:
        print(f"\n✅ 短期改进方案验证测试完成")
        return 0
    else:
        print(f"\n❌ 短期改进方案验证测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
