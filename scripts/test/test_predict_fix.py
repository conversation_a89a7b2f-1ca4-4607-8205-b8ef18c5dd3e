#!/usr/bin/env python3
"""
简化的predict_large_scale.py修复验证测试
"""
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_batch_size_fix():
    """测试批次大小修复"""
    print("🧪 predict_large_scale.py修复验证测试")
    print("=" * 50)
    
    try:
        # 导入修复后的类
        from src.billing_audit.inference.predict_large_scale import LargeScalePrediction
        
        # 测试参数
        model_path = "outputs/models/hierarchical_model_20250805_111931.pkl"
        feature_engineer_path = "outputs/models/large_scale_feature_engineer_20250805_111919.pkl"
        
        print(f"📊 测试批次大小设置:")
        
        # 测试1: 不传递batch_size参数（应该使用默认250）
        print(f"\n1. 测试默认批次大小:")
        predictor1 = LargeScalePrediction(model_path, feature_engineer_path)
        print(f"   - 默认批次大小: {predictor1.batch_size}")
        
        # 测试2: 传递batch_size=250
        print(f"\n2. 测试指定批次大小250:")
        predictor2 = LargeScalePrediction(model_path, feature_engineer_path, batch_size=250)
        print(f"   - 指定批次大小: {predictor2.batch_size}")
        
        # 测试3: 传递batch_size=500
        print(f"\n3. 测试指定批次大小500:")
        predictor3 = LargeScalePrediction(model_path, feature_engineer_path, batch_size=500)
        print(f"   - 指定批次大小: {predictor3.batch_size}")
        
        # 验证结果
        print(f"\n📋 验证结果:")
        if predictor1.batch_size == 250:
            print(f"   ✅ 默认批次大小正确: {predictor1.batch_size}")
        else:
            print(f"   ❌ 默认批次大小错误: {predictor1.batch_size} (应为250)")
            
        if predictor2.batch_size == 250:
            print(f"   ✅ 指定批次大小250正确: {predictor2.batch_size}")
        else:
            print(f"   ❌ 指定批次大小250错误: {predictor2.batch_size}")
            
        if predictor3.batch_size == 500:
            print(f"   ✅ 指定批次大小500正确: {predictor3.batch_size}")
        else:
            print(f"   ❌ 指定批次大小500错误: {predictor3.batch_size}")
        
        # 检查短期改进措施
        print(f"\n🔍 检查短期改进措施:")
        print(f"   - 状态检查间隔: {predictor1.state_check_interval}")
        print(f"   - 状态恢复次数: {predictor1.state_restorations}")
        print(f"   - 回归器状态备份: {len(predictor1.regressor_state_backup)} 个属性")
        
        # 检查方法是否存在
        methods_to_check = [
            '_backup_regressor_state',
            '_restore_regressor_state', 
            '_aggressive_state_reset',
            'predict_chunk'
        ]
        
        print(f"\n🔧 检查短期改进方法:")
        for method_name in methods_to_check:
            if hasattr(predictor1, method_name):
                print(f"   ✅ {method_name}: 存在")
            else:
                print(f"   ❌ {method_name}: 缺失")
        
        print(f"\n🎯 修复验证总结:")
        all_correct = (
            predictor1.batch_size == 250 and
            predictor2.batch_size == 250 and
            predictor3.batch_size == 500 and
            predictor1.state_check_interval == 5 and
            all(hasattr(predictor1, method) for method in methods_to_check)
        )
        
        if all_correct:
            print(f"   ✅ 所有修复项目验证通过")
            return True
        else:
            print(f"   ❌ 部分修复项目验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_batch_size_fix()
    
    if success:
        print(f"\n✅ predict_large_scale.py修复验证成功")
        return 0
    else:
        print(f"\n❌ predict_large_scale.py修复验证失败")
        return 1

if __name__ == "__main__":
    exit(main())
