#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
山西电信出账稽核AI系统 - 算法支持情况分析
详细分析系统的模型训练算法支持情况和训练策略
"""

import sys
import os
import json
import importlib
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class AlgorithmSupportAnalyzer:
    """算法支持分析器"""
    
    def __init__(self):
        self.project_root = project_root
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.analysis_results = {}
        
        print("山西电信出账稽核AI系统 - 算法支持情况分析")
        print("=" * 60)
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def check_algorithm_dependencies(self):
        """检查算法依赖库"""
        print("🔍 检查算法依赖库...")
        
        dependencies = {
            'RandomForest': {
                'library': 'sklearn.ensemble',
                'class': 'RandomForestRegressor',
                'package': 'scikit-learn'
            },
            'XGBoost': {
                'library': 'xgboost',
                'class': 'XGBRegressor',
                'package': 'xgboost'
            },
            'LightGBM': {
                'library': 'lightgbm',
                'class': 'LGBMRegressor',
                'package': 'lightgbm'
            }
        }
        
        dependency_status = {}
        
        for algo_name, dep_info in dependencies.items():
            try:
                # 尝试导入库
                if algo_name == 'RandomForest':
                    from sklearn.ensemble import RandomForestRegressor
                    import sklearn
                    version = sklearn.__version__
                elif algo_name == 'XGBoost':
                    import xgboost as xgb
                    version = xgb.__version__
                elif algo_name == 'LightGBM':
                    import lightgbm as lgb
                    version = lgb.__version__
                
                dependency_status[algo_name] = {
                    'available': True,
                    'version': version,
                    'package': dep_info['package'],
                    'import_path': dep_info['library']
                }
                print(f"  ✅ {algo_name}: v{version}")
                
            except ImportError as e:
                dependency_status[algo_name] = {
                    'available': False,
                    'error': str(e),
                    'package': dep_info['package'],
                    'import_path': dep_info['library']
                }
                print(f"  ❌ {algo_name}: 不可用 - {str(e)}")
        
        self.analysis_results['dependencies'] = dependency_status
        
        available_count = sum(1 for status in dependency_status.values() if status['available'])
        print(f"\n依赖库总结: {available_count}/3 个算法可用")
        
        return dependency_status
    
    def analyze_config_support(self):
        """分析配置文件中的算法支持"""
        print("\n⚙️ 分析配置文件中的算法支持...")
        
        config_path = self.project_root / "config" / "production_config.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            model_training = config.get('model_training', {})
            
            # 分析算法配置
            algorithms = model_training.get('algorithms', [])
            default_algorithm = model_training.get('default_algorithm', '')
            hyperparameters = model_training.get('hyperparameters', {})
            
            config_analysis = {
                'supported_algorithms': algorithms,
                'default_algorithm': default_algorithm,
                'hyperparameter_configs': list(hyperparameters.keys()),
                'algorithm_count': len(algorithms),
                'hyperparameter_completeness': {}
            }
            
            print(f"  支持的算法: {algorithms}")
            print(f"  默认算法: {default_algorithm}")
            print(f"  算法数量: {len(algorithms)}")
            
            # 检查每个算法的超参数配置完整性
            for algo in algorithms:
                if algo in hyperparameters:
                    params = hyperparameters[algo]
                    param_count = len(params)
                    config_analysis['hyperparameter_completeness'][algo] = {
                        'configured': True,
                        'parameter_count': param_count,
                        'parameters': list(params.keys())
                    }
                    print(f"  {algo} 超参数: {param_count}个参数")
                else:
                    config_analysis['hyperparameter_completeness'][algo] = {
                        'configured': False,
                        'parameter_count': 0,
                        'parameters': []
                    }
                    print(f"  ❌ {algo} 缺少超参数配置")
            
            self.analysis_results['config_support'] = config_analysis
            return config_analysis
            
        except Exception as e:
            print(f"  ❌ 配置文件分析失败: {str(e)}")
            self.analysis_results['config_support'] = {'error': str(e)}
            return None
    
    def analyze_training_strategy(self):
        """分析模型训练策略"""
        print("\n🎯 分析模型训练策略...")
        
        # 检查当前训练脚本的实现
        training_script = self.project_root / "src" / "billing_audit" / "training" / "train_large_scale_model.py"
        
        strategy_analysis = {
            'current_implementation': 'single_algorithm',
            'supported_strategies': [],
            'fallback_mechanism': False,
            'multi_algorithm_comparison': False,
            'auto_selection': False
        }
        
        try:
            with open(training_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分析当前实现
            if 'RandomForestRegressor' in content:
                strategy_analysis['current_algorithm'] = 'RandomForest'
                strategy_analysis['supported_strategies'].append('RandomForest')
                print("  当前实现: RandomForest 单一算法训练")
            
            if 'XGBRegressor' in content or 'xgboost' in content:
                strategy_analysis['supported_strategies'].append('XGBoost')
            
            if 'LGBMRegressor' in content or 'lightgbm' in content:
                strategy_analysis['supported_strategies'].append('LightGBM')
            
            # 检查是否有多算法对比脚本
            multi_algo_script = self.project_root / "scripts" / "tools" / "multi_algorithm_training.py"
            if multi_algo_script.exists():
                strategy_analysis['multi_algorithm_comparison'] = True
                print("  ✅ 支持多算法对比训练")
            else:
                print("  ⚠️ 缺少多算法对比训练功能")
            
            # 检查legacy代码中的容错机制
            legacy_trainer = self.project_root / "legacy_code" / "src" / "billing_audit" / "training" / "model_trainer.py"
            if legacy_trainer.exists():
                with open(legacy_trainer, 'r', encoding='utf-8') as f:
                    legacy_content = f.read()
                if 'except' in legacy_content and ('XGBoost' in legacy_content or 'LightGBM' in legacy_content):
                    strategy_analysis['fallback_mechanism'] = True
                    print("  ✅ Legacy代码中存在算法降级机制")
            
        except Exception as e:
            print(f"  ❌ 训练策略分析失败: {str(e)}")
            strategy_analysis['error'] = str(e)
        
        self.analysis_results['training_strategy'] = strategy_analysis
        return strategy_analysis
    
    def analyze_configuration_flexibility(self):
        """分析配置灵活性"""
        print("\n🔧 分析配置灵活性...")
        
        flexibility_analysis = {
            'dynamic_algorithm_switching': False,
            'hyperparameter_completeness': 0,
            'environment_specific_configs': False,
            'runtime_configuration': False
        }
        
        try:
            # 检查配置管理器
            from src.config.production_config_manager import get_config_manager
            
            config_manager = get_config_manager()
            
            # 测试动态算法切换
            try:
                rf_params = config_manager.get_model_hyperparameters('random_forest')
                xgb_params = config_manager.get_model_hyperparameters('xgboost')
                lgb_params = config_manager.get_model_hyperparameters('lightgbm')
                
                flexibility_analysis['dynamic_algorithm_switching'] = True
                flexibility_analysis['hyperparameter_completeness'] = 3
                print("  ✅ 支持动态算法切换")
                print(f"    - RandomForest: {len(rf_params)}个参数")
                print(f"    - XGBoost: {len(xgb_params)}个参数")
                print(f"    - LightGBM: {len(lgb_params)}个参数")
                
            except Exception as e:
                print(f"  ⚠️ 动态算法切换测试失败: {str(e)}")
            
            # 检查环境变量支持
            try:
                env_setup = config_manager.get_environment_setup()
                if env_setup and len(env_setup) > 0:
                    flexibility_analysis['environment_specific_configs'] = True
                    print(f"  ✅ 支持环境特定配置 ({len(env_setup)}个环境变量)")
                else:
                    print("  ⚠️ 环境特定配置支持有限")
            except:
                print("  ❌ 环境特定配置不可用")
            
        except Exception as e:
            print(f"  ❌ 配置灵活性分析失败: {str(e)}")
            flexibility_analysis['error'] = str(e)
        
        self.analysis_results['configuration_flexibility'] = flexibility_analysis
        return flexibility_analysis
    
    def check_fallback_mechanisms(self):
        """检查容错和降级机制"""
        print("\n🛡️ 检查容错和降级机制...")
        
        fallback_analysis = {
            'algorithm_priority': [],
            'automatic_fallback': False,
            'error_handling': False,
            'graceful_degradation': False
        }
        
        # 检查配置文件中的算法优先级
        try:
            config_path = self.project_root / "config" / "production_config.json"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            algorithms = config.get('model_training', {}).get('algorithms', [])
            default_algo = config.get('model_training', {}).get('default_algorithm', '')
            
            fallback_analysis['algorithm_priority'] = algorithms
            fallback_analysis['default_algorithm'] = default_algo
            
            print(f"  算法优先级: {algorithms}")
            print(f"  默认算法: {default_algo}")
            
        except Exception as e:
            print(f"  ⚠️ 算法优先级配置检查失败: {str(e)}")
        
        # 检查legacy代码中的容错机制
        try:
            legacy_trainer = self.project_root / "legacy_code" / "src" / "billing_audit" / "training" / "model_trainer.py"
            if legacy_trainer.exists():
                with open(legacy_trainer, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有try-except降级逻辑
                if 'try:' in content and 'except' in content:
                    if 'XGBoost' in content and 'LightGBM' in content and 'RandomForest' in content:
                        fallback_analysis['automatic_fallback'] = True
                        fallback_analysis['graceful_degradation'] = True
                        print("  ✅ Legacy代码中存在完整的算法降级机制")
                        print("    - XGBoost → LightGBM → RandomForest → MockModel")
                    else:
                        print("  ⚠️ Legacy代码中存在部分容错机制")
                
                if 'MockXGBModel' in content:
                    fallback_analysis['mock_model_fallback'] = True
                    print("  ✅ 支持模拟模型降级")
            else:
                print("  ⚠️ 未找到Legacy容错实现")
        
        except Exception as e:
            print(f"  ❌ 容错机制检查失败: {str(e)}")
        
        self.analysis_results['fallback_mechanisms'] = fallback_analysis
        return fallback_analysis
    
    def generate_recommendations(self):
        """生成改进建议"""
        print("\n💡 生成改进建议...")
        
        recommendations = []
        
        # 基于依赖库状态
        deps = self.analysis_results.get('dependencies', {})
        available_algos = [name for name, info in deps.items() if info.get('available', False)]
        
        if len(available_algos) < 3:
            recommendations.append({
                'priority': 'HIGH',
                'category': '依赖库',
                'issue': f'只有{len(available_algos)}/3个算法可用',
                'solution': '安装缺失的机器学习库'
            })
        
        # 基于训练策略
        strategy = self.analysis_results.get('training_strategy', {})
        if not strategy.get('multi_algorithm_comparison', False):
            recommendations.append({
                'priority': 'MEDIUM',
                'category': '训练策略',
                'issue': '缺少多算法对比训练功能',
                'solution': '实现多算法对比训练脚本'
            })
        
        if strategy.get('current_implementation') == 'single_algorithm':
            recommendations.append({
                'priority': 'MEDIUM',
                'category': '训练策略',
                'issue': '当前只支持单一算法训练',
                'solution': '增强训练脚本支持多算法选择'
            })
        
        # 基于容错机制
        fallback = self.analysis_results.get('fallback_mechanisms', {})
        if not fallback.get('automatic_fallback', False):
            recommendations.append({
                'priority': 'HIGH',
                'category': '容错机制',
                'issue': '缺少自动算法降级机制',
                'solution': '实现算法异常时的自动降级策略'
            })
        
        # 基于配置灵活性
        flexibility = self.analysis_results.get('configuration_flexibility', {})
        if flexibility.get('hyperparameter_completeness', 0) < 3:
            recommendations.append({
                'priority': 'LOW',
                'category': '配置管理',
                'issue': '超参数配置不完整',
                'solution': '完善所有算法的超参数配置'
            })
        
        self.analysis_results['recommendations'] = recommendations
        
        for i, rec in enumerate(recommendations, 1):
            priority_icon = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}[rec['priority']]
            print(f"  {i}. {priority_icon} [{rec['category']}] {rec['issue']}")
            print(f"     解决方案: {rec['solution']}")
        
        if not recommendations:
            print("  ✅ 系统算法支持状况良好，无需改进")
        
        return recommendations
    
    def generate_analysis_report(self):
        """生成分析报告"""
        print("\n📊 生成分析报告...")
        
        report = {
            'analysis_info': {
                'timestamp': self.timestamp,
                'analysis_time': datetime.now().isoformat(),
                'project_root': str(self.project_root)
            },
            'summary': {
                'total_algorithms': 3,
                'available_algorithms': len([d for d in self.analysis_results.get('dependencies', {}).values() if d.get('available', False)]),
                'configured_algorithms': len(self.analysis_results.get('config_support', {}).get('supported_algorithms', [])),
                'recommendations_count': len(self.analysis_results.get('recommendations', []))
            },
            'detailed_analysis': self.analysis_results
        }
        
        # 保存报告
        report_dir = self.project_root / "outputs" / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        report_file = report_dir / f"algorithm_support_analysis_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"分析报告已保存: {report_file}")
        return report
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始运行算法支持情况完整分析...\n")
        
        # 运行各项分析
        self.check_algorithm_dependencies()
        self.analyze_config_support()
        self.analyze_training_strategy()
        self.analyze_configuration_flexibility()
        self.check_fallback_mechanisms()
        self.generate_recommendations()
        
        # 生成报告
        report = self.generate_analysis_report()
        
        # 输出总结
        print("\n" + "=" * 60)
        print("算法支持情况分析总结")
        print("=" * 60)
        
        summary = report['summary']
        print(f"可用算法: {summary['available_algorithms']}/{summary['total_algorithms']}")
        print(f"配置算法: {summary['configured_algorithms']}")
        print(f"改进建议: {summary['recommendations_count']}条")
        
        # 评估系统成熟度
        maturity_score = 0
        if summary['available_algorithms'] == 3:
            maturity_score += 30
        if summary['configured_algorithms'] == 3:
            maturity_score += 25
        if self.analysis_results.get('training_strategy', {}).get('multi_algorithm_comparison', False):
            maturity_score += 20
        if self.analysis_results.get('fallback_mechanisms', {}).get('automatic_fallback', False):
            maturity_score += 25
        
        print(f"\n系统算法支持成熟度: {maturity_score}/100")
        
        if maturity_score >= 80:
            print("🎉 算法支持非常完善！")
        elif maturity_score >= 60:
            print("✅ 算法支持基本完善，有改进空间")
        else:
            print("⚠️ 算法支持需要重点改进")
        
        return maturity_score >= 60

def main():
    """主函数"""
    analyzer = AlgorithmSupportAnalyzer()
    success = analyzer.run_complete_analysis()
    
    if success:
        print("\n✅ 算法支持分析完成，系统算法支持状况良好")
        return 0
    else:
        print("\n❌ 算法支持分析发现重要问题，请查看改进建议")
        return 1

if __name__ == "__main__":
    exit(main())
