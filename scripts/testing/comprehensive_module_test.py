#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
山西电信出账稽核AI系统 - 综合模块功能测试
测试各个核心模块的基本功能，不依赖numpy等可能有架构问题的库
"""

import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime
import subprocess
import importlib.util

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class ComprehensiveModuleTester:
    """综合模块功能测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_results = {}
        self.passed_tests = 0
        self.total_tests = 0
        
        # 设置日志
        self.setup_logging()
        
        print("山西电信出账稽核AI系统 - 综合模块功能测试")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"项目根目录: {self.project_root}")
        print()
    
    def setup_logging(self):
        """设置日志"""
        log_dir = self.project_root / "outputs" / "testing"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"module_test_{self.timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def test_module_import(self, module_path, module_name):
        """测试模块导入"""
        self.total_tests += 1
        try:
            # 尝试导入模块
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            if spec is None:
                raise ImportError(f"无法找到模块规格: {module_path}")
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            self.test_results[module_name] = {
                'status': 'PASS',
                'message': '模块导入成功',
                'path': str(module_path)
            }
            self.passed_tests += 1
            print(f"✅ {module_name}: 导入成功")
            return True
            
        except Exception as e:
            self.test_results[module_name] = {
                'status': 'FAIL',
                'message': f'导入失败: {str(e)}',
                'path': str(module_path)
            }
            print(f"❌ {module_name}: 导入失败 - {str(e)}")
            return False
    
    def test_config_manager(self):
        """测试配置管理器"""
        print("\n🔧 测试配置管理器...")
        self.total_tests += 1
        
        try:
            from src.config.production_config_manager import get_config_manager
            
            # 测试配置管理器基本功能
            config_manager = get_config_manager()
            
            # 测试基本配置获取
            batch_size = config_manager.get_batch_size()
            thresholds = config_manager.get_judgment_thresholds()
            
            if batch_size > 0 and isinstance(thresholds, dict):
                self.test_results['config_manager'] = {
                    'status': 'PASS',
                    'message': f'配置管理器正常，批次大小: {batch_size}',
                    'details': {
                        'batch_size': batch_size,
                        'thresholds_count': len(thresholds)
                    }
                }
                self.passed_tests += 1
                print(f"✅ 配置管理器: 正常工作")
                print(f"   - 批次大小: {batch_size}")
                print(f"   - 判定阈值: {len(thresholds)}个参数")
                return True
            else:
                raise ValueError("配置值异常")
                
        except Exception as e:
            self.test_results['config_manager'] = {
                'status': 'FAIL',
                'message': f'配置管理器测试失败: {str(e)}'
            }
            print(f"❌ 配置管理器: 测试失败 - {str(e)}")
            return False
    
    def test_file_structure(self):
        """测试文件结构完整性"""
        print("\n📁 测试文件结构...")
        
        # 关键文件和目录
        critical_paths = [
            "src/config/production_config_manager.py",
            "src/billing_audit/preprocessing/large_scale_feature_engineer.py",
            "src/billing_audit/training/train_large_scale_model.py",
            "src/billing_audit/inference/large_scale_billing_judge.py",
            "src/billing_audit/inference/predict_large_scale.py",
            "src/billing_audit/models/large_scale_model_evaluation.py",
            "config/production_config.json",
            "outputs/",
            "scripts/",
            "docs/"
        ]
        
        missing_files = []
        existing_files = []
        
        for path_str in critical_paths:
            path = self.project_root / path_str
            if path.exists():
                existing_files.append(path_str)
            else:
                missing_files.append(path_str)
        
        self.total_tests += 1
        if len(missing_files) == 0:
            self.test_results['file_structure'] = {
                'status': 'PASS',
                'message': '所有关键文件存在',
                'existing_files': existing_files
            }
            self.passed_tests += 1
            print(f"✅ 文件结构: 完整 ({len(existing_files)}个关键路径)")
        else:
            self.test_results['file_structure'] = {
                'status': 'FAIL',
                'message': f'缺少{len(missing_files)}个关键文件',
                'missing_files': missing_files,
                'existing_files': existing_files
            }
            print(f"❌ 文件结构: 缺少文件")
            for missing in missing_files:
                print(f"   - 缺少: {missing}")
    
    def test_scripts_executable(self):
        """测试脚本可执行性"""
        print("\n🔧 测试脚本可执行性...")
        
        # 关键脚本
        scripts_to_test = [
            "scripts/production/setup_production_env.sh",
            "scripts/testing/comprehensive_module_test.py",
            "scripts/tools/generate_mock_data.py",
            "scripts/validation/validate_config.py"
        ]
        
        executable_scripts = []
        non_executable_scripts = []
        
        for script_path in scripts_to_test:
            full_path = self.project_root / script_path
            if full_path.exists():
                if full_path.suffix == '.py':
                    # Python脚本检查语法
                    try:
                        result = subprocess.run([
                            sys.executable, '-m', 'py_compile', str(full_path)
                        ], capture_output=True, text=True)
                        if result.returncode == 0:
                            executable_scripts.append(script_path)
                        else:
                            non_executable_scripts.append(f"{script_path}: 语法错误")
                    except Exception as e:
                        non_executable_scripts.append(f"{script_path}: {str(e)}")
                else:
                    # Shell脚本检查存在性
                    executable_scripts.append(script_path)
            else:
                non_executable_scripts.append(f"{script_path}: 文件不存在")
        
        self.total_tests += 1
        if len(non_executable_scripts) == 0:
            self.test_results['scripts_executable'] = {
                'status': 'PASS',
                'message': '所有脚本可执行',
                'executable_scripts': executable_scripts
            }
            self.passed_tests += 1
            print(f"✅ 脚本可执行性: 正常 ({len(executable_scripts)}个脚本)")
        else:
            self.test_results['scripts_executable'] = {
                'status': 'FAIL',
                'message': f'{len(non_executable_scripts)}个脚本有问题',
                'executable_scripts': executable_scripts,
                'non_executable_scripts': non_executable_scripts
            }
            print(f"❌ 脚本可执行性: 有问题")
            for issue in non_executable_scripts:
                print(f"   - {issue}")
    
    def test_core_modules(self):
        """测试核心模块导入"""
        print("\n📦 测试核心模块导入...")
        
        # 核心模块列表
        core_modules = [
            ("src/config/production_config_manager.py", "production_config_manager"),
            ("src/billing_audit/inference/judgment_result.py", "judgment_result"),
            ("src/utils/logger.py", "logger"),
            ("src/utils/data_utils.py", "data_utils")
        ]
        
        for module_path, module_name in core_modules:
            full_path = self.project_root / module_path
            if full_path.exists():
                self.test_module_import(full_path, module_name)
            else:
                self.total_tests += 1
                self.test_results[module_name] = {
                    'status': 'FAIL',
                    'message': '模块文件不存在',
                    'path': str(full_path)
                }
                print(f"❌ {module_name}: 文件不存在")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")
        
        # 计算成功率
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        report = {
            'test_info': {
                'timestamp': self.timestamp,
                'test_time': datetime.now().isoformat(),
                'project_root': str(self.project_root)
            },
            'summary': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.total_tests - self.passed_tests,
                'success_rate': round(success_rate, 2)
            },
            'test_results': self.test_results
        }
        
        # 保存报告
        report_dir = self.project_root / "outputs" / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        report_file = report_dir / f"module_test_report_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"测试报告已保存: {report_file}")
        return report
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始运行综合模块功能测试...\n")
        
        # 运行各项测试
        self.test_file_structure()
        self.test_config_manager()
        self.test_core_modules()
        self.test_scripts_executable()
        
        # 生成报告
        report = self.generate_test_report()
        
        # 输出总结
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.total_tests - self.passed_tests}")
        print(f"成功率: {report['summary']['success_rate']}%")
        
        if report['summary']['success_rate'] >= 80:
            print("\n🎉 系统模块功能基本正常！")
            return True
        else:
            print("\n⚠️ 系统存在一些问题，需要修复")
            return False

def main():
    """主函数"""
    tester = ComprehensiveModuleTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 综合模块测试完成，系统状态良好")
        return 0
    else:
        print("\n❌ 综合模块测试发现问题，请检查失败项")
        return 1

if __name__ == "__main__":
    exit(main())
