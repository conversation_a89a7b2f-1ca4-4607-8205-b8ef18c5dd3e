#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心功能测试脚本 - 专门测试不依赖numpy的核心功能
"""

import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

class CoreFunctionalityTester:
    """核心功能测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_results = {}
        self.passed_tests = 0
        self.total_tests = 0
        
        print("山西电信出账稽核AI系统 - 核心功能测试")
        print("=" * 50)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def test_config_manager_comprehensive(self):
        """全面测试配置管理器"""
        print("🔧 测试配置管理器全面功能...")
        self.total_tests += 1
        
        try:
            from src.config.production_config_manager import get_config_manager, ProductionConfigManager
            
            # 测试单例模式
            cm1 = get_config_manager()
            cm2 = get_config_manager()
            assert cm1 is cm2, "单例模式失效"
            
            # 测试基本配置获取
            batch_size = cm1.get_batch_size()
            assert batch_size > 0, f"批次大小异常: {batch_size}"
            
            # 测试判定阈值
            thresholds = cm1.get_judgment_thresholds()
            assert isinstance(thresholds, dict), "判定阈值格式错误"
            assert len(thresholds) > 0, "判定阈值为空"
            
            # 测试特征列获取
            feature_columns = cm1.get_feature_columns('fixed_fee')
            assert isinstance(feature_columns, list), "特征列格式错误"
            assert len(feature_columns) > 0, "特征列为空"
            
            # 测试模型超参数
            model_params = cm1.get_model_hyperparameters('random_forest')
            assert isinstance(model_params, dict), "模型参数格式错误"
            assert len(model_params) > 0, "模型参数为空"
            
            # 测试环境变量设置
            env_vars = cm1.get_environment_setup()
            assert isinstance(env_vars, dict), "环境变量格式错误"
            
            self.test_results['config_manager_comprehensive'] = {
                'status': 'PASS',
                'message': '配置管理器全面功能正常',
                'details': {
                    'batch_size': batch_size,
                    'thresholds_count': len(thresholds),
                    'feature_columns_count': len(feature_columns),
                    'model_params_count': len(model_params),
                    'env_vars_count': len(env_vars)
                }
            }
            self.passed_tests += 1
            print("✅ 配置管理器全面功能测试通过")
            print(f"   - 单例模式: 正常")
            print(f"   - 批次大小: {batch_size}")
            print(f"   - 判定阈值: {len(thresholds)}个")
            print(f"   - 特征列: {len(feature_columns)}个")
            print(f"   - 模型参数: {len(model_params)}个")
            print(f"   - 环境变量: {len(env_vars)}个")
            return True
            
        except Exception as e:
            self.test_results['config_manager_comprehensive'] = {
                'status': 'FAIL',
                'message': f'配置管理器测试失败: {str(e)}'
            }
            print(f"❌ 配置管理器全面功能测试失败: {str(e)}")
            return False
    
    def test_judgment_result_module(self):
        """测试判定结果模块"""
        print("\n⚖️ 测试判定结果模块...")
        self.total_tests += 1
        
        try:
            from src.billing_audit.inference.judgment_result import JudgmentResult
            
            # 创建判定结果实例
            result = JudgmentResult(
                user_id="test_user_001",
                predicted_fee=100.50,
                actual_fee=98.75,
                judgment="合理",
                confidence=0.95,
                details={"test": "data"}
            )
            
            # 测试基本属性
            assert result.user_id == "test_user_001", "用户ID设置错误"
            assert result.predicted_fee == 100.50, "预测费用设置错误"
            assert result.actual_fee == 98.75, "实际费用设置错误"
            assert result.judgment == "合理", "判定结果设置错误"
            assert result.confidence == 0.95, "置信度设置错误"
            
            # 测试转换为字典
            result_dict = result.to_dict()
            assert isinstance(result_dict, dict), "转换字典失败"
            assert "user_id" in result_dict, "字典缺少用户ID"
            assert "timestamp" in result_dict, "字典缺少时间戳"
            
            # 测试JSON序列化
            json_str = result.to_json()
            assert isinstance(json_str, str), "JSON序列化失败"
            
            # 测试从字典创建
            new_result = JudgmentResult.from_dict(result_dict)
            assert new_result.user_id == result.user_id, "从字典创建失败"
            
            self.test_results['judgment_result_module'] = {
                'status': 'PASS',
                'message': '判定结果模块功能正常',
                'details': {
                    'basic_attributes': 'OK',
                    'dict_conversion': 'OK',
                    'json_serialization': 'OK',
                    'from_dict_creation': 'OK'
                }
            }
            self.passed_tests += 1
            print("✅ 判定结果模块测试通过")
            print(f"   - 基本属性: 正常")
            print(f"   - 字典转换: 正常")
            print(f"   - JSON序列化: 正常")
            print(f"   - 从字典创建: 正常")
            return True
            
        except Exception as e:
            self.test_results['judgment_result_module'] = {
                'status': 'FAIL',
                'message': f'判定结果模块测试失败: {str(e)}'
            }
            print(f"❌ 判定结果模块测试失败: {str(e)}")
            return False
    
    def test_project_structure(self):
        """测试项目结构完整性"""
        print("\n📁 测试项目结构完整性...")
        self.total_tests += 1
        
        # 关键目录结构
        required_structure = {
            "src/": ["config/", "billing_audit/", "utils/", "api/"],
            "src/billing_audit/": ["preprocessing/", "training/", "inference/", "models/"],
            "config/": ["production_config.json"],
            "scripts/": ["testing/", "tools/", "validation/", "production/"],
            "docs/": ["core/", "guides/", "technical/", "reports/", "archive/"],
            "outputs/": []  # 可以为空，但目录应该存在
        }
        
        missing_items = []
        existing_items = []
        
        for base_dir, required_items in required_structure.items():
            base_path = self.project_root / base_dir
            if not base_path.exists():
                missing_items.append(base_dir)
                continue
            
            existing_items.append(base_dir)
            
            for item in required_items:
                item_path = base_path / item
                if item_path.exists():
                    existing_items.append(f"{base_dir}{item}")
                else:
                    missing_items.append(f"{base_dir}{item}")
        
        if len(missing_items) == 0:
            self.test_results['project_structure'] = {
                'status': 'PASS',
                'message': '项目结构完整',
                'existing_items': existing_items
            }
            self.passed_tests += 1
            print(f"✅ 项目结构完整 ({len(existing_items)}个项目)")
        else:
            self.test_results['project_structure'] = {
                'status': 'FAIL',
                'message': f'项目结构不完整，缺少{len(missing_items)}个项目',
                'missing_items': missing_items,
                'existing_items': existing_items
            }
            print(f"❌ 项目结构不完整")
            for missing in missing_items:
                print(f"   - 缺少: {missing}")
    
    def test_config_files(self):
        """测试配置文件"""
        print("\n⚙️ 测试配置文件...")
        self.total_tests += 1
        
        try:
            # 测试生产配置文件
            prod_config_path = self.project_root / "config" / "production_config.json"
            assert prod_config_path.exists(), "生产配置文件不存在"
            
            with open(prod_config_path, 'r', encoding='utf-8') as f:
                prod_config = json.load(f)
            
            # 验证配置文件结构
            required_sections = ['project', 'data_sources', 'model_training', 'judgment_thresholds']
            for section in required_sections:
                assert section in prod_config, f"配置文件缺少{section}节"
            
            # 验证项目信息
            project_info = prod_config['project']
            assert 'name' in project_info, "项目信息缺少名称"
            assert 'version' in project_info, "项目信息缺少版本"
            
            self.test_results['config_files'] = {
                'status': 'PASS',
                'message': '配置文件格式正确',
                'details': {
                    'production_config': 'OK',
                    'sections_count': len(prod_config),
                    'project_name': project_info.get('name', 'Unknown'),
                    'project_version': project_info.get('version', 'Unknown')
                }
            }
            self.passed_tests += 1
            print("✅ 配置文件测试通过")
            print(f"   - 生产配置: 正常")
            print(f"   - 配置节数: {len(prod_config)}")
            print(f"   - 项目名称: {project_info.get('name', 'Unknown')}")
            print(f"   - 项目版本: {project_info.get('version', 'Unknown')}")
            return True
            
        except Exception as e:
            self.test_results['config_files'] = {
                'status': 'FAIL',
                'message': f'配置文件测试失败: {str(e)}'
            }
            print(f"❌ 配置文件测试失败: {str(e)}")
            return False
    
    def test_documentation_structure(self):
        """测试文档结构"""
        print("\n📚 测试文档结构...")
        self.total_tests += 1
        
        # 关键文档
        key_docs = [
            "README.md",
            "docs/文档中心.md",
            "docs/core/文档索引.md",
            "docs/core/技术规格文档.md",
            "docs/guides/快速开始指南.md",
            "docs/technical/配置管理改进.md"
        ]
        
        existing_docs = []
        missing_docs = []
        
        for doc in key_docs:
            doc_path = self.project_root / doc
            if doc_path.exists():
                existing_docs.append(doc)
            else:
                missing_docs.append(doc)
        
        if len(missing_docs) == 0:
            self.test_results['documentation_structure'] = {
                'status': 'PASS',
                'message': '文档结构完整',
                'existing_docs': existing_docs
            }
            self.passed_tests += 1
            print(f"✅ 文档结构完整 ({len(existing_docs)}个关键文档)")
        else:
            self.test_results['documentation_structure'] = {
                'status': 'FAIL',
                'message': f'文档结构不完整，缺少{len(missing_docs)}个文档',
                'missing_docs': missing_docs,
                'existing_docs': existing_docs
            }
            print(f"❌ 文档结构不完整")
            for missing in missing_docs:
                print(f"   - 缺少: {missing}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        report = {
            'test_info': {
                'test_name': '核心功能测试',
                'timestamp': self.timestamp,
                'test_time': datetime.now().isoformat(),
                'project_root': str(self.project_root)
            },
            'summary': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.total_tests - self.passed_tests,
                'success_rate': round(success_rate, 2)
            },
            'test_results': self.test_results
        }
        
        # 保存报告
        report_dir = self.project_root / "outputs" / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        report_file = report_dir / f"core_functionality_test_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"测试报告已保存: {report_file}")
        return report
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始运行核心功能测试...\n")
        
        # 运行各项测试
        self.test_project_structure()
        self.test_config_files()
        self.test_config_manager_comprehensive()
        self.test_judgment_result_module()
        self.test_documentation_structure()
        
        # 生成报告
        report = self.generate_test_report()
        
        # 输出总结
        print("\n" + "=" * 50)
        print("核心功能测试总结")
        print("=" * 50)
        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.total_tests - self.passed_tests}")
        print(f"成功率: {report['summary']['success_rate']}%")
        
        if report['summary']['success_rate'] >= 90:
            print("\n🎉 核心功能完全正常！")
            return True
        elif report['summary']['success_rate'] >= 75:
            print("\n✅ 核心功能基本正常，有少量问题")
            return True
        else:
            print("\n⚠️ 核心功能存在较多问题，需要修复")
            return False

def main():
    """主函数"""
    tester = CoreFunctionalityTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 核心功能测试完成，系统核心功能正常")
        return 0
    else:
        print("\n❌ 核心功能测试发现问题，请检查失败项")
        return 1

if __name__ == "__main__":
    exit(main())
