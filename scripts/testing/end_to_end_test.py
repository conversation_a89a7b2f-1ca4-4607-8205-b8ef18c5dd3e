#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端系统测试脚本
演示完整的收费稽核AI系统工作流程
"""

import sys
from pathlib import Path
import json
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.billing_audit.preprocessing import BillingDataPreprocessor, FeatureEngineer
from src.billing_audit.training import BillingModelTrainer
from src.billing_audit.models import ModelEvaluator
from src.billing_audit.inference import ModelPredictor, BillingJudge
from src.utils import get_logger

logger = get_logger(__name__)


def test_complete_workflow():
    """测试完整的工作流程"""
    print("开始端到端系统测试...")
    print("="*80)
    
    try:
        # 步骤1: 数据预处理和特征工程
        print("步骤1: 数据预处理和特征工程")
        print("-" * 50)
        
        preprocessor = BillingDataPreprocessor('fixed_fee')
        X, y, passthrough = preprocessor.process()
        
        feature_engineer = FeatureEngineer('fixed_fee')
        X_engineered = feature_engineer.fit_transform(X)
        
        print(f"数据预处理完成:")
        print(f"  - 原始数据: {X.shape}")
        print(f"  - 工程后数据: {X_engineered.shape}")
        print(f"  - 目标变量: {len(y)}")
        
        # 步骤2: 模型训练
        print(f"\n步骤2: 模型训练")
        print("-" * 50)
        
        trainer = BillingModelTrainer('fixed_fee')
        X_train, X_test, y_train, y_test = trainer.split_data(X_engineered, y)
        
        model = trainer.train_model(X_train, y_train)
        
        print(f"模型训练完成:")
        print(f"  - 训练集: {X_train.shape}")
        print(f"  - 测试集: {X_test.shape}")
        print(f"  - 训练时间: {trainer.training_history.get('training_time_seconds', 0):.2f}秒")
        
        # 步骤3: 模型评估
        print(f"\n步骤3: 模型评估")
        print("-" * 50)
        
        evaluator = ModelEvaluator('fixed_fee')
        # 获取对应的透传数据
        test_passthrough = passthrough.loc[y_test.index] if len(passthrough) > 0 else None

        evaluation_result = evaluator.comprehensive_evaluation(
            model, X_test, y_test, test_passthrough
        )
        
        metrics = evaluation_result['regression_metrics']
        business = evaluation_result['business_metrics']
        
        print(f"模型评估完成:")
        print(f"  - MAE: {metrics['mae']:.4f}")
        print(f"  - R²: {metrics['r2']:.4f}")
        print(f"  - 业务准确率: {business['accuracy_rate']:.2f}%")
        print(f"  - 综合得分: {evaluation_result['overall_score']:.2f}")
        
        # 步骤4: 保存模型
        print(f"\n步骤4: 保存模型")
        print("-" * 50)
        
        model_path = trainer.save_model()
        feature_engineer.save_transformers(str(Path(model_path).parent))
        
        print(f"模型保存完成:")
        print(f"  - 模型路径: {model_path}")
        
        # 步骤5: 模型推理测试
        print(f"\n🔮 步骤5: 模型推理测试")
        print("-" * 50)
        
        predictor = ModelPredictor('fixed_fee', model_path)
        
        # 使用测试数据的一个样本进行推理
        test_sample = X.iloc[0:1].to_dict('records')[0]
        actual_amount = y.iloc[0]
        
        prediction_result = predictor.predict_with_details(test_sample)
        predicted_amount = prediction_result['predictions'][0]
        
        print(f"模型推理完成:")
        print(f"  - 实际金额: {actual_amount:.2f}")
        print(f"  - 预测金额: {predicted_amount:.2f}")
        print(f"  - 绝对误差: {abs(predicted_amount - actual_amount):.2f}")
        
        # 步骤6: 收费判定测试
        print(f"\n⚖️ 步骤6: 收费判定测试")
        print("-" * 50)
        
        judge = BillingJudge('fixed_fee')
        
        judgment_result = judge.judge_single(test_sample, actual_amount)
        
        print(f"收费判定完成:")
        print(f"  - 判定结果: {judgment_result['judgment']}")
        print(f"  - 置信度: {judgment_result['confidence_score']:.3f}")
        print(f"  - 相对误差: {judgment_result['relative_error']:.4f}")
        
        # 步骤7: 批量判定测试
        print(f"\n步骤7: 批量判定测试")
        print("-" * 50)
        
        # 准备批量测试数据
        batch_records = []
        for i in range(min(10, len(X))):
            billing_data = X.iloc[i].to_dict()
            actual_amount = y.iloc[i]
            batch_records.append({
                'billing_data': billing_data,
                'actual_amount': actual_amount
            })
        
        batch_results = judge.judge_batch(batch_records)
        
        # 统计批量结果
        reasonable_count = len([r for r in batch_results if r.get('judgment') == 'reasonable'])
        unreasonable_count = len([r for r in batch_results if r.get('judgment') == 'unreasonable'])
        uncertain_count = len([r for r in batch_results if r.get('judgment') == 'uncertain'])
        
        print(f"批量判定完成:")
        print(f"  - 总样本数: {len(batch_results)}")
        print(f"  - 合理: {reasonable_count}")
        print(f"  - 不合理: {unreasonable_count}")
        print(f"  - 不确定: {uncertain_count}")
        print(f"  - 合理率: {reasonable_count/len(batch_results)*100:.1f}%")
        
        # 步骤8: 生成报告
        print(f"\n步骤8: 生成系统报告")
        print("-" * 50)
        
        # 生成综合报告
        system_report = {
            'system_info': {
                'test_timestamp': pd.Timestamp.now().isoformat(),
                'fee_type': 'fixed_fee',
                'workflow_status': 'success'
            },
            'data_processing': {
                'original_samples': X.shape[0],
                'original_features': X.shape[1],
                'engineered_features': X_engineered.shape[1],
                'target_variable_range': {
                    'min': float(y.min()),
                    'max': float(y.max()),
                    'mean': float(y.mean())
                }
            },
            'model_performance': {
                'mae': metrics['mae'],
                'r2': metrics['r2'],
                'business_accuracy': business['accuracy_rate'],
                'overall_score': evaluation_result['overall_score']
            },
            'inference_test': {
                'sample_prediction': {
                    'actual': float(actual_amount),
                    'predicted': float(predicted_amount),
                    'absolute_error': float(abs(predicted_amount - actual_amount))
                },
                'judgment': judgment_result['judgment'],
                'confidence': judgment_result['confidence_score']
            },
            'batch_judgment': {
                'total_samples': len(batch_results),
                'reasonable_count': reasonable_count,
                'unreasonable_count': unreasonable_count,
                'uncertain_count': uncertain_count,
                'reasonable_rate': reasonable_count/len(batch_results)*100
            }
        }
        
        # 保存报告
        reports_dir = project_root / "outputs" / "reports"
        reports_dir.mkdir(parents=True, exist_ok=True)

        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = reports_dir / f"end_to_end_test_report_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(system_report, f, ensure_ascii=False, indent=2)
        
        print(f"系统报告生成完成:")
        print(f"  - 报告文件: {report_file}")
        
        # 输出总结
        print(f"\n端到端测试总结")
        print("="*80)
        print(f"所有步骤成功完成!")
        print(f"系统性能:")
        print(f"  - 模型准确性 (R²): {metrics['r2']:.4f}")
        print(f"  - 业务准确率: {business['accuracy_rate']:.2f}%")
        print(f"  - 批量判定合理率: {reasonable_count/len(batch_results)*100:.1f}%")
        print(f"系统已就绪，可用于生产环境!")
        
        return True
        
    except Exception as e:
        print(f"端到端测试失败: {e}")
        logger.exception("端到端测试异常")
        return False


def main():
    """主函数"""
    success = test_complete_workflow()
    
    if success:
        print(f"\n恭喜！收费稽核AI系统端到端测试成功!")
        print(f"系统包含以下完整功能:")
        print(f"  数据预处理和特征工程")
        print(f"  机器学习模型训练")
        print(f"  模型评估和验证")
        print(f"  模型推理和预测")
        print(f"  收费合理性判定")
        print(f"  批量处理和报告生成")
        return True
    else:
        print(f"\n端到端测试失败，请检查系统配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
