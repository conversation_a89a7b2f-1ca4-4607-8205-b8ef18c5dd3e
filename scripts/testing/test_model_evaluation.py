#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估和验证测试脚本
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.billing_audit.models import ModelEvaluator, ModelValidator
from src.utils import get_logger

logger = get_logger(__name__)


class MockModel:
    """模拟模型类（用于测试）"""

    def __init__(self, **params):
        self.params = params
        self.is_fitted = False

    def get_params(self, deep=True):
        """获取参数（sklearn接口）"""
        return self.params.copy()

    def set_params(self, **params):
        """设置参数（sklearn接口）"""
        self.params.update(params)
        return self

    def fit(self, X, y):
        """模拟训练"""
        self.is_fitted = True
        return self

    def predict(self, X):
        """模拟预测"""
        if not self.is_fitted:
            raise ValueError("模型未训练")

        # 生成与真实值相近的预测值（添加一些噪声）
        np.random.seed(42)
        base_values = np.random.uniform(10, 100, len(X))
        noise = np.random.normal(0, 5, len(X))
        return base_values + noise


def create_mock_data(n_samples: int = 1000) -> tuple:
    """创建模拟数据"""
    np.random.seed(42)
    
    # 创建特征数据
    X = pd.DataFrame({
        'feature1': np.random.normal(0, 1, n_samples),
        'feature2': np.random.uniform(0, 10, n_samples),
        'feature3': np.random.randint(0, 5, n_samples),
        'feature4': np.random.exponential(2, n_samples)
    })
    
    # 创建目标变量（基于特征的线性组合加噪声）
    y = pd.Series(
        10 + 2 * X['feature1'] + 3 * X['feature2'] + 
        1.5 * X['feature3'] + 0.5 * X['feature4'] + 
        np.random.normal(0, 2, n_samples)
    )
    
    # 创建透传数据
    passthrough = pd.DataFrame({
        'user_id': [f'user_{i:04d}' for i in range(n_samples)],
        'order_id': [f'order_{i:06d}' for i in range(n_samples)]
    })
    
    return X, y, passthrough


def test_model_evaluator():
    """测试模型评估器"""
    print("测试模型评估器...")
    
    try:
        # 创建模拟数据
        X, y, passthrough = create_mock_data(500)
        
        # 划分训练集和测试集
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        passthrough_test = passthrough[split_idx:]
        
        # 训练模拟模型
        model = MockModel()
        model.fit(X_train, y_train)
        
        # 创建评估器
        evaluator = ModelEvaluator('fixed_fee')
        
        # 综合评估
        evaluation_result = evaluator.comprehensive_evaluation(
            model, X_test, y_test, passthrough_test
        )
        
        print(f"模型评估成功:")
        print(f"  - 测试样本数: {evaluation_result['model_info']['test_samples']}")
        print(f"  - MAE: {evaluation_result['regression_metrics']['mae']:.4f}")
        print(f"  - R²: {evaluation_result['regression_metrics']['r2']:.4f}")
        print(f"  - 业务准确率: {evaluation_result['business_metrics']['accuracy_rate']:.2f}%")
        print(f"  - 综合得分: {evaluation_result['overall_score']:.2f}")
        
        # 保存评估结果
        save_path = evaluator.save_evaluation_results()
        print(f"  - 结果已保存: {save_path}")
        
        # 显示评估摘要
        print(f"\n评估摘要:")
        print(evaluator.get_evaluation_summary())
        
        return True
        
    except Exception as e:
        print(f"模型评估测试失败: {e}")
        logger.exception("模型评估测试异常")
        return False


def test_model_validator():
    """测试模型验证器"""
    print("\n测试模型验证器...")
    
    try:
        # 创建模拟数据
        X, y, _ = create_mock_data(300)
        
        # 创建验证器
        validator = ModelValidator('discount')
        
        # 综合验证
        validation_result = validator.comprehensive_validation(
            MockModel, X, y, model_params={'param1': 1.0}
        )
        
        print(f"模型验证成功:")
        print(f"  - 样本数: {validation_result['validation_info']['samples']}")
        print(f"  - 交叉验证得分: {validation_result['cross_validation']['mean_score']:.4f} ± {validation_result['cross_validation']['std_score']:.4f}")
        print(f"  - 稳定性得分: {validation_result['stability_test']['stability_score']:.2f}")
        print(f"  - 综合验证得分: {validation_result['overall_validation_score']:.2f}")
        
        # 保存验证结果
        save_path = validator.save_validation_results()
        print(f"  - 结果已保存: {save_path}")
        
        # 测试数据漂移检测
        print(f"\n测试数据漂移检测...")
        X_new = X.copy()
        X_new['feature1'] = X_new['feature1'] + 2  # 人为制造漂移
        
        drift_result = validator.data_drift_detection(X, X_new)
        print(f"  - 漂移特征数: {drift_result['drifted_features']}")
        print(f"  - 漂移得分: {drift_result['drift_score']:.2f}%")
        print(f"  - 整体漂移: {'是' if drift_result['overall_drift_detected'] else '否'}")
        
        return True
        
    except Exception as e:
        print(f"模型验证测试失败: {e}")
        logger.exception("模型验证测试异常")
        return False


def test_metrics_calculation():
    """测试指标计算"""
    print("\n测试指标计算...")
    
    try:
        # 创建简单的测试数据
        y_true = pd.Series([1.0, 2.0, 3.0, 4.0, 5.0])
        y_pred = np.array([1.1, 1.9, 3.2, 3.8, 5.1])
        
        evaluator = ModelEvaluator('fixed_fee')
        
        # 测试回归指标
        regression_metrics = evaluator.calculate_regression_metrics(y_true, y_pred)
        print(f"回归指标计算成功:")
        print(f"  - MAE: {regression_metrics['mae']:.4f}")
        print(f"  - RMSE: {regression_metrics['rmse']:.4f}")
        print(f"  - R²: {regression_metrics['r2']:.4f}")
        
        # 测试业务指标
        business_metrics = evaluator.calculate_business_metrics(y_true, y_pred)
        print(f"业务指标计算成功:")
        print(f"  - 准确率: {business_metrics['accuracy_rate']:.2f}%")
        print(f"  - 准确样本数: {business_metrics['accurate_samples']}")
        
        # 测试误差分析
        error_analysis = evaluator.analyze_prediction_errors(y_true, y_pred)
        print(f"误差分析成功:")
        print(f"  - 平均误差: {error_analysis['error_statistics']['mean_error']:.4f}")
        print(f"  - 异常样本数: {error_analysis['anomaly_analysis']['anomaly_count']}")
        
        return True
        
    except Exception as e:
        print(f"指标计算测试失败: {e}")
        logger.exception("指标计算测试异常")
        return False


def main():
    """主函数"""
    print("开始模型评估和验证测试...")
    
    tests = [
        ("指标计算", test_metrics_calculation),
        ("模型评估器", test_model_evaluator),
        ("模型验证器", test_model_validator)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"{test_name} 测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("所有测试通过！模型评估和验证模块功能正常")
        print("\n下一步:")
        print("  1. 开发模型推理和判定模块")
        print("  2. 实现完整的训练-评估-推理流程")
        print("  3. 开始费用波动分析系统开发")
        return True
    else:
        print("部分测试失败，请检查模块实现")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
