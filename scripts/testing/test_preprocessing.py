#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.billing_audit.preprocessing import BillingDataPreprocessor, FeatureEngineer
from src.utils import get_logger

logger = get_logger(__name__)


def test_fixed_fee_preprocessing():
    """测试固费数据预处理"""
    print("测试固费数据预处理...")
    
    try:
        # 创建预处理器
        preprocessor = BillingDataPreprocessor('fixed_fee')
        
        # 加载和预处理数据
        X, y, passthrough = preprocessor.process()
        
        print(f"固费预处理成功:")
        print(f"  - 特征维度: {X.shape}")
        print(f"  - 目标变量长度: {len(y)}")
        print(f"  - 透传数据维度: {passthrough.shape}")
        print(f"  - 特征列: {list(X.columns)}")
        
        # 测试特征工程
        feature_engineer = FeatureEngineer('fixed_fee')
        X_engineered = feature_engineer.fit_transform(X)
        
        print(f"固费特征工程成功:")
        print(f"  - 工程后特征维度: {X_engineered.shape}")
        print(f"  - 新特征数量: {X_engineered.shape[1] - X.shape[1]}")
        
        return True
        
    except Exception as e:
        print(f"固费预处理失败: {e}")
        logger.exception("固费预处理异常")
        return False


def test_discount_preprocessing():
    """测试优惠费数据预处理"""
    print("\n测试优惠费数据预处理...")
    
    try:
        # 创建预处理器
        preprocessor = BillingDataPreprocessor('discount')
        
        # 加载和预处理数据
        X, y, passthrough = preprocessor.process()
        
        print(f"优惠费预处理成功:")
        print(f"  - 特征维度: {X.shape}")
        print(f"  - 目标变量长度: {len(y)}")
        print(f"  - 透传数据维度: {passthrough.shape}")
        print(f"  - 特征列: {list(X.columns)}")
        
        # 测试特征工程
        feature_engineer = FeatureEngineer('discount')
        X_engineered = feature_engineer.fit_transform(X)
        
        print(f"优惠费特征工程成功:")
        print(f"  - 工程后特征维度: {X_engineered.shape}")
        print(f"  - 新特征数量: {X_engineered.shape[1] - X.shape[1]}")
        
        return True
        
    except Exception as e:
        print(f"优惠费预处理失败: {e}")
        logger.exception("优惠费预处理异常")
        return False


def main():
    """主函数"""
    print("开始数据预处理测试...")
    
    success_count = 0
    total_tests = 2
    
    # 测试固费预处理
    if test_fixed_fee_preprocessing():
        success_count += 1
    
    # 测试优惠费预处理
    if test_discount_preprocessing():
        success_count += 1
    
    # 输出测试结果
    print(f"\n测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("所有测试通过!")
        return True
    else:
        print("部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
