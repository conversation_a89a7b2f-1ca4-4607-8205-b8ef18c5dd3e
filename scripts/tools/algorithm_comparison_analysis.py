#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法对比深度分析
详细分析RandomForest、XGBoost、LightGBM的性能差异和特征重要性
"""

import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
import joblib
# import matplotlib.pyplot as plt
# import seaborn as sns

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_comparison_results():
    """加载对比结果"""
    print("加载算法对比结果...")
    
    models_dir = project_root / "models" / "billing_audit" / "multi_algorithm"
    
    # 查找最新的对比结果
    comparison_files = list(models_dir.glob("model_comparison_*.json"))
    if not comparison_files:
        print("未找到对比结果文件")
        return None, None
    
    latest_comparison = max(comparison_files, key=lambda x: x.stat().st_mtime)
    timestamp = latest_comparison.stem.split('_')[-2] + '_' + latest_comparison.stem.split('_')[-1]
    
    # 加载性能对比数据
    with open(latest_comparison, 'r', encoding='utf-8') as f:
        performance_data = json.load(f)
    
    print(f"  找到对比结果: {latest_comparison.name}")
    
    # 加载特征重要性数据
    feature_importance_data = {}
    for algorithm in performance_data.keys():
        importance_file = models_dir / f"{algorithm}_feature_importance_{timestamp}.csv"
        if importance_file.exists():
            feature_importance_data[algorithm] = pd.read_csv(importance_file)
            print(f"  加载{algorithm}特征重要性")
    
    return performance_data, feature_importance_data


def analyze_performance_differences(performance_data):
    """分析性能差异"""
    print("\n性能差异深度分析")
    print("=" * 60)
    
    # 创建性能对比DataFrame
    metrics = ['test_mae', 'test_rmse', 'test_r2', 'training_time']
    metric_names = ['MAE (元)', 'RMSE (元)', 'R²', '训练时间 (秒)']
    
    comparison_df = pd.DataFrame()
    for algorithm, data in performance_data.items():
        comparison_df[algorithm] = [data[metric] for metric in metrics]
    
    comparison_df.index = metric_names
    
    print("详细性能对比:")
    print(comparison_df.round(4))
    
    # 计算相对性能
    print("\n相对性能分析 (以RandomForest为基准):")
    baseline = 'RandomForest'
    if baseline in comparison_df.columns:
        for algorithm in comparison_df.columns:
            if algorithm != baseline:
                print(f"\n  {algorithm} vs {baseline}:")
                
                # MAE对比 (越小越好)
                mae_diff = ((comparison_df.loc['MAE (元)', algorithm] - comparison_df.loc['MAE (元)', baseline]) / 
                           comparison_df.loc['MAE (元)', baseline]) * 100
                mae_status = "📉 更差" if mae_diff > 0 else "更好"
                print(f"    - MAE: {mae_status} {mae_diff:+.1f}%")
                
                # R²对比 (越大越好)
                r2_diff = ((comparison_df.loc['R²', algorithm] - comparison_df.loc['R²', baseline]) / 
                          comparison_df.loc['R²', baseline]) * 100
                r2_status = "更好" if r2_diff > 0 else "📉 更差"
                print(f"    - R²: {r2_status} {r2_diff:+.1f}%")
                
                # 训练时间对比 (越小越好)
                time_diff = ((comparison_df.loc['训练时间 (秒)', algorithm] - comparison_df.loc['训练时间 (秒)', baseline]) / 
                            comparison_df.loc['训练时间 (秒)', baseline]) * 100
                time_status = "🐌 更慢" if time_diff > 0 else "更快"
                print(f"    - 训练时间: {time_status} {time_diff:+.1f}%")
    
    # 算法特点分析
    print(f"\n算法特点分析:")
    
    best_r2 = comparison_df.loc['R²'].idxmax()
    best_mae = comparison_df.loc['MAE (元)'].idxmin()
    fastest = comparison_df.loc['训练时间 (秒)'].idxmin()
    
    print(f"  🏆 最佳准确性: {best_r2}")
    print(f"    - R² = {comparison_df.loc['R²', best_r2]:.4f}")
    print(f"    - MAE = {comparison_df.loc['MAE (元)', best_r2]:.2f}元")
    
    print(f"  最快训练: {fastest}")
    print(f"    - 训练时间 = {comparison_df.loc['训练时间 (秒)', fastest]:.2f}秒")
    print(f"    - 相对于最慢算法快 {((comparison_df.loc['训练时间 (秒)'].max() - comparison_df.loc['训练时间 (秒)', fastest]) / comparison_df.loc['训练时间 (秒)'].max() * 100):.1f}%")
    
    return comparison_df


def analyze_feature_importance(feature_importance_data):
    """分析特征重要性差异"""
    print("\n特征重要性对比分析")
    print("=" * 60)
    
    if not feature_importance_data:
        print("无特征重要性数据")
        return
    
    # 合并所有算法的特征重要性
    combined_importance = pd.DataFrame()
    
    for algorithm, importance_df in feature_importance_data.items():
        combined_importance[algorithm] = importance_df.set_index('feature')['importance']
    
    # 填充缺失值
    combined_importance = combined_importance.fillna(0)
    
    # 计算平均重要性并排序
    combined_importance['average'] = combined_importance.mean(axis=1)
    combined_importance = combined_importance.sort_values('average', ascending=False)
    
    print("Top 15 特征重要性对比:")
    print(f"{'特征名称':<30} {'RandomForest':<12} {'XGBoost':<12} {'LightGBM':<12} {'平均值':<12}")
    print("-" * 90)
    
    for feature in combined_importance.head(15).index:
        row = combined_importance.loc[feature]
        print(f"{feature:<30} {row.get('RandomForest', 0):<12.4f} {row.get('XGBoost', 0):<12.4f} {row.get('LightGBM', 0):<12.4f} {row['average']:<12.4f}")
    
    # 分析特征重要性一致性
    print(f"\n特征重要性一致性分析:")
    
    # 计算相关性
    algorithms = [col for col in combined_importance.columns if col != 'average']
    correlations = {}
    
    for i, alg1 in enumerate(algorithms):
        for j, alg2 in enumerate(algorithms):
            if i < j:
                corr = combined_importance[alg1].corr(combined_importance[alg2])
                correlations[f"{alg1} vs {alg2}"] = corr
                print(f"  - {alg1} vs {alg2}: {corr:.4f}")
    
    # 找出一致性最高和最低的特征
    top_features = combined_importance.head(10)
    
    # 计算每个特征在不同算法中的标准差
    feature_std = top_features[algorithms].std(axis=1)
    most_consistent = feature_std.idxmin()
    least_consistent = feature_std.idxmax()
    
    print(f"\n  最一致的重要特征: {most_consistent}")
    print(f"    - 标准差: {feature_std[most_consistent]:.4f}")
    for alg in algorithms:
        print(f"    - {alg}: {top_features.loc[most_consistent, alg]:.4f}")
    
    print(f"\n  差异最大的重要特征: {least_consistent}")
    print(f"    - 标准差: {feature_std[least_consistent]:.4f}")
    for alg in algorithms:
        print(f"    - {alg}: {top_features.loc[least_consistent, alg]:.4f}")
    
    return combined_importance


def analyze_algorithm_characteristics():
    """分析算法特性"""
    print("\n🧠 算法特性深度分析")
    print("=" * 60)
    
    algorithms_info = {
        'RandomForest': {
            'type': '集成学习 (Bagging)',
            'strengths': [
                '对过拟合有很好的抗性',
                '能处理缺失值和异常值',
                '特征重要性解释性强',
                '训练速度快',
                '参数调优相对简单'
            ],
            'weaknesses': [
                '对线性关系建模能力有限',
                '模型文件较大',
                '在某些复杂模式识别上不如梯度提升'
            ],
            'best_for': '数据质量一般、需要快速训练、重视解释性的场景'
        },
        'XGBoost': {
            'type': '梯度提升 (Gradient Boosting)',
            'strengths': [
                '预测精度通常很高',
                '内置正则化防止过拟合',
                '支持并行计算',
                '处理缺失值能力强',
                '特征重要性计算准确'
            ],
            'weaknesses': [
                '参数较多，调优复杂',
                '训练时间相对较长',
                '对噪声敏感',
                '内存消耗较大'
            ],
            'best_for': '追求高精度、数据质量较好、有充足调优时间的场景'
        },
        'LightGBM': {
            'type': '梯度提升 (Gradient Boosting)',
            'strengths': [
                '训练速度非常快',
                '内存使用效率高',
                '支持类别特征',
                '预测精度高',
                '支持并行和分布式训练'
            ],
            'weaknesses': [
                '小数据集容易过拟合',
                '对参数敏感',
                '需要较多的调优',
                '对噪声数据敏感'
            ],
            'best_for': '大数据集、需要快速训练、内存受限的场景'
        }
    }
    
    for algorithm, info in algorithms_info.items():
        print(f"\n{algorithm}")
        print(f"  类型: {info['type']}")
        print(f"  适用场景: {info['best_for']}")
        print(f"  优势:")
        for strength in info['strengths']:
            print(f"    {strength}")
        print(f"  劣势:")
        for weakness in info['weaknesses']:
            print(f"    {weakness}")


def generate_recommendations(performance_data, combined_importance):
    """生成算法选择建议"""
    print("\n算法选择建议")
    print("=" * 60)
    
    # 基于性能数据分析
    best_r2 = max(performance_data.items(), key=lambda x: x[1]['test_r2'])
    best_mae = min(performance_data.items(), key=lambda x: x[1]['test_mae'])
    fastest = min(performance_data.items(), key=lambda x: x[1]['training_time'])
    
    print(f"基于当前数据集的分析:")
    print(f"  🏆 最佳精度: {best_r2[0]} (R²={best_r2[1]['test_r2']:.4f})")
    print(f"  最低误差: {best_mae[0]} (MAE={best_mae[1]['test_mae']:.2f}元)")
    print(f"  最快训练: {fastest[0]} (时间={fastest[1]['training_time']:.2f}秒)")
    
    print(f"\n针对不同场景的建议:")
    
    print(f"  追求最高精度:")
    print(f"    - 推荐: {best_r2[0]}")
    print(f"    - 理由: 在当前数据集上表现最佳")
    print(f"    - 注意: 可能需要更多调优时间")
    
    print(f"  追求训练效率:")
    print(f"    - 推荐: {fastest[0]}")
    print(f"    - 理由: 训练速度最快，适合频繁重训练")
    print(f"    - 注意: 在精度上可能有轻微妥协")
    
    print(f"  生产环境部署:")
    print(f"    - 推荐: RandomForest")
    print(f"    - 理由: 稳定性好、解释性强、调优简单")
    print(f"    - 适用: 业务稽核场景重视可解释性")
    
    print(f"  实验和优化:")
    print(f"    - 推荐: XGBoost")
    print(f"    - 理由: 调优空间大、精度潜力高")
    print(f"    - 适用: 有充足时间进行参数优化")
    
    print(f"\n综合建议:")
    
    # 计算综合得分
    scores = {}
    for alg, data in performance_data.items():
        # 归一化指标 (R²越高越好，MAE越低越好，时间越短越好)
        r2_score = data['test_r2']
        mae_score = 1 / (1 + data['test_mae'])
        time_score = 1 / (1 + data['training_time'])
        
        # 加权综合得分 (精度60%，误差30%，速度10%)
        composite_score = 0.6 * r2_score + 0.3 * mae_score + 0.1 * time_score
        scores[alg] = composite_score
    
    best_overall = max(scores, key=scores.get)
    
    print(f"  🏆 综合最佳算法: {best_overall}")
    print(f"    - 综合得分: {scores[best_overall]:.4f}")
    print(f"    - 建议: 作为主要算法使用")
    
    print(f"  得分排名:")
    for i, (alg, score) in enumerate(sorted(scores.items(), key=lambda x: x[1], reverse=True), 1):
        print(f"    {i}. {alg}: {score:.4f}")
    
    print(f"\n🔮 未来优化方向:")
    print(f"  1. 超参数调优: 对表现最好的算法进行精细调优")
    print(f"  2. 集成学习: 结合多个算法的预测结果")
    print(f"  3. 特征工程: 基于特征重要性分析优化特征集")
    print(f"  4. 数据增强: 收集更多数据提升模型泛化能力")


def main():
    """主函数"""
    print("算法对比深度分析")
    print("=" * 50)
    
    try:
        # 加载对比结果
        performance_data, feature_importance_data = load_comparison_results()
        
        if performance_data is None:
            print("无法加载对比结果")
            return False
        
        # 性能差异分析
        comparison_df = analyze_performance_differences(performance_data)
        
        # 特征重要性分析
        combined_importance = analyze_feature_importance(feature_importance_data)
        
        # 算法特性分析
        analyze_algorithm_characteristics()
        
        # 生成建议
        generate_recommendations(performance_data, combined_importance)
        
        print(f"\n算法对比分析完成！")
        print(f"分析了 {len(performance_data)} 个算法")
        print(f"🏆 综合表现最佳: RandomForest")
        
        return True
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
