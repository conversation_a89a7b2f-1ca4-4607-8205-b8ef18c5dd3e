#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据样例分析脚本
分析固费、优惠和话单增量三个Excel样例文件的数据结构和字段含义
"""

import pandas as pd
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def analyze_excel_file(file_path, file_name):
    """分析单个Excel文件"""
    print(f"\n{'='*60}")
    print(f"分析文件: {file_name}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 基本信息
        print(f"数据基本信息:")
        print(f"  - 行数: {len(df)}")
        print(f"  - 列数: {len(df.columns)}")
        print(f"  - 文件大小: {os.path.getsize(file_path) / 1024:.2f} KB")
        
        # 字段信息
        print(f"\n字段列表:")
        for i, col in enumerate(df.columns, 1):
            dtype = str(df[col].dtype)
            null_count = df[col].isnull().sum()
            null_rate = (null_count / len(df)) * 100
            print(f"  {i:2d}. {col:<25} | 类型: {dtype:<15} | 缺失: {null_count:4d} ({null_rate:5.1f}%)")
        
        # 数据预览
        print(f"\n数据预览 (前5行):")
        print(df.head().to_string())
        
        # 数值型字段统计
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            print(f"\n数值型字段统计:")
            print(df[numeric_cols].describe().round(2).to_string())
        
        # 类别型字段统计
        categorical_cols = df.select_dtypes(include=['object']).columns
        if len(categorical_cols) > 0:
            print(f"\n类别型字段唯一值统计:")
            for col in categorical_cols[:5]:  # 只显示前5个类别字段
                unique_count = df[col].nunique()
                print(f"  {col}: {unique_count} 个唯一值")
                if unique_count <= 10:
                    print(f"    值: {list(df[col].unique())}")
        
        return df
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def main():
    """主函数"""
    print("开始分析数据样例文件...")
    
    # 数据文件路径
    data_dir = project_root / "数据"
    files = [
        ("固费预测样例@20250707.xlsx", "固定费用预测样例"),
        ("优惠预测样例@20250707.xlsx", "优惠费用预测样例"),
        ("话单增量样例.xlsx", "话单增量样例")
    ]
    
    analysis_results = {}
    
    # 分析每个文件
    for filename, description in files:
        file_path = data_dir / filename
        if file_path.exists():
            df = analyze_excel_file(file_path, description)
            if df is not None:
                analysis_results[filename] = df
        else:
            print(f"文件不存在: {file_path}")
    
    # 生成分析报告
    print(f"\n{'='*80}")
    print("数据分析总结报告")
    print(f"{'='*80}")
    
    for filename, df in analysis_results.items():
        print(f"\n📄 {filename}:")
        print(f"  - 数据量: {len(df)} 行 × {len(df.columns)} 列")
        print(f"  - 主要字段: {', '.join(df.columns[:5].tolist())}")
        
        # 识别可能的关键字段
        key_fields = []
        for col in df.columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['id', 'user', 'amount', 'fee', 'type']):
                key_fields.append(col)
        
        if key_fields:
            print(f"  - 关键字段: {', '.join(key_fields)}")
    
    print(f"\n数据分析完成！")
    print(f"建议:")
    print(f"  1. 检查数据质量，处理缺失值")
    print(f"  2. 确认字段含义和业务逻辑")
    print(f"  3. 设计特征工程策略")

if __name__ == "__main__":
    main()
