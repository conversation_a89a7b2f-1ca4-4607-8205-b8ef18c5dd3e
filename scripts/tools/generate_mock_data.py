#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟数据生成脚本
根据新的字段要求生成固费预测训练数据
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def generate_fixed_fee_data(num_samples=500):
    """
    生成固费预测模拟数据
    
    Args:
        num_samples: 生成样本数量
        
    Returns:
        pd.DataFrame: 生成的数据
    """
    print(f"开始生成 {num_samples} 条固费模拟数据...")
    
    np.random.seed(42)  # 设置随机种子以确保可重现性
    random.seed(42)
    
    data = []
    
    for i in range(num_samples):
        # 基础标识字段
        offer_inst_id = f"OI{10000000 + i:08d}"
        prod_inst_id = f"PI{20000000 + i:08d}"
        prod_id = random.choice([1001, 1002, 1003, 1004, 1005])
        offer_id = random.choice([2001, 2002, 2003, 2004, 2005])
        sub_prod_id = random.choice([3001, 3002, 3003, 3004])
        
        # 策略相关字段
        event_pricing_strategy_id = random.choice([4001, 4002, 4003, 4004, 4005])
        event_type_id = random.choice([171, 172, 173, 174, 175])
        calc_priority = random.choice([1, 2, 3, 4, 5])
        pricing_section_id = random.choice([5001, 5002, 5003, 5004])
        calc_method_id = random.choice([6001, 6002, 6003])
        role_id = random.choice([7001, 7002, 7003])
        
        # 特征字段
        cal_type = random.choice([0, 1, 2, 3])  # 0-非月租, 1-整月收, 2-按天折算, 3-日收
        unit_type = random.choice([0, 1, 2])    # 0-非多周期, 1-月, 2-天
        rate_unit = random.choice([0, 1, 2, 3, 6, 12]) if unit_type > 0 else 0
        
        # 生效日期 (2024年内的随机日期)
        start_date = datetime(2024, 1, 1)
        random_days = random.randint(0, 365)
        eff_date = start_date + timedelta(days=random_days)
        final_eff_year = eff_date.year
        final_eff_mon = eff_date.month
        final_eff_day = eff_date.day
        
        # 失效日期 (生效日期后1-12个月)
        exp_months = random.randint(1, 12)
        exp_date = eff_date + timedelta(days=30 * exp_months)
        final_exp_year = exp_date.year
        final_exp_mon = exp_date.month
        final_exp_day = exp_date.day
        
        # 当前年月 (2024年7月)
        cur_year_month = "202407"
        
        # 计费天数和当月天数
        month_day_count = 31  # 7月有31天
        if cal_type == 1:  # 整月收
            charge_day_count = month_day_count
        elif cal_type == 2:  # 按天折算
            charge_day_count = random.randint(1, month_day_count)
        elif cal_type == 3:  # 日收
            charge_day_count = random.randint(1, month_day_count)
        else:  # 非月租
            charge_day_count = 0
        
        # 运行状态
        run_code = random.choice(["NORMAL", "SUSPEND", "STOP", "ACTIVE"])
        run_time = "202407"
        
        # 应收费用 (基于不同的计费类型)
        base_fee = random.uniform(10, 200)
        if cal_type == 0:  # 非月租
            should_fee = 0
        elif cal_type == 1:  # 整月收
            should_fee = base_fee
        elif cal_type == 2:  # 按天折算
            should_fee = base_fee * (charge_day_count / month_day_count)
        else:  # 日收
            should_fee = base_fee * charge_day_count / 30
        
        should_fee = round(should_fee, 2)
        
        # 业务标识 (是否收费)
        # 精品班成员或特殊事件类型不收费
        is_vip = random.random() < 0.1  # 10%概率是精品班
        is_special_event = event_type_id == 171 and calc_priority > 1  # 171事件非最高优先级
        busi_flag = 1 if (is_vip or is_special_event) else 0
        
        # 实际账单金额 (目标变量)
        if busi_flag == 1:  # 不收费
            amount = 0
        else:
            # 在应收费用基础上添加一些随机波动
            noise_factor = random.uniform(0.8, 1.2)
            amount = should_fee * noise_factor
            
            # 添加一些异常值 (5%概率)
            if random.random() < 0.05:
                amount = should_fee * random.uniform(0.1, 3.0)
            
            amount = round(amount, 2)
        
        # 构建数据记录
        record = {
            # 透传字段
            'offer_inst_id': offer_inst_id,
            'prod_inst_id': prod_inst_id,
            'prod_id': prod_id,
            'offer_id': offer_id,
            'sub_prod_id': sub_prod_id,
            'event_pricing_strategy_id': event_pricing_strategy_id,
            'event_type_id': event_type_id,
            'calc_priority': calc_priority,
            'pricing_section_id': pricing_section_id,
            'calc_method_id': calc_method_id,
            'role_id': role_id,
            
            # 特征字段
            'cal_type': cal_type,
            'unit_type': unit_type,
            'rate_unit': rate_unit,
            'final_eff_year': final_eff_year,
            'final_eff_mon': final_eff_mon,
            'final_eff_day': final_eff_day,
            'final_exp_year': final_exp_year,
            'final_exp_mon': final_exp_mon,
            'final_exp_day': final_exp_day,
            'cur_year_month': cur_year_month,
            'charge_day_count': charge_day_count,
            'month_day_count': month_day_count,
            'run_code': run_code,
            'run_time': run_time,
            'should_fee': should_fee,
            'busi_flag': busi_flag,
            
            # 目标变量
            'amount': amount
        }
        
        data.append(record)
    
    df = pd.DataFrame(data)
    print(f"生成完成: {len(df)} 条记录")
    return df


def analyze_generated_data(df):
    """
    分析生成的数据
    
    Args:
        df: 生成的数据DataFrame
    """
    print("\n数据分析:")
    print(f"  - 总样本数: {len(df)}")
    print(f"  - 特征字段数: {len([col for col in df.columns if col != 'amount'])}")
    
    print("\n目标变量分布:")
    print(f"  - 最小值: {df['amount'].min():.2f}")
    print(f"  - 最大值: {df['amount'].max():.2f}")
    print(f"  - 平均值: {df['amount'].mean():.2f}")
    print(f"  - 中位数: {df['amount'].median():.2f}")
    print(f"  - 零值比例: {(df['amount'] == 0).mean():.2%}")
    
    print("\n🏷️ 类别字段分布:")
    categorical_fields = ['cal_type', 'unit_type', 'busi_flag', 'run_code']
    for field in categorical_fields:
        if field in df.columns:
            print(f"  - {field}: {dict(df[field].value_counts())}")
    
    print("\n📅 日期字段范围:")
    date_fields = ['final_eff_year', 'final_exp_year']
    for field in date_fields:
        if field in df.columns:
            print(f"  - {field}: {df[field].min()} - {df[field].max()}")


def save_mock_data(df, filename=None):
    """
    保存模拟数据到Excel文件
    
    Args:
        df: 数据DataFrame
        filename: 文件名，如果为None则使用默认名称
    """
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"固费预测模拟数据_{timestamp}.xlsx"
    
    # 确保数据目录存在
    data_dir = project_root / "数据"
    data_dir.mkdir(exist_ok=True)
    
    output_path = data_dir / filename
    
    # 保存到Excel
    df.to_excel(output_path, index=False)
    print(f"\n数据已保存到: {output_path}")
    
    return str(output_path)


def main():
    """主函数"""
    print("固费预测模拟数据生成器")
    print("=" * 50)
    
    # 生成数据
    df = generate_fixed_fee_data(num_samples=500)
    
    # 分析数据
    analyze_generated_data(df)
    
    # 保存数据
    output_file = save_mock_data(df)
    
    print(f"\n模拟数据生成完成！")
    print(f"文件位置: {output_file}")
    print(f"数据规模: {len(df)} 行 × {len(df.columns)} 列")
    
    # 显示前几行数据预览
    print(f"\n👀 数据预览 (前5行):")
    print(df.head().to_string())
    
    return output_file


if __name__ == "__main__":
    main()
