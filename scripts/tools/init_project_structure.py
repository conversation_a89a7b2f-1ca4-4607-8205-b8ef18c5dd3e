#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构初始化脚本
创建完整的项目目录结构
"""

import os
from pathlib import Path


def create_directory_structure():
    """创建项目目录结构"""
    
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    # 定义目录结构
    directories = [
        # 源代码目录
        "src",
        "src/billing_audit",
        "src/billing_audit/models",
        "src/billing_audit/preprocessing", 
        "src/billing_audit/training",
        "src/billing_audit/inference",
        "src/behavior_analysis",
        "src/behavior_analysis/clustering",
        "src/behavior_analysis/change_detection",
        "src/behavior_analysis/visualization",
        "src/utils",
        "src/api",
        
        # 配置目录
        "config",
        
        # 数据目录
        "data",
        "data/raw",
        "data/processed",
        "data/features",
        
        # 模型目录
        "models",
        "models/billing_audit",
        "models/behavior_analysis",
        "models/backups",
        
        # 日志目录
        "logs",
        
        # 测试目录
        "tests",
        "tests/unit",
        "tests/integration",
        "tests/data",
        
        # 脚本目录
        "scripts",
        
        # 文档目录
        "docs",
        "docs/api",
        "docs/user_guide",
        
        # 输出目录
        "outputs",
        "outputs/reports",
        "outputs/visualizations",
        
        # 部署目录
        "deployment",
        "deployment/docker",
        "deployment/kubernetes"
    ]
    
    # 创建目录
    created_dirs = []
    for directory in directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            created_dirs.append(directory)
    
    return created_dirs


def create_init_files():
    """创建__init__.py文件"""
    
    project_root = Path(__file__).parent.parent
    
    # 需要创建__init__.py的目录
    init_dirs = [
        "src",
        "src/billing_audit",
        "src/billing_audit/models",
        "src/billing_audit/preprocessing",
        "src/billing_audit/training", 
        "src/billing_audit/inference",
        "src/behavior_analysis",
        "src/behavior_analysis/clustering",
        "src/behavior_analysis/change_detection",
        "src/behavior_analysis/visualization",
        "src/utils",
        "src/api",
        "tests",
        "tests/unit",
        "tests/integration"
    ]
    
    created_files = []
    for directory in init_dirs:
        init_file = project_root / directory / "__init__.py"
        if not init_file.exists():
            init_file.write_text('#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n"""\n模块初始化文件\n"""\n')
            created_files.append(str(init_file.relative_to(project_root)))
    
    return created_files


def create_placeholder_files():
    """创建占位符文件"""
    
    project_root = Path(__file__).parent.parent
    
    placeholder_files = [
        ("data/raw/.gitkeep", "# 原始数据目录\n"),
        ("data/processed/.gitkeep", "# 处理后数据目录\n"),
        ("data/features/.gitkeep", "# 特征数据目录\n"),
        ("models/.gitkeep", "# 模型文件目录\n"),
        ("logs/.gitkeep", "# 日志文件目录\n"),
        ("outputs/reports/.gitkeep", "# 报告输出目录\n"),
        ("outputs/visualizations/.gitkeep", "# 可视化输出目录\n"),
        (".gitignore", create_gitignore_content()),
        ("README.md", create_readme_content())
    ]
    
    created_files = []
    for file_path, content in placeholder_files:
        full_path = project_root / file_path
        if not full_path.exists():
            full_path.write_text(content, encoding='utf-8')
            created_files.append(file_path)
    
    return created_files


def create_gitignore_content():
    """创建.gitignore内容"""
    return """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Jupyter Notebook
.ipynb_checkpoints

# Data files
*.csv
*.xlsx
*.xls
*.json
*.pickle
*.pkl

# Model files
models/*.pkl
models/*.joblib
models/*.h5

# Logs
logs/*.log

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
"""


def create_readme_content():
    """创建README.md内容"""
    return """# 山西电信出账稽核AI系统

基于AI的计费稽核和用户行为分析系统

## 项目结构

```
├── src/                    # 源代码
│   ├── billing_audit/      # 收费稽核模块
│   ├── behavior_analysis/  # 行为分析模块
│   ├── utils/             # 工具模块
│   └── api/               # API接口
├── config/                # 配置文件
├── data/                  # 数据目录
├── models/                # 模型文件
├── tests/                 # 测试代码
├── scripts/               # 脚本文件
├── docs/                  # 文档
└── outputs/               # 输出结果
```

## 快速开始

1. 创建虚拟环境
```bash
python3 -m venv venv
source venv/bin/activate
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 运行配置验证
```bash
python scripts/validate_config.py
```

## 功能模块

### 1. 收费稽核分析
- 固定费用合理性判定
- 优惠费用合理性判定
- 基于XGBoost的预测模型

### 2. 用户行为分析
- 话单数据聚类分析
- 用户行为模式识别
- 行为变化检测

## 开发指南

详见 `docs/` 目录下的开发文档。
"""


def main():
    """主函数"""
    print("开始初始化项目结构...")
    
    # 创建目录结构
    created_dirs = create_directory_structure()
    if created_dirs:
        print(f"创建了 {len(created_dirs)} 个目录:")
        for directory in created_dirs:
            print(f"  {directory}")
    
    # 创建__init__.py文件
    created_inits = create_init_files()
    if created_inits:
        print(f"创建了 {len(created_inits)} 个__init__.py文件")
    
    # 创建占位符文件
    created_files = create_placeholder_files()
    if created_files:
        print(f"创建了 {len(created_files)} 个占位符文件:")
        for file_path in created_files:
            print(f"  📄 {file_path}")
    
    print("项目结构初始化完成!")


if __name__ == "__main__":
    main()
