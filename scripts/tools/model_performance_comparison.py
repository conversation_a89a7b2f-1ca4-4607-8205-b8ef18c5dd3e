#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能对比分析
比较新旧模型的性能差异和改进效果
"""

import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime
import joblib

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_model_results():
    """加载模型结果"""
    print("加载模型结果...")
    
    # 查找最新的增强模型
    models_dir = project_root / "models" / "billing_audit"
    enhanced_models = list(models_dir.glob("fixed_fee_enhanced_model_*.pkl"))
    
    if not enhanced_models:
        print("未找到增强模型文件")
        return None, None
    
    # 使用最新的模型
    latest_enhanced_model = max(enhanced_models, key=lambda x: x.stat().st_mtime)
    print(f"  找到增强模型: {latest_enhanced_model.name}")
    
    # 查找对应的特征重要性文件
    importance_file = latest_enhanced_model.with_suffix('').with_suffix('') / f"{latest_enhanced_model.stem}_feature_importance.csv"
    importance_file = models_dir / f"{latest_enhanced_model.stem}_feature_importance.csv"
    
    if importance_file.exists():
        feature_importance = pd.read_csv(importance_file)
        print(f"  找到特征重要性文件: {importance_file.name}")
    else:
        print(f"  未找到特征重要性文件")
        feature_importance = None
    
    return latest_enhanced_model, feature_importance


def analyze_feature_improvements(feature_importance):
    """分析特征改进情况"""
    print("\n特征改进分析...")
    
    if feature_importance is None:
        print("  无特征重要性数据")
        return
    
    # 分类特征
    original_features = []
    new_date_features = []
    business_features = []
    
    for _, row in feature_importance.iterrows():
        feature = row['feature']
        if any(keyword in feature for keyword in ['final_eff_', 'final_exp_', 'subscription_', 'months_', 'quarter_']):
            if any(keyword in feature for keyword in ['dayofweek', 'quarter', 'weekend', 'duration', 'since', 'until', 'diff']):
                new_date_features.append(row)
            else:
                original_features.append(row)
        elif any(keyword in feature for keyword in ['daily_', 'efficiency', 'interaction']):
            business_features.append(row)
        else:
            original_features.append(row)
    
    print(f"  特征分类统计:")
    print(f"    - 原始特征: {len(original_features)} 个")
    print(f"    - 新增日期特征: {len(new_date_features)} 个")
    print(f"    - 新增业务特征: {len(business_features)} 个")
    
    # 分析新特征的贡献
    if new_date_features:
        new_date_importance = sum([f['importance'] for f in new_date_features])
        print(f"  新增日期特征总贡献度: {new_date_importance:.4f} ({new_date_importance*100:.2f}%)")
        
        print(f"  重要的新增日期特征:")
        for feature in sorted(new_date_features, key=lambda x: x['importance'], reverse=True)[:5]:
            print(f"    - {feature['feature']:<30} {feature['importance']:.4f}")
    
    if business_features:
        business_importance = sum([f['importance'] for f in business_features])
        print(f"  新增业务特征总贡献度: {business_importance:.4f} ({business_importance*100:.2f}%)")
        
        print(f"  重要的新增业务特征:")
        for feature in sorted(business_features, key=lambda x: x['importance'], reverse=True):
            print(f"    - {feature['feature']:<30} {feature['importance']:.4f}")


def compare_with_baseline():
    """与基线模型对比"""
    print("\n与基线模型性能对比...")
    
    # 基线模型性能（来自之前的快速训练测试）
    baseline_metrics = {
        'test_mae': 10.53,
        'test_r2': 0.7867,
        'business_accuracy': 97.0,
        'feature_count': 16,
        'training_time': 0.24
    }
    
    # 增强模型性能（来自刚才的训练结果）
    enhanced_metrics = {
        'test_mae': 10.78,
        'test_r2': 0.7953,
        'business_accuracy': 97.0,
        'feature_count': 31,
        'training_time': 0.11
    }
    
    print(f"  性能对比表:")
    print(f"  {'指标':<20} {'基线模型':<15} {'增强模型':<15} {'改进':<15}")
    print(f"  {'-'*65}")
    
    # MAE对比
    mae_improvement = ((baseline_metrics['test_mae'] - enhanced_metrics['test_mae']) / baseline_metrics['test_mae']) * 100
    mae_status = "" if mae_improvement > 0 else "📉" if mae_improvement < 0 else "➡️"
    print(f"  {'MAE (元)':<20} {baseline_metrics['test_mae']:<15.2f} {enhanced_metrics['test_mae']:<15.2f} {mae_status} {mae_improvement:+.1f}%")
    
    # R²对比
    r2_improvement = ((enhanced_metrics['test_r2'] - baseline_metrics['test_r2']) / baseline_metrics['test_r2']) * 100
    r2_status = "" if r2_improvement > 0 else "📉" if r2_improvement < 0 else "➡️"
    print(f"  {'R² 决定系数':<20} {baseline_metrics['test_r2']:<15.4f} {enhanced_metrics['test_r2']:<15.4f} {r2_status} {r2_improvement:+.1f}%")
    
    # 业务准确率对比
    acc_improvement = enhanced_metrics['business_accuracy'] - baseline_metrics['business_accuracy']
    acc_status = "" if acc_improvement > 0 else "📉" if acc_improvement < 0 else "➡️"
    print(f"  {'业务准确率 (%)':<20} {baseline_metrics['business_accuracy']:<15.1f} {enhanced_metrics['business_accuracy']:<15.1f} {acc_status} {acc_improvement:+.1f}%")
    
    # 特征数量对比
    feature_increase = enhanced_metrics['feature_count'] - baseline_metrics['feature_count']
    print(f"  {'特征数量':<20} {baseline_metrics['feature_count']:<15d} {enhanced_metrics['feature_count']:<15d} +{feature_increase}")
    
    # 训练时间对比
    time_improvement = ((baseline_metrics['training_time'] - enhanced_metrics['training_time']) / baseline_metrics['training_time']) * 100
    time_status = "" if time_improvement > 0 else "🐌" if time_improvement < 0 else "➡️"
    print(f"  {'训练时间 (秒)':<20} {baseline_metrics['training_time']:<15.2f} {enhanced_metrics['training_time']:<15.2f} {time_status} {time_improvement:+.1f}%")
    
    # 总体评估
    print(f"\n  总体评估:")
    
    improvements = []
    if r2_improvement > 0:
        improvements.append(f"R²提升{r2_improvement:.1f}%")
    if mae_improvement > 0:
        improvements.append(f"MAE降低{mae_improvement:.1f}%")
    if time_improvement > 0:
        improvements.append(f"训练速度提升{time_improvement:.1f}%")
    
    if improvements:
        print(f"    改进项: {', '.join(improvements)}")
    
    concerns = []
    if mae_improvement < 0:
        concerns.append(f"MAE略有增加({abs(mae_improvement):.1f}%)")
    if r2_improvement < 0:
        concerns.append(f"R²略有下降({abs(r2_improvement):.1f}%)")
    
    if concerns:
        print(f"    关注项: {', '.join(concerns)}")
    
    # 特征工程效果评估
    feature_efficiency = enhanced_metrics['test_r2'] / enhanced_metrics['feature_count']
    baseline_efficiency = baseline_metrics['test_r2'] / baseline_metrics['feature_count']
    efficiency_change = ((feature_efficiency - baseline_efficiency) / baseline_efficiency) * 100
    
    print(f"    特征效率: {feature_efficiency:.6f} (基线: {baseline_efficiency:.6f}, 变化: {efficiency_change:+.1f}%)")
    
    return enhanced_metrics, baseline_metrics


def generate_recommendations(enhanced_metrics, baseline_metrics):
    """生成优化建议"""
    print(f"\n优化建议:")
    
    # 基于性能分析给出建议
    r2_improvement = ((enhanced_metrics['test_r2'] - baseline_metrics['test_r2']) / baseline_metrics['test_r2']) * 100
    mae_improvement = ((baseline_metrics['test_mae'] - enhanced_metrics['test_mae']) / baseline_metrics['test_mae']) * 100
    
    if r2_improvement > 1:
        print(f"  模型性能有明显提升，建议:")
        print(f"    - 继续使用增强特征集")
        print(f"    - 考虑进一步的特征工程")
    elif r2_improvement > 0:
        print(f"  模型性能有轻微提升，建议:")
        print(f"    - 增强特征集有效，可以采用")
        print(f"    - 监控生产环境表现")
    else:
        print(f"  模型性能提升有限，建议:")
        print(f"    - 检查特征工程逻辑")
        print(f"    - 考虑特征选择和降维")
    
    if enhanced_metrics['feature_count'] > baseline_metrics['feature_count'] * 1.5:
        print(f"  特征数量增加较多，建议:")
        print(f"    - 进行特征重要性筛选")
        print(f"    - 移除低贡献度特征")
        print(f"    - 考虑使用正则化方法")
    
    if enhanced_metrics['business_accuracy'] >= 95:
        print(f"  业务准确率优秀，建议:")
        print(f"    - 模型可以投入生产使用")
        print(f"    - 建立模型监控机制")
    
    print(f"  下一步行动:")
    print(f"    1. 在更大的数据集上验证模型性能")
    print(f"    2. 进行A/B测试对比新旧模型")
    print(f"    3. 建立模型性能监控和预警")
    print(f"    4. 定期重训练和模型更新")


def main():
    """主函数"""
    print("模型性能对比分析")
    print("=" * 50)
    
    try:
        # 加载模型结果
        model_path, feature_importance = load_model_results()
        
        if model_path is None:
            print("无法加载模型结果")
            return False
        
        # 分析特征改进
        analyze_feature_improvements(feature_importance)
        
        # 性能对比
        enhanced_metrics, baseline_metrics = compare_with_baseline()
        
        # 生成建议
        generate_recommendations(enhanced_metrics, baseline_metrics)
        
        print(f"\n模型性能对比分析完成！")
        print(f"增强模型路径: {model_path}")
        
        return True
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
