#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多算法模型训练对比
使用XGBoost、LightGBM、RandomForest等算法进行训练和性能对比
"""

import sys
import pandas as pd
import numpy as np
import json
import pickle
from pathlib import Path
from datetime import datetime
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 尝试导入XGBoost和LightGBM
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
    print(f"XGBoost 可用，版本: {xgb.__version__}")
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost 不可用")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
    print(f"LightGBM 可用，版本: {lgb.__version__}")
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM 不可用")


def load_config():
    """加载配置文件"""
    config_path = project_root / "config" / "billing_audit_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_and_preprocess_data():
    """加载和预处理数据"""
    print("数据加载和预处理...")
    
    # 加载配置
    config = load_config()
    data_file = project_root / config['data_sources']['fixed_fee_sample']
    fixed_fee_config = config['billing_audit']['fixed_fee']
    
    # 加载数据
    df = pd.read_excel(data_file)
    print(f"  数据加载成功: {df.shape[0]} 行 × {df.shape[1]} 列")
    
    # 提取特征和目标变量
    feature_columns = fixed_fee_config['feature_columns']
    target_column = fixed_fee_config['target_column']
    
    X = df[feature_columns].copy()
    y = df[target_column].copy()
    
    print(f"  特征提取完成: {X.shape[1]} 个特征")
    print(f"  目标变量: {len(y)} 个样本")
    
    return X, y, fixed_fee_config


def create_enhanced_features(X, config):
    """创建增强的特征"""
    print("特征工程...")
    
    X_features = X.copy()
    
    # 1. 处理类别变量
    categorical_columns = config['categorical_columns']
    label_encoders = {}
    
    for col in categorical_columns:
        if col in X_features.columns:
            le = LabelEncoder()
            X_features[col] = le.fit_transform(X_features[col].astype(str))
            label_encoders[col] = le
    
    # 2. 创建基于新字段的日期特征
    if all(col in X_features.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']):
        try:
            # 组合生效日期
            eff_date = pd.to_datetime(
                X_features[['final_eff_year', 'final_eff_mon', 'final_eff_day']].rename(
                    columns={'final_eff_year': 'year', 'final_eff_mon': 'month', 'final_eff_day': 'day'}
                ), errors='coerce'
            )
            
            # 创建生效日期的额外特征
            X_features['final_eff_dayofweek'] = eff_date.dt.dayofweek
            X_features['final_eff_quarter'] = eff_date.dt.quarter
            X_features['final_eff_is_weekend'] = (eff_date.dt.dayofweek >= 5).astype(int)
            
        except Exception as e:
            print(f"  生效日期特征创建失败: {e}")
    
    if all(col in X_features.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day']):
        try:
            # 组合失效日期
            exp_date = pd.to_datetime(
                X_features[['final_exp_year', 'final_exp_mon', 'final_exp_day']].rename(
                    columns={'final_exp_year': 'year', 'final_exp_mon': 'month', 'final_exp_day': 'day'}
                ), errors='coerce'
            )
            
            # 创建失效日期的额外特征
            X_features['final_exp_dayofweek'] = exp_date.dt.dayofweek
            X_features['final_exp_quarter'] = exp_date.dt.quarter
            X_features['final_exp_is_weekend'] = (exp_date.dt.dayofweek >= 5).astype(int)
            
        except Exception as e:
            print(f"  失效日期特征创建失败: {e}")
    
    # 3. 创建组合特征
    if (all(col in X_features.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']) and
        all(col in X_features.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day'])):
        try:
            # 计算订阅时长（天数）
            X_features['subscription_duration_days'] = (exp_date - eff_date).dt.days
            
            # 计算当前月份与生效月份的差异
            if 'cur_year_month' in X_features.columns:
                cur_date = pd.to_datetime(X_features['cur_year_month'], format='%Y%m', errors='coerce')
                X_features['months_since_effective'] = (
                    (cur_date.dt.year - eff_date.dt.year) * 12 + 
                    (cur_date.dt.month - eff_date.dt.month)
                )
                X_features['months_until_expiry'] = (
                    (exp_date.dt.year - cur_date.dt.year) * 12 + 
                    (exp_date.dt.month - cur_date.dt.month)
                )
            
            # 创建生效和失效的季度差异
            X_features['quarter_diff'] = (
                exp_date.dt.quarter - eff_date.dt.quarter + 
                (exp_date.dt.year - eff_date.dt.year) * 4
            )
            
        except Exception as e:
            print(f"  组合日期特征创建失败: {e}")
    
    # 4. 创建业务逻辑特征
    if 'cal_type' in X_features.columns and 'charge_day_count' in X_features.columns:
        X_features['billing_efficiency'] = X_features['charge_day_count'] / X_features['month_day_count']
        X_features['cal_type_day_interaction'] = X_features['cal_type'] * X_features['charge_day_count']
    
    if 'should_fee' in X_features.columns and 'charge_day_count' in X_features.columns:
        X_features['daily_should_fee'] = X_features['should_fee'] / np.maximum(X_features['charge_day_count'], 1)
    
    # 5. 标准化数值特征
    numerical_columns = config['numerical_columns']
    scaler = StandardScaler()
    
    existing_numerical_cols = [col for col in numerical_columns if col in X_features.columns]
    
    if existing_numerical_cols:
        X_features[existing_numerical_cols] = scaler.fit_transform(X_features[existing_numerical_cols])
    
    # 处理日期字段
    date_columns = config['date_columns']
    for col in date_columns:
        if col in X_features.columns:
            X_features[col] = pd.to_numeric(X_features[col], errors='coerce')
    
    print(f"  特征工程完成: {X_features.shape[1]} 个特征")
    
    return X_features, label_encoders, scaler


def train_random_forest(X_train, X_test, y_train, y_test):
    """训练RandomForest模型"""
    print("\n🌲 训练RandomForest模型...")
    
    model = RandomForestRegressor(
        n_estimators=200,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    
    start_time = datetime.now()
    model.fit(X_train, y_train)
    training_time = (datetime.now() - start_time).total_seconds()
    
    # 预测
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # 评估指标
    metrics = {
        'train_mae': mean_absolute_error(y_train, y_pred_train),
        'test_mae': mean_absolute_error(y_test, y_pred_test),
        'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'train_r2': r2_score(y_train, y_pred_train),
        'test_r2': r2_score(y_test, y_pred_test),
        'training_time': training_time
    }
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': X_train.columns,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"  RandomForest训练完成，耗时: {training_time:.2f}秒")
    print(f"    - 测试集MAE: {metrics['test_mae']:.2f}")
    print(f"    - 测试集R²: {metrics['test_r2']:.4f}")
    
    return model, metrics, feature_importance


def train_xgboost(X_train, X_test, y_train, y_test):
    """训练XGBoost模型"""
    if not XGBOOST_AVAILABLE:
        print("\nXGBoost不可用，跳过训练")
        return None, None, None
    
    print("\n训练XGBoost模型...")
    
    model = xgb.XGBRegressor(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        n_jobs=-1,
        verbosity=0
    )
    
    start_time = datetime.now()
    model.fit(X_train, y_train)
    training_time = (datetime.now() - start_time).total_seconds()
    
    # 预测
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # 评估指标
    metrics = {
        'train_mae': mean_absolute_error(y_train, y_pred_train),
        'test_mae': mean_absolute_error(y_test, y_pred_test),
        'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'train_r2': r2_score(y_train, y_pred_train),
        'test_r2': r2_score(y_test, y_pred_test),
        'training_time': training_time
    }
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': X_train.columns,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"  XGBoost训练完成，耗时: {training_time:.2f}秒")
    print(f"    - 测试集MAE: {metrics['test_mae']:.2f}")
    print(f"    - 测试集R²: {metrics['test_r2']:.4f}")
    
    return model, metrics, feature_importance


def train_lightgbm(X_train, X_test, y_train, y_test):
    """训练LightGBM模型"""
    if not LIGHTGBM_AVAILABLE:
        print("\nLightGBM不可用，跳过训练")
        return None, None, None
    
    print("\n训练LightGBM模型...")
    
    model = lgb.LGBMRegressor(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        n_jobs=-1,
        verbosity=-1
    )
    
    start_time = datetime.now()
    model.fit(X_train, y_train)
    training_time = (datetime.now() - start_time).total_seconds()
    
    # 预测
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # 评估指标
    metrics = {
        'train_mae': mean_absolute_error(y_train, y_pred_train),
        'test_mae': mean_absolute_error(y_test, y_pred_test),
        'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
        'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'train_r2': r2_score(y_train, y_pred_train),
        'test_r2': r2_score(y_test, y_pred_test),
        'training_time': training_time
    }
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': X_train.columns,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"  LightGBM训练完成，耗时: {training_time:.2f}秒")
    print(f"    - 测试集MAE: {metrics['test_mae']:.2f}")
    print(f"    - 测试集R²: {metrics['test_r2']:.4f}")
    
    return model, metrics, feature_importance


def compare_models(results):
    """对比模型性能"""
    print("\n模型性能对比分析")
    print("=" * 80)
    
    # 创建对比表
    comparison_df = pd.DataFrame()
    
    for model_name, result in results.items():
        if result['metrics'] is not None:
            comparison_df[model_name] = [
                result['metrics']['test_mae'],
                result['metrics']['test_rmse'],
                result['metrics']['test_r2'],
                result['metrics']['training_time']
            ]
    
    comparison_df.index = ['MAE (元)', 'RMSE (元)', 'R²', '训练时间 (秒)']
    
    print("性能对比表:")
    print(comparison_df.round(4))
    
    # 找出最佳模型
    if len(comparison_df.columns) > 0:
        best_r2_model = comparison_df.loc['R²'].idxmax()
        best_mae_model = comparison_df.loc['MAE (元)'].idxmin()
        fastest_model = comparison_df.loc['训练时间 (秒)'].idxmin()
        
        print(f"\n🏆 最佳模型分析:")
        print(f"  - 最高R²: {best_r2_model} ({comparison_df.loc['R²', best_r2_model]:.4f})")
        print(f"  - 最低MAE: {best_mae_model} ({comparison_df.loc['MAE (元)', best_mae_model]:.2f}元)")
        print(f"  - 最快训练: {fastest_model} ({comparison_df.loc['训练时间 (秒)', fastest_model]:.2f}秒)")
        
        # 综合评分 (R²权重0.6, MAE权重0.3, 速度权重0.1)
        scores = {}
        for model in comparison_df.columns:
            r2_score = comparison_df.loc['R²', model]
            mae_score = 1 / (1 + comparison_df.loc['MAE (元)', model])  # MAE越小越好
            speed_score = 1 / (1 + comparison_df.loc['训练时间 (秒)', model])  # 时间越短越好
            
            # 归一化到0-1
            r2_norm = r2_score / comparison_df.loc['R²'].max()
            mae_norm = mae_score / (1 / (1 + comparison_df.loc['MAE (元)'].min()))
            speed_norm = speed_score / (1 / (1 + comparison_df.loc['训练时间 (秒)'].min()))
            
            composite_score = 0.6 * r2_norm + 0.3 * mae_norm + 0.1 * speed_norm
            scores[model] = composite_score
        
        best_overall = max(scores, key=scores.get)
        print(f"  - 综合最佳: {best_overall} (综合得分: {scores[best_overall]:.4f})")
    
    return comparison_df


def save_models_and_results(results):
    """保存模型和结果"""
    print("\n保存模型和结果...")
    
    models_dir = project_root / "models" / "billing_audit" / "multi_algorithm"
    models_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    saved_files = []
    
    for model_name, result in results.items():
        if result['model'] is not None:
            # 保存模型
            model_path = models_dir / f"{model_name}_model_{timestamp}.pkl"
            joblib.dump(result['model'], model_path)
            saved_files.append(str(model_path))
            
            # 保存特征重要性
            importance_path = models_dir / f"{model_name}_feature_importance_{timestamp}.csv"
            result['feature_importance'].to_csv(importance_path, index=False)
            saved_files.append(str(importance_path))
            
            print(f"  {model_name}模型已保存: {model_path.name}")
    
    # 保存性能对比结果
    comparison_path = models_dir / f"model_comparison_{timestamp}.json"
    comparison_data = {}
    for model_name, result in results.items():
        if result['metrics'] is not None:
            comparison_data[model_name] = result['metrics']
    
    with open(comparison_path, 'w', encoding='utf-8') as f:
        json.dump(comparison_data, f, indent=2, ensure_ascii=False)
    saved_files.append(str(comparison_path))
    
    print(f"  性能对比结果已保存: {comparison_path.name}")
    print(f"  共保存 {len(saved_files)} 个文件")
    
    return saved_files


def main():
    """主函数"""
    print("多算法模型训练对比")
    print("=" * 60)
    
    try:
        # 加载和预处理数据
        X, y, config = load_and_preprocess_data()
        
        # 创建增强特征
        X_enhanced, label_encoders, scaler = create_enhanced_features(X, config)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_enhanced, y, test_size=0.2, random_state=42
        )
        
        print(f"\n数据分割完成:")
        print(f"  - 训练集: {X_train.shape[0]} 样本")
        print(f"  - 测试集: {X_test.shape[0]} 样本")
        print(f"  - 特征数量: {X_train.shape[1]}")
        
        # 训练不同算法
        results = {}
        
        # RandomForest
        rf_model, rf_metrics, rf_importance = train_random_forest(X_train, X_test, y_train, y_test)
        results['RandomForest'] = {
            'model': rf_model,
            'metrics': rf_metrics,
            'feature_importance': rf_importance
        }
        
        # XGBoost
        xgb_model, xgb_metrics, xgb_importance = train_xgboost(X_train, X_test, y_train, y_test)
        results['XGBoost'] = {
            'model': xgb_model,
            'metrics': xgb_metrics,
            'feature_importance': xgb_importance
        }
        
        # LightGBM
        lgb_model, lgb_metrics, lgb_importance = train_lightgbm(X_train, X_test, y_train, y_test)
        results['LightGBM'] = {
            'model': lgb_model,
            'metrics': lgb_metrics,
            'feature_importance': lgb_importance
        }
        
        # 对比分析
        comparison_df = compare_models(results)
        
        # 保存结果
        saved_files = save_models_and_results(results)
        
        print(f"\n多算法训练对比完成！")
        print(f"训练了 {len([r for r in results.values() if r['model'] is not None])} 个模型")
        print(f"保存了 {len(saved_files)} 个文件")
        
        return True
        
    except Exception as e:
        print(f"多算法训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
