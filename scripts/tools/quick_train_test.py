#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速训练测试脚本
使用新的模拟数据进行简单的模型训练测试
"""

import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_config():
    """加载配置文件"""
    config_path = project_root / "config" / "billing_audit_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_and_preprocess_data():
    """加载和预处理数据"""
    print("加载和预处理数据...")
    
    # 加载配置
    config = load_config()
    data_file = project_root / config['data_sources']['fixed_fee_sample']
    fixed_fee_config = config['billing_audit']['fixed_fee']
    
    # 加载数据
    df = pd.read_excel(data_file)
    print(f"数据加载成功: {df.shape[0]} 行 × {df.shape[1]} 列")
    
    # 提取特征和目标变量
    feature_columns = fixed_fee_config['feature_columns']
    target_column = fixed_fee_config['target_column']
    
    X = df[feature_columns].copy()
    y = df[target_column].copy()
    
    print(f"特征提取完成: {X.shape[1]} 个特征")
    
    # 处理类别变量
    categorical_columns = fixed_fee_config['categorical_columns']
    label_encoders = {}
    
    for col in categorical_columns:
        if col in X.columns:
            le = LabelEncoder()
            X[col] = le.fit_transform(X[col].astype(str))
            label_encoders[col] = le
    
    print(f"类别变量编码完成: {len(categorical_columns)} 个字段")
    
    # 处理数值变量
    numerical_columns = fixed_fee_config['numerical_columns']
    scaler = StandardScaler()
    
    for col in numerical_columns:
        if col in X.columns:
            X[col] = scaler.fit_transform(X[[col]])
    
    print(f"数值变量标准化完成: {len(numerical_columns)} 个字段")
    
    # 处理日期变量 (简单处理，转换为数值)
    date_columns = fixed_fee_config['date_columns']
    for col in date_columns:
        if col in X.columns:
            # 将yyyymm格式转换为数值
            X[col] = pd.to_numeric(X[col], errors='coerce')
    
    print(f"日期变量处理完成: {len(date_columns)} 个字段")
    
    return X, y, label_encoders, scaler


def train_and_evaluate_model(X, y):
    """训练和评估模型"""
    print("\n训练和评估模型...")
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    print(f"数据分割完成:")
    print(f"  - 训练集: {X_train.shape[0]} 样本")
    print(f"  - 测试集: {X_test.shape[0]} 样本")
    
    # 训练模型
    model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    
    print("开始训练模型...")
    model.fit(X_train, y_train)
    print("模型训练完成")
    
    # 预测
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # 评估指标
    train_mae = mean_absolute_error(y_train, y_pred_train)
    test_mae = mean_absolute_error(y_test, y_pred_test)
    
    train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
    test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
    
    train_r2 = r2_score(y_train, y_pred_train)
    test_r2 = r2_score(y_test, y_pred_test)
    
    print("\n模型性能评估:")
    print(f"训练集指标:")
    print(f"  - MAE: {train_mae:.2f}")
    print(f"  - RMSE: {train_rmse:.2f}")
    print(f"  - R²: {train_r2:.4f}")
    
    print(f"测试集指标:")
    print(f"  - MAE: {test_mae:.2f}")
    print(f"  - RMSE: {test_rmse:.2f}")
    print(f"  - R²: {test_r2:.4f}")
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\n特征重要性 (Top 10):")
    for i, row in feature_importance.head(10).iterrows():
        print(f"  {row['feature']}: {row['importance']:.4f}")
    
    # 业务准确性评估 (在阈值内的预测比例)
    threshold = 50.0  # 50元阈值
    accurate_predictions = np.abs(y_test - y_pred_test) <= threshold
    business_accuracy = accurate_predictions.mean() * 100
    
    print(f"\n💼 业务准确性:")
    print(f"  - 阈值: ±{threshold}元")
    print(f"  - 准确率: {business_accuracy:.1f}%")
    
    return model, {
        'train_mae': train_mae,
        'test_mae': test_mae,
        'train_rmse': train_rmse,
        'test_rmse': test_rmse,
        'train_r2': train_r2,
        'test_r2': test_r2,
        'business_accuracy': business_accuracy,
        'feature_importance': feature_importance
    }


def test_prediction_examples(model, X, y, label_encoders, scaler):
    """测试几个预测示例"""
    print("\n预测示例测试:")
    
    # 随机选择几个样本进行预测
    sample_indices = np.random.choice(len(X), size=5, replace=False)
    
    for i, idx in enumerate(sample_indices):
        actual = y.iloc[idx]
        predicted = model.predict(X.iloc[[idx]])[0]
        error = abs(actual - predicted)
        
        print(f"  示例 {i+1}:")
        print(f"    实际金额: {actual:.2f}元")
        print(f"    预测金额: {predicted:.2f}元")
        print(f"    绝对误差: {error:.2f}元")
        print(f"    相对误差: {error/max(actual, 0.01)*100:.1f}%")


def main():
    """主函数"""
    print("快速模型训练测试")
    print("=" * 50)
    
    try:
        # 加载和预处理数据
        X, y, label_encoders, scaler = load_and_preprocess_data()
        
        # 训练和评估模型
        model, metrics = train_and_evaluate_model(X, y)
        
        # 测试预测示例
        test_prediction_examples(model, X, y, label_encoders, scaler)
        
        print(f"\n快速训练测试完成！")
        print(f"模型性能总结:")
        print(f"  - 测试集MAE: {metrics['test_mae']:.2f}元")
        print(f"  - 测试集R²: {metrics['test_r2']:.4f}")
        print(f"  - 业务准确率: {metrics['business_accuracy']:.1f}%")
        
        # 判断模型质量
        if metrics['test_r2'] > 0.8:
            print("模型质量优秀！")
        elif metrics['test_r2'] > 0.6:
            print("模型质量良好！")
        elif metrics['test_r2'] > 0.4:
            print("模型质量一般，建议优化")
        else:
            print("模型质量较差，需要改进")
        
        return True
        
    except Exception as e:
        print(f"训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
