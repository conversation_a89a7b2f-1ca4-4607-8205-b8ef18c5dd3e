#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量删除代码文件中的emoji表情
"""

import os
import re
from pathlib import Path

def remove_emojis_from_text(text):
    """删除文本中的emoji表情"""
    # 定义常见的emoji表情
    emoji_patterns = [
        r'', r'', r'', r'', r'', r'', r'', r'', r'', r'',
        r'', r'', r'', r'', r'', r'', r'', r'', r'', r'',
        r'', r'', r'', r'', r'', r'', r'', r'', r'', r'',
        r'', r'', r'', r'', r'', r'', r'', r'', r'', r'',
        r'', r'', r'', r'', r'', r'', r'', r''
    ]
    
    # 删除所有emoji
    for emoji in emoji_patterns:
        text = re.sub(emoji + r'\s*', '', text)
    
    return text

def process_file(file_path):
    """处理单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        new_content = remove_emojis_from_text(content)
        
        if new_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"已处理: {file_path}")
            return True
        else:
            print(f"无需处理: {file_path}")
            return False
            
    except Exception as e:
        print(f"处理失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("开始批量删除代码文件中的emoji表情")
    print("=" * 50)
    
    # 项目根目录
    project_root = Path(__file__).parent.parent.parent

    # 查找所有Python文件（src和scripts目录）
    python_files = []
    python_files.extend(list((project_root / "src").rglob("*.py")))
    python_files.extend(list((project_root / "scripts").rglob("*.py")))
    
    processed_count = 0
    total_count = len(python_files)
    
    for file_path in python_files:
        if process_file(file_path):
            processed_count += 1
    
    print(f"\n处理完成:")
    print(f"  - 总文件数: {total_count}")
    print(f"  - 已处理: {processed_count}")
    print(f"  - 无需处理: {total_count - processed_count}")

if __name__ == "__main__":
    main()
