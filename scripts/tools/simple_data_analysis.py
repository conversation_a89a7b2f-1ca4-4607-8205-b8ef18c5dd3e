#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的数据样例分析脚本
"""

import pandas as pd
import os
from pathlib import Path

def analyze_file(file_path, name):
    """分析单个文件"""
    print(f"\n{'='*50}")
    print(f"{name}")
    print(f"{'='*50}")
    
    try:
        df = pd.read_excel(file_path)
        print(f"数据维度: {df.shape[0]} 行 × {df.shape[1]} 列")
        print(f"文件大小: {os.path.getsize(file_path) / 1024:.1f} KB")
        
        print(f"\n字段列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i}. {col}")
        
        print(f"\n数据预览:")
        print(df.head(3).to_string())
        
        return df
    except Exception as e:
        print(f"错误: {e}")
        return None

def main():
    """主函数"""
    print("数据样例分析")
    
    project_root = Path(__file__).parent.parent
    data_dir = project_root / "数据"
    
    files = [
        ("固费预测样例@20250707.xlsx", "固定费用预测样例"),
        ("优惠预测样例@20250707.xlsx", "优惠费用预测样例"),
        ("话单增量样例.xlsx", "话单增量样例")
    ]
    
    results = {}
    for filename, description in files:
        file_path = data_dir / filename
        if file_path.exists():
            df = analyze_file(file_path, description)
            if df is not None:
                results[filename] = df
    
    print(f"\n{'='*60}")
    print("分析总结")
    print(f"{'='*60}")
    for filename, df in results.items():
        print(f"{filename}: {df.shape[0]}行 × {df.shape[1]}列")

if __name__ == "__main__":
    main()
