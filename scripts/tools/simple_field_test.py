#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的新字段处理测试
直接测试新字段结构的数据处理逻辑
"""

import sys
import pandas as pd
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_config():
    """加载配置文件"""
    config_path = project_root / "config" / "billing_audit_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def test_new_field_validation():
    """测试新字段的验证逻辑"""
    print("测试新字段验证逻辑...")
    
    # 创建测试数据（包含有效和无效数据用于验证测试）
    test_data = {
        'final_eff_year': [2024, 2024, 2023, 2025],
        'final_eff_mon': [1, 6, 12, 13],  # 包含一个无效月份
        'final_eff_day': [15, 31, 1, 32],  # 包含一个无效日期
        'final_exp_year': [2024, 2025, 2024, 2025],
        'final_exp_mon': [12, 6, 12, 6],
        'final_exp_day': [31, 30, 31, 30],
        'amount': [100.0, 200.0, 150.0, 300.0]
    }

    # 创建有效数据用于特征创建测试
    valid_test_data = {
        'final_eff_year': [2024, 2024, 2023, 2025],
        'final_eff_mon': [1, 6, 12, 6],   # 修正无效月份
        'final_eff_day': [15, 30, 1, 15], # 修正无效日期
        'final_exp_year': [2024, 2025, 2024, 2025],
        'final_exp_mon': [12, 6, 12, 6],
        'final_exp_day': [31, 30, 31, 30],
        'amount': [100.0, 200.0, 150.0, 300.0]
    }
    
    df = pd.DataFrame(test_data)
    print(f"测试数据创建: {df.shape}")
    
    # 验证年份字段
    def validate_year_field(series, col_name):
        result = {'is_valid': True, 'errors': [], 'warnings': []}
        valid_years = series.dropna()
        if len(valid_years) > 0:
            invalid_years = valid_years[(valid_years < 2020) | (valid_years > 2030)]
            if len(invalid_years) > 0:
                result['warnings'].append(f"列 '{col_name}' 有 {len(invalid_years)} 个年份超出合理范围")
        return result
    
    # 验证月份字段
    def validate_month_field(series, col_name):
        result = {'is_valid': True, 'errors': [], 'warnings': []}
        valid_months = series.dropna()
        if len(valid_months) > 0:
            invalid_months = valid_months[(valid_months < 1) | (valid_months > 12)]
            if len(invalid_months) > 0:
                result['is_valid'] = False
                result['errors'].append(f"列 '{col_name}' 有 {len(invalid_months)} 个无效月份")
        return result
    
    # 验证日期字段
    def validate_day_field(series, col_name):
        result = {'is_valid': True, 'errors': [], 'warnings': []}
        valid_days = series.dropna()
        if len(valid_days) > 0:
            invalid_days = valid_days[(valid_days < 1) | (valid_days > 31)]
            if len(invalid_days) > 0:
                result['is_valid'] = False
                result['errors'].append(f"列 '{col_name}' 有 {len(invalid_days)} 个无效日期")
        return result
    
    # 执行验证
    year_fields = ['final_eff_year', 'final_exp_year']
    month_fields = ['final_eff_mon', 'final_exp_mon']
    day_fields = ['final_eff_day', 'final_exp_day']
    
    all_valid = True
    
    for field in year_fields:
        result = validate_year_field(df[field], field)
        if result['warnings']:
            print(f"  {field}: {result['warnings'][0]}")
        else:
            print(f"  {field}: 验证通过")
    
    for field in month_fields:
        result = validate_month_field(df[field], field)
        if result['errors']:
            print(f"  {field}: {result['errors'][0]}")
            all_valid = False
        else:
            print(f"  {field}: 验证通过")
    
    for field in day_fields:
        result = validate_day_field(df[field], field)
        if result['errors']:
            print(f"  {field}: {result['errors'][0]}")
            all_valid = False
        else:
            print(f"  {field}: 验证通过")
    
    return all_valid, df


def test_date_feature_creation(df):
    """测试日期特征创建"""
    print("\n测试日期特征创建...")
    
    df_features = df.copy()
    
    try:
        # 组合生效日期
        if all(col in df.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']):
            eff_date = pd.to_datetime(
                df[['final_eff_year', 'final_eff_mon', 'final_eff_day']].rename(
                    columns={'final_eff_year': 'year', 'final_eff_mon': 'month', 'final_eff_day': 'day'}
                ), errors='coerce'
            )
            
            # 创建生效日期的额外特征
            df_features['final_eff_dayofweek'] = eff_date.dt.dayofweek
            df_features['final_eff_quarter'] = eff_date.dt.quarter
            df_features['final_eff_is_weekend'] = (eff_date.dt.dayofweek >= 5).astype(int)
            
            print(f"  生效日期特征创建成功")
        
        # 组合失效日期
        if all(col in df.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day']):
            exp_date = pd.to_datetime(
                df[['final_exp_year', 'final_exp_mon', 'final_exp_day']].rename(
                    columns={'final_exp_year': 'year', 'final_exp_mon': 'month', 'final_exp_day': 'day'}
                ), errors='coerce'
            )
            
            # 创建失效日期的额外特征
            df_features['final_exp_dayofweek'] = exp_date.dt.dayofweek
            df_features['final_exp_quarter'] = exp_date.dt.quarter
            df_features['final_exp_is_weekend'] = (exp_date.dt.dayofweek >= 5).astype(int)
            
            print(f"  失效日期特征创建成功")
        
        # 创建组合特征
        if (all(col in df.columns for col in ['final_eff_year', 'final_eff_mon', 'final_eff_day']) and
            all(col in df.columns for col in ['final_exp_year', 'final_exp_mon', 'final_exp_day'])):
            
            # 计算订阅时长（天数）
            df_features['subscription_duration_days'] = (exp_date - eff_date).dt.days
            
            # 创建生效和失效的季度差异
            df_features['quarter_diff'] = (
                exp_date.dt.quarter - eff_date.dt.quarter + 
                (exp_date.dt.year - eff_date.dt.year) * 4
            )
            
            print(f"  组合日期特征创建成功")
        
        # 显示新创建的特征
        original_cols = set(df.columns)
        new_cols = set(df_features.columns) - original_cols
        
        print(f"  原始字段数: {len(original_cols)}")
        print(f"  新增特征数: {len(new_cols)}")
        print(f"  总字段数: {len(df_features.columns)}")
        
        if new_cols:
            print(f"  新增特征列表:")
            for col in sorted(new_cols):
                print(f"    - {col}")
        
        return True, df_features
        
    except Exception as e:
        print(f"  日期特征创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, df


def test_config_compatibility():
    """测试配置兼容性"""
    print("\n⚙️  测试配置兼容性...")
    
    try:
        config = load_config()
        fixed_fee_config = config['billing_audit']['fixed_fee']
        
        # 检查新字段是否在配置中
        feature_columns = fixed_fee_config['feature_columns']
        new_fields = ['final_eff_year', 'final_eff_mon', 'final_eff_day',
                     'final_exp_year', 'final_exp_mon', 'final_exp_day']
        
        missing_fields = [field for field in new_fields if field not in feature_columns]
        if missing_fields:
            print(f"  配置中缺少新字段: {missing_fields}")
            return False
        else:
            print(f"  所有新字段都在配置中")
        
        # 检查旧字段是否已移除
        old_fields = ['final_eff_date', 'final_exp_date']
        remaining_old_fields = [field for field in old_fields if field in feature_columns]
        if remaining_old_fields:
            print(f"  配置中仍有旧字段: {remaining_old_fields}")
            return False
        else:
            print(f"  旧字段已从配置中移除")
        
        # 显示配置统计
        print(f"  特征字段总数: {len(feature_columns)}")
        print(f"  透传字段总数: {len(fixed_fee_config['passthrough_columns'])}")
        print(f"  类别字段总数: {len(fixed_fee_config['categorical_columns'])}")
        print(f"  数值字段总数: {len(fixed_fee_config['numerical_columns'])}")
        print(f"  日期字段总数: {len(fixed_fee_config['date_columns'])}")
        
        return True
        
    except Exception as e:
        print(f"  配置兼容性测试失败: {e}")
        return False


def main():
    """主函数"""
    print("新字段处理逻辑简单测试")
    print("=" * 50)
    
    # 测试配置兼容性
    config_ok = test_config_compatibility()
    
    # 测试字段验证
    validation_ok, test_df = test_new_field_validation()
    
    # 测试特征创建（使用有效数据）
    print("\n使用有效数据测试特征创建...")
    valid_test_data = {
        'final_eff_year': [2024, 2024, 2023, 2025],
        'final_eff_mon': [1, 6, 12, 6],   # 修正无效月份
        'final_eff_day': [15, 30, 1, 15], # 修正无效日期
        'final_exp_year': [2024, 2025, 2024, 2025],
        'final_exp_mon': [12, 6, 12, 6],
        'final_exp_day': [31, 30, 31, 30],
        'amount': [100.0, 200.0, 150.0, 300.0]
    }
    valid_df = pd.DataFrame(valid_test_data)
    feature_ok, df_with_features = test_date_feature_creation(valid_df)
    
    # 总结
    print(f"\n测试结果总结:")
    print(f"  - 配置兼容性: {'通过' if config_ok else '失败'}")
    print(f"  - 字段验证: {'通过' if validation_ok else '失败'}")
    print(f"  - 特征创建: {'通过' if feature_ok else '失败'}")
    
    if config_ok and validation_ok and feature_ok:
        print(f"\n所有测试通过！新字段处理逻辑工作正常。")
        return True
    else:
        print(f"\n部分测试失败，需要进一步检查。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
