#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模拟数据格式脚本
验证生成的模拟数据是否符合新的字段要求
"""

import sys
import pandas as pd
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def load_config():
    """加载配置文件"""
    config_path = project_root / "config" / "billing_audit_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def test_data_format(data_file):
    """
    测试数据格式是否符合配置要求
    
    Args:
        data_file: 数据文件路径
    """
    print(f"测试数据格式: {data_file}")
    
    try:
        # 加载数据
        df = pd.read_excel(data_file)
        print(f"数据加载成功: {df.shape[0]} 行 × {df.shape[1]} 列")
        
        # 加载配置
        config = load_config()
        fixed_fee_config = config['billing_audit']['fixed_fee']
        
        # 检查必需字段
        print("\n检查必需字段:")
        
        # 特征字段
        feature_columns = fixed_fee_config['feature_columns']
        missing_features = [col for col in feature_columns if col not in df.columns]
        if missing_features:
            print(f"缺少特征字段: {missing_features}")
            return False
        else:
            print(f"所有特征字段存在 ({len(feature_columns)} 个)")
        
        # 目标字段
        target_column = fixed_fee_config['target_column']
        if target_column not in df.columns:
            print(f"缺少目标字段: {target_column}")
            return False
        else:
            print(f"目标字段存在: {target_column}")
        
        # 透传字段
        passthrough_columns = fixed_fee_config['passthrough_columns']
        missing_passthrough = [col for col in passthrough_columns if col not in df.columns]
        if missing_passthrough:
            print(f"缺少透传字段: {missing_passthrough}")
            return False
        else:
            print(f"所有透传字段存在 ({len(passthrough_columns)} 个)")
        
        # 检查数据类型
        print("\n🔢 检查数据类型:")
        
        # 类别字段
        categorical_columns = fixed_fee_config['categorical_columns']
        for col in categorical_columns:
            if col in df.columns:
                unique_values = df[col].nunique()
                print(f"  - {col}: {unique_values} 个唯一值")
        
        # 数值字段
        numerical_columns = fixed_fee_config['numerical_columns']
        for col in numerical_columns:
            if col in df.columns:
                min_val = df[col].min()
                max_val = df[col].max()
                print(f"  - {col}: {min_val} - {max_val}")
        
        # 日期字段
        date_columns = fixed_fee_config['date_columns']
        for col in date_columns:
            if col in df.columns:
                unique_values = df[col].nunique()
                print(f"  - {col}: {unique_values} 个唯一值")
        
        # 检查新字段的数据质量
        print("\n检查新字段数据质量:")
        
        # 年月日字段范围检查
        year_fields = ['final_eff_year', 'final_exp_year']
        for field in year_fields:
            if field in df.columns:
                years = df[field].dropna()
                if len(years) > 0:
                    if years.min() < 2020 or years.max() > 2030:
                        print(f"{field} 年份范围异常: {years.min()} - {years.max()}")
                    else:
                        print(f"{field} 年份范围正常: {years.min()} - {years.max()}")
        
        month_fields = ['final_eff_mon', 'final_exp_mon']
        for field in month_fields:
            if field in df.columns:
                months = df[field].dropna()
                if len(months) > 0:
                    invalid_months = months[(months < 1) | (months > 12)]
                    if len(invalid_months) > 0:
                        print(f"{field} 包含无效月份: {invalid_months.unique()}")
                    else:
                        print(f"{field} 月份范围正常: {months.min()} - {months.max()}")
        
        day_fields = ['final_eff_day', 'final_exp_day']
        for field in day_fields:
            if field in df.columns:
                days = df[field].dropna()
                if len(days) > 0:
                    invalid_days = days[(days < 1) | (days > 31)]
                    if len(invalid_days) > 0:
                        print(f"{field} 包含无效日期: {invalid_days.unique()}")
                    else:
                        print(f"{field} 日期范围正常: {days.min()} - {days.max()}")
        
        # 业务逻辑检查
        print("\n🏢 检查业务逻辑:")
        
        # 检查busi_flag和amount的关系
        no_charge_records = df[df['busi_flag'] == 1]
        if len(no_charge_records) > 0:
            non_zero_amounts = no_charge_records[no_charge_records['amount'] > 0]
            if len(non_zero_amounts) > 0:
                print(f"发现 {len(non_zero_amounts)} 条不收费但金额非零的记录")
            else:
                print(f"不收费记录的金额都为零 ({len(no_charge_records)} 条)")
        
        # 检查cal_type和charge_day_count的关系
        non_monthly_records = df[df['cal_type'] == 0]  # 非月租
        if len(non_monthly_records) > 0:
            non_zero_days = non_monthly_records[non_monthly_records['charge_day_count'] > 0]
            if len(non_zero_days) > 0:
                print(f"发现 {len(non_zero_days)} 条非月租但计费天数非零的记录")
            else:
                print(f"非月租记录的计费天数都为零 ({len(non_monthly_records)} 条)")
        
        # 数据统计
        print("\n数据统计:")
        print(f"  - 总记录数: {len(df)}")
        print(f"  - 收费记录: {len(df[df['busi_flag'] == 0])} ({len(df[df['busi_flag'] == 0])/len(df)*100:.1f}%)")
        print(f"  - 不收费记录: {len(df[df['busi_flag'] == 1])} ({len(df[df['busi_flag'] == 1])/len(df)*100:.1f}%)")
        print(f"  - 零金额记录: {len(df[df['amount'] == 0])} ({len(df[df['amount'] == 0])/len(df)*100:.1f}%)")
        print(f"  - 平均金额: {df['amount'].mean():.2f}")
        print(f"  - 金额范围: {df['amount'].min():.2f} - {df['amount'].max():.2f}")
        
        print("\n数据格式测试通过！")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def main():
    """主函数"""
    print("模拟数据格式测试")
    print("=" * 50)
    
    # 获取最新的模拟数据文件
    data_dir = project_root / "数据"
    mock_files = list(data_dir.glob("固费预测模拟数据_*.xlsx"))
    
    if not mock_files:
        print("未找到模拟数据文件")
        return
    
    # 使用最新的文件
    latest_file = max(mock_files, key=lambda x: x.stat().st_mtime)
    print(f"测试文件: {latest_file.name}")
    
    # 执行测试
    success = test_data_format(latest_file)
    
    if success:
        print(f"\n测试完成！数据格式符合要求，可以用于模型训练。")
    else:
        print(f"\n测试失败！请检查数据格式。")


if __name__ == "__main__":
    main()
