#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新文档中的文件引用
"""

import os
import re
from pathlib import Path

def update_references():
    """更新文档引用"""
    print("开始更新文档引用")
    print("=" * 50)
    
    project_root = Path(__file__).parent.parent.parent
    
    # 引用更新映射表
    reference_map = {
        # 核心文档
        "DOCUMENT_INDEX.md": "文档索引.md",
        "TECHNICAL_SPECIFICATIONS.md": "技术规格文档.md",
        
        # 指南文档
        "API_USAGE_GUIDE.md": "API使用指南.md",
        "LARGE_SCALE_END_TO_END_GUIDE.md": "大规模端到端指南.md",
        "PRODUCTION_SCRIPTS_GUIDE.md": "生产脚本指南.md",
        "QUICK_START_GUIDE.md": "快速开始指南.md",
        
        # 技术文档
        "BILLING_JUDGMENT_ADAPTATION_REPORT.md": "收费判定适配报告.md",
        "CONFIG_MANAGEMENT_IMPROVEMENTS.md": "配置管理改进.md",
        "CONFIG_MANAGEMENT_E2E_TEST_REPORT.md": "配置管理端到端测试报告.md",
        "CODE_SEPARATION_SUMMARY.md": "代码分离总结.md",
        "END_TO_END_TEST_REPORT.md": "端到端测试报告.md",
        "LARGE_SCALE_DATA_PROCESSING_GUIDE.md": "大规模数据处理指南.md",
        "PRODUCTION_DEPLOYMENT_GUIDE.md": "生产环境部署指南.md",
        "PRODUCTION_SCRIPTS_REFACTORING.md": "生产脚本重构.md",
        "CHANGELOG_CONFIG_MANAGEMENT.md": "配置管理变更日志.md",
        
        # 报告文档
        "MOCK_DATA_GENERATION_REPORT.md": "模拟数据生成报告.md",
        "MODEL_TRAINING_SUMMARY.md": "模型训练总结.md",
        "MULTI_ALGORITHM_COMPARISON_REPORT.md": "多算法对比报告.md",
        
        # 归档文档
        "CODE_DOCUMENTATION.md": "代码文档.md",
        "FIELD_CHANGES_LOG.md": "字段变更日志.md",
        "MODEL_RETRAINING_REPORT.md": "模型重训练报告.md",
        "MODEL_RETRAINING_SUMMARY.md": "模型重训练总结.md",
        "MULTI_ALGORITHM_TRAINING_SUMMARY.md": "多算法训练总结.md",
        "PREPROCESSING_UPDATE_REPORT.md": "预处理更新报告.md",
        
        # 顶级文档
        "DOCS_INDEX.md": "文档中心.md"
    }
    
    # 需要更新的文件
    files_to_update = []
    
    # 查找所有markdown文件
    for md_file in project_root.rglob("*.md"):
        if "legacy_code" not in str(md_file) and "venv" not in str(md_file):
            files_to_update.append(md_file)
    
    updated_files = 0
    
    for file_path in files_to_update:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 更新所有引用
            for old_name, new_name in reference_map.items():
                # 更新链接引用 [text](filename)
                content = re.sub(
                    r'\[([^\]]*)\]\(' + re.escape(old_name) + r'\)',
                    r'[\1](' + new_name + ')',
                    content
                )
                
                # 更新相对路径引用 [text](../path/filename)
                content = re.sub(
                    r'\[([^\]]*)\]\(([^)]*/)' + re.escape(old_name) + r'\)',
                    r'[\1](\2' + new_name + ')',
                    content
                )
                
                # 更新直接文件名引用
                content = content.replace(old_name, new_name)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"已更新: {file_path}")
                updated_files += 1
                
        except Exception as e:
            print(f"更新失败 {file_path}: {e}")
    
    print(f"\n引用更新完成: {updated_files} 个文件")
    return updated_files

def main():
    """主函数"""
    try:
        updated_count = update_references()
        print(f"\n文档引用更新完成！更新了 {updated_count} 个文件")
    except Exception as e:
        print(f"执行失败: {e}")

if __name__ == "__main__":
    main()
