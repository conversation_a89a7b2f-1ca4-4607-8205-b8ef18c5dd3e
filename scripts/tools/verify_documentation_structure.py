#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证文档结构和归档是否正确
"""

import os
from pathlib import Path

def verify_documentation_structure():
    """验证文档结构"""
    print("验证文档结构和归档...")
    
    # 检查核心文档
    core_docs = [
        "docs/core/DOCUMENT_INDEX.md",
        "docs/core/README.md",
        "docs/core/TECHNICAL_SPECIFICATIONS.md"
    ]
    
    print("\n检查核心文档...")
    for doc in core_docs:
        if os.path.exists(doc):
            print(f"  {doc}")
        else:
            print(f"  {doc} - 未找到")
    
    # 检查技术文档
    technical_docs = [
        "docs/technical/CODE_SEPARATION_SUMMARY.md",
        "docs/technical/LARGE_SCALE_DATA_PROCESSING_GUIDE.md",
        "docs/technical/PRODUCTION_DEPLOYMENT_GUIDE.md",
        "docs/technical/END_TO_END_TEST_REPORT.md"
    ]
    
    print("\n检查技术文档...")
    for doc in technical_docs:
        if os.path.exists(doc):
            print(f"  {doc}")
        else:
            print(f"  {doc} - 未找到")
    
    # 检查指南文档
    guide_docs = [
        "docs/guides/QUICK_START_GUIDE.md",
        "docs/guides/API_USAGE_GUIDE.md",
        "docs/guides/PRODUCTION_SCRIPTS_GUIDE.md"
    ]
    
    print("\n📖 检查指南文档...")
    for doc in guide_docs:
        if os.path.exists(doc):
            print(f"  {doc}")
        else:
            print(f"  {doc} - 未找到")
    
    # 检查legacy_code文档
    legacy_docs = [
        "legacy_code/README.md"
    ]
    
    print("\n🗂️  检查存档文档...")
    for doc in legacy_docs:
        if os.path.exists(doc):
            print(f"  {doc}")
        else:
            print(f"  {doc} - 未找到")
    
    # 检查根目录是否清理干净
    print("\n检查根目录清理...")
    root_files_to_check = [
        "CODE_SEPARATION_SUMMARY.md",
        "test_code_separation.py"
    ]
    
    clean_root = True
    for file_name in root_files_to_check:
        if os.path.exists(file_name):
            print(f"  {file_name} - 仍在根目录")
            clean_root = False
    
    if clean_root:
        print("  根目录已清理完毕")
    
    # 统计文档数量
    print("\n文档统计...")
    
    def count_files_in_dir(directory, extension=".md"):
        if not os.path.exists(directory):
            return 0
        return len([f for f in os.listdir(directory) if f.endswith(extension)])
    
    core_count = count_files_in_dir("docs/core")
    technical_count = count_files_in_dir("docs/technical")
    guides_count = count_files_in_dir("docs/guides")
    reports_count = count_files_in_dir("docs/reports")
    archive_count = count_files_in_dir("docs/archive")
    
    print(f"  - 核心文档: {core_count} 个")
    print(f"  - 技术文档: {technical_count} 个")
    print(f"  - 指南文档: {guides_count} 个")
    print(f"  - 报告文档: {reports_count} 个")
    print(f"  - 存档文档: {archive_count} 个")
    print(f"  - 总计: {core_count + technical_count + guides_count + reports_count + archive_count} 个文档")
    
    print("\n文档结构验证完成！")
    return True

if __name__ == "__main__":
    verify_documentation_structure()
