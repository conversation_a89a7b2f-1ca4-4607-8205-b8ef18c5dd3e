#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据预处理逻辑（不依赖外部库）
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


def validate_config_structure():
    """验证配置文件结构"""
    print("验证配置文件结构...")
    
    try:
        from src.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # 验证固费配置
        fixed_fee_config = config.get_model_config('billing_audit', 'fixed_fee')
        print(f"固费配置验证通过:")
        print(f"  - 特征列数: {len(fixed_fee_config.feature_columns)}")
        print(f"  - 类别列数: {len(fixed_fee_config.categorical_columns)}")
        print(f"  - 数值列数: {len(fixed_fee_config.numerical_columns)}")
        print(f"  - 日期列数: {len(fixed_fee_config.date_columns)}")
        
        # 验证优惠费配置
        discount_config = config.get_model_config('billing_audit', 'discount')
        print(f"优惠费配置验证通过:")
        print(f"  - 特征列数: {len(discount_config.feature_columns)}")
        print(f"  - 类别列数: {len(discount_config.categorical_columns)}")
        print(f"  - 数值列数: {len(discount_config.numerical_columns)}")
        print(f"  - 日期列数: {len(discount_config.date_columns)}")
        
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False


def validate_data_files():
    """验证数据文件存在性"""
    print("\n验证数据文件...")
    
    try:
        from src.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # 检查数据文件
        data_sources = config.get('data_sources', {})
        
        for name, path in data_sources.items():
            full_path = project_root / path
            if full_path.exists():
                file_size = full_path.stat().st_size / 1024  # KB
                print(f"{name}: {path} ({file_size:.1f} KB)")
            else:
                print(f"{name}: {path} (文件不存在)")
        
        return True
        
    except Exception as e:
        print(f"数据文件验证失败: {e}")
        return False


def validate_module_imports():
    """验证模块导入"""
    print("\n验证模块导入...")
    
    try:
        # 测试工具模块
        from src.utils.config_manager import ConfigManager
        print("配置管理器导入成功")
        
        from src.utils.logger import Logger
        print("日志器导入成功")
        
        # 测试预处理模块结构（不实际导入pandas相关代码）
        preprocessing_files = [
            project_root / "src/billing_audit/preprocessing/data_preprocessor.py",
            project_root / "src/billing_audit/preprocessing/feature_engineer.py"
        ]
        
        for file_path in preprocessing_files:
            if file_path.exists():
                print(f"{file_path.name} 文件存在")
            else:
                print(f"{file_path.name} 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"模块导入验证失败: {e}")
        return False


def validate_project_structure():
    """验证项目结构"""
    print("\n验证项目结构...")
    
    required_dirs = [
        "src/billing_audit/preprocessing",
        "src/billing_audit/models", 
        "src/billing_audit/training",
        "src/billing_audit/inference",
        "config",
        "models",
        "logs",
        "tests"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if full_path.exists():
            print(f"{dir_path}/")
        else:
            print(f"{dir_path}/ (目录不存在)")
            missing_dirs.append(dir_path)
    
    return len(missing_dirs) == 0


def main():
    """主函数"""
    print("开始验证数据预处理逻辑...")
    
    tests = [
        ("配置文件结构", validate_config_structure),
        ("数据文件存在性", validate_data_files),
        ("模块导入", validate_module_imports),
        ("项目结构", validate_project_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"{test_name} 测试异常: {e}")
    
    print(f"\n验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("所有验证通过！数据预处理模块结构正确")
        print("\n下一步:")
        print("  1. 在有pandas环境的机器上测试实际数据处理")
        print("  2. 开始开发模型训练模块")
        print("  3. 实现推理和判定功能")
        return True
    else:
        print("部分验证失败，请检查项目配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
