#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分层模型评估器
专门用于评估分层模型的性能，包括零值分类和非零值回归的综合评估
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report,
    mean_absolute_error, mean_squared_error, r2_score,
    mean_absolute_percentage_error
)

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils.logger import get_logger


class HierarchicalModelEvaluator:
    """
    分层模型评估器
    
    专门用于评估分层模型的性能，包括：
    1. 零值分类性能评估
    2. 非零值回归性能评估
    3. 整体模型性能评估
    4. 业务指标评估
    """
    
    def __init__(self, zero_threshold: float = 1e-6):
        """
        初始化评估器
        
        Args:
            zero_threshold: 零值判定阈值
        """
        self.logger = get_logger('hierarchical_model_evaluator')
        self.zero_threshold = zero_threshold
        
        self.logger.info("分层模型评估器初始化完成")
    
    def evaluate_comprehensive(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, Any]:
        """
        综合评估分层模型性能
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            综合评估结果
        """
        self.logger.info("开始综合评估分层模型性能...")
        
        # 1. 零值分类评估
        classification_metrics = self.evaluate_zero_classification(y_true, y_pred)
        
        # 2. 非零值回归评估
        regression_metrics = self.evaluate_nonzero_regression(y_true, y_pred)
        
        # 3. 整体性能评估
        overall_metrics = self.evaluate_overall_performance(y_true, y_pred)
        
        # 4. 业务指标评估
        business_metrics = self.evaluate_business_metrics(y_true, y_pred)
        
        # 5. 数据分布分析
        distribution_analysis = self.analyze_prediction_distribution(y_true, y_pred)
        
        # 综合结果
        comprehensive_results = {
            'classification_metrics': classification_metrics,
            'regression_metrics': regression_metrics,
            'overall_metrics': overall_metrics,
            'business_metrics': business_metrics,
            'distribution_analysis': distribution_analysis,
            'summary': self._generate_performance_summary(
                classification_metrics, regression_metrics, overall_metrics, business_metrics
            )
        }
        
        self.logger.info("综合评估完成")
        return comprehensive_results
    
    def evaluate_zero_classification(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, Any]:
        """
        评估零值分类性能
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            分类性能指标
        """
        # 转换为二分类标签
        y_true_binary = (np.abs(y_true) <= self.zero_threshold).astype(int)
        y_pred_binary = (np.abs(y_pred) <= self.zero_threshold).astype(int)
        
        # 基础分类指标
        accuracy = accuracy_score(y_true_binary, y_pred_binary)
        precision = precision_score(y_true_binary, y_pred_binary, zero_division=0)
        recall = recall_score(y_true_binary, y_pred_binary, zero_division=0)
        f1 = f1_score(y_true_binary, y_pred_binary, zero_division=0)
        
        # 混淆矩阵
        cm = confusion_matrix(y_true_binary, y_pred_binary)
        
        # 详细分类报告
        class_report = classification_report(
            y_true_binary, y_pred_binary,
            target_names=['非零值', '零值'],
            output_dict=True,
            zero_division=0
        )
        
        # 零值和非零值的具体性能
        zero_precision = class_report['零值']['precision']
        zero_recall = class_report['零值']['recall']
        zero_f1 = class_report['零值']['f1-score']
        
        nonzero_precision = class_report['非零值']['precision']
        nonzero_recall = class_report['非零值']['recall']
        nonzero_f1 = class_report['非零值']['f1-score']
        
        classification_metrics = {
            'overall_accuracy': accuracy,
            'overall_precision': precision,
            'overall_recall': recall,
            'overall_f1': f1,
            'zero_class': {
                'precision': zero_precision,
                'recall': zero_recall,
                'f1_score': zero_f1
            },
            'nonzero_class': {
                'precision': nonzero_precision,
                'recall': nonzero_recall,
                'f1_score': nonzero_f1
            },
            'confusion_matrix': cm.tolist(),
            'classification_report': class_report
        }
        
        self.logger.info(f"零值分类评估: 准确率={accuracy:.4f}, F1={f1:.4f}")
        return classification_metrics
    
    def evaluate_nonzero_regression(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, Any]:
        """
        评估非零值回归性能
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            回归性能指标
        """
        # 提取非零值数据
        nonzero_mask = np.abs(y_true) > self.zero_threshold
        
        if not np.any(nonzero_mask):
            self.logger.warning("没有非零值数据，跳过回归评估")
            return {
                'error': 'No nonzero data found',
                'sample_count': 0
            }
        
        y_true_nonzero = y_true[nonzero_mask]
        y_pred_nonzero = y_pred[nonzero_mask]
        
        # 基础回归指标
        mae = mean_absolute_error(y_true_nonzero, y_pred_nonzero)
        mse = mean_squared_error(y_true_nonzero, y_pred_nonzero)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_true_nonzero, y_pred_nonzero)
        
        # MAPE（平均绝对百分比误差）
        try:
            mape = mean_absolute_percentage_error(y_true_nonzero, y_pred_nonzero) * 100
        except:
            mape = np.mean(np.abs((y_true_nonzero - y_pred_nonzero) / np.maximum(np.abs(y_true_nonzero), 1e-6))) * 100
        
        # 相对误差分析
        relative_errors = np.abs(y_true_nonzero - y_pred_nonzero) / np.maximum(np.abs(y_true_nonzero), 1e-6)
        
        # 误差分布统计
        error_percentiles = {
            'p25': np.percentile(relative_errors, 25) * 100,
            'p50': np.percentile(relative_errors, 50) * 100,
            'p75': np.percentile(relative_errors, 75) * 100,
            'p90': np.percentile(relative_errors, 90) * 100,
            'p95': np.percentile(relative_errors, 95) * 100
        }
        
        # 预测值统计
        pred_stats = {
            'mean': float(np.mean(y_pred_nonzero)),
            'median': float(np.median(y_pred_nonzero)),
            'std': float(np.std(y_pred_nonzero)),
            'min': float(np.min(y_pred_nonzero)),
            'max': float(np.max(y_pred_nonzero))
        }
        
        # 真实值统计
        true_stats = {
            'mean': float(np.mean(y_true_nonzero)),
            'median': float(np.median(y_true_nonzero)),
            'std': float(np.std(y_true_nonzero)),
            'min': float(np.min(y_true_nonzero)),
            'max': float(np.max(y_true_nonzero))
        }
        
        regression_metrics = {
            'sample_count': int(np.sum(nonzero_mask)),
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'mape': mape,
            'error_percentiles': error_percentiles,
            'prediction_stats': pred_stats,
            'true_value_stats': true_stats
        }
        
        self.logger.info(f"非零值回归评估: R²={r2:.4f}, MAE={mae:.2f}, MAPE={mape:.1f}%")
        return regression_metrics
    
    def evaluate_overall_performance(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, Any]:
        """
        评估整体性能
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            整体性能指标
        """
        # 整体回归指标
        overall_mae = mean_absolute_error(y_true, y_pred)
        overall_mse = mean_squared_error(y_true, y_pred)
        overall_rmse = np.sqrt(overall_mse)
        overall_r2 = r2_score(y_true, y_pred)
        
        # 数据分布
        total_samples = len(y_true)
        true_zeros = np.sum(np.abs(y_true) <= self.zero_threshold)
        pred_zeros = np.sum(np.abs(y_pred) <= self.zero_threshold)
        
        true_zero_ratio = true_zeros / total_samples * 100
        pred_zero_ratio = pred_zeros / total_samples * 100
        
        # 预测偏差分析
        bias = np.mean(y_pred - y_true)
        abs_bias = np.mean(np.abs(y_pred - y_true))
        
        overall_metrics = {
            'total_samples': total_samples,
            'overall_mae': overall_mae,
            'overall_mse': overall_mse,
            'overall_rmse': overall_rmse,
            'overall_r2': overall_r2,
            'true_zero_count': int(true_zeros),
            'pred_zero_count': int(pred_zeros),
            'true_zero_ratio': true_zero_ratio,
            'pred_zero_ratio': pred_zero_ratio,
            'prediction_bias': bias,
            'absolute_bias': abs_bias
        }
        
        self.logger.info(f"整体性能评估: R²={overall_r2:.4f}, MAE={overall_mae:.2f}")
        return overall_metrics
    
    def evaluate_business_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, Any]:
        """
        评估业务指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            业务指标
        """
        # 业务准确率（不同误差范围内的准确率）
        error_thresholds = [1, 5, 10, 20, 50, 100]  # 元
        business_accuracy = {}
        
        for threshold in error_thresholds:
            accurate_predictions = np.abs(y_true - y_pred) <= threshold
            accuracy_rate = np.mean(accurate_predictions) * 100
            business_accuracy[f'within_{threshold}_yuan'] = accuracy_rate
        
        # 金额预测偏差分析
        total_true_amount = np.sum(y_true)
        total_pred_amount = np.sum(y_pred)
        amount_bias = total_pred_amount - total_true_amount
        amount_bias_ratio = (amount_bias / max(total_true_amount, 1)) * 100
        
        # 零值识别的业务影响
        true_zero_mask = np.abs(y_true) <= self.zero_threshold
        pred_zero_mask = np.abs(y_pred) <= self.zero_threshold
        
        # 误判为零值的损失（应收但预测为0）
        false_zero_loss = np.sum(y_true[~true_zero_mask & pred_zero_mask])
        
        # 误判为非零值的成本（实际为0但预测有值）
        false_nonzero_cost = np.sum(y_pred[true_zero_mask & ~pred_zero_mask])
        
        business_metrics = {
            'business_accuracy': business_accuracy,
            'total_true_amount': float(total_true_amount),
            'total_pred_amount': float(total_pred_amount),
            'amount_bias': float(amount_bias),
            'amount_bias_ratio': float(amount_bias_ratio),
            'false_zero_loss': float(false_zero_loss),
            'false_nonzero_cost': float(false_nonzero_cost),
            'net_business_impact': float(false_zero_loss - false_nonzero_cost)
        }
        
        self.logger.info(f"业务指标评估: ±50元准确率={business_accuracy.get('within_50_yuan', 0):.1f}%")
        return business_metrics
    
    def analyze_prediction_distribution(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, Any]:
        """
        分析预测分布
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            分布分析结果
        """
        # 真实值分布
        true_nonzero = y_true[np.abs(y_true) > self.zero_threshold]
        pred_nonzero = y_pred[np.abs(y_pred) > self.zero_threshold]
        
        # 分布统计
        distribution_analysis = {
            'true_value_distribution': {
                'zero_count': int(np.sum(np.abs(y_true) <= self.zero_threshold)),
                'nonzero_count': len(true_nonzero),
                'nonzero_mean': float(np.mean(true_nonzero)) if len(true_nonzero) > 0 else 0,
                'nonzero_std': float(np.std(true_nonzero)) if len(true_nonzero) > 0 else 0,
                'nonzero_percentiles': {
                    'p25': float(np.percentile(true_nonzero, 25)) if len(true_nonzero) > 0 else 0,
                    'p50': float(np.percentile(true_nonzero, 50)) if len(true_nonzero) > 0 else 0,
                    'p75': float(np.percentile(true_nonzero, 75)) if len(true_nonzero) > 0 else 0,
                    'p90': float(np.percentile(true_nonzero, 90)) if len(true_nonzero) > 0 else 0
                }
            },
            'predicted_value_distribution': {
                'zero_count': int(np.sum(np.abs(y_pred) <= self.zero_threshold)),
                'nonzero_count': len(pred_nonzero),
                'nonzero_mean': float(np.mean(pred_nonzero)) if len(pred_nonzero) > 0 else 0,
                'nonzero_std': float(np.std(pred_nonzero)) if len(pred_nonzero) > 0 else 0,
                'nonzero_percentiles': {
                    'p25': float(np.percentile(pred_nonzero, 25)) if len(pred_nonzero) > 0 else 0,
                    'p50': float(np.percentile(pred_nonzero, 50)) if len(pred_nonzero) > 0 else 0,
                    'p75': float(np.percentile(pred_nonzero, 75)) if len(pred_nonzero) > 0 else 0,
                    'p90': float(np.percentile(pred_nonzero, 90)) if len(pred_nonzero) > 0 else 0
                }
            }
        }
        
        return distribution_analysis
    
    def _generate_performance_summary(self, classification_metrics: Dict, regression_metrics: Dict,
                                    overall_metrics: Dict, business_metrics: Dict) -> Dict[str, Any]:
        """生成性能摘要"""
        summary = {
            'model_type': 'HierarchicalBillingModel',
            'evaluation_timestamp': pd.Timestamp.now().isoformat(),
            'key_metrics': {
                'zero_classification_f1': classification_metrics.get('overall_f1', 0),
                'nonzero_regression_r2': regression_metrics.get('r2', 0),
                'overall_r2': overall_metrics.get('overall_r2', 0),
                'overall_mae': overall_metrics.get('overall_mae', 0),
                'business_accuracy_50yuan': business_metrics.get('business_accuracy', {}).get('within_50_yuan', 0)
            },
            'performance_grade': self._calculate_performance_grade(
                classification_metrics, regression_metrics, overall_metrics, business_metrics
            )
        }
        
        return summary
    
    def _calculate_performance_grade(self, classification_metrics: Dict, regression_metrics: Dict,
                                   overall_metrics: Dict, business_metrics: Dict) -> str:
        """计算性能等级"""
        # 综合评分
        classification_score = classification_metrics.get('overall_f1', 0) * 100
        regression_score = regression_metrics.get('r2', 0) * 100 if regression_metrics.get('r2', 0) > 0 else 0
        business_score = business_metrics.get('business_accuracy', {}).get('within_50_yuan', 0)
        
        # 加权平均
        overall_score = (classification_score * 0.3 + regression_score * 0.4 + business_score * 0.3)
        
        if overall_score >= 90:
            return 'A+'
        elif overall_score >= 80:
            return 'A'
        elif overall_score >= 70:
            return 'B+'
        elif overall_score >= 60:
            return 'B'
        elif overall_score >= 50:
            return 'C'
        else:
            return 'D'
