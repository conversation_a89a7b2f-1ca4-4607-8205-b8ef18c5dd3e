#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分层计费模型
专门解决零值数据问题的分层建模架构
包含零值分类器和非零值回归器的完整解决方案
"""

import numpy as np
import pandas as pd
import joblib
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, Union
import warnings
warnings.filterwarnings('ignore')

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor

from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.utils.logger import get_logger


class HierarchicalBillingModel:
    """
    分层计费模型
    
    采用两阶段建模策略：
    1. 第一阶段：零值分类器 - 预测数据是否为零值
    2. 第二阶段：非零值回归器 - 预测非零数据的具体金额
    
    这种架构专门解决零值占比过高导致的模型性能问题
    """
    
    def __init__(self, 
                 classifier_params: Optional[Dict] = None,
                 regressor_params: Optional[Dict] = None,
                 zero_threshold: float = 1e-6,
                 use_lightgbm: bool = True):
        """
        初始化分层模型
        
        Args:
            classifier_params: 分类器参数
            regressor_params: 回归器参数  
            zero_threshold: 零值判定阈值
            use_lightgbm: 是否使用LightGBM（如果可用）
        """
        self.logger = get_logger('hierarchical_billing_model')
        self.zero_threshold = zero_threshold
        self.use_lightgbm = use_lightgbm and LIGHTGBM_AVAILABLE
        
        # 默认参数
        self.default_classifier_params = {
            'n_estimators': 100,
            'max_depth': 10,
            'learning_rate': 0.1,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        
        self.default_regressor_params = {
            'n_estimators': 100,
            'max_depth': 10,
            'learning_rate': 0.1,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        
        # 设置参数
        self.classifier_params = classifier_params or self.default_classifier_params.copy()
        self.regressor_params = regressor_params or self.default_regressor_params.copy()
        
        # 初始化模型
        self.zero_classifier = None
        self.nonzero_regressor = None
        
        # 训练统计信息
        self.training_stats = {}
        self.feature_names = None
        self.is_fitted = False
        
        self.logger.info(f"分层模型初始化完成，使用算法: {'LightGBM' if self.use_lightgbm else 'RandomForest'}")
    
    def _create_classifier(self):
        """创建零值分类器"""
        if self.use_lightgbm:
            return lgb.LGBMClassifier(**self.classifier_params)
        else:
            # 转换参数适配RandomForest
            rf_params = {k: v for k, v in self.classifier_params.items() 
                        if k in ['n_estimators', 'max_depth', 'random_state', 'n_jobs']}
            return RandomForestClassifier(**rf_params)
    
    def _create_regressor(self):
        """创建非零值回归器"""
        if self.use_lightgbm:
            return lgb.LGBMRegressor(**self.regressor_params)
        else:
            # 转换参数适配RandomForest
            rf_params = {k: v for k, v in self.regressor_params.items() 
                        if k in ['n_estimators', 'max_depth', 'random_state', 'n_jobs']}
            return RandomForestRegressor(**rf_params)
    
    def _prepare_data(self, X: pd.DataFrame, y: pd.Series) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Returns:
            X_array: 特征数组
            y_binary: 二分类标签 (0=零值, 1=非零值)
            y_nonzero: 非零值的回归目标
        """
        # 转换为numpy数组
        X_array = X.values if isinstance(X, pd.DataFrame) else X
        y_array = y.values if isinstance(y, pd.Series) else y
        
        # 创建二分类标签
        y_binary = (np.abs(y_array) > self.zero_threshold).astype(int)
        
        # 提取非零值
        nonzero_mask = y_binary == 1
        y_nonzero = y_array[nonzero_mask]
        
        return X_array, y_binary, y_nonzero, nonzero_mask
    
    def fit(self, X: Union[pd.DataFrame, np.ndarray], y: Union[pd.Series, np.ndarray]) -> 'HierarchicalBillingModel':
        """
        训练分层模型
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            self: 训练后的模型实例
        """
        self.logger.info("开始训练分层模型...")
        start_time = datetime.now()
        
        # 保存特征名称
        if isinstance(X, pd.DataFrame):
            self.feature_names = list(X.columns)
        else:
            self.feature_names = [f'feature_{i}' for i in range(X.shape[1])]
        
        # 准备数据
        X_array, y_binary, y_nonzero, nonzero_mask = self._prepare_data(X, y)
        X_nonzero = X_array[nonzero_mask]
        
        # 记录数据统计
        total_samples = len(y_binary)
        zero_count = np.sum(y_binary == 0)
        nonzero_count = np.sum(y_binary == 1)
        zero_ratio = zero_count / total_samples * 100
        
        self.training_stats = {
            'total_samples': total_samples,
            'zero_count': zero_count,
            'nonzero_count': nonzero_count,
            'zero_ratio': zero_ratio,
            'training_time': None
        }
        
        self.logger.info(f"数据统计: 总样本={total_samples}, 零值={zero_count}({zero_ratio:.1f}%), 非零值={nonzero_count}")
        
        # 训练零值分类器
        self.logger.info("训练零值分类器...")
        self.zero_classifier = self._create_classifier()
        self.zero_classifier.fit(X_array, y_binary)
        
        # 评估分类器性能
        y_binary_pred = self.zero_classifier.predict(X_array)
        classifier_accuracy = accuracy_score(y_binary, y_binary_pred)
        self.logger.info(f"零值分类器训练完成，准确率: {classifier_accuracy:.4f}")
        
        # 训练非零值回归器
        if nonzero_count > 0:
            self.logger.info("训练非零值回归器...")
            self.nonzero_regressor = self._create_regressor()
            self.nonzero_regressor.fit(X_nonzero, y_nonzero)
            
            # 评估回归器性能
            y_nonzero_pred = self.nonzero_regressor.predict(X_nonzero)
            regressor_r2 = r2_score(y_nonzero, y_nonzero_pred)
            regressor_mae = mean_absolute_error(y_nonzero, y_nonzero_pred)
            self.logger.info(f"非零值回归器训练完成，R²: {regressor_r2:.4f}, MAE: {regressor_mae:.2f}")
            
            self.training_stats.update({
                'classifier_accuracy': classifier_accuracy,
                'regressor_r2': regressor_r2,
                'regressor_mae': regressor_mae
            })
        else:
            self.logger.warning("没有非零值样本，跳过回归器训练")
            self.training_stats['classifier_accuracy'] = classifier_accuracy
        
        # 记录训练时间
        training_duration = (datetime.now() - start_time).total_seconds()
        self.training_stats['training_time'] = training_duration
        
        self.is_fitted = True
        self.logger.info(f"分层模型训练完成，耗时: {training_duration:.2f}秒")
        
        return self
    
    def predict(self, X: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """
        分层预测
        
        Args:
            X: 特征数据
            
        Returns:
            predictions: 预测结果
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit()方法")
        
        # 转换数据格式
        X_array = X.values if isinstance(X, pd.DataFrame) else X
        
        # 第一阶段：零值分类
        zero_predictions = self.zero_classifier.predict(X_array)
        
        # 初始化预测结果
        final_predictions = np.zeros(len(X_array))
        
        # 第二阶段：非零值回归
        if self.nonzero_regressor is not None:
            nonzero_mask = zero_predictions == 1
            if np.any(nonzero_mask):
                X_nonzero = X_array[nonzero_mask]
                try:
                    nonzero_predictions = self.nonzero_regressor.predict(X_nonzero)
                    final_predictions[nonzero_mask] = nonzero_predictions
                except Exception as e:
                    # 如果非零值回归失败，记录错误但继续执行，使用零值作为预测结果
                    self.logger.warning(f"非零值回归预测失败: {e}，使用零值作为预测结果")
                    # final_predictions[nonzero_mask] 保持为0（已在初始化时设置）

                    # 尝试修复LightGBM模型状态
                    if hasattr(self.nonzero_regressor, '_n_classes') and self.nonzero_regressor._n_classes is None:
                        self.logger.warning("检测到LightGBM模型状态异常，尝试修复")
                        # 对于回归器，_n_classes应该为None或不存在，这里不需要设置
                        pass
        
        return final_predictions
    
    def predict_proba(self, X: Union[pd.DataFrame, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        预测概率
        
        Returns:
            Dict包含:
            - 'zero_proba': 零值概率
            - 'predictions': 最终预测值
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit()方法")
        
        X_array = X.values if isinstance(X, pd.DataFrame) else X
        
        # 获取零值概率
        zero_proba = self.zero_classifier.predict_proba(X_array)
        
        # 获取预测值
        predictions = self.predict(X)
        
        return {
            'zero_proba': zero_proba,
            'predictions': predictions
        }
    
    def save(self, model_path: str, metadata_path: Optional[str] = None) -> None:
        """
        保存模型
        
        Args:
            model_path: 模型保存路径
            metadata_path: 元数据保存路径
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，无法保存")
        
        model_path = Path(model_path)
        model_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存模型组件
        model_data = {
            'zero_classifier': self.zero_classifier,
            'nonzero_regressor': self.nonzero_regressor,
            'zero_threshold': self.zero_threshold,
            'use_lightgbm': self.use_lightgbm,
            'feature_names': self.feature_names,
            'training_stats': self.training_stats,
            'classifier_params': self.classifier_params,
            'regressor_params': self.regressor_params
        }
        
        joblib.dump(model_data, model_path)
        
        # 保存元数据
        if metadata_path:
            metadata = {
                'model_type': 'HierarchicalBillingModel',
                'algorithm': 'LightGBM' if self.use_lightgbm else 'RandomForest',
                'training_stats': self.training_stats,
                'feature_names': self.feature_names,
                'save_time': datetime.now().isoformat()
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"分层模型已保存到: {model_path}")
    
    @classmethod
    def load(cls, model_path: str) -> 'HierarchicalBillingModel':
        """
        加载模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            加载的模型实例
        """
        model_data = joblib.load(model_path)
        
        # 创建模型实例
        model = cls(
            classifier_params=model_data['classifier_params'],
            regressor_params=model_data['regressor_params'],
            zero_threshold=model_data['zero_threshold'],
            use_lightgbm=model_data['use_lightgbm']
        )
        
        # 恢复模型状态
        model.zero_classifier = model_data['zero_classifier']
        model.nonzero_regressor = model_data['nonzero_regressor']
        model.feature_names = model_data['feature_names']
        model.training_stats = model_data['training_stats']
        model.is_fitted = True
        
        model.logger.info(f"分层模型已从 {model_path} 加载")
        return model
    
    def get_feature_importance(self) -> Dict[str, np.ndarray]:
        """
        获取特征重要性
        
        Returns:
            Dict包含分类器和回归器的特征重要性
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        
        result = {}
        
        # 分类器特征重要性
        if hasattr(self.zero_classifier, 'feature_importances_'):
            result['classifier_importance'] = self.zero_classifier.feature_importances_
        
        # 回归器特征重要性
        if self.nonzero_regressor and hasattr(self.nonzero_regressor, 'feature_importances_'):
            result['regressor_importance'] = self.nonzero_regressor.feature_importances_
        
        return result
    
    def get_training_stats(self) -> Dict[str, Any]:
        """获取训练统计信息"""
        return self.training_stats.copy()
    
    def __str__(self) -> str:
        """字符串表示"""
        if not self.is_fitted:
            return "HierarchicalBillingModel(未训练)"
        
        stats = self.training_stats
        return (f"HierarchicalBillingModel("
                f"算法={'LightGBM' if self.use_lightgbm else 'RandomForest'}, "
                f"样本={stats['total_samples']}, "
                f"零值比例={stats['zero_ratio']:.1f}%, "
                f"分类准确率={stats.get('classifier_accuracy', 0):.3f}, "
                f"回归R²={stats.get('regressor_r2', 0):.3f})")
