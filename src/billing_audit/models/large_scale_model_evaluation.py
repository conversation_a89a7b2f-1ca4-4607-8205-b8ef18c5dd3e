#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大规模模型评估脚本
专门针对千万级数据优化的模型评估系统
支持分批评估、内存优化、详细指标计算
"""

import sys
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import argparse
from datetime import datetime
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import gc
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 导入大规模特征工程器和配置管理器
from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer
from src.config.production_config_manager import get_config_manager
from src.utils.logger import get_logger

# 导入分层模型和评估器
from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
from src.billing_audit.evaluation.hierarchical_evaluator import HierarchicalModelEvaluator


class LargeScaleModelEvaluator:
    """大规模模型评估器"""
    
    def __init__(self, model_path, feature_engineer_path, batch_size=None):
        # 使用生产配置管理器
        self.config_manager = get_config_manager()
        self.batch_size = batch_size or self.config_manager.get_batch_size()
        self.model = None
        self.feature_engineer = None

        # 初始化日志器
        self.logger = get_logger('large_scale_model_evaluator')

        # 评估指标累积器
        self.y_true_all = []
        self.y_pred_all = []
        self.total_samples = 0

        # 分层模型标识
        self.is_hierarchical = False

        # 加载模型和特征工程器
        self.load_model_and_feature_engineer(model_path, feature_engineer_path)
    
    def load_model_and_feature_engineer(self, model_path, feature_engineer_path):
        """加载模型和特征工程器"""
        print(f"加载模型和特征工程器...")

        try:
            # 尝试加载分层模型
            if str(model_path).find('hierarchical') != -1:
                self.model = HierarchicalBillingModel.load(model_path)
                self.is_hierarchical = True
                print(f"  分层模型加载成功: {type(self.model).__name__}")
            else:
                # 加载传统模型
                self.model = joblib.load(model_path)
                self.is_hierarchical = False
                print(f"  传统模型加载成功: {type(self.model).__name__}")

                # 显示特征数（仅对传统模型）
                if hasattr(self.model, 'n_features_in_'):
                    print(f"  模型特征数: {self.model.n_features_in_}")

            # 加载特征工程器
            self.feature_engineer = LargeScaleFeatureEngineer.load_preprocessor(feature_engineer_path)
            print(f"  特征工程器加载成功")

        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def read_large_csv(self, file_path, chunksize=None):
        """分批读取大型CSV文件"""
        if chunksize is None:
            chunksize = self.batch_size
            
        print(f"开始分批读取数据文件: {file_path}")
        print(f"  - 批次大小: {chunksize:,} 行")
        
        try:
            # 检测分隔符
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline()
                sep = ',' if ',' in first_line else '\t'
            
            print(f"  - 检测到分隔符: '{sep}'")
            
            # 分批读取
            chunk_reader = pd.read_csv(
                file_path, 
                sep=sep,
                chunksize=chunksize,
                encoding='utf-8',
                low_memory=False
            )
            
            return chunk_reader, sep
            
        except Exception as e:
            print(f"文件读取失败: {e}")
            raise
    
    def evaluate_chunk(self, chunk, target_column):
        """评估单个数据块"""
        try:
            # 获取训练特征列
            training_features = self.config_manager.get_training_features()

            # 提取特征和目标变量
            X = chunk[training_features].copy()
            y_true = chunk[target_column].copy()

            # 处理缺失值
            X = X.fillna(0)
            y_true = y_true.fillna(0)

            # 应用特征工程
            X_processed = self.feature_engineer.transform_chunk(X)

            # 预测
            y_pred = self.model.predict(X_processed)

            return y_true.values, y_pred

        except Exception as e:
            print(f"数据块评估失败: {e}")
            print(f"可用列: {list(chunk.columns)}")
            print(f"需要的训练特征: {self.config_manager.get_training_features()}")
            raise
    
    def evaluate_large_file(self, test_file, target_column='amount'):
        """评估大型文件"""
        self.logger.info(f"开始大规模模型评估: 测试文件={test_file}, 目标列={target_column}")
        print(f"开始大规模模型评估")
        print(f"  测试文件: {test_file}")
        print(f"  目标列: {target_column}")
        print("=" * 60)
        
        # 读取数据
        chunk_reader, _ = self.read_large_csv(test_file, self.batch_size)
        
        chunk_count = 0
        start_time = datetime.now()
        
        # 分批评估指标累积器
        batch_metrics = []
        
        for chunk in chunk_reader:
            chunk_count += 1
            print(f"\n  评估第 {chunk_count} 批数据...")
            
            try:
                # 评估数据块
                y_true_batch, y_pred_batch = self.evaluate_chunk(chunk, target_column)
                
                # 累积结果
                self.y_true_all.extend(y_true_batch)
                self.y_pred_all.extend(y_pred_batch)
                self.total_samples += len(y_true_batch)
                
                # 计算批次指标
                batch_mae = mean_absolute_error(y_true_batch, y_pred_batch)
                batch_rmse = np.sqrt(mean_squared_error(y_true_batch, y_pred_batch))
                batch_r2 = r2_score(y_true_batch, y_pred_batch)
                
                batch_metrics.append({
                    'batch': chunk_count,
                    'samples': len(y_true_batch),
                    'mae': batch_mae,
                    'rmse': batch_rmse,
                    'r2': batch_r2
                })
                
                print(f"    评估完成: {len(y_true_batch):,} 样本")
                print(f"    批次指标: MAE={batch_mae:.2f}, RMSE={batch_rmse:.2f}, R²={batch_r2:.4f}")
                print(f"    累计样本: {self.total_samples:,}")
                
                # 内存管理：每处理一定数量的批次就进行垃圾回收
                if chunk_count % 20 == 0:
                    gc.collect()
                    print(f"  内存清理完成")
                
            except Exception as e:
                print(f"    第 {chunk_count} 批评估失败: {e}")
                continue
        
        total_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n分批评估完成！")
        print(f"  - 总批次: {chunk_count}")
        print(f"  - 总样本: {self.total_samples:,}")
        print(f"  - 总耗时: {total_time:.2f}秒")
        print(f"  - 评估速度: {self.total_samples/total_time:.0f} 样本/秒")
        
        return batch_metrics
    
    def calculate_overall_metrics(self):
        """计算整体评估指标"""
        print(f"\n计算整体评估指标...")

        if len(self.y_true_all) == 0:
            print("没有评估数据")
            return None

        # 转换为numpy数组
        y_true = np.array(self.y_true_all)
        y_pred = np.array(self.y_pred_all)

        # 如果是分层模型，使用专门的分层评估器
        if self.is_hierarchical:
            print("使用分层模型评估器...")
            hierarchical_evaluator = HierarchicalModelEvaluator()
            hierarchical_results = hierarchical_evaluator.evaluate_comprehensive(y_true, y_pred)

            # 提取关键指标用于兼容性
            overall_metrics = hierarchical_results['overall_metrics']
            classification_metrics = hierarchical_results['classification_metrics']
            regression_metrics = hierarchical_results['regression_metrics']
            business_metrics = hierarchical_results['business_metrics']

            # 构建兼容的返回格式
            return {
                'model_type': 'hierarchical',
                'total_samples': self.total_samples,
                'overall_mae': overall_metrics['overall_mae'],
                'overall_rmse': overall_metrics['overall_rmse'],
                'overall_r2': overall_metrics['overall_r2'],
                'zero_classification_accuracy': classification_metrics['overall_accuracy'],
                'zero_classification_f1': classification_metrics['overall_f1'],
                'nonzero_regression_r2': regression_metrics.get('r2', 0),
                'nonzero_regression_mae': regression_metrics.get('mae', 0),
                'business_accuracy_50yuan': business_metrics['business_accuracy'].get('within_50_yuan', 0),
                'hierarchical_results': hierarchical_results,  # 完整的分层评估结果
                'performance_grade': hierarchical_results['summary']['performance_grade']
            }

        # 传统模型评估
        print("使用传统模型评估...")
        overall_mae = mean_absolute_error(y_true, y_pred)
        overall_rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        overall_r2 = r2_score(y_true, y_pred)
        
        # 计算业务指标
        residuals = np.abs(y_true - y_pred)
        
        # 不同阈值的准确率
        thresholds = [10, 20, 50, 100]
        accuracy_by_threshold = {}
        for threshold in thresholds:
            accuracy = (residuals <= threshold).mean() * 100
            accuracy_by_threshold[threshold] = accuracy
        
        # 预测分布统计
        pred_stats = {
            'min': y_pred.min(),
            'max': y_pred.max(),
            'mean': y_pred.mean(),
            'median': np.median(y_pred),
            'std': y_pred.std(),
            'zero_ratio': (y_pred == 0).mean() * 100
        }
        
        # 真实值分布统计
        true_stats = {
            'min': y_true.min(),
            'max': y_true.max(),
            'mean': y_true.mean(),
            'median': np.median(y_true),
            'std': y_true.std(),
            'zero_ratio': (y_true == 0).mean() * 100
        }
        
        metrics = {
            'overall': {
                'mae': overall_mae,
                'rmse': overall_rmse,
                'r2': overall_r2,
                'samples': len(y_true)
            },
            'business_accuracy': accuracy_by_threshold,
            'prediction_stats': pred_stats,
            'true_stats': true_stats
        }
        
        return metrics
    
    def print_evaluation_report(self, metrics, batch_metrics):
        """打印评估报告"""
        print(f"\n" + "="*80)
        if metrics.get('model_type') == 'hierarchical':
            print(f"分层模型评估报告")
        else:
            print(f"大规模模型评估报告")
        print(f"="*80)

        # 分层模型报告
        if metrics.get('model_type') == 'hierarchical':
            print(f"\n🎯 分层模型综合评估:")
            print(f"  - 样本数量: {metrics['total_samples']:,}")
            print(f"  - 整体 R²: {metrics['overall_r2']:.4f}")
            print(f"  - 整体 MAE: {metrics['overall_mae']:.2f}元")
            print(f"  - 整体 RMSE: {metrics['overall_rmse']:.2f}元")
            print(f"  - 性能等级: {metrics['performance_grade']}")

            print(f"\n🔍 零值分类性能:")
            print(f"  - 分类准确率: {metrics['zero_classification_accuracy']:.4f}")
            print(f"  - 分类 F1 分数: {metrics['zero_classification_f1']:.4f}")

            print(f"\n📈 非零值回归性能:")
            print(f"  - 回归 R²: {metrics['nonzero_regression_r2']:.4f}")
            print(f"  - 回归 MAE: {metrics['nonzero_regression_mae']:.2f}元")

            print(f"\n💼 业务指标:")
            print(f"  - ±50元准确率: {metrics['business_accuracy_50yuan']:.1f}%")

            # 显示详细的分层评估结果
            hierarchical_results = metrics.get('hierarchical_results', {})
            if 'business_metrics' in hierarchical_results:
                business = hierarchical_results['business_metrics']
                print(f"\n📊 详细业务准确率:")
                business_accuracy = business.get('business_accuracy', {})
                for key, value in business_accuracy.items():
                    threshold = key.replace('within_', '').replace('_yuan', '')
                    print(f"  - ±{threshold}元内: {value:.1f}%")

            return

        # 传统模型报告
        overall = metrics['overall']
        print(f"\n整体性能指标:")
        print(f"  - 样本数量: {overall['samples']:,}")
        print(f"  - MAE (平均绝对误差): {overall['mae']:.2f}")
        print(f"  - RMSE (均方根误差): {overall['rmse']:.2f}")
        print(f"  - R² (决定系数): {overall['r2']:.4f}")
        
        # 业务准确率
        print(f"\n💼 业务准确率 (预测误差在阈值内的比例):")
        for threshold, accuracy in metrics['business_accuracy'].items():
            print(f"  - ±{threshold}元内: {accuracy:.1f}%")
        
        # 预测分布
        pred_stats = metrics['prediction_stats']
        print(f"\n预测值分布:")
        print(f"  - 范围: {pred_stats['min']:.2f} - {pred_stats['max']:.2f}")
        print(f"  - 均值: {pred_stats['mean']:.2f}")
        print(f"  - 中位数: {pred_stats['median']:.2f}")
        print(f"  - 标准差: {pred_stats['std']:.2f}")
        print(f"  - 零值比例: {pred_stats['zero_ratio']:.1f}%")
        
        # 真实值分布
        true_stats = metrics['true_stats']
        print(f"\n真实值分布:")
        print(f"  - 范围: {true_stats['min']:.2f} - {true_stats['max']:.2f}")
        print(f"  - 均值: {true_stats['mean']:.2f}")
        print(f"  - 中位数: {true_stats['median']:.2f}")
        print(f"  - 标准差: {true_stats['std']:.2f}")
        print(f"  - 零值比例: {true_stats['zero_ratio']:.1f}%")
        
        # 批次性能稳定性
        if batch_metrics:
            batch_r2_values = [b['r2'] for b in batch_metrics]
            batch_mae_values = [b['mae'] for b in batch_metrics]
            
            print(f"\n批次性能稳定性:")
            print(f"  - R²变化范围: {min(batch_r2_values):.4f} - {max(batch_r2_values):.4f}")
            print(f"  - R²标准差: {np.std(batch_r2_values):.4f}")
            print(f"  - MAE变化范围: {min(batch_mae_values):.2f} - {max(batch_mae_values):.2f}")
            print(f"  - MAE标准差: {np.std(batch_mae_values):.2f}")
        
        # 模型质量评估
        print(f"\n🏆 模型质量评估:")
        r2_score = overall['r2']
        if r2_score > 0.9:
            quality = "优秀"
            emoji = ""
        elif r2_score > 0.8:
            quality = "良好"
            emoji = ""
        elif r2_score > 0.6:
            quality = "一般"
            emoji = ""
        else:
            quality = "较差"
            emoji = ""
        
        print(f"  {emoji} 模型质量: {quality} (R²={r2_score:.4f})")
        
        # 业务适用性
        high_accuracy = metrics['business_accuracy'][50]  # 50元阈值准确率
        if high_accuracy > 95:
            print(f"  业务适用性: 优秀 (±50元准确率: {high_accuracy:.1f}%)")
        elif high_accuracy > 90:
            print(f"  业务适用性: 良好 (±50元准确率: {high_accuracy:.1f}%)")
        else:
            print(f"  业务适用性: 需要改进 (±50元准确率: {high_accuracy:.1f}%)")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='大规模模型评估')
    parser.add_argument('--test-data', '-t', required=True, help='测试数据文件路径 (CSV/TXT)')
    parser.add_argument('--model', '-m', required=True, help='模型文件路径 (.pkl)')
    parser.add_argument('--feature-engineer', '-f', required=True, help='特征工程器文件路径 (.pkl)')
    parser.add_argument('--target-column', '-c', default='amount', help='目标列名 (默认: amount)')
    parser.add_argument('--batch-size', '-b', type=int, default=50000, help='批处理大小')
    parser.add_argument('--output', '-o', help='评估报告保存路径 (可选)')
    
    args = parser.parse_args()
    
    try:
        # 初始化评估器
        evaluator = LargeScaleModelEvaluator(
            model_path=args.model,
            feature_engineer_path=args.feature_engineer,
            batch_size=args.batch_size
        )
        
        # 执行评估
        batch_metrics = evaluator.evaluate_large_file(
            test_file=args.test_data,
            target_column=args.target_column
        )
        
        # 计算整体指标
        overall_metrics = evaluator.calculate_overall_metrics()
        
        # 打印报告
        evaluator.print_evaluation_report(overall_metrics, batch_metrics)
        
        # 保存报告 (可选)
        if args.output:
            import json
            import numpy as np
            
            def convert_numpy_types(obj):
                """递归转换numpy类型为Python原生类型"""
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, dict):
                    return {key: convert_numpy_types(value) for key, value in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(item) for item in obj]
                else:
                    return obj
            
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'test_file': args.test_data,
                'model_file': args.model,
                'feature_engineer_file': args.feature_engineer,
                'batch_size': args.batch_size,
                'overall_metrics': convert_numpy_types(overall_metrics),
                'batch_count': len(batch_metrics)
            }
            
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n评估报告已保存: {args.output}")
        
        print(f"\n大规模模型评估完成！")
        return True
        
    except Exception as e:
        print(f"评估失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
