#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态数据模式管理器
支持配置化字段管理，无需修改代码即可增减字段
山西电信出账稽核AI系统 v2.1.0
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging


class DynamicSchemaManager:
    """动态数据模式管理器"""
    
    def __init__(self, config_manager):
        """初始化动态模式管理器"""
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 缓存字段配置
        self._field_cache = {}
        self._schema_cache = {}
    
    def get_field_schema(self, data_type: str = 'fixed_fee') -> Dict[str, Any]:
        """获取字段模式配置"""
        if data_type not in self._schema_cache:
            schema = self.config_manager.get(f'data_schema.{data_type}', {})
            self._schema_cache[data_type] = schema
        
        return self._schema_cache[data_type]
    
    def get_all_fields(self, data_type: str = 'fixed_fee') -> List[str]:
        """获取所有字段列表"""
        schema = self.get_field_schema(data_type)
        
        training_features = schema.get('training_features', [])
        target_column = schema.get('target_column', '')
        passthrough_columns = schema.get('passthrough_columns', [])
        
        all_fields = training_features + passthrough_columns
        if target_column:
            all_fields.append(target_column)
        
        return all_fields
    
    def get_field_by_category(self, category: str, data_type: str = 'fixed_fee') -> List[str]:
        """根据类别获取字段列表"""
        schema = self.get_field_schema(data_type)
        return schema.get(category, [])
    
    def get_field_metadata(self, field_name: str, data_type: str = 'fixed_fee') -> Dict[str, Any]:
        """获取字段元数据"""
        schema = self.get_field_schema(data_type)
        
        # 确定字段类别
        field_category = self._get_field_category(field_name, schema)
        
        # 确定数据类型
        data_type_category = self._get_data_type_category(field_name, schema)
        
        return {
            'name': field_name,
            'category': field_category,
            'data_type': data_type_category,
            'required': field_name in schema.get('required_columns', []),
            'description': self._get_field_description(field_name)
        }
    
    def _get_field_category(self, field_name: str, schema: Dict[str, Any]) -> str:
        """确定字段类别"""
        if field_name in schema.get('training_features', []):
            return 'training_feature'
        elif field_name == schema.get('target_column', ''):
            return 'target'
        elif field_name in schema.get('passthrough_columns', []):
            return 'passthrough'
        else:
            return 'unknown'
    
    def _get_data_type_category(self, field_name: str, schema: Dict[str, Any]) -> str:
        """确定数据类型类别"""
        if field_name in schema.get('categorical_columns', []):
            return 'categorical'
        elif field_name in schema.get('numerical_columns', []):
            return 'numerical'
        elif field_name in schema.get('date_columns', []):
            return 'date'
        else:
            return 'string'
    
    def _get_field_description(self, field_name: str) -> str:
        """获取字段描述"""
        # 字段描述映射
        descriptions = {
            'cal_type': '费用计算类型 (0-非月租；1-整月收；2-按天折算；3-日收)',
            'unit_type': '周期类型 (0-非多周期；1-月；2-天)',
            'rate_unit': '周期数量 (0-非多周期；>1为周期数量)',
            'final_eff_year': '最终生效年',
            'final_eff_mon': '最终生效月',
            'final_eff_day': '最终生效日',
            'final_exp_year': '最终失效年',
            'final_exp_mon': '最终失效月',
            'final_exp_day': '最终失效日',
            'cur_year_month': '当前年月 (格式yyyymm)',
            'charge_day_count': '计费天数',
            'month_day_count': '当月总天数',
            'should_fee': '应收费 (金额，单位元)',
            'busi_flag': '业务标识 (0-正常收费；1-不收费)',
            'amount': '账单费用金额 (预测目标)',
            'offer_inst_id': '优惠实例ID',
            'prod_inst_id': '产品实例ID',
            'prod_id': '产品ID',
            'offer_id': '优惠ID',
            'sub_prod_id': '子产品ID',
            'event_pricing_strategy_id': '事件定价策略ID',
            'event_type_id': '事件类型ID',
            'calc_priority': '计算优先级',
            'pricing_section_id': '定价段ID',
            'calc_method_id': '计算方法ID',
            'role_id': '角色ID'
        }
        
        return descriptions.get(field_name, f'{field_name} (无描述)')
    
    def validate_schema_consistency(self, data_type: str = 'fixed_fee') -> Dict[str, Any]:
        """验证模式一致性"""
        schema = self.get_field_schema(data_type)
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        # 获取各类字段
        training_features = schema.get('training_features', [])
        target_column = schema.get('target_column', '')
        passthrough_columns = schema.get('passthrough_columns', [])
        required_columns = schema.get('required_columns', [])
        categorical_columns = schema.get('categorical_columns', [])
        numerical_columns = schema.get('numerical_columns', [])
        date_columns = schema.get('date_columns', [])
        
        # 统计信息
        validation_result['statistics'] = {
            'training_features_count': len(training_features),
            'passthrough_columns_count': len(passthrough_columns),
            'total_fields': len(training_features) + len(passthrough_columns) + (1 if target_column else 0),
            'categorical_count': len(categorical_columns),
            'numerical_count': len(numerical_columns),
            'date_count': len(date_columns)
        }
        
        # 验证必需字段完整性
        all_expected_fields = training_features + passthrough_columns
        if target_column:
            all_expected_fields.append(target_column)
        
        missing_required = set(all_expected_fields) - set(required_columns)
        if missing_required:
            validation_result['errors'].append(f"必需字段列表缺少: {missing_required}")
            validation_result['valid'] = False
        
        # 验证数据类型分类完整性
        all_typed_fields = set(categorical_columns + numerical_columns + date_columns)
        untyped_training_features = set(training_features) - all_typed_fields
        if untyped_training_features:
            validation_result['warnings'].append(f"训练特征缺少数据类型定义: {untyped_training_features}")
        
        # 验证字段重复
        all_fields_list = training_features + passthrough_columns + ([target_column] if target_column else [])
        if len(all_fields_list) != len(set(all_fields_list)):
            validation_result['errors'].append("存在重复字段定义")
            validation_result['valid'] = False
        
        # 验证目标字段
        if not target_column:
            validation_result['errors'].append("缺少目标字段定义")
            validation_result['valid'] = False
        
        return validation_result
    
    def generate_field_mapping_code(self, data_type: str = 'fixed_fee') -> str:
        """生成字段映射代码"""
        schema = self.get_field_schema(data_type)
        
        training_features = schema.get('training_features', [])
        target_column = schema.get('target_column', '')
        passthrough_columns = schema.get('passthrough_columns', [])
        
        code_lines = [
            "# 自动生成的字段映射代码",
            "def get_field_mapping():",
            "    \"\"\"获取字段映射配置\"\"\"",
            "    return {",
            "        'training_features': [",
        ]
        
        for feature in training_features:
            code_lines.append(f"            '{feature}',")
        
        code_lines.extend([
            "        ],",
            f"        'target_column': '{target_column}',",
            "        'passthrough_columns': [",
        ])
        
        for column in passthrough_columns:
            code_lines.append(f"            '{column}',")
        
        code_lines.extend([
            "        ]",
            "    }",
            "",
            "# 使用示例:",
            "# mapping = get_field_mapping()",
            "# training_features = mapping['training_features']"
        ])
        
        return "\n".join(code_lines)
    
    def export_schema_documentation(self, data_type: str = 'fixed_fee') -> str:
        """导出模式文档"""
        schema = self.get_field_schema(data_type)
        validation = self.validate_schema_consistency(data_type)
        
        doc_lines = [
            f"# 数据模式文档 - {data_type}",
            "",
            "## 模式概览",
            f"- **训练特征**: {validation['statistics']['training_features_count']} 个字段",
            f"- **透传字段**: {validation['statistics']['passthrough_columns_count']} 个字段",
            f"- **目标字段**: 1 个字段",
            f"- **总字段数**: {validation['statistics']['total_fields']} 个字段",
            "",
            "## 字段详情",
            ""
        ]
        
        # 训练特征
        training_features = schema.get('training_features', [])
        if training_features:
            doc_lines.extend([
                "### 训练特征字段",
                "用于AI模型训练和预测的核心特征字段。",
                "",
                "| 序号 | 字段名 | 数据类型 | 描述 |",
                "|------|--------|----------|------|",
            ])
            
            for i, field in enumerate(training_features, 1):
                metadata = self.get_field_metadata(field, data_type)
                doc_lines.append(f"| {i} | {field} | {metadata['data_type']} | {metadata['description']} |")
            
            doc_lines.append("")
        
        # 目标字段
        target_column = schema.get('target_column', '')
        if target_column:
            doc_lines.extend([
                "### 目标字段",
                "AI模型的预测目标，用于监督学习。",
                "",
                "| 字段名 | 数据类型 | 描述 |",
                "|--------|----------|------|",
            ])
            
            metadata = self.get_field_metadata(target_column, data_type)
            doc_lines.append(f"| {target_column} | {metadata['data_type']} | {metadata['description']} |")
            doc_lines.append("")
        
        # 透传字段
        passthrough_columns = schema.get('passthrough_columns', [])
        if passthrough_columns:
            doc_lines.extend([
                "### 透传字段",
                "不参与AI训练，但在结果文件中保留的业务标识字段。",
                "",
                "| 序号 | 字段名 | 数据类型 | 描述 |",
                "|------|--------|----------|------|",
            ])
            
            for i, field in enumerate(passthrough_columns, 1):
                metadata = self.get_field_metadata(field, data_type)
                doc_lines.append(f"| {i} | {field} | {metadata['data_type']} | {metadata['description']} |")
            
            doc_lines.append("")
        
        # 验证结果
        doc_lines.extend([
            "## 模式验证",
            f"- **验证状态**: {'✅ 通过' if validation['valid'] else '❌ 失败'}",
        ])
        
        if validation['errors']:
            doc_lines.append("- **错误**:")
            for error in validation['errors']:
                doc_lines.append(f"  - {error}")
        
        if validation['warnings']:
            doc_lines.append("- **警告**:")
            for warning in validation['warnings']:
                doc_lines.append(f"  - {warning}")
        
        return "\n".join(doc_lines)


def get_dynamic_schema_manager(config_manager=None):
    """获取动态模式管理器实例"""
    if config_manager is None:
        from src.config.production_config_manager import get_config_manager
        config_manager = get_config_manager()
    
    return DynamicSchemaManager(config_manager)
