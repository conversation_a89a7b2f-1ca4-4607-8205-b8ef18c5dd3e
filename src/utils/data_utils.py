#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理工具模块
提供通用的数据处理和验证功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import warnings

from .logger import get_logger

logger = get_logger(__name__)


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, required_columns: List[str] = None) -> Dict[str, Any]:
        """
        验证DataFrame的基本信息
        
        Args:
            df: 待验证的DataFrame
            required_columns: 必需的列名列表
            
        Returns:
            验证结果字典
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': {
                'shape': df.shape,
                'memory_usage': df.memory_usage(deep=True).sum(),
                'dtypes': df.dtypes.to_dict()
            }
        }
        
        # 检查是否为空
        if df.empty:
            result['errors'].append("DataFrame为空")
            result['is_valid'] = False
            return result
        
        # 检查必需列
        if required_columns:
            missing_cols = set(required_columns) - set(df.columns)
            if missing_cols:
                result['errors'].append(f"缺少必需列: {list(missing_cols)}")
                result['is_valid'] = False
        
        # 检查重复行
        duplicate_count = df.duplicated().sum()
        if duplicate_count > 0:
            result['warnings'].append(f"发现 {duplicate_count} 行重复数据")
        
        # 检查缺失值
        missing_info = {}
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            if missing_count > 0:
                missing_rate = missing_count / len(df)
                missing_info[col] = {
                    'count': missing_count,
                    'rate': missing_rate
                }
                if missing_rate > 0.5:
                    result['warnings'].append(f"列 '{col}' 缺失率过高: {missing_rate:.2%}")
        
        result['info']['missing_values'] = missing_info
        
        return result
    
    @staticmethod
    def validate_numerical_column(series: pd.Series, col_name: str) -> Dict[str, Any]:
        """验证数值列"""
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'stats': {}
        }
        
        if not pd.api.types.is_numeric_dtype(series):
            result['errors'].append(f"列 '{col_name}' 不是数值类型")
            result['is_valid'] = False
            return result
        
        # 基本统计信息
        result['stats'] = {
            'count': series.count(),
            'mean': series.mean(),
            'std': series.std(),
            'min': series.min(),
            'max': series.max(),
            'median': series.median()
        }
        
        # 检查异常值（使用IQR方法）
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = series[(series < lower_bound) | (series > upper_bound)]
        if len(outliers) > 0:
            outlier_rate = len(outliers) / len(series)
            result['warnings'].append(f"列 '{col_name}' 发现 {len(outliers)} 个异常值 ({outlier_rate:.2%})")
            result['stats']['outliers'] = {
                'count': len(outliers),
                'rate': outlier_rate,
                'bounds': (lower_bound, upper_bound)
            }
        
        return result


class DataLoader:
    """数据加载器"""
    
    @staticmethod
    def load_excel(file_path: str, sheet_name: str = None, **kwargs) -> pd.DataFrame:
        """
        加载Excel文件
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称
            **kwargs: pandas.read_excel的其他参数
            
        Returns:
            DataFrame
        """
        try:
            logger.info(f"开始加载Excel文件: {file_path}")
            
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            df = pd.read_excel(file_path, sheet_name=sheet_name, **kwargs)

            # 如果返回字典（多个sheet），优先选择样例数据sheet
            if isinstance(df, dict):
                sheet_names = list(df.keys())
                logger.info(f"检测到多个sheet: {sheet_names}")

                # 优先选择包含"样例"、"数据"、"data"等关键词的sheet
                data_sheet = None
                for name in sheet_names:
                    if any(keyword in name.lower() for keyword in ['样例', '数据', 'data', 'sample']):
                        data_sheet = name
                        break

                if data_sheet:
                    logger.info(f"使用数据sheet: {data_sheet}")
                    df = df[data_sheet]
                else:
                    logger.info(f"未找到数据sheet，使用第一个: {sheet_names[0]}")
                    df = df[sheet_names[0]]

            logger.info(f"成功加载数据: {df.shape[0]} 行 × {df.shape[1]} 列")
            
            return df
            
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            raise
    
    @staticmethod
    def load_csv(file_path: str, **kwargs) -> pd.DataFrame:
        """加载CSV文件"""
        try:
            logger.info(f"开始加载CSV文件: {file_path}")
            
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            df = pd.read_csv(file_path, **kwargs)
            logger.info(f"成功加载数据: {df.shape[0]} 行 × {df.shape[1]} 列")
            
            return df
            
        except Exception as e:
            logger.error(f"加载CSV文件失败: {e}")
            raise


class DataCleaner:
    """数据清洗器"""
    
    @staticmethod
    def handle_missing_values(df: pd.DataFrame, strategy: Dict[str, str] = None) -> pd.DataFrame:
        """
        处理缺失值
        
        Args:
            df: 原始DataFrame
            strategy: 处理策略字典，格式为 {column: strategy}
                     strategy可以是: 'drop', 'mean', 'median', 'mode', 'forward_fill', 'backward_fill'
        
        Returns:
            处理后的DataFrame
        """
        df_cleaned = df.copy()
        
        if strategy is None:
            strategy = {}
        
        for col in df_cleaned.columns:
            if df_cleaned[col].isnull().sum() == 0:
                continue
            
            col_strategy = strategy.get(col, 'drop')
            
            if col_strategy == 'drop':
                df_cleaned = df_cleaned.dropna(subset=[col])
            elif col_strategy == 'mean' and pd.api.types.is_numeric_dtype(df_cleaned[col]):
                df_cleaned[col].fillna(df_cleaned[col].mean(), inplace=True)
            elif col_strategy == 'median' and pd.api.types.is_numeric_dtype(df_cleaned[col]):
                df_cleaned[col].fillna(df_cleaned[col].median(), inplace=True)
            elif col_strategy == 'mode':
                mode_value = df_cleaned[col].mode()
                if len(mode_value) > 0:
                    df_cleaned[col].fillna(mode_value[0], inplace=True)
            elif col_strategy == 'forward_fill':
                df_cleaned[col].fillna(method='ffill', inplace=True)
            elif col_strategy == 'backward_fill':
                df_cleaned[col].fillna(method='bfill', inplace=True)
        
        logger.info(f"缺失值处理完成，数据形状: {df_cleaned.shape}")
        return df_cleaned
    
    @staticmethod
    def remove_outliers(df: pd.DataFrame, columns: List[str] = None, method: str = 'iqr', 
                       multiplier: float = 1.5) -> pd.DataFrame:
        """
        移除异常值
        
        Args:
            df: 原始DataFrame
            columns: 需要处理的列名列表，None表示所有数值列
            method: 异常值检测方法 ('iqr', 'zscore')
            multiplier: 倍数阈值
            
        Returns:
            处理后的DataFrame
        """
        df_cleaned = df.copy()
        
        if columns is None:
            columns = df_cleaned.select_dtypes(include=[np.number]).columns.tolist()
        
        for col in columns:
            if col not in df_cleaned.columns:
                continue
            
            if method == 'iqr':
                Q1 = df_cleaned[col].quantile(0.25)
                Q3 = df_cleaned[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - multiplier * IQR
                upper_bound = Q3 + multiplier * IQR
                
                mask = (df_cleaned[col] >= lower_bound) & (df_cleaned[col] <= upper_bound)
                df_cleaned = df_cleaned[mask]
                
            elif method == 'zscore':
                z_scores = np.abs((df_cleaned[col] - df_cleaned[col].mean()) / df_cleaned[col].std())
                mask = z_scores <= multiplier
                df_cleaned = df_cleaned[mask]
        
        logger.info(f"异常值处理完成，数据形状: {df_cleaned.shape}")
        return df_cleaned


def get_data_info(df: pd.DataFrame) -> str:
    """获取数据信息摘要"""
    info = []
    info.append(f"数据形状: {df.shape}")
    info.append(f"内存使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    info.append(f"数值列数: {len(df.select_dtypes(include=[np.number]).columns)}")
    info.append(f"类别列数: {len(df.select_dtypes(include=['object']).columns)}")
    info.append(f"缺失值总数: {df.isnull().sum().sum()}")
    
    return "\n".join(info)
