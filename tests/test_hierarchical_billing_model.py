#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分层计费模型单元测试
测试分层模型的核心功能和性能
"""

import unittest
import numpy as np
import pandas as pd
import tempfile
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
from src.billing_audit.models.zero_value_classifier import ZeroValueClassifierOptimizer
from src.billing_audit.models.nonzero_value_regressor import NonzeroValueRegressorOptimizer
from src.billing_audit.evaluation.hierarchical_evaluator import HierarchicalModelEvaluator


class TestHierarchicalBillingModel(unittest.TestCase):
    """分层计费模型测试类"""
    
    def setUp(self):
        """测试前准备"""
        np.random.seed(42)
        self.n_samples = 1000
        
        # 创建测试数据
        self.X = pd.DataFrame({
            'should_fee': np.random.uniform(0, 100, self.n_samples),
            'charge_day_count': np.random.randint(1, 31, self.n_samples),
            'month_day_count': np.full(self.n_samples, 31),
            'cal_type': np.random.randint(0, 4, self.n_samples),
            'unit_type': np.random.randint(0, 3, self.n_samples)
        })
        
        # 生成目标变量（模拟零值问题）
        self.y = np.zeros(self.n_samples)
        # 30%的样本为非零值
        nonzero_indices = np.random.choice(self.n_samples, size=int(0.3 * self.n_samples), replace=False)
        self.y[nonzero_indices] = np.random.uniform(10, 200, len(nonzero_indices))
        
        # 创建分层模型
        self.model = HierarchicalBillingModel(use_lightgbm=False)  # 使用RandomForest确保兼容性
    
    def test_model_initialization(self):
        """测试模型初始化"""
        self.assertIsNotNone(self.model)
        self.assertFalse(self.model.is_fitted)
        self.assertEqual(self.model.zero_threshold, 1e-6)
        self.assertFalse(self.model.use_lightgbm)
    
    def test_model_training(self):
        """测试模型训练"""
        # 训练模型
        self.model.fit(self.X, self.y)
        
        # 检查训练状态
        self.assertTrue(self.model.is_fitted)
        self.assertIsNotNone(self.model.zero_classifier)
        self.assertIsNotNone(self.model.nonzero_regressor)
        self.assertIsNotNone(self.model.feature_names)
        
        # 检查训练统计
        stats = self.model.get_training_stats()
        self.assertIn('total_samples', stats)
        self.assertIn('zero_count', stats)
        self.assertIn('nonzero_count', stats)
        self.assertIn('classifier_accuracy', stats)
        
        # 验证样本数量
        self.assertEqual(stats['total_samples'], self.n_samples)
        self.assertEqual(stats['zero_count'] + stats['nonzero_count'], self.n_samples)
    
    def test_model_prediction(self):
        """测试模型预测"""
        # 训练模型
        self.model.fit(self.X, self.y)
        
        # 预测
        predictions = self.model.predict(self.X)
        
        # 检查预测结果
        self.assertEqual(len(predictions), self.n_samples)
        self.assertTrue(isinstance(predictions, np.ndarray))
        
        # 检查预测值的合理性
        self.assertTrue(np.all(predictions >= 0))  # 预测值应该非负
        
        # 检查零值预测
        zero_predictions = np.sum(predictions == 0)
        self.assertGreater(zero_predictions, 0)  # 应该有零值预测
    
    def test_model_predict_proba(self):
        """测试概率预测"""
        # 训练模型
        self.model.fit(self.X, self.y)
        
        # 概率预测
        proba_results = self.model.predict_proba(self.X)
        
        # 检查返回格式
        self.assertIn('zero_proba', proba_results)
        self.assertIn('predictions', proba_results)
        
        # 检查概率形状
        zero_proba = proba_results['zero_proba']
        self.assertEqual(zero_proba.shape[0], self.n_samples)
        self.assertEqual(zero_proba.shape[1], 2)  # 二分类概率
        
        # 检查概率范围
        self.assertTrue(np.all(zero_proba >= 0))
        self.assertTrue(np.all(zero_proba <= 1))
        self.assertTrue(np.allclose(zero_proba.sum(axis=1), 1))  # 概率和为1
    
    def test_model_save_load(self):
        """测试模型保存和加载"""
        # 训练模型
        self.model.fit(self.X, self.y)
        
        # 获取原始预测
        original_predictions = self.model.predict(self.X[:10])
        
        # 保存模型
        with tempfile.NamedTemporaryFile(suffix='.pkl', delete=False) as tmp_file:
            model_path = tmp_file.name
        
        try:
            self.model.save(model_path)
            
            # 加载模型
            loaded_model = HierarchicalBillingModel.load(model_path)
            
            # 检查加载的模型
            self.assertTrue(loaded_model.is_fitted)
            self.assertEqual(loaded_model.feature_names, self.model.feature_names)
            
            # 检查预测一致性
            loaded_predictions = loaded_model.predict(self.X[:10])
            np.testing.assert_array_almost_equal(original_predictions, loaded_predictions)
            
        finally:
            # 清理临时文件
            if os.path.exists(model_path):
                os.unlink(model_path)
    
    def test_feature_importance(self):
        """测试特征重要性"""
        # 训练模型
        self.model.fit(self.X, self.y)
        
        # 获取特征重要性
        importance = self.model.get_feature_importance()
        
        # 检查返回格式
        self.assertIn('classifier_importance', importance)
        if self.model.nonzero_regressor:
            self.assertIn('regressor_importance', importance)
        
        # 检查重要性数组长度
        classifier_importance = importance['classifier_importance']
        self.assertEqual(len(classifier_importance), len(self.model.feature_names))
        
        # 检查重要性值的合理性
        self.assertTrue(np.all(classifier_importance >= 0))
        self.assertGreater(np.sum(classifier_importance), 0)
    
    def test_model_string_representation(self):
        """测试模型字符串表示"""
        # 未训练的模型
        str_repr = str(self.model)
        self.assertIn('未训练', str_repr)
        
        # 训练后的模型
        self.model.fit(self.X, self.y)
        str_repr = str(self.model)
        self.assertIn('RandomForest', str_repr)
        self.assertIn('样本=', str_repr)
        self.assertIn('零值比例=', str_repr)


class TestZeroValueClassifierOptimizer(unittest.TestCase):
    """零值分类器优化器测试类"""
    
    def setUp(self):
        """测试前准备"""
        np.random.seed(42)
        self.n_samples = 500
        
        # 创建测试数据
        self.X = pd.DataFrame({
            'should_fee': np.random.uniform(0, 100, self.n_samples),
            'charge_day_count': np.random.randint(1, 31, self.n_samples),
            'month_day_count': np.full(self.n_samples, 31)
        })
        
        # 生成目标变量
        self.y = np.zeros(self.n_samples)
        nonzero_indices = np.random.choice(self.n_samples, size=int(0.4 * self.n_samples), replace=False)
        self.y[nonzero_indices] = np.random.uniform(10, 100, len(nonzero_indices))
        
        self.optimizer = ZeroValueClassifierOptimizer(use_lightgbm=False)
    
    def test_feature_importance_analysis(self):
        """测试特征重要性分析"""
        results = self.optimizer.analyze_feature_importance(self.X, self.y)
        
        # 检查返回格式
        self.assertIn('data_stats', results)
        self.assertIn('feature_importance', results)
        self.assertIn('feature_discrimination', results)
        
        # 检查数据统计
        data_stats = results['data_stats']
        self.assertEqual(data_stats['total_samples'], self.n_samples)
        self.assertGreater(data_stats['zero_count'], 0)
        self.assertGreater(data_stats['nonzero_count'], 0)
        
        # 检查特征重要性
        feature_importance = results['feature_importance']
        self.assertEqual(len(feature_importance), len(self.X.columns))
        self.assertIn('composite_score', feature_importance.columns)
    
    def test_feature_selection(self):
        """测试特征选择"""
        selected_features = self.optimizer.select_best_features(self.X, self.y, max_features=2)
        
        # 检查选择结果
        self.assertIsInstance(selected_features, list)
        self.assertLessEqual(len(selected_features), 2)
        self.assertTrue(all(feature in self.X.columns for feature in selected_features))
    
    def test_hyperparameter_optimization(self):
        """测试超参数优化"""
        results = self.optimizer.optimize_hyperparameters(self.X, self.y)
        
        # 检查返回格式
        self.assertIn('best_params', results)
        self.assertIn('best_score', results)
        self.assertIn('all_results', results)
        
        # 检查最佳参数
        best_params = results['best_params']
        self.assertIsInstance(best_params, dict)
        self.assertIn('n_estimators', best_params)
        
        # 检查最佳分数
        best_score = results['best_score']
        self.assertGreaterEqual(best_score, 0)
        self.assertLessEqual(best_score, 1)


class TestNonzeroValueRegressorOptimizer(unittest.TestCase):
    """非零值回归器优化器测试类"""
    
    def setUp(self):
        """测试前准备"""
        np.random.seed(42)
        self.n_samples = 500
        
        # 创建测试数据
        self.X = pd.DataFrame({
            'should_fee': np.random.uniform(10, 100, self.n_samples),  # 确保有非零值
            'charge_day_count': np.random.randint(1, 31, self.n_samples),
            'month_day_count': np.full(self.n_samples, 31)
        })
        
        # 生成目标变量（主要是非零值）
        self.y = np.random.uniform(10, 200, self.n_samples)
        # 添加少量零值
        zero_indices = np.random.choice(self.n_samples, size=int(0.1 * self.n_samples), replace=False)
        self.y[zero_indices] = 0
        
        self.optimizer = NonzeroValueRegressorOptimizer(use_lightgbm=False)
    
    def test_nonzero_data_analysis(self):
        """测试非零值数据分析"""
        results = self.optimizer.analyze_nonzero_data(self.X, self.y)
        
        # 检查返回格式
        self.assertIn('stats', results)
        self.assertIn('X_nonzero', results)
        self.assertIn('y_nonzero', results)
        
        # 检查统计信息
        stats = results['stats']
        self.assertIn('nonzero_count', stats)
        self.assertIn('mean_value', stats)
        self.assertIn('outliers', stats)
        
        # 验证非零值数量
        self.assertGreater(stats['nonzero_count'], 0)
    
    def test_feature_engineering(self):
        """测试特征工程"""
        X_engineered = self.optimizer.engineer_nonzero_features(self.X, self.y)
        
        # 检查特征工程结果
        self.assertIsInstance(X_engineered, pd.DataFrame)
        self.assertGreaterEqual(X_engineered.shape[1], self.X.shape[1])  # 特征数量应该增加
        
        # 检查新特征
        if 'daily_should_fee' in X_engineered.columns:
            self.assertTrue(np.all(X_engineered['daily_should_fee'] >= 0))


class TestHierarchicalModelEvaluator(unittest.TestCase):
    """分层模型评估器测试类"""
    
    def setUp(self):
        """测试前准备"""
        np.random.seed(42)
        self.n_samples = 1000
        
        # 生成真实值
        self.y_true = np.zeros(self.n_samples)
        nonzero_indices = np.random.choice(self.n_samples, size=int(0.3 * self.n_samples), replace=False)
        self.y_true[nonzero_indices] = np.random.uniform(10, 200, len(nonzero_indices))
        
        # 生成预测值（模拟分层模型预测）
        self.y_pred = np.zeros(self.n_samples)
        for i in range(self.n_samples):
            if self.y_true[i] == 0:
                # 零值：85%概率预测正确
                if np.random.random() < 0.85:
                    self.y_pred[i] = 0
                else:
                    self.y_pred[i] = np.random.uniform(5, 50)
            else:
                # 非零值：90%概率预测为非零值
                if np.random.random() < 0.9:
                    self.y_pred[i] = self.y_true[i] + np.random.normal(0, 20)
                    self.y_pred[i] = max(0, self.y_pred[i])
                else:
                    self.y_pred[i] = 0
        
        self.evaluator = HierarchicalModelEvaluator()
    
    def test_comprehensive_evaluation(self):
        """测试综合评估"""
        results = self.evaluator.evaluate_comprehensive(self.y_true, self.y_pred)
        
        # 检查返回格式
        self.assertIn('classification_metrics', results)
        self.assertIn('regression_metrics', results)
        self.assertIn('overall_metrics', results)
        self.assertIn('business_metrics', results)
        self.assertIn('summary', results)
        
        # 检查分类指标
        classification = results['classification_metrics']
        self.assertIn('overall_accuracy', classification)
        self.assertIn('overall_f1', classification)
        
        # 检查回归指标
        regression = results['regression_metrics']
        if 'error' not in regression:
            self.assertIn('r2', regression)
            self.assertIn('mae', regression)
        
        # 检查业务指标
        business = results['business_metrics']
        self.assertIn('business_accuracy', business)
        
        # 检查摘要
        summary = results['summary']
        self.assertIn('performance_grade', summary)
        self.assertIn('key_metrics', summary)


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
