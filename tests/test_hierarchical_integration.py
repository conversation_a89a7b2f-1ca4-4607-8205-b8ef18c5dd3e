#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分层建模系统集成测试
测试完整的分层建模流程，包括训练、预测、评估和判定
"""

import unittest
import numpy as np
import pandas as pd
import tempfile
import os
from pathlib import Path
import sys
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.billing_audit.training.train_large_scale_model import train_hierarchical_model
from src.billing_audit.inference.predict_large_scale import predict_with_hierarchical_model
from src.billing_audit.models.large_scale_model_evaluation import LargeScaleModelEvaluator
from src.billing_audit.inference.large_scale_billing_judge import LargeScaleBillingJudge
from src.billing_audit.preprocessing.large_scale_feature_engineer import LargeScaleFeatureEngineer


class TestHierarchicalIntegration(unittest.TestCase):
    """分层建模系统集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        np.random.seed(42)
        self.n_samples = 2000
        
        # 创建测试数据集
        self.test_data = self._create_test_dataset()
        
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp())
        self.data_file = self.temp_dir / "test_data.csv"
        self.model_dir = self.temp_dir / "models"
        self.model_dir.mkdir(exist_ok=True)
        
        # 保存测试数据
        self.test_data.to_csv(self.data_file, index=False)
        
        print(f"集成测试准备完成:")
        print(f"  测试数据: {self.n_samples}样本")
        print(f"  零值比例: {(self.test_data['amount'] == 0).mean()*100:.1f}%")
        print(f"  数据文件: {self.data_file}")
        print(f"  模型目录: {self.model_dir}")
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def _create_test_dataset(self):
        """创建测试数据集"""
        # 生成基础特征
        data = {
            'should_fee': np.random.uniform(0, 100, self.n_samples),
            'charge_day_count': np.random.randint(1, 31, self.n_samples),
            'month_day_count': np.full(self.n_samples, 31),
            'cal_type': np.random.randint(0, 4, self.n_samples),
            'unit_type': np.random.randint(0, 3, self.n_samples),
            'rate_unit': np.random.randint(0, 5, self.n_samples),
            'busi_flag': np.random.randint(0, 2, self.n_samples),
            'final_eff_year': np.full(self.n_samples, 2024),
            'final_eff_mon': np.random.randint(1, 13, self.n_samples)
        }
        
        df = pd.DataFrame(data)
        
        # 生成目标变量（模拟真实业务规律）
        amounts = np.zeros(self.n_samples)
        for i in range(self.n_samples):
            if df.loc[i, 'should_fee'] == 0:
                # should_fee为0时，90%概率为零值
                if np.random.random() < 0.9:
                    amounts[i] = 0
                else:
                    amounts[i] = np.random.uniform(1, 20)
            else:
                # should_fee>0时，30%概率为零值
                if np.random.random() < 0.3:
                    amounts[i] = 0
                else:
                    # 非零值与should_fee相关
                    base_amount = df.loc[i, 'should_fee'] * (0.8 + np.random.random() * 0.4)
                    amounts[i] = base_amount + np.random.normal(0, 10)
                    amounts[i] = max(0, amounts[i])
        
        df['amount'] = amounts
        return df
    
    def test_end_to_end_hierarchical_workflow(self):
        """测试端到端分层建模工作流"""
        print("\n🔄 开始端到端分层建模工作流测试...")
        
        # 第一步：分层模型训练
        print("\n1️⃣ 分层模型训练...")
        try:
            hierarchical_model, training_stats = train_hierarchical_model(
                file_path=str(self.data_file),
                output_dir=str(self.model_dir),
                batch_size=500,
                use_lightgbm=False  # 使用RandomForest确保兼容性
            )
            
            # 验证训练结果
            self.assertIsNotNone(hierarchical_model)
            self.assertIsNotNone(training_stats)
            self.assertTrue(hierarchical_model.is_fitted)
            
            # 检查训练统计
            self.assertIn('samples', training_stats)
            self.assertIn('r2', training_stats)
            self.assertIn('zero_accuracy', training_stats)
            
            print(f"✅ 分层模型训练成功:")
            print(f"   样本数: {training_stats['samples']}")
            print(f"   R²: {training_stats['r2']:.4f}")
            print(f"   零值识别准确率: {training_stats['zero_accuracy']:.4f}")
            
        except Exception as e:
            self.fail(f"分层模型训练失败: {e}")
        
        # 第二步：查找生成的模型文件
        print("\n2️⃣ 查找模型文件...")
        model_files = list(self.model_dir.glob("hierarchical_model_*.pkl"))
        feature_engineer_files = list(self.model_dir.glob("large_scale_feature_engineer_*.pkl"))
        
        self.assertGreater(len(model_files), 0, "未找到分层模型文件")
        self.assertGreater(len(feature_engineer_files), 0, "未找到特征工程器文件")
        
        model_path = model_files[0]
        feature_engineer_path = feature_engineer_files[0]
        
        print(f"✅ 找到模型文件:")
        print(f"   模型: {model_path.name}")
        print(f"   特征工程器: {feature_engineer_path.name}")
        
        # 第三步：分层模型预测
        print("\n3️⃣ 分层模型预测...")
        prediction_output = self.temp_dir / "predictions.csv"
        
        try:
            # 使用分层预测函数
            prediction_stats = predict_with_hierarchical_model(
                input_file=str(self.data_file),
                model_path=str(model_path),
                feature_engineer_path=str(feature_engineer_path),
                output_file=str(prediction_output),
                batch_size=500
            )
            
            # 验证预测结果
            self.assertIsNotNone(prediction_stats)
            self.assertTrue(prediction_output.exists())
            
            # 读取预测结果
            predictions_df = pd.read_csv(prediction_output)
            self.assertEqual(len(predictions_df), self.n_samples)
            self.assertIn('predicted_amount', predictions_df.columns)
            
            print(f"✅ 分层模型预测成功:")
            print(f"   预测样本数: {prediction_stats['total_samples']}")
            print(f"   零值预测: {prediction_stats['zero_predictions']} ({prediction_stats['zero_ratio']:.1f}%)")
            print(f"   非零值预测: {prediction_stats['nonzero_predictions']}")
            print(f"   处理速度: {prediction_stats['processing_speed']:,.0f}条/秒")
            
        except Exception as e:
            self.fail(f"分层模型预测失败: {e}")
        
        # 第四步：分层模型评估
        print("\n4️⃣ 分层模型评估...")
        try:
            evaluator = LargeScaleModelEvaluator(
                model_path=str(model_path),
                feature_engineer_path=str(feature_engineer_path),
                batch_size=500
            )
            
            # 执行评估
            evaluation_results = evaluator.evaluate_large_file(str(self.data_file))
            
            # 验证评估结果
            self.assertIsNotNone(evaluation_results)
            
            # 计算整体指标
            overall_metrics = evaluator.calculate_overall_metrics()
            self.assertIsNotNone(overall_metrics)
            
            if overall_metrics.get('model_type') == 'hierarchical':
                print(f"✅ 分层模型评估成功:")
                print(f"   整体R²: {overall_metrics['overall_r2']:.4f}")
                print(f"   整体MAE: {overall_metrics['overall_mae']:.2f}元")
                print(f"   零值分类F1: {overall_metrics['zero_classification_f1']:.4f}")
                print(f"   非零值回归R²: {overall_metrics['nonzero_regression_r2']:.4f}")
                print(f"   性能等级: {overall_metrics['performance_grade']}")
            else:
                print(f"✅ 传统模型评估成功:")
                print(f"   R²: {overall_metrics['overall']['r2']:.4f}")
                print(f"   MAE: {overall_metrics['overall']['mae']:.2f}元")
            
        except Exception as e:
            self.fail(f"分层模型评估失败: {e}")
        
        # 第五步：收费合理性判定
        print("\n5️⃣ 收费合理性判定...")
        judgment_output = self.temp_dir / "judgments.csv"
        
        try:
            judge = LargeScaleBillingJudge(
                model_path=str(model_path),
                feature_engineer_path=str(feature_engineer_path),
                batch_size=500
            )
            
            # 执行判定
            judgment_results = judge.judge_large_file(
                input_file=str(self.data_file),
                output_file=str(judgment_output)
            )
            
            # 验证判定结果
            self.assertIsNotNone(judgment_results)
            self.assertTrue(judgment_output.exists())
            
            # 读取判定结果
            judgments_df = pd.read_csv(judgment_output)
            self.assertEqual(len(judgments_df), self.n_samples)
            self.assertIn('judgment', judgments_df.columns)
            self.assertIn('confidence_score', judgments_df.columns)
            
            # 统计判定结果
            judgment_counts = judgments_df['judgment'].value_counts()
            
            print(f"✅ 收费合理性判定成功:")
            print(f"   判定样本数: {len(judgments_df)}")
            for judgment, count in judgment_counts.items():
                print(f"   {judgment}: {count}个 ({count/len(judgments_df)*100:.1f}%)")
            
        except Exception as e:
            self.fail(f"收费合理性判定失败: {e}")
        
        print(f"\n🎉 端到端分层建模工作流测试完成！")
        print(f"所有5个步骤均成功执行：")
        print(f"  ✅ 分层模型训练")
        print(f"  ✅ 模型文件生成")
        print(f"  ✅ 分层模型预测")
        print(f"  ✅ 分层模型评估")
        print(f"  ✅ 收费合理性判定")
    
    def test_hierarchical_vs_traditional_comparison(self):
        """测试分层模型与传统模型的性能对比"""
        print("\n📊 开始分层模型与传统模型性能对比测试...")
        
        # 训练分层模型
        print("\n训练分层模型...")
        hierarchical_model, hierarchical_stats = train_hierarchical_model(
            file_path=str(self.data_file),
            output_dir=str(self.model_dir),
            batch_size=500,
            use_lightgbm=False
        )
        
        # 训练传统模型（通过大规模训练脚本）
        print("\n训练传统模型...")
        from src.billing_audit.training.train_large_scale_model import train_large_scale_model
        traditional_stats = train_large_scale_model(
            file_path=str(self.data_file),
            output_dir=str(self.model_dir),
            batch_size=500
        )
        
        # 性能对比
        print(f"\n📈 性能对比结果:")
        print(f"分层模型:")
        print(f"  R²: {hierarchical_stats['r2']:.4f}")
        print(f"  MAE: {hierarchical_stats['mae']:.2f}元")
        print(f"  零值识别准确率: {hierarchical_stats['zero_accuracy']:.4f}")
        
        print(f"传统模型:")
        print(f"  R²: {traditional_stats['r2']:.4f}")
        print(f"  MAE: {traditional_stats['mae']:.2f}元")
        
        # 验证分层模型在零值识别方面的优势
        self.assertGreaterEqual(hierarchical_stats['zero_accuracy'], 0.7, "分层模型零值识别准确率应该≥70%")
        
        print(f"\n✅ 性能对比测试完成")
    
    def test_model_persistence_and_reload(self):
        """测试模型持久化和重新加载"""
        print("\n💾 开始模型持久化和重新加载测试...")
        
        # 训练并保存模型
        hierarchical_model, training_stats = train_hierarchical_model(
            file_path=str(self.data_file),
            output_dir=str(self.model_dir),
            batch_size=500,
            use_lightgbm=False
        )
        
        # 获取原始预测
        original_predictions = hierarchical_model.predict(self.test_data.iloc[:100, :-1])
        
        # 查找保存的模型文件
        model_files = list(self.model_dir.glob("hierarchical_model_*.pkl"))
        self.assertGreater(len(model_files), 0)
        
        model_path = model_files[0]
        
        # 重新加载模型
        from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
        reloaded_model = HierarchicalBillingModel.load(str(model_path))
        
        # 验证加载的模型
        self.assertTrue(reloaded_model.is_fitted)
        
        # 获取重新加载后的预测
        reloaded_predictions = reloaded_model.predict(self.test_data.iloc[:100, :-1])
        
        # 验证预测一致性
        np.testing.assert_array_almost_equal(original_predictions, reloaded_predictions, decimal=6)
        
        print(f"✅ 模型持久化和重新加载测试成功")
        print(f"   原始预测与重新加载预测完全一致")


if __name__ == '__main__':
    # 运行集成测试
    unittest.main(verbosity=2)
