#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分层建模系统性能基准测试
对比分层模型与传统模型在不同数据规模和零值比例下的性能表现
"""

import unittest
import numpy as np
import pandas as pd
import time
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
from src.billing_audit.evaluation.hierarchical_evaluator import HierarchicalModelEvaluator
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_absolute_error


class TestPerformanceBenchmark(unittest.TestCase):
    """性能基准测试类"""
    
    def setUp(self):
        """测试前准备"""
        np.random.seed(42)
        print("\n🚀 性能基准测试开始...")
    
    def _create_benchmark_dataset(self, n_samples: int, zero_ratio: float = 0.7):
        """创建基准测试数据集"""
        # 生成特征
        X = pd.DataFrame({
            'should_fee': np.random.uniform(0, 100, n_samples),
            'charge_day_count': np.random.randint(1, 31, n_samples),
            'month_day_count': np.full(n_samples, 31),
            'cal_type': np.random.randint(0, 4, n_samples),
            'unit_type': np.random.randint(0, 3, n_samples),
            'rate_unit': np.random.randint(0, 5, n_samples),
            'busi_flag': np.random.randint(0, 2, n_samples)
        })
        
        # 生成目标变量
        y = np.zeros(n_samples)
        n_nonzero = int(n_samples * (1 - zero_ratio))
        nonzero_indices = np.random.choice(n_samples, size=n_nonzero, replace=False)
        
        # 根据should_fee生成更真实的非零值
        for idx in nonzero_indices:
            base_amount = X.loc[idx, 'should_fee'] * (0.8 + np.random.random() * 0.4)
            y[idx] = base_amount + np.random.normal(0, 10)
            y[idx] = max(0, y[idx])
        
        return X, y
    
    def test_scalability_benchmark(self):
        """测试可扩展性基准"""
        print("\n📈 可扩展性基准测试...")
        
        sample_sizes = [1000, 5000, 10000, 20000]
        results = []
        
        for n_samples in sample_sizes:
            print(f"\n测试样本规模: {n_samples:,}")
            
            # 创建测试数据
            X, y = self._create_benchmark_dataset(n_samples, zero_ratio=0.6)
            
            # 分层模型性能测试
            hierarchical_start = time.time()
            hierarchical_model = HierarchicalBillingModel(use_lightgbm=False)
            hierarchical_model.fit(X, y)
            hierarchical_pred = hierarchical_model.predict(X)
            hierarchical_time = time.time() - hierarchical_start
            
            # 传统模型性能测试
            traditional_start = time.time()
            traditional_model = RandomForestRegressor(n_estimators=100, random_state=42)
            traditional_model.fit(X, y)
            traditional_pred = traditional_model.predict(X)
            traditional_time = time.time() - traditional_start
            
            # 计算性能指标
            hierarchical_r2 = r2_score(y, hierarchical_pred)
            hierarchical_mae = mean_absolute_error(y, hierarchical_pred)
            
            traditional_r2 = r2_score(y, traditional_pred)
            traditional_mae = mean_absolute_error(y, traditional_pred)
            
            # 零值识别准确率
            zero_mask_true = (y == 0)
            hierarchical_zero_acc = (zero_mask_true == (hierarchical_pred == 0)).mean()
            traditional_zero_acc = (zero_mask_true == (traditional_pred <= 1e-6)).mean()
            
            result = {
                'samples': n_samples,
                'hierarchical_time': hierarchical_time,
                'traditional_time': traditional_time,
                'hierarchical_r2': hierarchical_r2,
                'traditional_r2': traditional_r2,
                'hierarchical_mae': hierarchical_mae,
                'traditional_mae': traditional_mae,
                'hierarchical_zero_acc': hierarchical_zero_acc,
                'traditional_zero_acc': traditional_zero_acc,
                'speed_ratio': traditional_time / hierarchical_time
            }
            results.append(result)
            
            print(f"  分层模型: 时间={hierarchical_time:.2f}s, R²={hierarchical_r2:.4f}, 零值准确率={hierarchical_zero_acc:.4f}")
            print(f"  传统模型: 时间={traditional_time:.2f}s, R²={traditional_r2:.4f}, 零值准确率={traditional_zero_acc:.4f}")
            print(f"  速度比: {result['speed_ratio']:.2f}x")
        
        # 验证可扩展性
        print(f"\n📊 可扩展性分析:")
        for i, result in enumerate(results):
            if i > 0:
                prev_result = results[i-1]
                scale_factor = result['samples'] / prev_result['samples']
                time_factor = result['hierarchical_time'] / prev_result['hierarchical_time']
                print(f"  {prev_result['samples']:,} -> {result['samples']:,}: 规模x{scale_factor:.1f}, 时间x{time_factor:.2f}")
        
        # 验证分层模型在大规模数据上的表现
        large_scale_result = results[-1]  # 最大规模的结果
        self.assertGreater(large_scale_result['hierarchical_zero_acc'], 0.7, "大规模数据下零值识别准确率应该>70%")
        
        print("✅ 可扩展性基准测试完成")
    
    def test_zero_ratio_impact_benchmark(self):
        """测试零值比例影响基准"""
        print("\n🎯 零值比例影响基准测试...")
        
        zero_ratios = [0.3, 0.5, 0.7, 0.9]
        n_samples = 5000
        results = []
        
        for zero_ratio in zero_ratios:
            print(f"\n测试零值比例: {zero_ratio*100:.0f}%")
            
            # 创建测试数据
            X, y = self._create_benchmark_dataset(n_samples, zero_ratio=zero_ratio)
            
            # 分层模型测试
            hierarchical_model = HierarchicalBillingModel(use_lightgbm=False)
            hierarchical_model.fit(X, y)
            hierarchical_pred = hierarchical_model.predict(X)
            
            # 传统模型测试
            traditional_model = RandomForestRegressor(n_estimators=100, random_state=42)
            traditional_model.fit(X, y)
            traditional_pred = traditional_model.predict(X)
            
            # 性能评估
            evaluator = HierarchicalModelEvaluator()
            hierarchical_eval = evaluator.evaluate_comprehensive(y, hierarchical_pred)
            
            # 计算指标
            hierarchical_r2 = r2_score(y, hierarchical_pred)
            traditional_r2 = r2_score(y, traditional_pred)
            
            zero_mask_true = (y == 0)
            hierarchical_zero_acc = (zero_mask_true == (hierarchical_pred == 0)).mean()
            traditional_zero_acc = (zero_mask_true == (traditional_pred <= 1e-6)).mean()
            
            # 业务准确率
            business_acc_50 = hierarchical_eval['business_metrics']['business_accuracy']['within_50_yuan']
            
            result = {
                'zero_ratio': zero_ratio,
                'hierarchical_r2': hierarchical_r2,
                'traditional_r2': traditional_r2,
                'hierarchical_zero_acc': hierarchical_zero_acc,
                'traditional_zero_acc': traditional_zero_acc,
                'business_acc_50': business_acc_50,
                'performance_grade': hierarchical_eval['summary']['performance_grade']
            }
            results.append(result)
            
            print(f"  分层模型: R²={hierarchical_r2:.4f}, 零值准确率={hierarchical_zero_acc:.4f}, 业务准确率={business_acc_50:.1f}%")
            print(f"  传统模型: R²={traditional_r2:.4f}, 零值准确率={traditional_zero_acc:.4f}")
            print(f"  性能等级: {result['performance_grade']}")
        
        # 分析零值比例对性能的影响
        print(f"\n📊 零值比例影响分析:")
        for result in results:
            zero_advantage = result['hierarchical_zero_acc'] - result['traditional_zero_acc']
            print(f"  零值比例{result['zero_ratio']*100:.0f}%: 分层模型零值识别优势={zero_advantage:+.4f}")
        
        # 验证分层模型在高零值比例下的优势
        high_zero_result = results[-1]  # 90%零值比例的结果
        self.assertGreater(high_zero_result['hierarchical_zero_acc'], 0.8, "高零值比例下分层模型零值识别准确率应该>80%")
        
        print("✅ 零值比例影响基准测试完成")
    
    def test_feature_dimension_impact_benchmark(self):
        """测试特征维度影响基准"""
        print("\n🔢 特征维度影响基准测试...")
        
        n_samples = 3000
        base_features = ['should_fee', 'charge_day_count', 'month_day_count', 'cal_type', 'unit_type']
        
        feature_configs = [
            {'features': base_features[:3], 'name': '3特征'},
            {'features': base_features[:5], 'name': '5特征'},
            {'features': base_features + [f'extra_{i}' for i in range(3)], 'name': '8特征'},
            {'features': base_features + [f'extra_{i}' for i in range(8)], 'name': '13特征'}
        ]
        
        results = []
        
        for config in feature_configs:
            print(f"\n测试特征配置: {config['name']}")
            
            # 创建特征数据
            X_data = {}
            for feature in config['features']:
                if feature in base_features:
                    if feature == 'should_fee':
                        X_data[feature] = np.random.uniform(0, 100, n_samples)
                    elif feature == 'charge_day_count':
                        X_data[feature] = np.random.randint(1, 31, n_samples)
                    elif feature == 'month_day_count':
                        X_data[feature] = np.full(n_samples, 31)
                    else:
                        X_data[feature] = np.random.randint(0, 4, n_samples)
                else:
                    # 额外特征
                    X_data[feature] = np.random.normal(0, 1, n_samples)
            
            X = pd.DataFrame(X_data)
            
            # 生成目标变量
            y = np.zeros(n_samples)
            nonzero_indices = np.random.choice(n_samples, size=int(0.4 * n_samples), replace=False)
            for idx in nonzero_indices:
                if 'should_fee' in X.columns:
                    base_amount = X.loc[idx, 'should_fee'] * 0.9
                else:
                    base_amount = 50
                y[idx] = base_amount + np.random.normal(0, 10)
                y[idx] = max(0, y[idx])
            
            # 训练和测试
            start_time = time.time()
            hierarchical_model = HierarchicalBillingModel(use_lightgbm=False)
            hierarchical_model.fit(X, y)
            hierarchical_pred = hierarchical_model.predict(X)
            training_time = time.time() - start_time
            
            # 性能指标
            hierarchical_r2 = r2_score(y, hierarchical_pred)
            hierarchical_mae = mean_absolute_error(y, hierarchical_pred)
            
            zero_mask_true = (y == 0)
            hierarchical_zero_acc = (zero_mask_true == (hierarchical_pred == 0)).mean()
            
            result = {
                'feature_count': len(config['features']),
                'feature_name': config['name'],
                'training_time': training_time,
                'r2': hierarchical_r2,
                'mae': hierarchical_mae,
                'zero_accuracy': hierarchical_zero_acc
            }
            results.append(result)
            
            print(f"  训练时间: {training_time:.2f}s")
            print(f"  R²: {hierarchical_r2:.4f}")
            print(f"  MAE: {hierarchical_mae:.2f}元")
            print(f"  零值准确率: {hierarchical_zero_acc:.4f}")
        
        # 分析特征维度对性能的影响
        print(f"\n📊 特征维度影响分析:")
        for i, result in enumerate(results):
            if i > 0:
                prev_result = results[i-1]
                time_change = (result['training_time'] - prev_result['training_time']) / prev_result['training_time'] * 100
                r2_change = (result['r2'] - prev_result['r2']) / abs(prev_result['r2']) * 100 if prev_result['r2'] != 0 else 0
                print(f"  {prev_result['feature_name']} -> {result['feature_name']}: 时间变化{time_change:+.1f}%, R²变化{r2_change:+.1f}%")
        
        # 验证模型在不同特征维度下的稳定性
        r2_values = [r['r2'] for r in results]
        r2_std = np.std(r2_values)
        self.assertLess(r2_std, 0.3, "不同特征维度下R²的标准差应该<0.3，表明模型稳定")
        
        print("✅ 特征维度影响基准测试完成")
    
    def test_comprehensive_performance_summary(self):
        """综合性能总结测试"""
        print("\n📋 综合性能总结测试...")
        
        # 标准测试配置
        n_samples = 10000
        zero_ratio = 0.7
        
        print(f"标准测试配置: {n_samples:,}样本, {zero_ratio*100:.0f}%零值比例")
        
        # 创建测试数据
        X, y = self._create_benchmark_dataset(n_samples, zero_ratio=zero_ratio)
        
        # 分层模型完整测试
        print("\n🔄 分层模型完整性能测试...")
        start_time = time.time()
        
        # 训练
        hierarchical_model = HierarchicalBillingModel(use_lightgbm=False)
        hierarchical_model.fit(X, y)
        training_time = time.time() - start_time
        
        # 预测
        pred_start = time.time()
        hierarchical_pred = hierarchical_model.predict(X)
        prediction_time = time.time() - pred_start
        
        # 评估
        eval_start = time.time()
        evaluator = HierarchicalModelEvaluator()
        evaluation_results = evaluator.evaluate_comprehensive(y, hierarchical_pred)
        evaluation_time = time.time() - eval_start
        
        # 提取关键指标
        summary = evaluation_results['summary']
        key_metrics = summary['key_metrics']
        
        # 性能总结
        total_time = training_time + prediction_time + evaluation_time
        throughput = n_samples / total_time
        
        print(f"\n🎯 分层模型性能总结:")
        print(f"  训练时间: {training_time:.2f}s")
        print(f"  预测时间: {prediction_time:.2f}s ({n_samples/prediction_time:,.0f}条/秒)")
        print(f"  评估时间: {evaluation_time:.2f}s")
        print(f"  总处理时间: {total_time:.2f}s")
        print(f"  整体吞吐量: {throughput:,.0f}条/秒")
        print(f"  零值分类F1: {key_metrics['zero_classification_f1']:.4f}")
        print(f"  非零值回归R²: {key_metrics['nonzero_regression_r2']:.4f}")
        print(f"  整体R²: {key_metrics['overall_r2']:.4f}")
        print(f"  整体MAE: {key_metrics['overall_mae']:.2f}元")
        print(f"  业务准确率(±50元): {key_metrics['business_accuracy_50yuan']:.1f}%")
        print(f"  性能等级: {summary['performance_grade']}")
        
        # 性能验证
        self.assertGreater(throughput, 1000, "整体吞吐量应该>1000条/秒")
        self.assertGreater(key_metrics['zero_classification_f1'], 0.7, "零值分类F1应该>0.7")
        self.assertGreater(key_metrics['business_accuracy_50yuan'], 70, "业务准确率应该>70%")
        
        print("✅ 综合性能总结测试完成")


if __name__ == '__main__':
    # 运行性能基准测试
    unittest.main(verbosity=2)
