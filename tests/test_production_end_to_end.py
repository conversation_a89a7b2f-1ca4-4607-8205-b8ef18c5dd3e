#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境端到端测试
使用真实数据测试完整的分层建模流程
"""

import unittest
import numpy as np
import pandas as pd
import tempfile
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
from src.billing_audit.evaluation.hierarchical_evaluator import HierarchicalModelEvaluator


class TestProductionEndToEnd(unittest.TestCase):
    """生产环境端到端测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data_file = Path("数据/test_data.csv")
        
        # 检查测试数据是否存在
        if not self.test_data_file.exists():
            self.skipTest(f"测试数据文件不存在: {self.test_data_file}")
        
        print(f"\n🚀 生产环境端到端测试开始...")
        print(f"使用测试数据: {self.test_data_file}")
    
    def test_real_data_hierarchical_modeling(self):
        """使用真实数据测试分层建模"""
        print("\n📊 真实数据分层建模测试...")
        
        # 读取真实测试数据
        try:
            df = pd.read_csv(self.test_data_file)
            print(f"✅ 成功读取测试数据: {len(df)}行, {df.shape[1]}列")
        except Exception as e:
            self.fail(f"读取测试数据失败: {e}")
        
        # 数据预处理
        print("🔄 数据预处理...")
        
        # 选择特征列（排除目标变量）
        feature_columns = [col for col in df.columns if col != 'amount']
        X = df[feature_columns]
        y = df['amount'].values
        
        # 数据统计
        total_samples = len(y)
        zero_count = np.sum(y == 0)
        nonzero_count = total_samples - zero_count
        zero_ratio = zero_count / total_samples * 100
        
        print(f"数据统计:")
        print(f"  总样本数: {total_samples}")
        print(f"  特征数: {X.shape[1]}")
        print(f"  零值样本: {zero_count} ({zero_ratio:.1f}%)")
        print(f"  非零值样本: {nonzero_count} ({100-zero_ratio:.1f}%)")
        
        # 验证数据质量
        self.assertGreater(total_samples, 100, "测试数据样本数应该>100")
        self.assertGreater(X.shape[1], 3, "特征数应该>3")
        self.assertGreater(zero_ratio, 10, "零值比例应该>10%")
        
        # 分层模型训练
        print("\n🎯 分层模型训练...")
        
        try:
            # 创建分层模型
            hierarchical_model = HierarchicalBillingModel(use_lightgbm=False)  # 使用RandomForest确保兼容性
            
            # 训练模型
            hierarchical_model.fit(X, y)
            
            # 验证训练状态
            self.assertTrue(hierarchical_model.is_fitted)
            self.assertIsNotNone(hierarchical_model.zero_classifier)
            self.assertIsNotNone(hierarchical_model.nonzero_regressor)
            
            # 获取训练统计
            training_stats = hierarchical_model.get_training_stats()
            
            print(f"✅ 分层模型训练成功:")
            print(f"  训练样本: {training_stats['total_samples']}")
            print(f"  零值比例: {training_stats['zero_ratio']:.1f}%")
            print(f"  分类准确率: {training_stats['classifier_accuracy']:.4f}")
            print(f"  回归R²: {training_stats.get('regressor_r2', 0):.4f}")
            print(f"  训练时间: {training_stats['training_time']:.2f}秒")
            
        except Exception as e:
            self.fail(f"分层模型训练失败: {e}")
        
        # 模型预测
        print("\n🔮 模型预测...")
        
        try:
            predictions = hierarchical_model.predict(X)
            
            # 验证预测结果
            self.assertEqual(len(predictions), total_samples)
            self.assertTrue(np.all(predictions >= 0))
            
            # 预测统计
            pred_zero_count = np.sum(predictions == 0)
            pred_nonzero_count = total_samples - pred_zero_count
            pred_zero_ratio = pred_zero_count / total_samples * 100
            
            print(f"✅ 模型预测成功:")
            print(f"  预测零值: {pred_zero_count} ({pred_zero_ratio:.1f}%)")
            print(f"  预测非零值: {pred_nonzero_count} ({100-pred_zero_ratio:.1f}%)")
            
        except Exception as e:
            self.fail(f"模型预测失败: {e}")
        
        # 性能评估
        print("\n📈 性能评估...")
        
        try:
            evaluator = HierarchicalModelEvaluator()
            evaluation_results = evaluator.evaluate_comprehensive(y, predictions)
            
            # 提取关键指标
            summary = evaluation_results['summary']
            key_metrics = summary['key_metrics']
            
            print(f"✅ 性能评估完成:")
            print(f"  零值分类F1: {key_metrics['zero_classification_f1']:.4f}")
            print(f"  非零值回归R²: {key_metrics['nonzero_regression_r2']:.4f}")
            print(f"  整体R²: {key_metrics['overall_r2']:.4f}")
            print(f"  整体MAE: {key_metrics['overall_mae']:.2f}元")
            print(f"  业务准确率(±50元): {key_metrics['business_accuracy_50yuan']:.1f}%")
            print(f"  性能等级: {summary['performance_grade']}")
            
            # 性能验证
            self.assertGreater(key_metrics['zero_classification_f1'], 0.5, "零值分类F1应该>0.5")
            self.assertGreater(key_metrics['business_accuracy_50yuan'], 30, "业务准确率应该>30%")
            
        except Exception as e:
            self.fail(f"性能评估失败: {e}")
        
        # 模型持久化测试
        print("\n💾 模型持久化测试...")
        
        try:
            with tempfile.NamedTemporaryFile(suffix='.pkl', delete=False) as tmp_file:
                model_path = tmp_file.name
            
            # 保存模型
            hierarchical_model.save(model_path)
            
            # 重新加载模型
            loaded_model = HierarchicalBillingModel.load(model_path)
            
            # 验证加载的模型
            self.assertTrue(loaded_model.is_fitted)
            
            # 验证预测一致性
            loaded_predictions = loaded_model.predict(X[:10])
            original_predictions = predictions[:10]
            np.testing.assert_array_almost_equal(loaded_predictions, original_predictions, decimal=6)
            
            print(f"✅ 模型持久化测试成功")
            
            # 清理临时文件
            os.unlink(model_path)
            
        except Exception as e:
            self.fail(f"模型持久化测试失败: {e}")
        
        print(f"\n🎉 真实数据分层建模测试完成！")
    
    def test_production_performance_benchmark(self):
        """生产环境性能基准测试"""
        print("\n⚡ 生产环境性能基准测试...")
        
        # 读取测试数据
        df = pd.read_csv(self.test_data_file)
        feature_columns = [col for col in df.columns if col != 'amount']
        X = df[feature_columns]
        y = df['amount'].values
        
        print(f"基准测试数据: {len(df)}样本, {X.shape[1]}特征")
        
        # 性能测试
        import time
        
        # 训练性能测试
        print("\n🏃‍♂️ 训练性能测试...")
        train_start = time.time()
        
        hierarchical_model = HierarchicalBillingModel(use_lightgbm=False)
        hierarchical_model.fit(X, y)
        
        train_time = time.time() - train_start
        train_throughput = len(df) / train_time
        
        print(f"  训练时间: {train_time:.2f}秒")
        print(f"  训练吞吐量: {train_throughput:,.0f}样本/秒")
        
        # 预测性能测试
        print("\n🎯 预测性能测试...")
        pred_start = time.time()
        
        predictions = hierarchical_model.predict(X)
        
        pred_time = time.time() - pred_start
        pred_throughput = len(df) / pred_time
        
        print(f"  预测时间: {pred_time:.2f}秒")
        print(f"  预测吞吐量: {pred_throughput:,.0f}样本/秒")
        
        # 评估性能测试
        print("\n📊 评估性能测试...")
        eval_start = time.time()
        
        evaluator = HierarchicalModelEvaluator()
        evaluation_results = evaluator.evaluate_comprehensive(y, predictions)
        
        eval_time = time.time() - eval_start
        eval_throughput = len(df) / eval_time
        
        print(f"  评估时间: {eval_time:.2f}秒")
        print(f"  评估吞吐量: {eval_throughput:,.0f}样本/秒")
        
        # 总体性能
        total_time = train_time + pred_time + eval_time
        overall_throughput = len(df) / total_time
        
        print(f"\n📈 总体性能:")
        print(f"  总处理时间: {total_time:.2f}秒")
        print(f"  总体吞吐量: {overall_throughput:,.0f}样本/秒")
        
        # 性能验证
        self.assertGreater(pred_throughput, 1000, "预测吞吐量应该>1000样本/秒")
        self.assertGreater(overall_throughput, 100, "总体吞吐量应该>100样本/秒")
        
        # 质量指标
        summary = evaluation_results['summary']
        key_metrics = summary['key_metrics']
        
        print(f"\n🎯 质量指标:")
        print(f"  零值分类F1: {key_metrics['zero_classification_f1']:.4f}")
        print(f"  整体R²: {key_metrics['overall_r2']:.4f}")
        print(f"  业务准确率: {key_metrics['business_accuracy_50yuan']:.1f}%")
        print(f"  性能等级: {summary['performance_grade']}")
        
        print(f"\n✅ 生产环境性能基准测试完成！")
    
    def test_data_quality_validation(self):
        """数据质量验证测试"""
        print("\n🔍 数据质量验证测试...")
        
        # 读取测试数据
        df = pd.read_csv(self.test_data_file)
        
        print(f"验证数据: {len(df)}行 x {df.shape[1]}列")
        
        # 基本数据质量检查
        print("\n📋 基本数据质量检查:")
        
        # 1. 缺失值检查
        missing_counts = df.isnull().sum()
        missing_ratio = missing_counts / len(df) * 100
        
        print(f"  缺失值检查:")
        has_missing = False
        for col, count in missing_counts.items():
            if count > 0:
                print(f"    {col}: {count}个 ({missing_ratio[col]:.1f}%)")
                has_missing = True
        
        if not has_missing:
            print(f"    ✅ 无缺失值")
        
        # 2. 数据类型检查
        print(f"  数据类型检查:")
        for col, dtype in df.dtypes.items():
            print(f"    {col}: {dtype}")
        
        # 3. 目标变量分布检查
        if 'amount' in df.columns:
            amount = df['amount']
            zero_count = (amount == 0).sum()
            zero_ratio = zero_count / len(amount) * 100
            
            print(f"  目标变量分布:")
            print(f"    零值: {zero_count}个 ({zero_ratio:.1f}%)")
            print(f"    非零值: {len(amount) - zero_count}个 ({100-zero_ratio:.1f}%)")
            print(f"    最小值: {amount.min():.2f}")
            print(f"    最大值: {amount.max():.2f}")
            print(f"    平均值: {amount.mean():.2f}")
            print(f"    中位数: {amount.median():.2f}")
            
            # 验证目标变量质量
            self.assertGreaterEqual(amount.min(), 0, "目标变量应该非负")
            self.assertGreater(zero_ratio, 5, "零值比例应该>5%")
            self.assertLess(zero_ratio, 95, "零值比例应该<95%")
        
        # 4. 特征变量检查
        feature_columns = [col for col in df.columns if col != 'amount']
        print(f"  特征变量检查:")
        print(f"    特征数量: {len(feature_columns)}")
        
        for col in feature_columns[:5]:  # 显示前5个特征的统计
            if df[col].dtype in ['int64', 'float64']:
                print(f"    {col}: 范围[{df[col].min():.2f}, {df[col].max():.2f}], 均值{df[col].mean():.2f}")
            else:
                unique_count = df[col].nunique()
                print(f"    {col}: {unique_count}个唯一值")
        
        print(f"\n✅ 数据质量验证完成！")


if __name__ == '__main__':
    # 运行生产环境端到端测试
    unittest.main(verbosity=2)
