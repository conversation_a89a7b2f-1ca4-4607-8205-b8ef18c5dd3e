#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的分层建模系统集成测试
测试核心组件的集成功能
"""

import unittest
import numpy as np
import pandas as pd
import tempfile
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.billing_audit.models.hierarchical_billing_model import HierarchicalBillingModel
from src.billing_audit.evaluation.hierarchical_evaluator import HierarchicalModelEvaluator


class TestSimpleIntegration(unittest.TestCase):
    """简化的分层建模系统集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        np.random.seed(42)
        self.n_samples = 1000
        
        # 创建测试数据
        self.X = pd.DataFrame({
            'should_fee': np.random.uniform(0, 100, self.n_samples),
            'charge_day_count': np.random.randint(1, 31, self.n_samples),
            'month_day_count': np.full(self.n_samples, 31),
            'cal_type': np.random.randint(0, 4, self.n_samples),
            'unit_type': np.random.randint(0, 3, self.n_samples)
        })
        
        # 生成目标变量（模拟零值问题）
        self.y = np.zeros(self.n_samples)
        # 30%的样本为非零值
        nonzero_indices = np.random.choice(self.n_samples, size=int(0.3 * self.n_samples), replace=False)
        self.y[nonzero_indices] = np.random.uniform(10, 200, len(nonzero_indices))
        
        print(f"集成测试数据准备完成:")
        print(f"  样本数: {self.n_samples}")
        print(f"  特征数: {self.X.shape[1]}")
        print(f"  零值比例: {(self.y == 0).mean()*100:.1f}%")
    
    def test_hierarchical_model_training_and_prediction(self):
        """测试分层模型训练和预测集成"""
        print("\n🔄 测试分层模型训练和预测集成...")
        
        # 创建分层模型
        model = HierarchicalBillingModel(use_lightgbm=False)
        
        # 训练模型
        print("训练分层模型...")
        model.fit(self.X, self.y)
        
        # 验证训练状态
        self.assertTrue(model.is_fitted)
        self.assertIsNotNone(model.zero_classifier)
        self.assertIsNotNone(model.nonzero_regressor)
        
        # 获取训练统计
        training_stats = model.get_training_stats()
        print(f"训练统计:")
        print(f"  总样本: {training_stats['total_samples']}")
        print(f"  零值比例: {training_stats['zero_ratio']:.1f}%")
        print(f"  分类准确率: {training_stats['classifier_accuracy']:.4f}")
        print(f"  回归R²: {training_stats.get('regressor_r2', 0):.4f}")
        
        # 预测
        print("执行预测...")
        predictions = model.predict(self.X)
        
        # 验证预测结果
        self.assertEqual(len(predictions), self.n_samples)
        self.assertTrue(np.all(predictions >= 0))
        
        # 统计预测结果
        zero_predictions = np.sum(predictions == 0)
        nonzero_predictions = self.n_samples - zero_predictions
        
        print(f"预测结果:")
        print(f"  零值预测: {zero_predictions} ({zero_predictions/self.n_samples*100:.1f}%)")
        print(f"  非零值预测: {nonzero_predictions} ({nonzero_predictions/self.n_samples*100:.1f}%)")
        
        print("✅ 分层模型训练和预测集成测试成功")
    
    def test_hierarchical_model_evaluation_integration(self):
        """测试分层模型评估集成"""
        print("\n📊 测试分层模型评估集成...")
        
        # 创建和训练分层模型
        model = HierarchicalBillingModel(use_lightgbm=False)
        model.fit(self.X, self.y)
        
        # 获取预测结果
        predictions = model.predict(self.X)
        
        # 创建分层评估器
        evaluator = HierarchicalModelEvaluator()
        
        # 执行综合评估
        print("执行综合评估...")
        evaluation_results = evaluator.evaluate_comprehensive(self.y, predictions)
        
        # 验证评估结果
        self.assertIn('classification_metrics', evaluation_results)
        self.assertIn('regression_metrics', evaluation_results)
        self.assertIn('overall_metrics', evaluation_results)
        self.assertIn('business_metrics', evaluation_results)
        self.assertIn('summary', evaluation_results)
        
        # 显示关键指标
        summary = evaluation_results['summary']
        key_metrics = summary['key_metrics']
        
        print(f"评估结果:")
        print(f"  零值分类F1: {key_metrics['zero_classification_f1']:.4f}")
        print(f"  非零值回归R²: {key_metrics['nonzero_regression_r2']:.4f}")
        print(f"  整体R²: {key_metrics['overall_r2']:.4f}")
        print(f"  整体MAE: {key_metrics['overall_mae']:.2f}元")
        print(f"  业务准确率(±50元): {key_metrics['business_accuracy_50yuan']:.1f}%")
        print(f"  性能等级: {summary['performance_grade']}")
        
        # 验证性能指标的合理性
        self.assertGreaterEqual(key_metrics['zero_classification_f1'], 0.5)
        self.assertGreaterEqual(key_metrics['overall_r2'], 0.0)
        self.assertGreaterEqual(key_metrics['business_accuracy_50yuan'], 30.0)
        
        print("✅ 分层模型评估集成测试成功")
    
    def test_model_save_load_integration(self):
        """测试模型保存和加载集成"""
        print("\n💾 测试模型保存和加载集成...")
        
        # 创建和训练分层模型
        original_model = HierarchicalBillingModel(use_lightgbm=False)
        original_model.fit(self.X, self.y)
        
        # 获取原始预测
        original_predictions = original_model.predict(self.X[:100])
        
        # 保存模型
        with tempfile.NamedTemporaryFile(suffix='.pkl', delete=False) as tmp_file:
            model_path = tmp_file.name
        
        try:
            print("保存模型...")
            original_model.save(model_path)
            
            # 加载模型
            print("加载模型...")
            loaded_model = HierarchicalBillingModel.load(model_path)
            
            # 验证加载的模型
            self.assertTrue(loaded_model.is_fitted)
            self.assertEqual(loaded_model.feature_names, original_model.feature_names)
            
            # 获取加载后的预测
            loaded_predictions = loaded_model.predict(self.X[:100])
            
            # 验证预测一致性
            np.testing.assert_array_almost_equal(original_predictions, loaded_predictions, decimal=6)
            
            print(f"预测一致性验证:")
            print(f"  原始预测前5个: {original_predictions[:5]}")
            print(f"  加载预测前5个: {loaded_predictions[:5]}")
            print(f"  最大差异: {np.max(np.abs(original_predictions - loaded_predictions)):.8f}")
            
        finally:
            # 清理临时文件
            if os.path.exists(model_path):
                os.unlink(model_path)
        
        print("✅ 模型保存和加载集成测试成功")
    
    def test_hierarchical_vs_traditional_performance(self):
        """测试分层模型与传统模型性能对比"""
        print("\n⚖️ 测试分层模型与传统模型性能对比...")
        
        # 分割数据
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=0.3, random_state=42
        )
        
        # 训练分层模型
        print("训练分层模型...")
        hierarchical_model = HierarchicalBillingModel(use_lightgbm=False)
        hierarchical_model.fit(X_train, y_train)
        hierarchical_pred = hierarchical_model.predict(X_test)
        
        # 训练传统模型（RandomForest）
        print("训练传统模型...")
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.metrics import r2_score, mean_absolute_error
        
        traditional_model = RandomForestRegressor(n_estimators=100, random_state=42)
        traditional_model.fit(X_train, y_train)
        traditional_pred = traditional_model.predict(X_test)
        
        # 计算性能指标
        hierarchical_r2 = r2_score(y_test, hierarchical_pred)
        hierarchical_mae = mean_absolute_error(y_test, hierarchical_pred)
        
        traditional_r2 = r2_score(y_test, traditional_pred)
        traditional_mae = mean_absolute_error(y_test, traditional_pred)
        
        # 零值识别性能
        zero_mask_true = (y_test == 0)
        zero_mask_hierarchical = (hierarchical_pred == 0)
        zero_mask_traditional = (traditional_pred <= 1e-6)  # 传统模型很少预测精确的0
        
        hierarchical_zero_accuracy = (zero_mask_true == zero_mask_hierarchical).mean()
        traditional_zero_accuracy = (zero_mask_true == zero_mask_traditional).mean()
        
        print(f"性能对比结果:")
        print(f"分层模型:")
        print(f"  R²: {hierarchical_r2:.4f}")
        print(f"  MAE: {hierarchical_mae:.2f}元")
        print(f"  零值识别准确率: {hierarchical_zero_accuracy:.4f}")
        
        print(f"传统模型:")
        print(f"  R²: {traditional_r2:.4f}")
        print(f"  MAE: {traditional_mae:.2f}元")
        print(f"  零值识别准确率: {traditional_zero_accuracy:.4f}")
        
        # 验证分层模型在零值识别方面的优势
        self.assertGreaterEqual(hierarchical_zero_accuracy, traditional_zero_accuracy - 0.1,
                               "分层模型在零值识别方面应该不逊于传统模型")
        
        print("✅ 分层模型与传统模型性能对比测试成功")
    
    def test_end_to_end_workflow_simulation(self):
        """测试端到端工作流模拟"""
        print("\n🔄 测试端到端工作流模拟...")
        
        # 模拟完整的工作流程
        steps_completed = []
        
        try:
            # 步骤1: 数据准备
            print("步骤1: 数据准备...")
            self.assertIsNotNone(self.X)
            self.assertIsNotNone(self.y)
            steps_completed.append("数据准备")
            
            # 步骤2: 模型训练
            print("步骤2: 分层模型训练...")
            model = HierarchicalBillingModel(use_lightgbm=False)
            model.fit(self.X, self.y)
            self.assertTrue(model.is_fitted)
            steps_completed.append("模型训练")
            
            # 步骤3: 模型预测
            print("步骤3: 模型预测...")
            predictions = model.predict(self.X)
            self.assertEqual(len(predictions), len(self.y))
            steps_completed.append("模型预测")
            
            # 步骤4: 性能评估
            print("步骤4: 性能评估...")
            evaluator = HierarchicalModelEvaluator()
            evaluation_results = evaluator.evaluate_comprehensive(self.y, predictions)
            self.assertIn('summary', evaluation_results)
            steps_completed.append("性能评估")
            
            # 步骤5: 结果分析
            print("步骤5: 结果分析...")
            summary = evaluation_results['summary']
            performance_grade = summary['performance_grade']
            self.assertIn(performance_grade, ['A+', 'A', 'B+', 'B', 'C', 'D'])
            steps_completed.append("结果分析")
            
            print(f"端到端工作流完成:")
            for i, step in enumerate(steps_completed, 1):
                print(f"  ✅ 步骤{i}: {step}")
            
            print(f"最终性能等级: {performance_grade}")
            
        except Exception as e:
            print(f"工作流在步骤 '{len(steps_completed)+1}' 失败: {e}")
            raise
        
        print("✅ 端到端工作流模拟测试成功")


if __name__ == '__main__':
    # 运行简化集成测试
    unittest.main(verbosity=2)
