#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器单元测试
"""

import unittest
import tempfile
import json
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.utils.config_manager import ConfigManager, ModelConfig


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_config = {
            "project": {
                "name": "测试项目",
                "version": "1.0.0"
            },
            "billing_audit": {
                "fixed_fee": {
                    "feature_columns": ["col1", "col2"],
                    "target_column": "amount",
                    "passthrough_columns": ["id"],
                    "categorical_columns": ["col1"],
                    "numerical_columns": ["col2"],
                    "date_columns": []
                }
            },
            "data_sources": {
                "test_data": "test.xlsx"
            }
        }
        
        # 创建临时文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.temp_config, self.temp_file, ensure_ascii=False, indent=2)
        self.temp_file.close()
        
        # 创建配置管理器
        self.config_manager = ConfigManager(self.temp_file.name)
    
    def tearDown(self):
        """测试后清理"""
        Path(self.temp_file.name).unlink(missing_ok=True)
    
    def test_load_config(self):
        """测试配置加载"""
        self.assertIsNotNone(self.config_manager.config)
        self.assertEqual(self.config_manager.get('project.name'), '测试项目')
    
    def test_get_config_value(self):
        """测试获取配置值"""
        # 测试存在的配置
        self.assertEqual(self.config_manager.get('project.version'), '1.0.0')
        
        # 测试不存在的配置
        self.assertIsNone(self.config_manager.get('nonexistent.key'))
        
        # 测试默认值
        self.assertEqual(self.config_manager.get('nonexistent.key', 'default'), 'default')
    
    def test_get_model_config(self):
        """测试获取模型配置"""
        model_config = self.config_manager.get_model_config('billing_audit', 'fixed_fee')
        
        self.assertIsInstance(model_config, ModelConfig)
        self.assertEqual(model_config.feature_columns, ['col1', 'col2'])
        self.assertEqual(model_config.target_column, 'amount')
        self.assertEqual(model_config.categorical_columns, ['col1'])
    
    def test_get_data_source(self):
        """测试获取数据源"""
        data_source = self.config_manager.get_data_source('test_data')
        self.assertEqual(data_source, 'test.xlsx')
    
    def test_update_config(self):
        """测试更新配置"""
        self.config_manager.update_config('project.version', '2.0.0')
        self.assertEqual(self.config_manager.get('project.version'), '2.0.0')
    
    def test_validate_config(self):
        """测试配置验证"""
        # 由于测试配置不完整，应该有错误
        errors = self.config_manager.validate_config()
        self.assertIsInstance(errors, list)


class TestModelConfig(unittest.TestCase):
    """模型配置数据类测试"""
    
    def test_model_config_creation(self):
        """测试模型配置创建"""
        config = ModelConfig(
            feature_columns=['a', 'b'],
            target_column='target',
            passthrough_columns=['id'],
            categorical_columns=['a'],
            numerical_columns=['b'],
            date_columns=[]
        )
        
        self.assertEqual(config.feature_columns, ['a', 'b'])
        self.assertEqual(config.target_column, 'target')


if __name__ == '__main__':
    unittest.main()
